import os
import sys
import logging
import json
from PIL import Image
import cv2
import numpy as np
import imagehash
from collections import defaultdict
import math
import moviepy
from moviepy.video.VideoClip import ImageClip
from moviepy.audio.io.AudioFileClip import AudioFileClip
from config import logger, OUTRO_MUSIC, LANGUAGE_CODES, ASSET_DIR
from typing import List, Tuple
import argparse
import tempfile
from config import VIDEO_CODEC, VIDEO_AUDIO_CODEC, VIDEO_FPS, VIDEO_BITRATE, VIDEO_PRESET, VIDEO_CRF, logger, VIDEO_RESOLUTION

USE_FRONT_COVER = False  # 控制是否生成前封面

# 定义不同语言的音乐文件路径
english_outro_music = os.path.join(ASSET_DIR, 'music', 'outro_en.m4a')
english_history_outro_music = os.path.join(ASSET_DIR, 'music', 'outro_en-story.m4a')
spanish_outro_music = os.path.join(ASSET_DIR, 'music', 'outro_es.m4a')
japanese_outro_music = os.path.join(ASSET_DIR, 'music', 'outro_ja.m4a')
portuguese_outro_music = os.path.join(ASSET_DIR, 'music', 'outro_pt.m4a')

# Increase PIL's maximum image size limit
Image.MAX_IMAGE_PIXELS = None
logger = logging.getLogger(__name__)

# Configuration Variables
NUM_IMAGES = 5
CANDIDATE_IMAGE_NUMBERS = 15
SPACING = 0
OUTPUT_FILE = 'front_cover.jpg'
END_COVER_FILE = 'end_cover.jpg'
END_COVER_END_TIME = 15
FRONT_COVER_END_TIME = 4
MAX_IMAGE_DIMENSION = 65000  # Maximum supported image dimension
MIN_ASPECT_RATIO = 0.75  # 最小宽高比
MAX_ASPECT_RATIO = 1.5   # 最大宽高比

# 在文件顶部添加以下变量定义
COVER_DIR = 'cover_clips'  # 默认封面目录名


def is_bw(image_path):
    try:
        image = cv2.imread(image_path)
        if image is None:
            logger.warning(f"Failed to read image: {image_path}")
            return False
        return np.array_equal(image[:, :, 0], image[:, :, 1]) and np.array_equal(image[:, :, 1], image[:, :, 2])
    except Exception as e:
        logger.error(f"Error checking if image is black and white: {e}, image path: {image_path}")
        return False

def load_images_from_folder(folder):
    images = []
    try:
        for filename in os.listdir(folder):
            img_path = os.path.join(folder, filename)
            if os.path.isfile(img_path) and img_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
                images.append(img_path)
        if not images:
            logger.warning(f"No valid images found in the folder: {folder}")
        logger.info(f"Loaded {len(images)} images from folder: {folder}")
    except Exception as e:
        logger.error(f"Error loading images from folder: {folder}, error: {e}")
    return images

def get_image_hash(image_path):
    try:
        image = Image.open(image_path).convert('RGB')
        image_hash = imagehash.average_hash(image)
        image.close()  # 释放资源
        return image_hash
    except Exception as e:
        logger.error(f"Error generating hash for image: {image_path}, error: {e}")
        return None

def find_unrelated_images(image_hashes, m):
    try:
        distances = defaultdict(list)
        keys = list(image_hashes.keys())
        for i in range(len(keys)):
            for j in range(i + 1, len(keys)):
                dist = image_hashes[keys[i]] - image_hashes[keys[j]]
                distances[keys[i]].append((dist, keys[j]))
                distances[keys[j]].append((dist, keys[i]))

        unrelated_images = sorted(distances.keys(), key=lambda k: sum(d[0] for d in distances[k]), reverse=True)
        return unrelated_images[:m]
    except Exception as e:
        logger.error(f"Error finding unrelated images: {e}")
        return []

def get_top_n_images(images, candidate_image_numbers):
    try:
        remaining_images = sorted(images, key=os.path.getsize, reverse=True)
        logger.info(f"Selected top {candidate_image_numbers} images based on size")
        return remaining_images[:candidate_image_numbers]
    except Exception as e:
        logger.error(f"Error selecting top N images: {e}")
        return images[:candidate_image_numbers]

def select_images(image_folder, num_images, candidate_image_numbers, min_aspect_ratio=MIN_ASPECT_RATIO, max_aspect_ratio=MAX_ASPECT_RATIO):
    try:
        images = load_images_from_folder(image_folder)
        if not images:
            logger.error("No images found in the folder.")
            return [], []
        
        top_n_images = get_top_n_images(images, candidate_image_numbers)
        
        image_hashes = {img: get_image_hash(img) for img in top_n_images}
        
        # 过滤掉宽高比不在允许范围内的图片
        filtered_images = []
        for img_path in top_n_images:
            img = Image.open(img_path)
            width, height = img.size
            aspect_ratio = width / height
            if min_aspect_ratio <= aspect_ratio <= max_aspect_ratio:
                filtered_images.append(img_path)
            img.close()  # 释放资源

        if len(filtered_images) < num_images:
            logger.warning("Filtered images are less than the required number, consider adjusting the aspect ratio range.")
            filtered_images = top_n_images[:num_images]  # 如果过滤后数量不足，使用最初的图片

        bw_images = [img for img in filtered_images if is_bw(img)]
        if len(bw_images) >= num_images:
            unrelated_images = find_unrelated_images({img: image_hashes[img] for img in bw_images}, num_images)
        else:
            color_images = [img for img in filtered_images if img not in bw_images]
            if len(color_images) >= num_images:
                unrelated_images = find_unrelated_images({img: image_hashes[img] for img in color_images}, num_images)
            else:
                unrelated_images = find_unrelated_images(image_hashes, num_images)
        
        selected_images = [Image.open(img).convert('RGB') for img in unrelated_images[:num_images]]
        logger.info(f"Selected {len(selected_images)} images for processing")
        return selected_images, top_n_images
    except Exception as e:
        logger.error(f"Error selecting images: {e}")
        return [], []

def create_masonry_layout(images, column_width, spacing):
    try:
        columns = [[] for _ in range(math.ceil(len(images) ** 0.5))]
        column_heights = [0] * len(columns)

        for img in images:
            min_height_index = column_heights.index(min(column_heights))
            columns[min_height_index].append(img)
            column_heights[min_height_index] += img.height + spacing

        logger.info(f"Created masonry layout with {len(columns)} columns")
        return columns, max(column_heights)
    except Exception as e:
        logger.error(f"Error creating masonry layout: {e}")
        return [], 0

def combine_columns(columns, canvas_width, spacing):
    try:
        max_height = max(
            sum(img.height for img in col) + spacing * (len(col) - 1)
            for col in columns
        )
        canvas_height = max_height
        canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
        
        # 计算每列的宽度
        num_columns = len(columns)
        column_width = (canvas_width - (num_columns - 1) * spacing) // num_columns
        
        # 开始绘制
        x_offset = 0
        for col in columns:
            y_offset = 0
            for img in col:
                canvas.paste(img, (x_offset, y_offset))
                y_offset += img.height + spacing
            x_offset += column_width + spacing

        logger.info(f"Combined columns into final image with dimensions: {canvas.size}")
        return canvas
    except Exception as e:
        logger.error(f"Error combining columns: {e}")
        return None

def create_end_cover(image_paths, resolution, spacing=0):
    try:
        total_width, total_height = resolution
        images = [Image.open(img).convert('RGB') for img in image_paths]

        # 计算列数和列宽
        num_columns = int(math.ceil(len(images) ** 0.5))
        column_width = (total_width - (num_columns - 1) * spacing) // num_columns

        # 调整图片大小时保持宽度一致
        resized_images = []
        for img in images:
            aspect_ratio = img.width / img.height
            new_height = int(column_width / aspect_ratio)
            resized_img = img.resize((column_width, new_height), Image.LANCZOS)
            resized_images.append(resized_img)

        columns, _ = create_masonry_layout(resized_images, column_width, spacing)

        new_image = combine_columns(columns, total_width, spacing)

        final_image = new_image.crop((0, 0, total_width, min(new_image.height, total_height)))

        logger.info(f"Created end cover image with final dimensions: {final_image.size}")
        return final_image
    except Exception as e:
        logger.error(f"Error creating end cover: {e}")
        return None

def resize_and_pad(image, target_width, target_height):
    # 计算宽高比
    aspect_ratio = image.width / image.height
    target_ratio = target_width / target_height

    if aspect_ratio > target_ratio:
        # 图片较宽，以宽度为准
        new_width = target_width
        new_height = int(new_width / aspect_ratio)
    else:
        # 图片较高，以高度为准
        new_height = target_height
        new_width = int(new_height * aspect_ratio)

    # 调整图片大小
    resized_image = image.resize((new_width, new_height), Image.LANCZOS)

    # 创建新的背景图片
    new_image = Image.new("RGB", (target_width, target_height), (0, 0, 0))

    # 计算粘贴位置（居中）
    paste_x = (target_width - new_width) // 2
    paste_y = (target_height - new_height) // 2

    # 粘贴调整后的图片
    new_image.paste(resized_image, (paste_x, paste_y))

    return new_image

def create_cover_clip(image_path, duration, audio_path, output_path, fps=VIDEO_FPS):
    try:
        # 打开图片并调整大小
        with Image.open(image_path) as img:
            resized_img = resize_and_pad(img, VIDEO_RESOLUTION[0], VIDEO_RESOLUTION[1])
            
            # 使用 tempfile 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
                resized_img.save(temp_path)

        clip = ImageClip(temp_path).set_duration(duration).set_fps(fps)

        if audio_path and os.path.exists(audio_path):
            audio_clip = AudioFileClip(audio_path)
            audio_duration = min(duration, audio_clip.duration)
            audio_clip = audio_clip.subclip(0, audio_duration)
            clip = clip.set_audio(audio_clip)

        clip.write_videofile(output_path, 
                           codec=VIDEO_CODEC,
                           audio_codec=VIDEO_AUDIO_CODEC,
                           fps=VIDEO_FPS,
                           bitrate=VIDEO_BITRATE,
                           preset=VIDEO_PRESET,
                           ffmpeg_params=['-crf', str(VIDEO_CRF)],
                           logger=None)
        logger.info(f"Cover clip created at: {output_path}")

        # 删除临时文件
        os.unlink(temp_path)

        return output_path
    except Exception as e:
        logger.error(f"Error creating cover clip: {e}")
        # 确保在发生异常时也删除临时文件
        if 'temp_path' in locals():
            try:
                os.unlink(temp_path)
            except Exception:
                pass
        return None

def generate_cover(image_folder, language='English'):
    try:
        logger.info("Enter generate_cover.")
        resolution = VIDEO_RESOLUTION

        logger.info("Selecting images for end cover.")
        selected_images, top_n_images_paths = select_images(image_folder, NUM_IMAGES, CANDIDATE_IMAGE_NUMBERS)
        
        if not selected_images:
            logger.error("No images selected for end cover.")
            return None

        # 获取图片文件夹的绝对路径和父目录
        image_folder_abs = os.path.abspath(image_folder)
        parent_dir = os.path.dirname(image_folder_abs)
        
        # 获取语言后缀
        lang_code = LANGUAGE_CODES.get(language, 'en')
        lang_suffix = f"_{lang_code}" if lang_code != 'en' else ""

        # 创建与图片文件夹同级的 covers 目录
        covers_dir = os.path.join(parent_dir, COVER_DIR)
        os.makedirs(covers_dir, exist_ok=True)

        logger.info("Creating end cover.")
        cover_image = create_end_cover(top_n_images_paths, resolution, SPACING)
        cover_path = os.path.join(covers_dir, f'end_cover{lang_suffix}.jpg')

        if cover_image is None:
            logger.error("Failed to create end cover image.")
            return None

        logger.info(f"Saving end cover image to {cover_path}.")
        cover_image.save(cover_path, quality=95, optimize=True)
        logger.info(f"End cover image saved as {cover_path}")

        # 视频剪辑也保存在同一个 covers 目录下
        clip_path = os.path.join(covers_dir, f"end_cover_clip{lang_suffix}.mp4")

        # 根据语言选择音乐
        if language == 'English':
            outro_music = english_outro_music
        elif language == 'Spanish':
            outro_music = spanish_outro_music
        elif language == 'Japanese':
            outro_music = japanese_outro_music
        elif language == 'Portuguese':
            outro_music = portuguese_outro_music
        elif language == 'English-Story':
            outro_music = english_history_outro_music
        else:
            outro_music = OUTRO_MUSIC

        cover_clip_path = create_cover_clip(cover_path, END_COVER_END_TIME, outro_music, clip_path)
        return cover_clip_path

    except Exception as e:
        logger.error(f"Error in generate_cover: {str(e)}")
        logger.debug("Exception details:", exc_info=True)
        return None

def main():
    parser = argparse.ArgumentParser(description="Generate cover for presentation")
    parser.add_argument("image_folder", help="Path to the image folder")
    parser.add_argument("--lang", default="English", help="Language for the cover (default: English)")
    
    args = parser.parse_args()

    if not os.path.isdir(args.image_folder):
        print(f"Error: {args.image_folder} is not a valid directory.")
        sys.exit(1)

    try:
        end_cover_clip = generate_cover(args.image_folder, args.lang)
        if end_cover_clip:
            logger.info("Cover generation completed successfully.")
            logger.info(f"End cover clip: {end_cover_clip}")
        else:
            logger.error("Cover generation failed.")
    except Exception as e:
        logger.error(f"An error occurred: {e}")

if __name__ == "__main__":
    main()


