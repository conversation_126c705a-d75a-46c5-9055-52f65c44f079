#!/usr/bin/env python3
"""
测试 temperature 参数修复
验证所有参数都在 Google Gemini 允许的范围内
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gpt_parameters import (
    GPT_PARAMETERS,
    get_gpt_parameters,
    LLM_PROVIDER_GOOGLE,
    MODEL_KEY_GEMINI_25_PRO
)


def validate_parameter_ranges():
    """验证所有参数是否在允许范围内"""
    print("=" * 60)
    print("验证 Google Gemini 参数范围")
    print("=" * 60)
    
    # Google Gemini 的参数范围
    valid_ranges = {
        "temperature": (0.0, 1.0),
        "top_p": (0.0, 1.0),
        "frequency_penalty": (-2.0, 2.0),  # 通常范围
        "presence_penalty": (-2.0, 2.0)    # 通常范围
    }
    
    issues_found = []
    
    print("检查所有 API 函数的参数配置...")
    print("-" * 60)
    
    for api_function, params in GPT_PARAMETERS.items():
        if api_function == "default":
            continue
            
        print(f"\n检查 {api_function}:")
        
        # 检查每个参数
        for param_name, param_value in params.items():
            if param_name in valid_ranges:
                min_val, max_val = valid_ranges[param_name]
                
                if isinstance(param_value, (int, float)):
                    if param_value < min_val or param_value > max_val:
                        issue = f"  ❌ {param_name}: {param_value} (超出范围 [{min_val}, {max_val}])"
                        print(issue)
                        issues_found.append(f"{api_function}.{param_name}: {param_value}")
                    else:
                        print(f"  ✅ {param_name}: {param_value}")
                else:
                    print(f"  ⚠️  {param_name}: {param_value} (非数值类型)")
    
    print("\n" + "=" * 60)
    if issues_found:
        print(f"❌ 发现 {len(issues_found)} 个参数问题:")
        for issue in issues_found:
            print(f"  - {issue}")
    else:
        print("✅ 所有参数都在有效范围内！")
    
    return len(issues_found) == 0


def test_specific_functions():
    """测试特定的剧本生成函数参数"""
    print("\n" + "=" * 60)
    print("测试关键剧本生成函数参数")
    print("=" * 60)
    
    key_functions = [
        "generate_full_script",
        "generate_episode_script", 
        "refine_script",
        "convert_script_to_json"
    ]
    
    for func_name in key_functions:
        print(f"\n{func_name}:")
        print("-" * 40)
        
        # 获取参数
        params = get_gpt_parameters(
            func_name, 
            LLM_PROVIDER_GOOGLE, 
            MODEL_KEY_GEMINI_25_PRO
        )
        
        # 显示关键参数
        key_params = ["temperature", "top_p", "frequency_penalty", "presence_penalty"]
        for param in key_params:
            if param in params:
                value = params[param]
                if param == "temperature":
                    if 0.0 <= value <= 1.0:
                        print(f"  ✅ {param}: {value}")
                    else:
                        print(f"  ❌ {param}: {value} (超出范围 [0.0, 1.0])")
                else:
                    print(f"  📊 {param}: {value}")


def test_model_compatibility():
    """测试模型兼容性"""
    print("\n" + "=" * 60)
    print("测试 Google Gemini 模型兼容性")
    print("=" * 60)
    
    try:
        # 测试获取参数是否正常工作
        params = get_gpt_parameters(
            "generate_full_script",
            LLM_PROVIDER_GOOGLE,
            MODEL_KEY_GEMINI_25_PRO
        )
        
        print("✅ 参数获取成功")
        print(f"📊 获取到的参数: {params}")
        
        # 检查必要参数是否存在
        required_params = ["temperature", "top_p"]
        missing_params = [p for p in required_params if p not in params]
        
        if missing_params:
            print(f"⚠️  缺少必要参数: {missing_params}")
        else:
            print("✅ 所有必要参数都存在")
            
    except Exception as e:
        print(f"❌ 参数获取失败: {str(e)}")


def show_before_after_comparison():
    """显示修复前后的对比"""
    print("\n" + "=" * 60)
    print("修复前后对比")
    print("=" * 60)
    
    fixes = [
        {
            "function": "generate_full_script",
            "parameter": "temperature", 
            "before": 1.3,
            "after": 0.9,
            "reason": "超出 Google Gemini 允许范围 [0.0, 1.0]"
        },
        {
            "function": "generate_episode_script",
            "parameter": "temperature",
            "before": 1.2, 
            "after": 0.9,
            "reason": "超出 Google Gemini 允许范围 [0.0, 1.0]"
        },
        {
            "function": "refine_script",
            "parameter": "temperature",
            "before": 1.5,
            "after": 0.8,
            "reason": "超出 Google Gemini 允许范围 [0.0, 1.0]"
        }
    ]
    
    for fix in fixes:
        print(f"\n{fix['function']}.{fix['parameter']}:")
        print(f"  修复前: {fix['before']}")
        print(f"  修复后: {fix['after']}")
        print(f"  原因: {fix['reason']}")
        print(f"  状态: ✅ 已修复")


def main():
    """主测试函数"""
    print("开始测试 temperature 参数修复...")
    
    # 显示修复对比
    show_before_after_comparison()
    
    # 验证参数范围
    all_valid = validate_parameter_ranges()
    
    # 测试特定函数
    test_specific_functions()
    
    # 测试模型兼容性
    test_model_compatibility()
    
    print("\n" + "=" * 60)
    if all_valid:
        print("🎉 所有参数修复成功！")
        print("✅ Google Gemini 模型现在可以正常工作")
        print("✅ 长剧集生成功能已就绪")
    else:
        print("⚠️  仍有部分参数需要调整")
    print("=" * 60)


if __name__ == "__main__":
    main()
