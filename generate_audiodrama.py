# generate_audiodrama.py
import os
import sys
import json
import argparse
from typing import Dict, Any, List, Optional, Tuple
from tqdm import tqdm
from dataclasses import dataclass

from config import (
    logger,
    get_param,
    ANIMATION_OUTPUTS_DIR,
    ANIMATION_DRAMA_DIR,
    LANGUAGE_CODES,
    GPT_CONTEXT_LIMIT,
    GPT_OUTPUT_LIMIT
)
from modules.gpt4_interface import call_gpt_json_response
from modules.text_preprocessing import split_text_for_analysis
from modules.utils import read_file_with_encoding, smart_paragraph_split
from validate_drama_script import check_scene_emotional_depth, check_narrative_flow, validate_scene_pacing
from modules.episode_outline import generate_global_outline, group_chapter_summaries, summarize_groups

@dataclass
class ConflictAnalysis:
    """章节冲突分析结果"""
    type: str  # internal/external
    setup: str
    development: str
    resolution: str
    emotional_intensity: str  # low/medium/high

@dataclass
class CharacterDynamic:
    """角色动态信息"""
    character: str
    emotional_state: str
    goals: List[str]
    obstacles: List[str]
    development: str

@dataclass
class ThemeElement:
    """主题元素信息"""
    main_themes: List[str]
    emotional_tone: str
    atmosphere: str
    audio_enhancement_points: List[str]

@dataclass
class EmotionalWave:
    """情感波动控制"""
    intensity: float  # 0.0-1.0
    duration: float  # 秒
    peak_position: float  # 0.0-1.0，波峰位置
    transition_type: str  # gradual/sudden/plateau

@dataclass
class ChapterAnalysis:
    """章节分析结果"""
    core_conflict: ConflictAnalysis
    character_dynamics: List[CharacterDynamic]
    theme_elements: ThemeElement

@dataclass
class Shot:
    """镜头信息"""
    shot_number: int
    shot_type: str  # establishing/action/dialogue/emotional
    duration: float
    focus: str
    mood: str

@dataclass
class Scene:
    """场景信息"""
    scene_number: int
    conflict_type: str  # emotional/external
    duration: float
    emotional_intensity: str  # low/medium/high
    emotional_wave: EmotionalWave  # 新增情感波动控制
    mini_conflict: Dict[str, str]
    shots: List[Shot]
    transition: Dict[str, str]

@dataclass
class ContinuousConflictStructure:
    """连续冲突结构"""
    total_duration: int
    num_scenes: int
    scenes: List[Scene]

def analyze_chapter_conflict(
    chapter_text: str,
    style: str = "engaging",
    language: str = "Chinese"
) -> ChapterAnalysis:
    """分析章节的冲突、角色动态和主题元素"""
    try:
        data = {
            "chapter_text": chapter_text,
            "style": style,
            "language": language
        }
        
        response = call_gpt_json_response(
            "analyze_chapter_conflict",
            data,
            using_cache=True
        )
        
        if not response or "chapter_analysis" not in response:
            raise ValueError("章节分析失败")
            
        analysis = response["chapter_analysis"]
        
        # 构建结构化的分析结果
        core_conflict = ConflictAnalysis(
            type=analysis["core_conflict"]["type"],
            setup=analysis["core_conflict"]["setup"],
            development=analysis["core_conflict"]["development"],
            resolution=analysis["core_conflict"]["resolution"],
            emotional_intensity=analysis["core_conflict"]["emotional_intensity"]
        )
        
        character_dynamics = [
            CharacterDynamic(
                character=char["character"],
                emotional_state=char["emotional_state"],
                goals=char["goals"],
                obstacles=char["obstacles"],
                development=char["development"]
            )
            for char in analysis["character_dynamics"]
        ]
        
        theme_elements = ThemeElement(
            main_themes=analysis["theme_elements"]["main_themes"],
            emotional_tone=analysis["theme_elements"]["emotional_tone"],
            atmosphere=analysis["theme_elements"]["atmosphere"],
            audio_enhancement_points=analysis["theme_elements"]["audio_enhancement_points"]
        )
        
        return ChapterAnalysis(
            core_conflict=core_conflict,
            character_dynamics=character_dynamics,
            theme_elements=theme_elements
        )
        
    except Exception as e:
        logger.error(f"章节分析失败: {str(e)}")
        raise

def generate_and_optimize_structure(chapter_analyses: List[ChapterAnalysis], total_duration: int = 180, num_scenes: int = 30, style: str = "engaging", language: str = "Chinese") -> ContinuousConflictStructure:
    """生成并优化连续冲突结构"""
    data = {
        "chapter_analyses": [
            {
                "core_conflict": {
                    "type": analysis.core_conflict.type,
                    "setup": analysis.core_conflict.setup,
                    "development": analysis.core_conflict.development,
                    "resolution": analysis.core_conflict.resolution,
                    "emotional_intensity": analysis.core_conflict.emotional_intensity
                },
                "character_dynamics": [
                    {
                        "character": char.character,
                        "emotional_state": char.emotional_state,
                        "goals": char.goals,
                        "obstacles": char.obstacles,
                        "development": char.development
                    }
                    for char in analysis.character_dynamics
                ],
                "theme_elements": {
                    "main_themes": analysis.theme_elements.main_themes,
                    "emotional_tone": analysis.theme_elements.emotional_tone,
                    "atmosphere": analysis.theme_elements.atmosphere,
                    "audio_enhancement_points": analysis.theme_elements.audio_enhancement_points
                }
            }
            for analysis in chapter_analyses
        ],
        "total_duration": total_duration,
        "num_scenes": num_scenes,
        "style": style,
        "language": language
    }
    
    response = call_gpt_json_response("generate_continuous_conflict_structure", data, using_cache=True)
    structure = response["continuous_conflict_structure"]
    
    scenes = [
        Scene(
            scene_number=scene["scene_number"],
            conflict_type=scene["conflict_type"],
            duration=scene["duration"],
            emotional_intensity=scene["emotional_intensity"],
            emotional_wave=EmotionalWave(
                intensity=scene["emotional_wave"]["intensity"],
                duration=scene["emotional_wave"]["duration"],
                peak_position=scene["emotional_wave"]["peak_position"],
                transition_type=scene["emotional_wave"]["transition_type"]
            ),
            mini_conflict=scene["mini_conflict"],
            shots=[
                Shot(
                    shot_number=shot["shot_number"],
                    shot_type=shot["shot_type"],
                    duration=shot["duration"],
                    focus=shot["focus"],
                    mood=shot["mood"]
                )
                for shot in scene["shots"]
            ],
            transition=scene["transition"]
        )
        for scene in structure["scenes"]
    ]
    
    return ContinuousConflictStructure(
        total_duration=structure["total_duration"],
        num_scenes=structure["num_scenes"],
        scenes=scenes
    )

def validate_script_segment(segment: Dict[str, Any], scene_number: int) -> bool:
    """验证脚本片段的质量"""
    try:
        # 检查情感深度
        if not check_scene_emotional_depth(segment):
            logger.warning(f"场景 {scene_number} 情感深度不足")
            return False
            
        # 检查叙事流
        if not check_narrative_flow(segment):
            logger.warning(f"场景 {scene_number} 叙事流不连贯")
            return False
            
        # 检查场景节奏
        if not validate_scene_pacing(segment):
            logger.warning(f"场景 {scene_number} 节奏控制不当")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"验证脚本片段时出错: {str(e)}")
        return False

def generate_script_for_scene(
    scene: Scene, 
    story_outline: Dict[str, Any],
    style: str, 
    language: str
) -> Dict[str, Any]:
    """生成场景脚本"""
    scene_context = {
        "current_scene": {
            "scene_number": scene.scene_number,
            "conflict_type": scene.conflict_type,
            "duration": scene.duration,
            "emotional_intensity": scene.emotional_intensity,
            "emotional_wave": {
                "intensity": scene.emotional_wave.intensity,
                "duration": scene.emotional_wave.duration,
                "peak_position": scene.emotional_wave.peak_position,
                "transition_type": scene.emotional_wave.transition_type
            },
            "mini_conflict": scene.mini_conflict,
            "shots": [
                {
                    "shot_number": shot.shot_number,
                    "shot_type": shot.shot_type,
                    "duration": shot.duration,
                    "focus": shot.focus,
                    "mood": shot.mood
                }
                for shot in scene.shots
            ],
            "transition": scene.transition
        },
        "story_outline": story_outline,  # 添加故事大纲信息
        "style": style,
        "language": language
    }
    
    response = call_gpt_json_response("generate_script_segment", scene_context, using_cache=True)
    
    if not response or "script_segment" not in response:
        raise ValueError("脚本生成失败")
    
    return response["script_segment"]

def process_novel(input_file: str, style: str = "engaging", language: str = "Chinese") -> None:
    """处理小说文本，生成基于连续冲突的音频剧本"""
    try:
        input_basename = os.path.splitext(os.path.basename(input_file))[0]
        os.makedirs(ANIMATION_DRAMA_DIR, exist_ok=True)
        
        # 1. 加载章节摘要
        with open(input_file, 'r', encoding='utf-8') as f:
            chapter_summaries = json.load(f)

        # 2. 生成全局大纲
        logger.info("生成全局大纲...")
        # 2.1 按token限制分组章节
        chapter_groups = group_chapter_summaries(
            chapter_summaries,
            total_context_size=GPT_CONTEXT_LIMIT,
            group_output_size=GPT_OUTPUT_LIMIT
        )
        
        # 2.2 生成组摘要
        group_summary = summarize_groups(chapter_groups, style, language)
        
        # 2.3 生成全局大纲
        story_outline = generate_global_outline(
            group_summary,
            style,
            language,
            input_file
        )
        
        # 保存全局大纲
        outline_path = os.path.join(ANIMATION_DRAMA_DIR, f"{input_basename}_outline.json")
        with open(outline_path, 'w', encoding='utf-8') as f:
            json.dump(story_outline, f, ensure_ascii=False, indent=2)

        # 3. 分析章节冲突
        chapter_analyses = [
            analyze_chapter_conflict(chapter, style, language) 
            for chapter in tqdm(chapter_summaries, desc="分析章节冲突")
        ]

        # 4. 生成并优化连续冲突结构
        structure = generate_and_optimize_structure(
            chapter_analyses, 
            total_duration=180, 
            num_scenes=len(chapter_analyses) * 2, 
            style=style, 
            language=language
        )
        
        # 保存优化后的结构
        optimized_path = os.path.join(ANIMATION_DRAMA_DIR, "optimized_structure.json")
        with open(optimized_path, 'w', encoding='utf-8') as f:
            json.dump(
                {
                    "total_duration": structure.total_duration,
                    "num_scenes": structure.num_scenes,
                    "scenes": [
                        {
                            "scene_number": s.scene_number,
                            "conflict_type": s.conflict_type,
                            "duration": s.duration,
                            "emotional_intensity": s.emotional_intensity,
                            "emotional_wave": {
                                "intensity": s.emotional_wave.intensity,
                                "duration": s.emotional_wave.duration,
                                "peak_position": s.emotional_wave.peak_position,
                                "transition_type": s.emotional_wave.transition_type
                            },
                            "mini_conflict": s.mini_conflict,
                            "shots": [
                                {
                                    "shot_number": shot.shot_number,
                                    "shot_type": shot.shot_type,
                                    "duration": shot.duration,
                                    "focus": shot.focus,
                                    "mood": shot.mood
                                }
                                for shot in s.shots
                            ],
                            "transition": s.transition
                        }
                        for s in structure.scenes
                    ]
                },
                f,
                ensure_ascii=False,
                indent=2
            )
        
        # 5. 生成音频剧本
        logger.info("生成音频剧本...")
        script_segments = []
        
        for scene in tqdm(structure.scenes, desc="生成场景脚本"):
            try:
                segment = generate_script_for_scene(
                    scene, 
                    story_outline=story_outline,  # 使用生成的全局大纲
                    style=style, 
                    language=language
                )
                
                if validate_script_segment(segment, scene.scene_number):
                    script_segments.append(segment)
                    
                    # 每个场景生成后保存进度
                    segments_path = os.path.join(ANIMATION_DRAMA_DIR, "script_segments.json")
                    with open(segments_path, 'w', encoding='utf-8') as f:
                        json.dump(script_segments, f, ensure_ascii=False, indent=2)
                else:
                    logger.warning(f"场景 {scene.scene_number} 未通过质量验证，将重新生成")
                    continue
                    
            except Exception as e:
                logger.error(f"生成场景 {scene.scene_number} 的脚本时出错: {str(e)}")
                continue
        
        # 6. 合并和优化脚本
        logger.info("合并和优化脚本...")
        merged_script = merge_script_segments(script_segments, story_outline, style, language)
        final_script = review_and_refine_script(
            merged_script, 
            {"story_outline": story_outline}, 
            style, 
            language, 
            is_complete=True
        )
        
        # 保存最终脚本
        final_script_path = os.path.join(ANIMATION_DRAMA_DIR, "final_script.json")
        with open(final_script_path, 'w', encoding='utf-8') as f:
            json.dump(final_script, f, ensure_ascii=False, indent=2)
            
        logger.info("音频剧本生成完成")
        
    except Exception as e:
        logger.error(f"处理小说时出错: {str(e)}")
        raise

def merge_script_segments(
    script_segments: List[Dict[str, Any]],
    story_outline: Dict[str, Any],
    style: str,
    language: str
) -> Dict[str, Any]:
    """合并脚本片段，确保情节连贯性和过渡的流畅性"""
    try:
        merge_data = {
            "script_segments": script_segments,
            "story_outline": story_outline,
            "style": style,
            "language": language,
            "total_segments": len(script_segments)
        }
        
        response = call_gpt_json_response(
            "merge_script_segments",
            merge_data,
            using_cache=True
        )
        
        if not response or "merged_script" not in response:
            raise ValueError("脚本合并失败")
            
        merged_script = response["merged_script"]
        
        # 验证合并结果
        validate_merged_script(merged_script, script_segments)
        
        # 优化场景转换
        merged_script = optimize_scene_transitions(merged_script)
        
        return merged_script
        
    except Exception as e:
        logger.error(f"合并脚本片段时出错: {str(e)}")
        return {
            "scenes": script_segments,
            "total_duration": sum(s.get("duration", 0) for s in script_segments),
            "scene_count": len(script_segments)
        }

def review_and_refine_script(
    merged_script: Dict[str, Any],
    context: Dict[str, Any],
    style: str,
    language: str,
    is_complete: bool = True
) -> Dict[str, Any]:
    """审查和优化合并后的脚本"""
    try:
        review_data = {
            "script": merged_script,
            "context": context,
            "style": style,
            "language": language,
            "is_complete": is_complete,
            "review_aspects": [
                "character_consistency",
                "plot_coherence",
                "emotional_flow",
                "pacing",
                "dialogue_quality",
                "scene_transitions"
            ]
        }
        
        response = call_gpt_json_response(
            "review_and_refine_script",
            review_data,
            using_cache=True
        )
        
        if not response or "refined_script" not in response:
            raise ValueError("脚本优化失败")
            
        refined_script = response["refined_script"]
        
        if "improvement_suggestions" in response:
            refined_script = apply_improvements(
                refined_script,
                response["improvement_suggestions"]
            )
            
        validate_refined_script(refined_script, merged_script)
        
        return refined_script
        
    except Exception as e:
        logger.error(f"审查和优化脚本时出错: {str(e)}")
        return merged_script

def validate_merged_script(
    merged_script: Dict[str, Any],
    original_segments: List[Dict[str, Any]]
) -> None:
    """验证合并后的脚本是否保留了所有必要的内容"""
    try:
        if len(merged_script.get("scenes", [])) != len(original_segments):
            raise ValueError("合并后的场景数量与原始片段不匹配")
            
        for i, (merged_scene, original_scene) in enumerate(
            zip(merged_script["scenes"], original_segments)
        ):
            if merged_scene.get("scene_number") != original_scene.get("scene_number"):
                raise ValueError(f"场景 {i+1} 的编号不匹配")
                
            if "dialogues" in original_scene:
                for dialogue in original_scene["dialogues"]:
                    if not any(
                        d.get("content") == dialogue.get("content")
                        for d in merged_scene.get("dialogues", [])
                    ):
                        logger.warning(f"场景 {i+1} 中的对话可能丢失")
                        
    except Exception as e:
        logger.error(f"验证合并脚本时出错: {str(e)}")
        raise

def validate_refined_script(
    refined_script: Dict[str, Any],
    original_script: Dict[str, Any]
) -> None:
    """验证优化后的脚本是否维持了原有的核心内容和结构"""
    try:
        if set(refined_script.keys()) != set(original_script.keys()):
            raise ValueError("优化后的脚本结构发生变化")
            
        if len(refined_script["scenes"]) != len(original_script["scenes"]):
            raise ValueError("优化后的场景数量发生变化")
            
        if abs(
            refined_script.get("total_duration", 0) -
            original_script.get("total_duration", 0)
        ) > 60:  # 允许1分钟的误差
            logger.warning("优化后的总时长发生显著变化")
            
    except Exception as e:
        logger.error(f"验证优化脚本时出错: {str(e)}")
        raise

def optimize_scene_transitions(script: Dict[str, Any]) -> Dict[str, Any]:
    """优化场景之间的转换"""
    try:
        scenes = script.get("scenes", [])
        for i in range(len(scenes) - 1):
            current_scene = scenes[i]
            next_scene = scenes[i + 1]
            
            if "transition" not in current_scene:
                current_scene["transition"] = {
                    "type": "fade",
                    "duration": 1.0,
                    "to_next_scene": f"过渡到场景 {next_scene.get('scene_number', i+2)}"
                }
                
            if current_scene.get("emotional_intensity") != next_scene.get("emotional_intensity"):
                current_scene["transition"]["emotional_bridge"] = (
                    f"从 {current_scene.get('emotional_intensity', 'medium')} "
                    f"过渡到 {next_scene.get('emotional_intensity', 'medium')}"
                )
                
        return script
        
    except Exception as e:
        logger.error(f"优化场景转换时出错: {str(e)}")
        return script

def apply_improvements(
    script: Dict[str, Any],
    suggestions: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """应用优化建议到脚本中"""
    try:
        for suggestion in suggestions:
            scene_number = suggestion.get("scene_number")
            improvement_type = suggestion.get("type")
            content = suggestion.get("content")
            
            if not all([scene_number, improvement_type, content]):
                continue
                
            scene = next(
                (s for s in script["scenes"] if s.get("scene_number") == scene_number),
                None
            )
            
            if not scene:
                continue
                
            if improvement_type == "dialogue":
                if "dialogues" not in scene:
                    scene["dialogues"] = []
                scene["dialogues"].extend(content)
                
            elif improvement_type == "narration":
                scene["narration"] = content
                
            elif improvement_type == "transition":
                scene["transition"] = content
                
        return script
        
    except Exception as e:
        logger.error(f"应用优化建议时出错: {str(e)}")
        return script

def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Generate a two-part audio drama from a novel')
    
    parser.add_argument(
        'input_file',
        help='Input novel text file path'
    )
    
    parser.add_argument(
        '--style',
        default=get_param('STYLE', 'engaging'),
        help='Style of the audio drama'
    )
    
    parser.add_argument(
        '--language',
        default=get_param('LANGUAGE', 'Chinese'),
        help='Language of the audio drama'
    )
    
    return parser.parse_args()

def main():
    """Main function."""
    try:
        args = parse_arguments()
        process_novel(
            input_file=args.input_file,
            style=args.style,
            language=args.language
        )
    except Exception as e:
        logger.error(f"Error processing novel: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 