# 新架构迁移指南

## 📋 迁移概述

本指南将帮助您从当前的剧集结构化系统迁移到新的优化架构。新架构将显著提升性能、降低成本，并提高系统的可维护性。

## 🎯 迁移收益

| 指标 | 旧架构 | 新架构 | 提升幅度 |
|------|--------|--------|----------|
| 生成时间 | 15-30分钟/集 | 3-8分钟/集 | **60-75%提升** |
| API调用 | 8-10次/集 | 1-3次/集 | **70-90%减少** |
| 成本 | $2-5/集 | $0.3-1.5/集 | **70-85%降低** |
| 成功率 | ~85% | >95% | **10-15%提升** |
| 并发能力 | 1集/时 | 3-6集/时 | **200-500%提升** |

## 🗂️ 迁移前准备

### 1. 环境准备

```bash
# 1. 备份现有系统
cp -r 2-animation-drama/ 2-animation-drama-backup/

# 2. 创建新架构目录
mkdir new-episode-system/
cd new-episode-system/

# 3. 安装依赖
pip install pyyaml asyncio aiofiles
```

### 2. 数据评估

```python
# data_assessment.py - 评估现有数据质量
import json
import glob

def assess_existing_data():
    """评估现有数据结构和质量"""
    
    # 检查章节摘要文件
    summary_files = glob.glob("2-animation-drama/raw_text/*.json")
    print(f"发现章节摘要文件: {len(summary_files)}")
    
    # 检查现有剧集文件
    episode_files = glob.glob("2-animation-drama/episodes/*/episode_*.json")
    print(f"发现现有剧集文件: {len(episode_files)}")
    
    # 分析数据结构一致性
    inconsistencies = []
    for file_path in summary_files[:3]:  # 检查前3个文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if not validate_chapter_structure(data):
                inconsistencies.append(file_path)
    
    if inconsistencies:
        print(f"发现数据结构不一致文件: {inconsistencies}")
        print("建议在迁移前修复这些文件")
    
    return {
        "summary_files": len(summary_files),
        "episode_files": len(episode_files),
        "inconsistencies": inconsistencies
    }

def validate_chapter_structure(data):
    """验证章节数据结构"""
    required_fields = ['basic_info', 'narrative', 'key_events', 'key_characters']
    
    if isinstance(data, list):
        for chapter in data[:2]:  # 检查前2个章节
            for field in required_fields:
                if field not in chapter:
                    return False
    
    return True

if __name__ == "__main__":
    assessment = assess_existing_data()
    print(f"数据评估完成: {assessment}")
```

## 🔄 分阶段迁移策略

### 阶段1: 并行运行 (1-2周)

在这个阶段，新旧系统并行运行，确保新系统稳定可靠。

#### 1.1 部署新系统

```bash
# 1. 复制新架构文件
cp changes/NEW_ARCHITECTURE_DESIGN.md ./
cp changes/new_episode_generator.py ./
cp changes/episode_config.yaml ./

# 2. 修改配置适应现有环境
```

**修改 `episode_config.yaml`**:
```yaml
# 迁移期配置 - 保守设置
processing:
  max_episodes: 2                 # 限制测试集数
  target_episodes: 5              # 较小的目标集数
  parallel_workers: 2             # 较少的并行数
  debug_mode: true                # 启用调试
  enable_cache: true              # 启用缓存

cache:
  directory: ".cache_new"         # 使用不同的缓存目录
  ttl_hours: 12                   # 较短的缓存时间

monitoring:
  enable_detailed_logging: true   # 详细日志
  log_level: "DEBUG"
```

#### 1.2 创建对比测试脚本

```python
# comparison_test.py - 新旧系统对比测试
import time
import json
import subprocess
from pathlib import Path

async def run_comparison_test(test_file: str):
    """运行新旧系统对比测试"""
    
    print("=== 开始对比测试 ===")
    
    # 1. 运行旧系统
    print("运行旧系统...")
    old_start = time.time()
    
    old_cmd = [
        "python", "generate_episodes.py",
        test_file,
        "--output_dir", "test_output_old",
        "--max_episodes", "2"
    ]
    
    old_result = subprocess.run(old_cmd, capture_output=True, text=True)
    old_duration = time.time() - old_start
    
    # 2. 运行新系统
    print("运行新系统...")
    new_start = time.time()
    
    new_cmd = [
        "python", "new_episode_generator.py",
        test_file,
        "--config", "episode_config.yaml",
        "--output_dir", "test_output_new"
    ]
    
    new_result = subprocess.run(new_cmd, capture_output=True, text=True)
    new_duration = time.time() - new_start
    
    # 3. 比较结果
    comparison_report = {
        "old_system": {
            "duration": old_duration,
            "success": old_result.returncode == 0,
            "output": old_result.stdout,
            "errors": old_result.stderr
        },
        "new_system": {
            "duration": new_duration,
            "success": new_result.returncode == 0,
            "output": new_result.stdout,
            "errors": new_result.stderr
        },
        "improvement": {
            "speed_up": old_duration / new_duration if new_duration > 0 else 0,
            "time_saved": old_duration - new_duration
        }
    }
    
    # 4. 比较输出质量
    quality_comparison = compare_output_quality("test_output_old", "test_output_new")
    comparison_report["quality"] = quality_comparison
    
    # 5. 保存报告
    with open("comparison_report.json", 'w', encoding='utf-8') as f:
        json.dump(comparison_report, f, ensure_ascii=False, indent=2)
    
    print(f"对比测试完成:")
    print(f"  旧系统耗时: {old_duration:.2f}秒")
    print(f"  新系统耗时: {new_duration:.2f}秒")
    print(f"  性能提升: {comparison_report['improvement']['speed_up']:.1f}倍")
    
    return comparison_report

def compare_output_quality(old_dir: str, new_dir: str) -> dict:
    """比较输出质量"""
    old_files = list(Path(old_dir).glob("episode_*.json"))
    new_files = list(Path(new_dir).glob("episode_*.json"))
    
    quality_metrics = {
        "file_count": {"old": len(old_files), "new": len(new_files)},
        "structure_completeness": {},
        "content_richness": {}
    }
    
    # 比较结构完整性
    for old_file, new_file in zip(old_files, new_files):
        episode_num = old_file.stem.split('_')[-1]
        
        with open(old_file, 'r', encoding='utf-8') as f:
            old_episode = json.load(f)
        
        with open(new_file, 'r', encoding='utf-8') as f:
            new_episode = json.load(f)
        
        old_completeness = calculate_completeness_score(old_episode)
        new_completeness = calculate_completeness_score(new_episode)
        
        quality_metrics["structure_completeness"][episode_num] = {
            "old": old_completeness,
            "new": new_completeness,
            "improvement": new_completeness - old_completeness
        }
    
    return quality_metrics

def calculate_completeness_score(episode: dict) -> float:
    """计算结构完整性评分"""
    score = 0.0
    
    # 检查必需字段
    required_fields = ['title', 'scenes', 'characters']
    for field in required_fields:
        if field in episode and episode[field]:
            score += 0.3
    
    # 检查场景数量
    scene_count = len(episode.get('scenes', []))
    if 3 <= scene_count <= 8:
        score += 0.1
    
    return min(score, 1.0)
```

#### 1.3 运行并行测试

```bash
# 1. 运行对比测试
python comparison_test.py

# 2. 分析结果
cat comparison_report.json | jq '.improvement'

# 3. 监控新系统稳定性
tail -f .cache_new/*.log
```

### 阶段2: 渐进式替换 (2-3周)

基于阶段1的测试结果，开始渐进式替换。

#### 2.1 替换策略

```python
# migration_controller.py - 迁移控制器
class MigrationController:
    def __init__(self):
        self.migration_config = {
            "new_system_ratio": 0.3,  # 30%的请求使用新系统
            "rollback_threshold": 0.1,  # 10%错误率则回滚
            "monitoring_window": 100    # 监控最近100个请求
        }
        self.request_history = []
    
    def should_use_new_system(self, request_id: str) -> bool:
        """决定是否使用新系统"""
        
        # 基于哈希的一致性路由
        hash_value = hash(request_id) % 100
        
        # 检查错误率
        recent_requests = self.request_history[-self.migration_config["monitoring_window"]:]
        if recent_requests:
            error_rate = sum(1 for r in recent_requests if not r['success']) / len(recent_requests)
            
            if error_rate > self.migration_config["rollback_threshold"]:
                # 错误率过高，暂停使用新系统
                return False
        
        return hash_value < self.migration_config["new_system_ratio"] * 100
    
    def record_request(self, request_id: str, success: bool, system: str, duration: float):
        """记录请求结果"""
        self.request_history.append({
            "request_id": request_id,
            "success": success,
            "system": system,
            "duration": duration,
            "timestamp": time.time()
        })
        
        # 保持历史记录在合理范围内
        if len(self.request_history) > self.migration_config["monitoring_window"] * 2:
            self.request_history = self.request_history[-self.migration_config["monitoring_window"]:]
```

#### 2.2 统一接口层

```python
# unified_interface.py - 统一接口层
import asyncio
from typing import Union

class UnifiedEpisodeGenerator:
    """统一的剧集生成接口"""
    
    def __init__(self):
        self.migration_controller = MigrationController()
        self.old_generator = OldEpisodeGenerator()  # 包装旧系统
        self.new_generator = NewEpisodeGenerator()  # 新系统
    
    async def generate_episodes(
        self,
        chapter_summaries: List[Dict],
        request_id: str = None
    ) -> GenerationResult:
        """统一的剧集生成接口"""
        
        request_id = request_id or f"req_{int(time.time())}"
        
        # 决定使用哪个系统
        use_new_system = self.migration_controller.should_use_new_system(request_id)
        
        start_time = time.time()
        
        try:
            if use_new_system:
                logger.info(f"使用新系统处理请求: {request_id}")
                result = await self.new_generator.generate_episodes(chapter_summaries)
                system = "new"
            else:
                logger.info(f"使用旧系统处理请求: {request_id}")
                result = await self.old_generator.generate_episodes(chapter_summaries)
                system = "old"
            
            duration = time.time() - start_time
            success = result.success if hasattr(result, 'success') else True
            
            # 记录结果
            self.migration_controller.record_request(request_id, success, system, duration)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            system = "new" if use_new_system else "old"
            
            # 记录失败
            self.migration_controller.record_request(request_id, False, system, duration)
            
            # 如果新系统失败，自动回退到旧系统
            if use_new_system:
                logger.warning(f"新系统失败，回退到旧系统: {e}")
                try:
                    fallback_result = await self.old_generator.generate_episodes(chapter_summaries)
                    fallback_duration = time.time() - start_time
                    self.migration_controller.record_request(
                        f"{request_id}_fallback", True, "old", fallback_duration
                    )
                    return fallback_result
                except Exception as fallback_error:
                    logger.error(f"旧系统也失败了: {fallback_error}")
                    raise
            else:
                raise
```

#### 2.3 监控和度量

```python
# migration_monitor.py - 迁移监控
import matplotlib.pyplot as plt
import pandas as pd

class MigrationMonitor:
    """迁移过程监控"""
    
    def __init__(self):
        self.metrics_history = []
    
    def collect_metrics(self, migration_controller: MigrationController):
        """收集性能指标"""
        
        recent_requests = migration_controller.request_history[-100:]
        
        if not recent_requests:
            return
        
        new_system_requests = [r for r in recent_requests if r['system'] == 'new']
        old_system_requests = [r for r in recent_requests if r['system'] == 'old']
        
        metrics = {
            "timestamp": time.time(),
            "total_requests": len(recent_requests),
            "new_system_count": len(new_system_requests),
            "old_system_count": len(old_system_requests),
            "new_system_success_rate": sum(1 for r in new_system_requests if r['success']) / len(new_system_requests) if new_system_requests else 0,
            "old_system_success_rate": sum(1 for r in old_system_requests if r['success']) / len(old_system_requests) if old_system_requests else 0,
            "new_system_avg_duration": sum(r['duration'] for r in new_system_requests) / len(new_system_requests) if new_system_requests else 0,
            "old_system_avg_duration": sum(r['duration'] for r in old_system_requests) / len(old_system_requests) if old_system_requests else 0
        }
        
        self.metrics_history.append(metrics)
        
        # 保存到文件
        with open("migration_metrics.json", 'w', encoding='utf-8') as f:
            json.dump(self.metrics_history, f, ensure_ascii=False, indent=2)
    
    def generate_dashboard(self):
        """生成监控仪表板"""
        if not self.metrics_history:
            return
        
        df = pd.DataFrame(self.metrics_history)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 成功率对比
        axes[0, 0].plot(df['new_system_success_rate'], label='新系统')
        axes[0, 0].plot(df['old_system_success_rate'], label='旧系统')
        axes[0, 0].set_title('成功率对比')
        axes[0, 0].set_ylabel('成功率')
        axes[0, 0].legend()
        
        # 性能对比
        axes[0, 1].plot(df['new_system_avg_duration'], label='新系统')
        axes[0, 1].plot(df['old_system_avg_duration'], label='旧系统')
        axes[0, 1].set_title('性能对比')
        axes[0, 1].set_ylabel('平均响应时间(秒)')
        axes[0, 1].legend()
        
        # 请求分布
        axes[1, 0].plot(df['new_system_count'], label='新系统')
        axes[1, 0].plot(df['old_system_count'], label='旧系统')
        axes[1, 0].set_title('请求分布')
        axes[1, 0].set_ylabel('请求数量')
        axes[1, 0].legend()
        
        # 总体趋势
        df['total_success_rate'] = (df['new_system_success_rate'] * df['new_system_count'] + 
                                   df['old_system_success_rate'] * df['old_system_count']) / df['total_requests']
        axes[1, 1].plot(df['total_success_rate'])
        axes[1, 1].set_title('总体成功率趋势')
        axes[1, 1].set_ylabel('成功率')
        
        plt.tight_layout()
        plt.savefig('migration_dashboard.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("监控仪表板已生成: migration_dashboard.png")
```

### 阶段3: 完全切换 (1周)

当新系统稳定运行且性能表现良好时，执行完全切换。

#### 3.1 切换检查清单

```bash
# migration_checklist.sh - 迁移检查清单
#!/bin/bash

echo "=== 迁移检查清单 ==="

# 1. 检查新系统稳定性
echo "1. 检查新系统稳定性..."
RECENT_SUCCESS_RATE=$(cat migration_metrics.json | jq '.[-10:] | map(.new_system_success_rate) | add/length')
echo "   最近新系统成功率: $RECENT_SUCCESS_RATE"

if (( $(echo "$RECENT_SUCCESS_RATE > 0.95" | bc -l) )); then
    echo "   ✅ 新系统稳定性良好"
else
    echo "   ❌ 新系统稳定性不足，建议继续观察"
    exit 1
fi

# 2. 检查性能提升
echo "2. 检查性能提升..."
PERFORMANCE_RATIO=$(cat migration_metrics.json | jq '.[-10:] | map(.old_system_avg_duration / .new_system_avg_duration) | add/length')
echo "   性能提升倍数: $PERFORMANCE_RATIO"

if (( $(echo "$PERFORMANCE_RATIO > 1.5" | bc -l) )); then
    echo "   ✅ 性能提升显著"
else
    echo "   ⚠️  性能提升不明显，建议检查配置"
fi

# 3. 检查数据一致性
echo "3. 检查数据一致性..."
python data_consistency_check.py
if [ $? -eq 0 ]; then
    echo "   ✅ 数据一致性良好"
else
    echo "   ❌ 发现数据一致性问题"
    exit 1
fi

# 4. 检查缓存状态
echo "4. 检查缓存状态..."
CACHE_SIZE=$(du -sh .cache_new | cut -f1)
echo "   缓存大小: $CACHE_SIZE"

# 5. 检查错误日志
echo "5. 检查错误日志..."
ERROR_COUNT=$(grep -c "ERROR" logs/new_system.log || echo "0")
echo "   最近错误数量: $ERROR_COUNT"

if [ "$ERROR_COUNT" -lt 5 ]; then
    echo "   ✅ 错误数量在可接受范围内"
else
    echo "   ⚠️  错误数量较多，建议检查"
fi

echo ""
echo "=== 检查完成 ==="
echo "如果所有检查项都通过，可以执行完全切换"
```

#### 3.2 执行完全切换

```python
# full_migration.py - 完全迁移脚本
import shutil
import os
from pathlib import Path

def execute_full_migration():
    """执行完全迁移"""
    
    print("=== 开始完全迁移 ===")
    
    # 1. 最终备份旧系统
    print("1. 备份旧系统...")
    backup_dir = f"old_system_backup_{int(time.time())}"
    shutil.copytree("generate_episodes.py", f"{backup_dir}/generate_episodes.py")
    shutil.copytree("modules/episode_outline.py", f"{backup_dir}/episode_outline.py")
    print(f"   旧系统已备份到: {backup_dir}")
    
    # 2. 替换主要文件
    print("2. 替换主要文件...")
    
    # 替换主生成器
    shutil.copy("new_episode_generator.py", "generate_episodes.py")
    
    # 更新配置文件
    shutil.copy("episode_config.yaml", "config/episode_config.yaml")
    
    # 3. 更新缓存目录
    print("3. 更新缓存...")
    if os.path.exists(".cache"):
        shutil.move(".cache", f"{backup_dir}/.cache_old")
    
    if os.path.exists(".cache_new"):
        shutil.move(".cache_new", ".cache")
    
    # 4. 更新配置引用
    print("4. 更新配置引用...")
    update_config_references()
    
    # 5. 清理临时文件
    print("5. 清理临时文件...")
    cleanup_migration_files()
    
    # 6. 验证迁移
    print("6. 验证迁移...")
    if verify_migration():
        print("   ✅ 迁移验证成功")
    else:
        print("   ❌ 迁移验证失败")
        return False
    
    print("=== 完全迁移完成 ===")
    print(f"旧系统备份位置: {backup_dir}")
    print("新系统已正式启用")
    
    return True

def update_config_references():
    """更新配置文件引用"""
    # 更新主控脚本中的配置引用
    config_updates = [
        ("generate_animation_drama.py", "episode_config.yaml"),
        ("gen_animation_drama.sh", "episode_config.yaml")
    ]
    
    for file_path, config_path in config_updates:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单的字符串替换（实际中可能需要更复杂的逻辑）
            content = content.replace(
                'config.py',
                config_path
            )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

def cleanup_migration_files():
    """清理迁移相关的临时文件"""
    temp_files = [
        "comparison_test.py",
        "migration_controller.py",
        "unified_interface.py",
        "migration_monitor.py",
        "comparison_report.json",
        "migration_metrics.json",
        "migration_dashboard.png",
        "test_output_old/",
        "test_output_new/"
    ]
    
    for file_path in temp_files:
        if os.path.exists(file_path):
            if os.path.isdir(file_path):
                shutil.rmtree(file_path)
            else:
                os.remove(file_path)

def verify_migration():
    """验证迁移成功"""
    try:
        # 检查关键文件
        required_files = [
            "generate_episodes.py",
            "config/episode_config.yaml",
            ".cache"
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"   ❌ 缺少关键文件: {file_path}")
                return False
        
        # 尝试导入新模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("generate_episodes", "generate_episodes.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 检查关键类是否存在
        if not hasattr(module, 'EpisodeStructureGenerator'):
            print("   ❌ 新生成器类不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 验证过程出错: {e}")
        return False

if __name__ == "__main__":
    success = execute_full_migration()
    exit(0 if success else 1)
```

### 阶段4: 后续优化 (持续)

迁移完成后的持续优化和监控。

#### 4.1 性能监控

```python
# post_migration_monitor.py - 迁移后监控
class PostMigrationMonitor:
    """迁移后性能监控"""
    
    def __init__(self):
        self.baseline_metrics = self.load_baseline()
        self.alert_thresholds = {
            "success_rate": 0.95,
            "avg_response_time": 600,  # 10分钟
            "error_rate": 0.05
        }
    
    def check_system_health(self):
        """检查系统健康状态"""
        current_metrics = self.collect_current_metrics()
        
        alerts = []
        
        # 检查成功率
        if current_metrics["success_rate"] < self.alert_thresholds["success_rate"]:
            alerts.append(f"成功率过低: {current_metrics['success_rate']:.2f}")
        
        # 检查响应时间
        if current_metrics["avg_response_time"] > self.alert_thresholds["avg_response_time"]:
            alerts.append(f"响应时间过长: {current_metrics['avg_response_time']:.2f}秒")
        
        # 检查错误率
        if current_metrics["error_rate"] > self.alert_thresholds["error_rate"]:
            alerts.append(f"错误率过高: {current_metrics['error_rate']:.2f}")
        
        return {
            "healthy": len(alerts) == 0,
            "alerts": alerts,
            "metrics": current_metrics
        }
    
    def generate_performance_report(self):
        """生成性能报告"""
        current_metrics = self.collect_current_metrics()
        
        improvement = {
            "speed_improvement": self.baseline_metrics["avg_response_time"] / current_metrics["avg_response_time"],
            "reliability_improvement": current_metrics["success_rate"] - self.baseline_metrics["success_rate"],
            "cost_reduction": (self.baseline_metrics["avg_cost"] - current_metrics["avg_cost"]) / self.baseline_metrics["avg_cost"]
        }
        
        report = {
            "migration_date": self.baseline_metrics.get("migration_date"),
            "baseline_metrics": self.baseline_metrics,
            "current_metrics": current_metrics,
            "improvements": improvement,
            "recommendations": self.generate_recommendations(current_metrics)
        }
        
        return report
```

## 🚨 风险管理

### 1. 回滚计划

```bash
# rollback.sh - 紧急回滚脚本
#!/bin/bash

echo "=== 紧急回滚 ==="

# 1. 停止新系统
echo "1. 停止新系统..."
pkill -f "new_episode_generator.py"

# 2. 恢复旧文件
echo "2. 恢复旧文件..."
BACKUP_DIR=$(ls -1 | grep "old_system_backup_" | tail -1)

if [ -z "$BACKUP_DIR" ]; then
    echo "❌ 找不到备份目录"
    exit 1
fi

cp "$BACKUP_DIR/generate_episodes.py" ./
cp "$BACKUP_DIR/modules/episode_outline.py" modules/

# 3. 恢复缓存
echo "3. 恢复缓存..."
if [ -d "$BACKUP_DIR/.cache_old" ]; then
    rm -rf .cache
    cp -r "$BACKUP_DIR/.cache_old" .cache
fi

# 4. 验证回滚
echo "4. 验证回滚..."
python -c "import generate_episodes; print('回滚验证成功')"

if [ $? -eq 0 ]; then
    echo "✅ 回滚成功"
else
    echo "❌ 回滚失败"
    exit 1
fi
```

### 2. 数据一致性检查

```python
# data_consistency_check.py - 数据一致性检查
def check_data_consistency():
    """检查新旧系统数据一致性"""
    
    # 比较相同输入的输出结果
    test_inputs = [
        "2-animation-drama/raw_text/test_sample.json"
    ]
    
    inconsistencies = []
    
    for test_input in test_inputs:
        if not os.path.exists(test_input):
            continue
            
        # 运行新系统
        new_result = run_new_system(test_input)
        
        # 运行旧系统（如果还有备份）
        old_result = run_old_system_backup(test_input)
        
        # 比较关键字段
        if not compare_episode_structure(new_result, old_result):
            inconsistencies.append(test_input)
    
    return len(inconsistencies) == 0

def compare_episode_structure(new_result, old_result):
    """比较剧集结构"""
    # 实现结构比较逻辑
    return True  # 简化实现
```

## 📊 迁移成功指标

### 关键指标监控

1. **性能指标**
   - 生成时间: 目标 < 8分钟/集
   - API调用次数: 目标 < 3次/集
   - 成功率: 目标 > 95%

2. **质量指标**
   - 结构完整性: 目标 > 0.8
   - 内容丰富度: 目标保持或提升
   - 用户满意度: 定期收集反馈

3. **运营指标**
   - 系统可用性: 目标 > 99%
   - 错误恢复时间: 目标 < 5分钟
   - 维护成本: 目标降低50%

### 验收标准

- [ ] 新系统性能测试通过
- [ ] 质量对比测试通过
- [ ] 并行运行1周无重大问题
- [ ] 回滚机制验证通过
- [ ] 文档和培训完成
- [ ] 监控告警系统就绪

## 📞 支持和帮助

### 迁移过程中的支持

1. **技术支持**: 提供详细的故障排查指南
2. **在线文档**: 维护最新的迁移文档
3. **社区支持**: 建立迁移经验分享渠道

### 常见问题解决

**Q: 新系统性能不如预期怎么办？**
A: 检查配置参数，特别是并发数和缓存设置，必要时调整 `episode_config.yaml`

**Q: 迁移过程中出现数据丢失？**
A: 立即执行回滚，检查备份完整性，重新评估迁移计划

**Q: 新旧系统输出结果不一致？**
A: 这是正常现象，新系统采用了优化的算法，重点关注质量指标而非完全一致

通过这个详细的迁移指南，您可以安全、稳定地从旧架构过渡到新架构，实现显著的性能提升和成本降低。 