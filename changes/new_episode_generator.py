#!/usr/bin/env python3
"""
新的剧集结构化生成器
基于优化架构设计的高性能实现
"""

import os
import sys
import json
import yaml
import time
import hashlib
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from concurrent.futures import ThreadPoolExecutor
import numpy as np

# 导入现有模块
from modules.langchain_interface import call_llm_json_response

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EpisodeConfig:
    """统一配置数据类"""
    # 处理配置
    max_episodes: Optional[int] = None
    target_episodes: int = 10
    enable_cache: bool = True
    parallel_workers: int = 3
    debug_mode: bool = False
    force_regenerate: bool = False
    
    # 质量配置
    min_words_per_episode: int = 3000
    max_words_per_episode: int = 8000
    target_words_per_episode: int = 6000
    quality_threshold: float = 0.7
    enable_auto_optimization: bool = True
    
    # LLM配置
    llm_provider: str = "google"
    llm_model: str = "gemini-25-pro"
    temperature: float = 0.7
    max_tokens: int = 4000
    max_retries: int = 3
    timeout_seconds: int = 120
    
    # 缓存配置
    cache_ttl_hours: int = 24
    cache_directory: str = ".cache"
    cache_max_size_mb: int = 1000
    
    # 并行配置
    max_concurrent_episodes: int = 3
    max_concurrent_analysis: int = 5
    auto_scaling: bool = True

@dataclass
class ChapterSummary:
    """标准化章节摘要"""
    chapter_number: int
    title: str
    content: str
    key_events: List[str]
    key_characters: List[str]
    emotional_tone: str
    themes: List[str]
    word_count: int
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ChapterSummary':
        """从字典创建实例"""
        basic_info = data.get('basic_info', {})
        narrative = data.get('narrative', {})
        return cls(
            chapter_number=basic_info.get('chapter_number', 0),
            title=basic_info.get('title', ''),
            content=narrative.get('content', ''),
            key_events=data.get('key_events', []),
            key_characters=data.get('key_characters', []),
            emotional_tone=narrative.get('emotional_tone', 'neutral'),
            themes=narrative.get('themes', []),
            word_count=len(narrative.get('content', ''))
        )

@dataclass
class EpisodeStructure:
    """剧集结构"""
    episode_number: int
    chapters: List[int]
    title: str
    theme: str
    scenes: List[Dict[str, Any]]
    characters: List[str]
    estimated_duration: int
    quality_score: float = 0.0

@dataclass
class EpisodeSpec:
    """剧集规格"""
    episode_number: int
    chapters: List[int]
    estimated_length: int
    primary_theme: str
    key_characters: List[str]

@dataclass
class AnalysisResult:
    """分析结果"""
    story_structure: Dict[str, Any]
    characters: Dict[str, Any]
    themes: List[str]

@dataclass
class AllocationStrategy:
    """分配策略"""
    total_episodes: int
    episodes: List[EpisodeSpec]
    reasoning: str

@dataclass
class QualityReport:
    """质量报告"""
    overall_score: float
    structure_score: float
    content_score: float
    duration_score: float
    character_score: float
    needs_llm_review: bool
    issues: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)

@dataclass
class GenerationResult:
    """生成结果"""
    success: bool
    episode: Optional[EpisodeStructure]
    errors: List[str]
    warnings: List[str]
    generation_time: float
    api_calls: int
    cost_estimate: float

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "episode_config.yaml"):
        self.config = self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> EpisodeConfig:
        """加载配置文件"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        else:
            logger.warning(f"配置文件不存在: {config_path}, 使用默认配置")
            config_data = {}
        
        # 展平嵌套的配置
        flat_config = self._flatten_config(config_data)
        
        # 过滤只保留EpisodeConfig的字段
        valid_fields = {f.name for f in EpisodeConfig.__dataclass_fields__.values()}
        filtered_config = {k: v for k, v in flat_config.items() if k in valid_fields}
        
        return EpisodeConfig(**filtered_config)
    
    def _flatten_config(self, config_data: dict, prefix: str = "") -> dict:
        """展平嵌套的配置字典"""
        flat = {}
        for key, value in config_data.items():
            new_key = f"{prefix}_{key}" if prefix else key
            
            if isinstance(value, dict):
                flat.update(self._flatten_config(value, new_key))
            else:
                flat[new_key] = value
        
        return flat

class SmartCache:
    """智能缓存系统"""
    
    def __init__(self, config: EpisodeConfig):
        self.config = config
        self.cache_dir = Path(config.cache_directory)
        self.cache_dir.mkdir(exist_ok=True)
        
    def get_cache_key(self, data: Any, operation: str) -> str:
        """生成缓存键"""
        content_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        content_hash = hashlib.sha256(content_str.encode()).hexdigest()[:16]
        return f"{operation}_{content_hash}"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        cache_file = self.cache_dir / f"{key}.json"
        if not cache_file.exists():
            return None
            
        # 检查过期时间
        stat = cache_file.stat()
        if time.time() - stat.st_mtime > self.config.cache_ttl_hours * 3600:
            cache_file.unlink()  # 删除过期缓存
            return None
            
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"缓存文件损坏: {cache_file}, 错误: {e}")
            cache_file.unlink()
            return None
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存"""
        cache_file = self.cache_dir / f"{key}.json"
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(value, f, ensure_ascii=False, indent=2)
        except IOError as e:
            logger.warning(f"无法写入缓存: {cache_file}, 错误: {e}")

class QualityAssessment:
    """质量评估系统"""
    
    def __init__(self, config: EpisodeConfig):
        self.config = config
        
    def assess_episode(self, episode: EpisodeStructure) -> QualityReport:
        """评估剧集质量"""
        # 1. 结构完整性检查
        structure_score = self._check_structure_completeness(episode)
        
        # 2. 内容质量评分
        content_score = self._assess_content_quality(episode)
        
        # 3. 时长合理性检查
        duration_score = self._check_duration_appropriateness(episode)
        
        # 4. 角色一致性检查
        character_score = self._check_character_consistency(episode)
        
        # 综合评分 (权重来自配置)
        overall_score = (
            structure_score * 0.3 +
            content_score * 0.4 +
            duration_score * 0.2 +
            character_score * 0.1
        )
        
        # 识别问题和建议
        issues = []
        suggestions = []
        
        if structure_score < 0.7:
            issues.append("结构完整性不足")
            suggestions.append("增加场景数量和细节")
            
        if content_score < 0.7:
            issues.append("内容质量较低")
            suggestions.append("增强对话和冲突元素")
        
        return QualityReport(
            overall_score=overall_score,
            structure_score=structure_score,
            content_score=content_score,
            duration_score=duration_score,
            character_score=character_score,
            needs_llm_review=overall_score < self.config.quality_threshold,
            issues=issues,
            suggestions=suggestions
        )
    
    def _check_structure_completeness(self, episode: EpisodeStructure) -> float:
        """检查结构完整性"""
        score = 0.0
        
        # 检查必需字段
        if episode.title: score += 0.2
        if episode.scenes: score += 0.3
        if episode.characters: score += 0.2
        if len(episode.scenes) >= 3: score += 0.3  # 至少3个场景
        
        return min(score, 1.0)
    
    def _assess_content_quality(self, episode: EpisodeStructure) -> float:
        """评估内容质量"""
        score = 0.0
        
        # 检查场景数量合理性
        scene_count = len(episode.scenes)
        if 3 <= scene_count <= 8:
            score += 0.3
        elif scene_count > 0:
            score += 0.1
            
        # 检查对话比例（如果有）
        total_dialogue = sum(
            len(scene.get('dialogue', []))
            for scene in episode.scenes
        )
        if total_dialogue > 0:
            score += 0.4
            
        # 检查冲突元素
        has_conflict = any(
            'conflict' in scene or 'tension' in scene
            for scene in episode.scenes
        )
        if has_conflict:
            score += 0.3
            
        return min(score, 1.0)
    
    def _check_duration_appropriateness(self, episode: EpisodeStructure) -> float:
        """检查时长合理性"""
        if not episode.estimated_duration:
            return 0.5  # 中性分数
            
        target = self.config.target_words_per_episode
        min_words = self.config.min_words_per_episode
        max_words = self.config.max_words_per_episode
        
        duration = episode.estimated_duration
        
        if min_words <= duration <= max_words:
            # 在合理范围内，根据接近目标程度评分
            deviation = abs(duration - target) / target
            return max(0.7, 1.0 - deviation)
        else:
            # 超出范围，低分
            return 0.3
    
    def _check_character_consistency(self, episode: EpisodeStructure) -> float:
        """检查角色一致性"""
        if not episode.characters:
            return 0.5
            
        # 检查角色数量合理性
        char_count = len(episode.characters)
        if 2 <= char_count <= 6:
            return 0.8
        elif char_count > 0:
            return 0.6
        else:
            return 0.2

class LLMClient:
    """LLM客户端"""
    
    def __init__(self, config: EpisodeConfig):
        self.config = config
    
    async def call_async(self, function_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """异步调用LLM"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._call_sync, function_name, data
        )
    
    def _call_sync(self, function_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """同步调用LLM"""
        try:
            result = call_llm_json_response(
                api_function=function_name,
                prompt_data=data,
                max_retries=self.config.max_retries,
                using_cache=self.config.enable_cache,
                llm_type=self.config.llm_provider,
                model_key=self.config.llm_model
            )
            return result
        except Exception as e:
            logger.error(f"LLM调用失败: {function_name}, 错误: {e}")
            raise

class EpisodeStructureGenerator:
    """新的剧集结构生成器"""
    
    def __init__(self, config_path: str = "episode_config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        self.cache = SmartCache(self.config)
        self.quality_assessor = QualityAssessment(self.config)
        self.llm_client = LLMClient(self.config)
        
        logger.info(f"初始化剧集生成器，配置: {self.config}")
        
    async def generate_episodes(
        self,
        chapter_summaries: List[Dict[str, Any]]
    ) -> List[GenerationResult]:
        """异步生成所有剧集"""
        
        logger.info(f"开始生成剧集，输入章节数: {len(chapter_summaries)}")
        
        # 1. 预处理 - 数据标准化
        standardized_chapters = self._standardize_chapters(chapter_summaries)
        logger.info(f"标准化完成，有效章节数: {len(standardized_chapters)}")
        
        # 2. 智能分析 - 并行执行
        analysis_result = await self._parallel_analysis(standardized_chapters)
        logger.info("并行分析完成")
        
        # 3. 策略生成 - 一次性生成完整策略
        allocation_strategy = await self._generate_allocation_strategy(
            standardized_chapters, analysis_result
        )
        logger.info(f"分配策略生成完成，总集数: {allocation_strategy.total_episodes}")
        
        # 4. 并行生成 - 多线程生成剧集
        results = await self._parallel_episode_generation(
            allocation_strategy, standardized_chapters
        )
        
        # 5. 生成总结报告
        self._generate_summary_report(results)
        
        return results
    
    def _standardize_chapters(
        self, 
        chapter_summaries: List[Dict[str, Any]]
    ) -> List[ChapterSummary]:
        """标准化章节数据"""
        standardized = []
        for chapter_data in chapter_summaries:
            try:
                chapter = ChapterSummary.from_dict(chapter_data)
                standardized.append(chapter)
            except Exception as e:
                logger.warning(f"跳过无效章节数据: {e}")
        
        return standardized
    
    async def _parallel_analysis(
        self,
        chapters: List[ChapterSummary]
    ) -> AnalysisResult:
        """并行分析：故事、角色、结构"""
        
        # 并行执行三个分析任务
        tasks = [
            self._analyze_story_structure(chapters),
            self._analyze_characters(chapters),
            self._analyze_themes(chapters)
        ]
        
        story_analysis, character_analysis, theme_analysis = await asyncio.gather(*tasks)
        
        return AnalysisResult(
            story_structure=story_analysis,
            characters=character_analysis,
            themes=theme_analysis
        )
    
    async def _analyze_story_structure(self, chapters: List[ChapterSummary]) -> Dict[str, Any]:
        """分析故事结构"""
        cache_key = self.cache.get_cache_key(
            [asdict(ch) for ch in chapters], "story_analysis"
        )
        
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        data = {
            "chapters": [asdict(ch) for ch in chapters],
            "analysis_type": "story_structure"
        }
        
        result = await self.llm_client.call_async("analyze_story_structure", data)
        self.cache.set(cache_key, result)
        return result
    
    async def _analyze_characters(self, chapters: List[ChapterSummary]) -> Dict[str, Any]:
        """分析角色"""
        cache_key = self.cache.get_cache_key(
            [asdict(ch) for ch in chapters], "character_analysis"
        )
        
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        data = {
            "chapters": [asdict(ch) for ch in chapters],
            "analysis_type": "characters"
        }
        
        result = await self.llm_client.call_async("analyze_characters", data)
        self.cache.set(cache_key, result)
        return result
    
    async def _analyze_themes(self, chapters: List[ChapterSummary]) -> List[str]:
        """分析主题"""
        cache_key = self.cache.get_cache_key(
            [asdict(ch) for ch in chapters], "theme_analysis"
        )
        
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        data = {
            "chapters": [asdict(ch) for ch in chapters],
            "analysis_type": "themes"
        }
        
        result = await self.llm_client.call_async("analyze_themes", data)
        themes = result.get("themes", [])
        self.cache.set(cache_key, themes)
        return themes
    
    async def _generate_allocation_strategy(
        self,
        chapters: List[ChapterSummary],
        analysis: AnalysisResult
    ) -> AllocationStrategy:
        """一次性生成完整的分配策略"""
        
        # 检查缓存
        cache_key = self.cache.get_cache_key(
            {"chapters": [asdict(ch) for ch in chapters], "analysis": asdict(analysis)},
            "allocation_strategy"
        )
        
        cached_result = self.cache.get(cache_key)
        if cached_result and not self.config.force_regenerate:
            return AllocationStrategy(**cached_result)
        
        # 生成策略
        strategy_data = {
            "chapters": [asdict(ch) for ch in chapters],
            "analysis": asdict(analysis),
            "target_episodes": self.config.target_episodes,
            "target_words_per_episode": self.config.target_words_per_episode,
            "max_episodes": self.config.max_episodes
        }
        
        result = await self.llm_client.call_async(
            "generate_complete_allocation_strategy",
            strategy_data
        )
        
        strategy = AllocationStrategy(**result)
        
        # 缓存结果
        self.cache.set(cache_key, asdict(strategy))
        
        return strategy
    
    async def _parallel_episode_generation(
        self,
        strategy: AllocationStrategy,
        chapters: List[ChapterSummary]
    ) -> List[GenerationResult]:
        """并行生成剧集"""
        
        # 识别独立剧集（无依赖关系）
        independent_episodes, dependent_episodes = self._categorize_episodes(
            strategy.episodes
        )
        
        logger.info(f"独立剧集: {len(independent_episodes)}, 依赖剧集: {len(dependent_episodes)}")
        
        # 并行生成独立剧集
        semaphore = asyncio.Semaphore(self.config.max_concurrent_episodes)
        independent_tasks = [
            self._generate_single_episode(episode, chapters, semaphore)
            for episode in independent_episodes
        ]
        
        independent_results = await asyncio.gather(*independent_tasks, return_exceptions=True)
        
        # 处理异常
        for i, result in enumerate(independent_results):
            if isinstance(result, Exception):
                logger.error(f"独立剧集 {independent_episodes[i].episode_number} 生成失败: {result}")
                independent_results[i] = GenerationResult(
                    success=False,
                    episode=None,
                    errors=[str(result)],
                    warnings=[],
                    generation_time=0,
                    api_calls=0,
                    cost_estimate=0
                )
        
        # 顺序生成有依赖的剧集
        dependent_results = []
        previous_context = None
        
        for episode_spec in dependent_episodes:
            try:
                result = await self._generate_single_episode(
                    episode_spec, chapters, semaphore, previous_context
                )
                dependent_results.append(result)
                
                if result.success:
                    previous_context = result.episode
            except Exception as e:
                logger.error(f"依赖剧集 {episode_spec.episode_number} 生成失败: {e}")
                dependent_results.append(GenerationResult(
                    success=False,
                    episode=None,
                    errors=[str(e)],
                    warnings=[],
                    generation_time=0,
                    api_calls=0,
                    cost_estimate=0
                ))
        
        return list(independent_results) + dependent_results
    
    def _categorize_episodes(
        self, 
        episodes: List[EpisodeSpec]
    ) -> Tuple[List[EpisodeSpec], List[EpisodeSpec]]:
        """将剧集分为独立和依赖两类"""
        # 简单策略：前3集视为独立，其余为依赖
        # 更复杂的逻辑可以基于角色关系、情节连续性等
        
        independent = episodes[:3] if len(episodes) > 3 else episodes
        dependent = episodes[3:] if len(episodes) > 3 else []
        
        return independent, dependent
    
    async def _generate_single_episode(
        self,
        episode_spec: EpisodeSpec,
        chapters: List[ChapterSummary],
        semaphore: asyncio.Semaphore,
        previous_context: Optional[EpisodeStructure] = None
    ) -> GenerationResult:
        """生成单个剧集"""
        
        async with semaphore:
            start_time = time.time()
            api_calls = 0
            
            try:
                logger.info(f"开始生成第 {episode_spec.episode_number} 集")
                
                # 检查缓存
                cache_key = self.cache.get_cache_key(
                    {"episode_spec": asdict(episode_spec), "chapters": [asdict(ch) for ch in chapters]},
                    "episode_generation"
                )
                
                cached_result = self.cache.get(cache_key)
                if cached_result and not self.config.force_regenerate:
                    logger.info(f"使用缓存的第 {episode_spec.episode_number} 集")
                    return GenerationResult(**cached_result)
                
                # 获取相关章节
                relevant_chapters = [
                    ch for ch in chapters
                    if ch.chapter_number in episode_spec.chapters
                ]
                
                # 生成剧集结构
                episode_data = {
                    "episode_spec": asdict(episode_spec),
                    "relevant_chapters": [asdict(ch) for ch in relevant_chapters],
                    "previous_context": asdict(previous_context) if previous_context else None,
                    "style_requirements": {
                        "min_words": self.config.min_words_per_episode,
                        "max_words": self.config.max_words_per_episode,
                        "target_words": self.config.target_words_per_episode
                    }
                }
                
                # 调用LLM生成
                llm_result = await self.llm_client.call_async(
                    "generate_complete_episode",
                    episode_data
                )
                api_calls += 1
                
                # 创建剧集结构
                episode = EpisodeStructure(**llm_result)
                
                # 质量评估
                quality_report = self.quality_assessor.assess_episode(episode)
                episode.quality_score = quality_report.overall_score
                
                logger.info(f"第 {episode_spec.episode_number} 集初始质量评分: {quality_report.overall_score:.2f}")
                
                # 如果质量不达标且启用自动优化，进行LLM评审优化
                if quality_report.needs_llm_review and self.config.enable_auto_optimization:
                    logger.info(f"第 {episode_spec.episode_number} 集质量不达标，进行优化")
                    
                    optimized_result = await self.llm_client.call_async(
                        "optimize_episode_quality",
                        {
                            "episode": asdict(episode),
                            "quality_issues": quality_report.issues,
                            "suggestions": quality_report.suggestions
                        }
                    )
                    api_calls += 1
                    
                    episode = EpisodeStructure(**optimized_result)
                    episode.quality_score = self.quality_assessor.assess_episode(episode).overall_score
                    
                    logger.info(f"第 {episode_spec.episode_number} 集优化后质量评分: {episode.quality_score:.2f}")
                
                generation_time = time.time() - start_time
                cost_estimate = api_calls * 0.5  # 估算成本
                
                result = GenerationResult(
                    success=True,
                    episode=episode,
                    errors=[],
                    warnings=quality_report.issues if quality_report.issues else [],
                    generation_time=generation_time,
                    api_calls=api_calls,
                    cost_estimate=cost_estimate
                )
                
                # 缓存成功结果
                if not self.config.debug_mode:
                    self.cache.set(cache_key, asdict(result))
                
                logger.info(f"第 {episode_spec.episode_number} 集生成完成，耗时: {generation_time:.2f}秒")
                
                return result
                
            except Exception as e:
                generation_time = time.time() - start_time
                logger.error(f"第 {episode_spec.episode_number} 集生成失败: {e}")
                
                return GenerationResult(
                    success=False,
                    episode=None,
                    errors=[str(e)],
                    warnings=[],
                    generation_time=generation_time,
                    api_calls=api_calls,
                    cost_estimate=api_calls * 0.5
                )
    
    def _generate_summary_report(self, results: List[GenerationResult]) -> None:
        """生成总结报告"""
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]
        
        total_time = sum(r.generation_time for r in results)
        total_api_calls = sum(r.api_calls for r in results)
        total_cost = sum(r.cost_estimate for r in results)
        
        avg_quality = np.mean([
            r.episode.quality_score for r in successful_results 
            if r.episode
        ]) if successful_results else 0
        
        logger.info("=== 生成总结报告 ===")
        logger.info(f"总剧集数: {len(results)}")
        logger.info(f"成功生成: {len(successful_results)}")
        logger.info(f"失败数量: {len(failed_results)}")
        logger.info(f"成功率: {len(successful_results)/len(results)*100:.1f}%")
        logger.info(f"总耗时: {total_time:.2f}秒")
        logger.info(f"平均每集耗时: {total_time/len(results):.2f}秒")
        logger.info(f"总API调用: {total_api_calls}")
        logger.info(f"平均每集API调用: {total_api_calls/len(results):.1f}")
        logger.info(f"总成本估算: ${total_cost:.2f}")
        logger.info(f"平均质量评分: {avg_quality:.2f}")
        
        if failed_results:
            logger.warning("失败的剧集:")
            for result in failed_results:
                logger.warning(f"  错误: {result.errors}")

# 主函数和命令行接口
def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='新的剧集结构化生成器')
    parser.add_argument('input_file', help='章节摘要JSON文件')
    parser.add_argument('--config', default='episode_config.yaml', help='配置文件路径')
    parser.add_argument('--output_dir', default='episodes_output', help='输出目录')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--force', action='store_true', help='强制重新生成')
    
    args = parser.parse_args()
    
    # 更新配置
    if args.debug:
        # 可以在这里临时修改配置
        pass
    
    # 异步运行
    asyncio.run(run_generation(args))

async def run_generation(args):
    """运行生成流程"""
    # 创建生成器
    generator = EpisodeStructureGenerator(args.config)
    
    # 更新配置
    if args.debug:
        generator.config.debug_mode = True
    if args.force:
        generator.config.force_regenerate = True
    
    # 加载输入数据
    with open(args.input_file, 'r', encoding='utf-8') as f:
        chapter_summaries = json.load(f)
    
    # 生成剧集
    results = await generator.generate_episodes(chapter_summaries)
    
    # 保存结果
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for result in results:
        if result.success and result.episode:
            episode_file = output_dir / f"episode_{result.episode.episode_number:02d}.json"
            with open(episode_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(result.episode), f, ensure_ascii=False, indent=2)
            logger.info(f"已保存: {episode_file}")

if __name__ == "__main__":
    main() 