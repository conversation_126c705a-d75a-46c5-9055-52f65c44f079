# 剧集结构化系统配置文件
# Episode Structuring System Configuration

# 处理配置
processing:
  max_episodes: null              # 最大生成集数，null表示无限制
  target_episodes: 10             # 目标集数
  enable_cache: true              # 启用缓存
  parallel_workers: 3             # 并行工作线程数
  debug_mode: false               # 调试模式
  force_regenerate: false         # 强制重新生成

# 质量配置
quality:
  min_words_per_episode: 3000     # 每集最小字数
  max_words_per_episode: 8000     # 每集最大字数
  target_words_per_episode: 6000  # 每集目标字数
  quality_threshold: 0.7          # 质量阈值
  enable_auto_optimization: true  # 启用自动优化
  
  # 质量权重配置
  weights:
    structure_completeness: 0.3   # 结构完整性权重
    content_quality: 0.4          # 内容质量权重
    duration_appropriateness: 0.2 # 时长合理性权重
    character_consistency: 0.1    # 角色一致性权重

# LLM配置
llm:
  provider: "google"              # LLM提供商: google, openai, anthropic
  model: "gemini-25-pro"          # 模型名称
  temperature: 0.7                # 温度参数
  max_tokens: 4000                # 最大输出tokens
  max_retries: 3                  # 最大重试次数
  timeout_seconds: 120            # 超时时间
  
  # 备用配置
  fallback:
    provider: "openai"
    model: "gpt-4"
    temperature: 0.7

# 缓存配置
cache:
  ttl_hours: 24                   # 缓存生存时间(小时)
  directory: ".cache"             # 缓存目录
  max_size_mb: 1000              # 最大缓存大小(MB)
  cleanup_interval_hours: 6       # 清理间隔(小时)
  compression: true               # 启用压缩

# 并行处理配置
parallel:
  max_concurrent_episodes: 3      # 最大并发剧集数
  max_concurrent_analysis: 5      # 最大并发分析数
  batch_size: 10                  # 批处理大小
  auto_scaling: true              # 自动扩缩容
  
  # 负载均衡配置
  load_balancing:
    enable: true
    performance_window: 3         # 性能评估窗口大小
    adjustment_threshold: 0.3     # 调整阈值

# 监控配置
monitoring:
  enable_metrics: true            # 启用指标收集
  enable_detailed_logging: false # 启用详细日志
  log_level: "INFO"              # 日志级别
  metrics_file: "metrics.json"   # 指标文件
  
  # 性能监控
  performance:
    track_generation_time: true
    track_api_costs: true
    track_quality_scores: true
    track_cache_hit_rates: true

# 输出配置
output:
  format: "json"                  # 输出格式: json, yaml
  pretty_print: true             # 美化输出
  include_metadata: true         # 包含元数据
  backup_enabled: true           # 启用备份
  
  # 文件命名
  naming:
    episode_prefix: "episode_"
    date_format: "%Y%m%d_%H%M%S"
    include_hash: true

# 验证配置
validation:
  enable_structure_validation: true    # 启用结构验证
  enable_content_validation: true     # 启用内容验证
  enable_llm_review: false            # 启用LLM评审(仅在质量不达标时)
  strict_mode: false                  # 严格模式
  
  # 验证规则
  rules:
    min_scenes_per_episode: 3         # 每集最少场景数
    max_scenes_per_episode: 10        # 每集最多场景数
    min_characters_per_episode: 2     # 每集最少角色数
    require_conflict_elements: true   # 要求冲突元素

# 实验性功能
experimental:
  enable_ai_assisted_optimization: false  # AI辅助优化
  enable_cross_episode_validation: false  # 跨集验证
  enable_adaptive_prompting: false        # 自适应提示词
  enable_multi_model_ensemble: false      # 多模型集成

# 开发配置
development:
  enable_profiling: false         # 启用性能分析
  enable_debug_output: false      # 启用调试输出
  save_intermediate_results: false # 保存中间结果
  test_mode: false               # 测试模式 