# AI驱动的高级提示工程系统

## 🎭 专家角色提示体系

### 核心专家角色定义

```python
class ExpertPersonaSystem:
    """专家角色提示系统"""
    
    def __init__(self):
        self.expert_personas = {
            "literary_analyst": {
                "identity": "资深文学评论家和编剧理论专家",
                "experience": "30年文学分析和剧本创作经验",
                "specialties": [
                    "深度文本解读和主题挖掘",
                    "文学价值评估和品质判断", 
                    "原著精神的准确把握",
                    "跨文化文学作品分析"
                ],
                "thinking_style": "深邃、批判性、注重文学内涵",
                "output_format": "结构化分析报告，包含深层解读"
            },
            
            "master_dramatist": {
                "identity": "世界级戏剧大师和剧本结构专家",
                "experience": "创作过50+经典剧本，获得过托尼奖", 
                "specialties": [
                    "三幕式结构的艺术化运用",
                    "戏剧冲突的精妙设计",
                    "情节节奏的精确控制",
                    "悬念和张力的布局艺术"
                ],
                "thinking_style": "结构化、逻辑性强、注重戏剧效果",
                "output_format": "详细的结构分析和改进建议"
            },
            
            "character_psychologist": {
                "identity": "心理学博士兼资深编剧",
                "experience": "心理学研究15年+编剧实践20年",
                "specialties": [
                    "深度角色心理分析",
                    "复杂人格结构设计",
                    "角色弧线和成长轨迹",
                    "人际关系动态建模"
                ],
                "thinking_style": "科学严谨、深入内心、注重心理真实性", 
                "output_format": "心理档案和发展建议"
            },
            
            "dialogue_virtuoso": {
                "identity": "对话艺术大师和语言天才",
                "experience": "创作过无数经典台词，语言功底深厚",
                "specialties": [
                    "角色语言个性化塑造",
                    "潜台词的精妙设计",
                    "情感表达的语言艺术",
                    "经典台词的创作技巧"
                ],
                "thinking_style": "敏感、富有诗意、注重语言的力量",
                "output_format": "台词优化和语言风格指导"
            },
            
            "emotion_director": {
                "identity": "情感设计大师和观众心理专家",
                "experience": "导演过多部感人至深的作品",
                "specialties": [
                    "情感曲线的精确设计",
                    "观众共鸣点的准确把握",
                    "情感冲击力的最大化",
                    "细腻情感的表达技巧"
                ],
                "thinking_style": "感性、直觉性强、注重情感真实",
                "output_format": "情感增强策略和共鸣点分析"
            }
        }
    
    def generate_expert_prompt(self, expert_type, task_context, specific_requirements):
        """生成专家角色的专业提示"""
        
        expert = self.expert_personas[expert_type]
        
        prompt = f"""
你是{expert['identity']}，拥有{expert['experience']}。

你的专业领域包括：
{chr(10).join(f"- {specialty}" for specialty in expert['specialties'])}

你的思维特点：{expert['thinking_style']}

当前任务背景：
{task_context}

具体要求：
{specific_requirements}

请以你的专业视角和丰富经验，提供最高质量的分析和建议。
你的输出应该体现{expert['thinking_style']}的特点，并遵循{expert['output_format']}的格式。

记住：质量是你的唯一追求，不要考虑时间和成本限制。每一个建议都应该是深思熟虑的专业判断。
"""
        
        return prompt
```

### 上下文感知的动态提示生成

```python
class ContextAwarePromptGenerator:
    """上下文感知的提示生成器"""
    
    def __init__(self):
        self.context_layers = {
            "story_world": {},  # 故事世界观
            "character_states": {},  # 角色状态
            "emotional_arc": {},  # 情感弧线
            "stylistic_patterns": {},  # 风格模式
            "quality_evolution": {}  # 质量演进
        }
    
    def generate_contextual_prompt(self, episode_number, task_type, expert_type):
        """生成考虑全部上下文的智能提示"""
        
        # 构建多层次上下文
        context_summary = self._build_comprehensive_context(episode_number)
        
        # 根据任务类型调整关注重点
        task_focus = self._determine_task_focus(task_type, episode_number)
        
        # 生成专家专用的上下文感知提示
        contextual_prompt = f"""
作为第{episode_number}集的{task_type}专家，你需要深度理解整个故事的发展脉络：

=== 故事世界观进展 ===
{context_summary['story_world_evolution']}

=== 角色发展状态 ===
{context_summary['character_development_state']}

=== 情感弧线进展 ===
{context_summary['emotional_arc_progression']}

=== 风格特征总结 ===
{context_summary['stylistic_consistency']}

=== 质量标准演进 ===
{context_summary['quality_benchmarks']}

本集创作重点：
{task_focus['primary_focus']}

具体创作要求：
{task_focus['specific_requirements']}

连贯性要求：
{task_focus['continuity_requirements']}

质量提升目标：
{task_focus['quality_improvement_targets']}

请基于以上全面的背景信息，以{expert_type}的专业视角，为本集的{task_type}提供最高质量的创作指导。

你的创作应该：
1. 与前面所有剧集形成有机的整体
2. 在保持连贯性的基础上实现新的突破
3. 体现你作为{expert_type}的专业水准
4. 追求艺术价值的最大化

记住：每一个创作决策都应该为整体的艺术品质服务。
"""
        
        return contextual_prompt
    
    def _build_comprehensive_context(self, current_episode):
        """构建综合上下文摘要"""
        
        # 这里会调用LLM来分析和总结前面所有剧集的关键信息
        context_analysis_prompt = f"""
作为故事分析专家，请深度分析前{current_episode-1}集的发展情况，
为第{current_episode}集的创作提供全面的背景信息。

请从以下维度进行分析：
1. 故事世界观的建立和发展
2. 主要角色的成长轨迹和当前状态
3. 整体情感弧线的进展情况
4. 已确立的风格特征和叙事模式
5. 质量标准的提升轨迹

输出要求：结构化、详细、为创作提供具体指导
"""
        
        # 返回LLM生成的上下文分析结果
        return self._call_context_analyzer_llm(context_analysis_prompt)
```

## 🔬 多阶段质量精进流程

### 质量递进的提示策略

```python
class QualityProgressionSystem:
    """质量递进系统"""
    
    def __init__(self):
        self.refinement_stages = {
            "initial_creation": {
                "focus": "创意发散和基础结构",
                "quality_target": 0.6,
                "prompt_strategy": "鼓励创新和想象力"
            },
            "structure_optimization": {
                "focus": "剧本结构和节奏优化",
                "quality_target": 0.7,
                "prompt_strategy": "专注于戏剧理论和结构完善"
            },
            "character_deepening": {
                "focus": "角色深度和心理层次",
                "quality_target": 0.8,
                "prompt_strategy": "心理学角度的角色分析和优化"
            },
            "dialogue_refinement": {
                "focus": "对话艺术和语言表达",
                "quality_target": 0.85,
                "prompt_strategy": "语言艺术和台词精雕细琢"
            },
            "emotional_enhancement": {
                "focus": "情感深度和共鸣力",
                "quality_target": 0.9,
                "prompt_strategy": "情感设计和观众体验优化"
            },
            "literary_elevation": {
                "focus": "文学性和艺术价值",
                "quality_target": 0.95,
                "prompt_strategy": "文学价值和艺术品质提升"
            }
        }
    
    def generate_stage_specific_prompt(self, stage, script_content, quality_assessment):
        """生成阶段特定的优化提示"""
        
        stage_config = self.refinement_stages[stage]
        
        prompt = f"""
=== {stage.upper()} 阶段专业优化 ===

当前阶段目标：{stage_config['focus']}
质量目标：{stage_config['quality_target']}
策略重点：{stage_config['prompt_strategy']}

当前剧本质量评估：
总体评分：{quality_assessment['overall_score']}
各维度详情：{self._format_dimension_scores(quality_assessment)}

需要重点优化的问题：
{self._identify_stage_specific_issues(stage, quality_assessment)}

具体优化要求：
{self._generate_stage_requirements(stage, script_content)}

请以{stage}阶段的专业标准，对剧本进行深度优化。
你的目标是将质量提升到{stage_config['quality_target']}以上。

优化原则：
1. 保持已有的优质部分
2. 重点改进薄弱环节
3. 确保整体协调一致
4. 追求该阶段的专业极致

输出要求：优化后的完整剧本 + 详细的改进说明
"""
        
        return prompt
```

### 智能迭代控制系统

```python
class IntelligentIterationController:
    """智能迭代控制系统"""
    
    def __init__(self):
        self.quality_threshold = 0.9
        self.max_iterations = 10
        self.improvement_tracking = {}
        
    async def orchestrate_quality_progression(self, initial_script, target_quality=0.95):
        """协调质量递进过程"""
        
        current_script = initial_script
        iteration_history = []
        
        for iteration in range(self.max_iterations):
            # 当前质量评估
            quality_assessment = await self._comprehensive_quality_assessment(current_script)
            
            # 记录进展
            iteration_history.append({
                "iteration": iteration,
                "quality_score": quality_assessment["overall_score"],
                "improvements": quality_assessment["improvements_from_previous"]
            })
            
            # 检查是否达到目标质量
            if quality_assessment["overall_score"] >= target_quality:
                return {
                    "final_script": current_script,
                    "final_quality": quality_assessment,
                    "iteration_history": iteration_history,
                    "success": True
                }
            
            # 分析改进空间
            improvement_analysis = await self._analyze_improvement_potential(
                current_script, quality_assessment, iteration_history
            )
            
            # 如果改进空间有限，停止迭代
            if improvement_analysis["potential"] < 0.05:
                break
            
            # 确定下一轮优化策略
            optimization_strategy = await self._design_next_optimization(
                quality_assessment, improvement_analysis
            )
            
            # 执行优化
            current_script = await self._execute_targeted_optimization(
                current_script, optimization_strategy
            )
        
        return {
            "final_script": current_script,
            "final_quality": await self._comprehensive_quality_assessment(current_script),
            "iteration_history": iteration_history,
            "success": False,
            "reason": "达到最大迭代次数或改进空间不足"
        }
```

## 🎯 专业化质量评估提示

### 多专家评估体系

```python
class MultiExpertAssessmentSystem:
    """多专家评估体系"""
    
    def __init__(self):
        self.assessment_experts = {
            "literary_critic": {
                "evaluation_focus": "文学价值和艺术成就",
                "assessment_criteria": [
                    "主题深度和思想内涵",
                    "文学技巧和表达艺术",
                    "原创性和创新价值",
                    "文化意义和社会价值"
                ],
                "scoring_weight": 0.25
            },
            
            "drama_theorist": {
                "evaluation_focus": "戏剧理论和结构艺术",
                "assessment_criteria": [
                    "戏剧结构的完整性和有效性",
                    "冲突设计的戏剧张力",
                    "节奏控制的精准度",
                    "戏剧技法的运用水平"
                ],
                "scoring_weight": 0.25
            },
            
            "audience_psychology_expert": {
                "evaluation_focus": "观众体验和心理效果",
                "assessment_criteria": [
                    "情感共鸣的强度和真实性",
                    "观众参与度和沉浸感",
                    "心理满足感和期待管理",
                    "观看体验的流畅性"
                ],
                "scoring_weight": 0.25
            },
            
            "industry_veteran": {
                "evaluation_focus": "制作可行性和市场潜力",
                "assessment_criteria": [
                    "制作难度和技术要求",
                    "演员表演的可实现性",
                    "视觉呈现的创作空间",
                    "商业价值和市场吸引力"
                ],
                "scoring_weight": 0.25
            }
        }
    
    def generate_expert_assessment_prompt(self, expert_type, script_content):
        """生成专家评估提示"""
        
        expert_config = self.assessment_experts[expert_type]
        
        prompt = f"""
作为{expert_type}，请对以下剧本进行专业评估。

你的评估重点：{expert_config['evaluation_focus']}

评估标准：
{chr(10).join(f"- {criterion}" for criterion in expert_config['assessment_criteria'])}

剧本内容：
{script_content}

请提供详细的专业评估，包括：

1. 总体评价（1-10分，保留两位小数）
2. 各项标准的具体评分和分析
3. 突出的优点和创新之处
4. 需要改进的具体方面
5. 改进建议和实施方案
6. 与同类作品的比较分析

评估要求：
- 以你的专业视角进行严格而公正的评价
- 提供具体、可操作的改进建议
- 既要挑剔也要给出建设性意见
- 评分要体现专业标准的严格性

请记住：你的评估将直接影响剧本的最终质量，请确保评估的专业性和准确性。
"""
        
        return prompt
```

### 综合质量裁决系统

```python
class QualityArbitrationSystem:
    """质量裁决系统"""
    
    async def synthesize_expert_opinions(self, expert_assessments, script_content):
        """综合专家意见形成最终质量判断"""
        
        synthesis_prompt = f"""
作为资深的创作总监和质量评审专家，你需要综合多位专家的评估意见，
形成对以下剧本的最终质量判断和改进指导。

专家评估汇总：
{self._format_expert_assessments(expert_assessments)}

剧本内容：
{script_content}

请基于专家意见进行综合分析，提供：

1. 综合质量评分（考虑各专家意见的权重）
2. 专家意见的一致性分析
3. 争议点的深入分析和裁决
4. 优先改进建议的排序
5. 整体改进策略和实施路径
6. 质量提升的预期效果

综合原则：
- 平衡不同专家的专业视角
- 识别并解决专家意见的分歧
- 形成统一、可执行的改进方案
- 确保改进建议的专业性和可行性

你的综合判断将作为下一轮优化的权威指导，请确保判断的准确性和指导性。
"""
        
        return await self._call_synthesis_expert_llm(synthesis_prompt)
```

## 🚀 AI代码生成的提示策略

### 自我优化的代码生成提示

```python
class SelfOptimizingCodeGenerator:
    """自我优化的代码生成器"""
    
    def generate_code_generation_prompt(self, component_spec, quality_requirements):
        """生成代码生成的高级提示"""
        
        prompt = f"""
作为世界顶级的AI编程专家，你需要生成最高质量的代码来实现以下组件：

=== 组件规格 ===
{component_spec}

=== 质量要求 ===
{quality_requirements}

=== 编程原则 ===
1. 代码质量优先于开发速度
2. 追求优雅、可读、可维护的代码
3. 充分考虑扩展性和灵活性
4. 实现完善的错误处理和日志记录
5. 包含详细的文档和注释

=== 特殊要求 ===
- 这是用于生成高质量剧本的AI系统
- 代码需要体现对艺术创作的深度理解
- 性能不是首要考虑，质量是核心目标
- 代码应该易于AI系统理解和修改

请生成：
1. 完整的、可运行的代码实现
2. 详细的代码文档和使用说明
3. 单元测试代码
4. 性能优化建议
5. 可能的改进方向

代码风格要求：
- 使用清晰、描述性的变量和函数名
- 充分的注释和文档字符串
- 遵循Python最佳实践
- 优雅的异常处理
- 模块化和可测试的设计

记住：你生成的代码将被用于创作艺术作品，请确保代码本身也体现出工匠精神。
"""
        
        return prompt
```

这个AI驱动的提示工程系统将确保每个LLM调用都能产生最高质量的输出，通过专业化的角色扮演、上下文感知和递进式优化，实现剧本创作的艺术价值最大化。 