# 动画剧本生成系统使用指南

## 概述

这是一个完整的从小说文本到动画视频的自动化生成系统。系统将几百万字的小说自动转换为结构化的剧本，然后生成对应的音频、图片和视频内容。

## 系统架构

### 完整流程

```
小说文本(.txt) → 章节摘要(.json) → 剧集结构(.json) → 剧本生成(.json) → 音频合成(.wav) → 图片生成 → 视频合成(.mp4)
```

### 各阶段详细说明

1. **阶段1: 章节摘要生成**
   - 输入: 原始小说文本文件 (`.txt`)
   - 脚本: `generate_summary.py`
   - 处理: 使用 LLM 分析并生成结构化章节摘要
   - 输出: 章节摘要JSON文件 (`project_name.json`)

2. **阶段2: 剧集结构化**
   - 输入: 章节摘要JSON文件
   - 脚本: `generate_episodes.py`
   - 处理: 分组、生成故事大纲、确定集数分配、生成剧本
   - 输出: 结构化剧本JSON文件 (`episode_XX.json`)

3. **阶段3: 音频生成**
   - 输入: 剧本文件 + 语音配置
   - 脚本: `episode_tts.py`
   - 处理: 使用 Azure TTS 合成对话和旁白
   - 输出: 音频文件 (`.wav`) + 时间轴文件 (`_timing.json`)

4. **阶段4: 图片生成**
   - 输入: 剧本文件
   - 脚本: `generate_image_prompts.py` + `generate_images.py`
   - 处理: 生成图片提示词 → 使用 ComfyUI 生成场景图片
   - 输出: 场景图片文件

5. **阶段5: 视频合成**
   - 输入: 时间轴文件 + 图片 + 音频
   - 脚本: `generate_video.py`
   - 处理: 图片动效 + 音视频同步
   - 输出: 最终视频文件 (`.mp4`)

## 快速开始

### 1. 环境准备

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

确保以下服务可用：
- Azure Speech Services (用于TTS)
- ComfyUI (用于图片生成)
- FFmpeg (用于视频处理)

### 2. 准备配置文件

复制并修改语音配置文件：
```bash
cp voice_config_example.json voice_config.json
# 根据需要修改角色语音配置
```

### 3. 运行完整流程

#### 方法1: 使用主控脚本（推荐）

```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json"
```

#### 方法2: 使用Shell脚本（详细控制）

```bash
# 先生成摘要和剧集
python generate_summary.py "2-animation-drama/raw_text/save_witch_whole.txt" \
    --output "2-animation-drama/raw_text/save_witch_whole.json"

python generate_episodes.py "2-animation-drama/raw_text/save_witch_whole.json" \
    --output_dir "2-animation-drama/episodes/save_witch_whole" \
    --max_episodes 2

# 然后为每个剧集运行shell脚本
./gen_animation_drama.sh \
    "2-animation-drama/episodes/save_witch_whole/episode_01.json" \
    --novel "save_witch_whole" \
    --output-dir "2-animation-drama/outputs" \
    all
```

### 4. 查看状态

```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json" \
    --status
```

## 高级用法

### 分阶段执行

只执行特定阶段：
```bash
# 只生成摘要和剧集
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages summary episodes

# 只生成音频
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages audio
```

### 强制重新生成

```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --force
```

### 自定义输出目录

```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --output_dir "/custom/output/path"
```

### 使用Shell脚本进行细粒度控制

Shell脚本提供了更详细的控制选项：

```bash
# 只生成图片提示
./gen_animation_drama.sh episode_01.json --novel project_name 1

# 只生成图片
./gen_animation_drama.sh episode_01.json --novel project_name 2

# 只合成音频
./gen_animation_drama.sh episode_01.json --novel project_name 3

# 执行所有步骤
./gen_animation_drama.sh episode_01.json --novel project_name all

# 强制重新生成
./gen_animation_drama.sh episode_01.json --novel project_name --force all
```

### Shell脚本功能编号对照表

| 编号 | 功能 | 脚本 | 说明 |
|------|------|------|------|
| 1 | 生成图片提示 | `generate_image_prompts.py` | 为每个场景生成图片描述 |
| 2 | 生成图片 | `generate_images.py` | 使用ComfyUI生成场景图片 |
| 3 | 合成音频 | `episode_tts.py` | Azure TTS合成语音 |
| 4 | 图片转视频 | `images2video.py` | 将图片序列转换为视频 |
| 5 | 合并音频 | `merge_voice_music.py` | 合并语音和背景音乐 |
| 6 | 生成字幕 | `generate_subtitles.py` | 从音频生成字幕文件 |
| 7 | 嵌入字幕 | `embed_subtitles.py` | 将字幕嵌入到视频中 |
| 8 | 生成视频片段 | `video_generator.py` | 生成特定场景的视频片段 |
| 9 | 替换视频片段 | `replace_video_clip.py` | 用生成的片段替换原视频 |

## 配置说明

### 语音配置文件格式

```json
{
  "narration": {
    "voice": "zh-CN-YunxiNeural",
    "default_style": "narration"
  },
  "characters": {
    "角色名": {
      "voice": {
        "voice": "zh-CN-YunyangNeural",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm",
          "Cheerful": "cheerful",
          "Sad": "sad",
          "Angry": "angry"
        }
      }
    }
  }
}
```

### 支持的情绪风格

- `Neutral`: 中性
- `Cheerful`: 愉快
- `Sad`: 悲伤
- `Angry`: 愤怒
- `Nervous`: 紧张
- `Determined`: 坚定
- `Hopeful`: 希望

## 输出文件结构

### 主控脚本输出结构
```
2-animation-drama/episodes/project_name/
├── generation_state.json          # 生成状态文件
├── episode_01.json                # 剧本文件
├── episode_01.wav                 # 音频文件
├── episode_01_timing.json         # 时间轴文件
├── episode_01_video.mp4           # 视频文件
├── episode_02.json
├── episode_02.wav
├── episode_02_timing.json
├── episode_02_video.mp4
└── images/                        # 图片目录
    ├── episode_01/
    └── episode_02/
```

### Shell脚本输出结构
```
2-animation-drama/outputs/project_name/episode_XX/
├── episode_XX.json                # 剧本文件
├── episode_XX_prompts.json        # 图片提示文件
├── episode_XX.wav                 # 音频文件
├── episode_XX_timing.json         # 时间轴文件
├── episode_XX_image.mp4           # 纯图片视频
├── episode_XX_voice.mp4           # 带音频的视频
├── episode_XX.mp4                 # 最终视频（带字幕）
├── episode_XX.srt                 # 字幕文件
└── video/                         # 生成的视频片段
    └── scene_1.mp4
```

## 当前问题分析

### 1. **工作流程不一致问题**

**问题：**
- 主控脚本(`generate_animation_drama.py`)和Shell脚本(`gen_animation_drama.sh`)使用不同的输出目录结构
- 两套脚本的功能有重叠但不完全一致

**影响：**
- 用户容易混淆使用方式
- 输出文件分散在不同位置
- 维护成本高

### 2. **剧本生成质量问题**

**当前问题表现：**
- 场景转换生硬，缺乏自然的情感流动
- 对话缺乏层次感和潜台词，显得平面化
- 角色心理描写不够深入，缺乏内在冲突的展现
- 剧情节奏单调，缺乏张弛有度的戏剧性

**根本原因：**
- 从章节摘要到剧本的转换过程中，丢失了原文的细腻情感和心理描写
- 结构生成过于依赖模板化的三幕式结构，缺乏灵活性
- 角色发展弧线在单集内不够完整，缺乏微妙的情感变化

### 3. **技术架构问题**

**问题：**
- 脚本间依赖关系复杂，缺乏统一的接口
- 错误处理机制不够完善
- 配置文件分散，缺乏统一管理

## 改进建议

### 1. **统一工作流程**

**建议：**
- 以主控脚本为主要接口，将Shell脚本功能整合进去
- 统一输出目录结构
- 简化用户使用方式

### 2. **优化剧本生成质量**

**具体改进措施：**

#### a. 增强章节摘要质量
```python
# 在 generate_summary.py 中增加情感分析
def enhanced_summarize_chapter(chapter_text, context):
    return {
        "plot_summary": "...",
        "emotional_beats": ["角色A的愤怒转为绝望", "角色B的犹豫变为坚定"],
        "character_psychology": {
            "角色A": "内心冲突：权力与道德的抉择",
            "角色B": "成长弧线：从被动到主动"
        },
        "dialogue_style": "正式、带有政治色彩的宫廷用语",
        "atmosphere": "紧张、压抑，暗示即将到来的冲突"
    }
```

#### b. 优化剧本结构设计
```python
# 新的场景结构设计
scene_structure = {
    "emotional_goal": "从怀疑转向信任",
    "micro_conflicts": ["身份质疑", "价值观碰撞", "利益冲突"],
    "emotional_bridge": "罗兰的内心独白暗示下一场景的冲突",
    "tension_curve": "低→高→缓解→新的紧张"
}
```

#### c. 提升对话质量
```python
# 角色语言特征定义
character_voice_patterns = {
    "罗兰": {
        "speech_style": "理性、略带讽刺，常用反问句",
        "vocabulary": "现代思维与古代用词的混合",
        "emotional_markers": {
            "愤怒": "语句变短，多用感叹",
            "思考": "长句，逻辑性强"
        }
    }
}
```

### 3. **技术架构改进**

**建议：**
- 重构主控脚本，整合Shell脚本功能
- 建立统一的配置管理系统
- 增强错误处理和日志记录
- 添加进度保存和恢复机制

## 故障排除

### 常见问题

1. **Azure TTS 认证失败**
   - 检查环境变量 `AZURE_SUBSCRIPTION_KEY` 和 `AZURE_REGION`
   - 确认 Azure Speech Services 配额

2. **ComfyUI 连接失败**
   - 确认 ComfyUI 服务运行在 `127.0.0.1:8188`
   - 检查工作流文件是否存在

3. **FFmpeg 错误**
   - 确认 FFmpeg 已正确安装并在 PATH 中
   - 检查视频编码参数

4. **脚本名称错误**
   - 文档中的 `generate_chapter_summary.py` 实际为 `generate_summary.py`
   - 确认使用正确的脚本名称

5. **输出目录不一致**
   - 主控脚本输出到 `2-animation-drama/episodes/`
   - Shell脚本输出到 `2-animation-drama/outputs/`
   - 根据使用的脚本检查对应目录

### 恢复中断的生成

#### 主控脚本恢复
系统会自动保存进度，重新运行相同命令即可从中断点继续：
```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json"
```

#### Shell脚本恢复
Shell脚本会检查已存在的文件，自动跳过已完成的步骤：
```bash
# 不使用 --force 参数时会跳过已存在的文件
./gen_animation_drama.sh episode_01.json --novel project_name all
```

### 查看详细错误信息

```bash
# 主控脚本状态查看
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --status | jq '.errors'

# Shell脚本日志查看
tail -f logs/generate_novel_video_*.log
```

## 性能优化建议

1. **并行处理**: 可以同时为多个剧集生成音频和图片
2. **缓存利用**: 系统会自动跳过已生成的内容
3. **资源监控**: 注意 GPU 内存使用情况（图片生成阶段）
4. **网络稳定**: 确保 Azure API 调用的网络稳定性

## 扩展开发

### 添加新的语音角色

在 `voice_config.json` 中添加新角色配置：

```json
{
  "characters": {
    "新角色名": {
      "voice": {
        "voice": "zh-CN-VoiceName",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm"
        }
      }
    }
  }
}
```

### 自定义图片生成

修改 `generate_image_prompts.py` 中的提示词生成逻辑，或调整 ComfyUI 工作流文件。

### 添加新的视频效果

在 `generate_video.py` 中添加新的视频效果处理逻辑。

## 迁移指南

### 从Shell脚本迁移到主控脚本

1. **备份现有输出**：
   ```bash
   cp -r 2-animation-drama/outputs/ 2-animation-drama/backup/
   ```

2. **转换配置文件**：
   - Shell脚本使用 `characters_assign.json`
   - 主控脚本使用 `voice_config.json`

3. **运行主控脚本**：
   ```bash
   python generate_animation_drama.py \
       "原小说文件.txt" "项目名" \
       --voice_config "voice_config.json"
   ```

### 配置文件转换

从 `characters_assign.json` 转换为 `voice_config.json`：

```python
# 转换脚本示例
import json

def convert_config(old_config_path, new_config_path):
    with open(old_config_path, 'r', encoding='utf-8') as f:
        old_config = json.load(f)
    
    new_config = {
        "narration": {
            "voice": "zh-CN-YunxiNeural",
            "default_style": "narration"
        },
        "characters": {}
    }
    
    for char_name, char_info in old_config.get("characters", {}).items():
        if "voice" in char_info:
            new_config["characters"][char_name] = {
                "voice": char_info["voice"]
            }
    
    with open(new_config_path, 'w', encoding='utf-8') as f:
        json.dump(new_config, f, ensure_ascii=False, indent=2)

# 使用示例
convert_config("characters_assign.json", "voice_config.json")
```
