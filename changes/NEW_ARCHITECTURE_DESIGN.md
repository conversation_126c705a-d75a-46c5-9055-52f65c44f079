# 新架构设计：剧集结构化系统重构方案

## 🎯 设计目标

基于现有系统的深度分析，新架构旨在解决以下核心问题：
- **效率问题**: 从15-30分钟减少到5-10分钟
- **成本问题**: 从$2-5降低到$0.8-2
- **可靠性**: 从85%提升到95%+
- **可维护性**: 统一配置，模块化设计

## 🏗️ 整体架构设计

### 核心设计原则

1. **并行优先**: 最大化并行处理，减少串行依赖
2. **一次生成**: 减少多轮迭代，追求一次性精准生成
3. **混合验证**: 确定性检查 + 启发式评分 + 选择性LLM验证
4. **配置驱动**: 所有参数外化，支持动态调整
5. **缓存优先**: 智能缓存，避免重复计算

### 新架构流程图

```
章节摘要(.json)
    ↓
[预处理阶段] 数据标准化 + 验证
    ↓
[智能分析阶段] 并行执行: {故事分析, 角色分析, 结构分析}
    ↓
[策略生成阶段] 一次性生成: 完整剧集分配策略
    ↓
[并行生成阶段] 多线程: {结构生成, 剧本生成, 质量检查}
    ↓
[后处理阶段] 格式化 + 最终验证
    ↓
结构化剧集(.json)
```

## 🧩 核心组件设计

### 1. 配置管理系统 (ConfigManager)

```python
@dataclass
class EpisodeConfig:
    """统一配置数据类"""
    # 处理配置
    max_episodes: Optional[int] = None
    target_episodes: int = 10
    enable_cache: bool = True
    parallel_workers: int = 3
    debug_mode: bool = False
    
    # 质量配置
    min_words_per_episode: int = 3000
    max_words_per_episode: int = 8000
    target_words_per_episode: int = 6000
    quality_threshold: float = 0.7
    
    # LLM配置
    llm_provider: str = "google"
    llm_model: str = "gemini-25-pro"
    temperature: float = 0.7
    max_tokens: int = 4000
    max_retries: int = 3
    
    # 缓存配置
    cache_ttl_hours: int = 24
    cache_directory: str = ".cache"

class ConfigManager:
    """配置管理器"""
    def __init__(self, config_path: str = "episode_config.yaml"):
        self.config = self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> EpisodeConfig:
        """加载配置文件"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        else:
            config_data = {}
        
        return EpisodeConfig(**config_data)
    
    def update_from_args(self, args: argparse.Namespace):
        """从命令行参数更新配置"""
        for key, value in vars(args).items():
            if value is not None and hasattr(self.config, key):
                setattr(self.config, key, value)
```

### 2. 数据结构标准化

```python
@dataclass
class ChapterSummary:
    """标准化章节摘要"""
    chapter_number: int
    title: str
    content: str
    key_events: List[str]
    key_characters: List[str]
    emotional_tone: str
    themes: List[str]
    word_count: int
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ChapterSummary':
        """从字典创建实例"""
        basic_info = data.get('basic_info', {})
        narrative = data.get('narrative', {})
        return cls(
            chapter_number=basic_info.get('chapter_number', 0),
            title=basic_info.get('title', ''),
            content=narrative.get('content', ''),
            key_events=data.get('key_events', []),
            key_characters=data.get('key_characters', []),
            emotional_tone=narrative.get('emotional_tone', 'neutral'),
            themes=narrative.get('themes', []),
            word_count=len(narrative.get('content', ''))
        )

@dataclass
class EpisodeStructure:
    """剧集结构"""
    episode_number: int
    chapters: List[int]
    title: str
    theme: str
    scenes: List[Dict[str, Any]]
    characters: List[str]
    estimated_duration: int
    quality_score: float
    
@dataclass
class GenerationResult:
    """生成结果"""
    success: bool
    episode: Optional[EpisodeStructure]
    errors: List[str]
    warnings: List[str]
    generation_time: float
    api_calls: int
    cost_estimate: float
```

### 3. 智能缓存系统

```python
class SmartCache:
    """智能缓存系统"""
    def __init__(self, config: EpisodeConfig):
        self.config = config
        self.cache_dir = Path(config.cache_directory)
        self.cache_dir.mkdir(exist_ok=True)
        
    def get_cache_key(self, data: Any, operation: str) -> str:
        """生成缓存键"""
        content_hash = hashlib.sha256(
            json.dumps(data, sort_keys=True, ensure_ascii=False).encode()
        ).hexdigest()[:16]
        return f"{operation}_{content_hash}"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        cache_file = self.cache_dir / f"{key}.json"
        if not cache_file.exists():
            return None
            
        # 检查过期时间
        stat = cache_file.stat()
        if time.time() - stat.st_mtime > self.config.cache_ttl_hours * 3600:
            cache_file.unlink()  # 删除过期缓存
            return None
            
        with open(cache_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存"""
        cache_file = self.cache_dir / f"{key}.json"
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(value, f, ensure_ascii=False, indent=2)
```

### 4. 质量评估系统

```python
class QualityAssessment:
    """质量评估系统"""
    
    def __init__(self, config: EpisodeConfig):
        self.config = config
        
    def assess_episode(self, episode: EpisodeStructure) -> QualityReport:
        """评估剧集质量"""
        # 1. 结构完整性检查
        structure_score = self._check_structure_completeness(episode)
        
        # 2. 内容质量评分
        content_score = self._assess_content_quality(episode)
        
        # 3. 时长合理性检查
        duration_score = self._check_duration_appropriateness(episode)
        
        # 4. 角色一致性检查
        character_score = self._check_character_consistency(episode)
        
        # 综合评分
        overall_score = (
            structure_score * 0.3 +
            content_score * 0.4 +
            duration_score * 0.2 +
            character_score * 0.1
        )
        
        return QualityReport(
            overall_score=overall_score,
            structure_score=structure_score,
            content_score=content_score,
            duration_score=duration_score,
            character_score=character_score,
            needs_llm_review=overall_score < self.config.quality_threshold
        )
    
    def _check_structure_completeness(self, episode: EpisodeStructure) -> float:
        """检查结构完整性"""
        score = 0.0
        
        # 检查必需字段
        if episode.title: score += 0.2
        if episode.scenes: score += 0.3
        if episode.characters: score += 0.2
        if len(episode.scenes) >= 3: score += 0.3  # 至少3个场景
        
        return min(score, 1.0)
    
    def _assess_content_quality(self, episode: EpisodeStructure) -> float:
        """评估内容质量"""
        score = 0.0
        
        # 检查场景数量合理性
        scene_count = len(episode.scenes)
        if 3 <= scene_count <= 8:
            score += 0.3
        elif scene_count > 0:
            score += 0.1
            
        # 检查对话比例（如果有）
        total_dialogue = sum(
            len(scene.get('dialogue', []))
            for scene in episode.scenes
        )
        if total_dialogue > 0:
            score += 0.4
            
        # 检查冲突元素
        has_conflict = any(
            'conflict' in scene or 'tension' in scene
            for scene in episode.scenes
        )
        if has_conflict:
            score += 0.3
            
        return min(score, 1.0)

@dataclass
class QualityReport:
    overall_score: float
    structure_score: float
    content_score: float
    duration_score: float
    character_score: float
    needs_llm_review: bool
    issues: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
```

## 🔄 新生成流程

### 主控制器

```python
class EpisodeStructureGenerator:
    """新的剧集结构生成器"""
    
    def __init__(self, config_path: str = "episode_config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        self.cache = SmartCache(self.config)
        self.quality_assessor = QualityAssessment(self.config)
        self.llm_client = LLMClient(self.config)
        
    async def generate_episodes(
        self,
        chapter_summaries: List[Dict[str, Any]]
    ) -> List[GenerationResult]:
        """异步生成所有剧集"""
        
        # 1. 预处理 - 数据标准化
        standardized_chapters = self._standardize_chapters(chapter_summaries)
        
        # 2. 智能分析 - 并行执行
        analysis_result = await self._parallel_analysis(standardized_chapters)
        
        # 3. 策略生成 - 一次性生成完整策略
        allocation_strategy = await self._generate_allocation_strategy(
            standardized_chapters, analysis_result
        )
        
        # 4. 并行生成 - 多线程生成剧集
        results = await self._parallel_episode_generation(
            allocation_strategy, standardized_chapters
        )
        
        return results
    
    def _standardize_chapters(
        self, 
        chapter_summaries: List[Dict[str, Any]]
    ) -> List[ChapterSummary]:
        """标准化章节数据"""
        return [
            ChapterSummary.from_dict(chapter)
            for chapter in chapter_summaries
        ]
    
    async def _parallel_analysis(
        self,
        chapters: List[ChapterSummary]
    ) -> AnalysisResult:
        """并行分析：故事、角色、结构"""
        
        # 并行执行三个分析任务
        story_task = self._analyze_story_structure(chapters)
        character_task = self._analyze_characters(chapters)
        theme_task = self._analyze_themes(chapters)
        
        story_analysis, character_analysis, theme_analysis = await asyncio.gather(
            story_task, character_task, theme_task
        )
        
        return AnalysisResult(
            story_structure=story_analysis,
            characters=character_analysis,
            themes=theme_analysis
        )
    
    async def _generate_allocation_strategy(
        self,
        chapters: List[ChapterSummary],
        analysis: AnalysisResult
    ) -> AllocationStrategy:
        """一次性生成完整的分配策略"""
        
        # 检查缓存
        cache_key = self.cache.get_cache_key(
            {"chapters": chapters, "analysis": analysis},
            "allocation_strategy"
        )
        
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return AllocationStrategy(**cached_result)
        
        # 生成策略
        strategy_data = {
            "chapters": [asdict(ch) for ch in chapters],
            "analysis": asdict(analysis),
            "target_episodes": self.config.target_episodes,
            "target_words_per_episode": self.config.target_words_per_episode
        }
        
        result = await self.llm_client.call_async(
            "generate_complete_allocation_strategy",
            strategy_data
        )
        
        strategy = AllocationStrategy(**result)
        
        # 缓存结果
        self.cache.set(cache_key, asdict(strategy))
        
        return strategy
    
    async def _parallel_episode_generation(
        self,
        strategy: AllocationStrategy,
        chapters: List[ChapterSummary]
    ) -> List[GenerationResult]:
        """并行生成剧集"""
        
        # 识别独立剧集（无依赖关系）
        independent_episodes, dependent_episodes = self._categorize_episodes(
            strategy.episodes
        )
        
        # 并行生成独立剧集
        semaphore = asyncio.Semaphore(self.config.parallel_workers)
        independent_tasks = [
            self._generate_single_episode(episode, chapters, semaphore)
            for episode in independent_episodes
        ]
        
        independent_results = await asyncio.gather(*independent_tasks)
        
        # 顺序生成有依赖的剧集
        dependent_results = []
        previous_context = None
        
        for episode_spec in dependent_episodes:
            result = await self._generate_single_episode(
                episode_spec, chapters, semaphore, previous_context
            )
            dependent_results.append(result)
            
            if result.success:
                previous_context = result.episode
        
        return independent_results + dependent_results
    
    async def _generate_single_episode(
        self,
        episode_spec: EpisodeSpec,
        chapters: List[ChapterSummary],
        semaphore: asyncio.Semaphore,
        previous_context: Optional[EpisodeStructure] = None
    ) -> GenerationResult:
        """生成单个剧集"""
        
        async with semaphore:
            start_time = time.time()
            api_calls = 0
            
            try:
                # 检查缓存
                cache_key = self.cache.get_cache_key(
                    {"episode_spec": asdict(episode_spec), "chapters": chapters},
                    "episode_generation"
                )
                
                cached_result = self.cache.get(cache_key)
                if cached_result and not self.config.debug_mode:
                    return GenerationResult(**cached_result)
                
                # 生成剧集结构
                episode_data = {
                    "episode_spec": asdict(episode_spec),
                    "relevant_chapters": [
                        asdict(ch) for ch in chapters
                        if ch.chapter_number in episode_spec.chapters
                    ],
                    "previous_context": asdict(previous_context) if previous_context else None,
                    "style_requirements": {
                        "min_words": self.config.min_words_per_episode,
                        "max_words": self.config.max_words_per_episode,
                        "target_words": self.config.target_words_per_episode
                    }
                }
                
                # 调用LLM生成
                llm_result = await self.llm_client.call_async(
                    "generate_complete_episode",
                    episode_data
                )
                api_calls += 1
                
                # 创建剧集结构
                episode = EpisodeStructure(**llm_result)
                
                # 质量评估
                quality_report = self.quality_assessor.assess_episode(episode)
                episode.quality_score = quality_report.overall_score
                
                # 如果质量不达标，进行LLM评审优化
                if quality_report.needs_llm_review:
                    optimized_result = await self.llm_client.call_async(
                        "optimize_episode_quality",
                        {
                            "episode": asdict(episode),
                            "quality_issues": quality_report.issues,
                            "suggestions": quality_report.suggestions
                        }
                    )
                    api_calls += 1
                    episode = EpisodeStructure(**optimized_result)
                    episode.quality_score = self.quality_assessor.assess_episode(episode).overall_score
                
                generation_time = time.time() - start_time
                cost_estimate = api_calls * 0.5  # 估算成本
                
                result = GenerationResult(
                    success=True,
                    episode=episode,
                    errors=[],
                    warnings=[],
                    generation_time=generation_time,
                    api_calls=api_calls,
                    cost_estimate=cost_estimate
                )
                
                # 缓存成功结果
                self.cache.set(cache_key, asdict(result))
                
                return result
                
            except Exception as e:
                generation_time = time.time() - start_time
                return GenerationResult(
                    success=False,
                    episode=None,
                    errors=[str(e)],
                    warnings=[],
                    generation_time=generation_time,
                    api_calls=api_calls,
                    cost_estimate=api_calls * 0.5
                )
```

## 📊 性能优化特性

### 1. 智能并行处理

```python
class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def process_independent_tasks(
        self,
        tasks: List[Callable],
        max_concurrent: int = None
    ) -> List[Any]:
        """处理独立任务"""
        semaphore = asyncio.Semaphore(max_concurrent or self.max_workers)
        
        async def bounded_task(task):
            async with semaphore:
                return await task()
        
        return await asyncio.gather(*[bounded_task(task) for task in tasks])
```

### 2. 自适应负载均衡

```python
class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, config: EpisodeConfig):
        self.config = config
        self.performance_history = []
        
    def adjust_concurrency(self, current_performance: Dict[str, float]) -> int:
        """根据性能调整并发数"""
        self.performance_history.append(current_performance)
        
        if len(self.performance_history) < 3:
            return self.config.parallel_workers
        
        # 分析最近的性能趋势
        recent_avg_time = np.mean([
            p['generation_time'] for p in self.performance_history[-3:]
        ])
        
        if recent_avg_time > 300:  # 5分钟
            return max(1, self.config.parallel_workers - 1)
        elif recent_avg_time < 60:  # 1分钟
            return min(6, self.config.parallel_workers + 1)
        
        return self.config.parallel_workers
```

## 🔧 实施计划

### 阶段1: 基础架构搭建 (1-2周)
1. 配置管理系统
2. 数据结构标准化
3. 缓存系统
4. 基础的质量评估

### 阶段2: 核心功能实现 (2-3周)
1. 并行分析功能
2. 一次性策略生成
3. 并行剧集生成
4. LLM客户端优化

### 阶段3: 性能优化 (1-2周)
1. 负载均衡
2. 智能缓存策略优化
3. 错误恢复机制
4. 监控和日志系统

### 阶段4: 测试和优化 (1周)
1. 性能基准测试
2. 质量对比测试
3. 稳定性测试
4. 文档完善

## 📈 预期效果

| 指标 | 当前值 | 目标值 | 改进幅度 |
|------|--------|--------|----------|
| 生成时间 | 15-30分钟 | 3-8分钟 | 60-75%提升 |
| API调用 | 8-10次/集 | 1-3次/集 | 70-90%减少 |
| 成本 | $2-5/集 | $0.3-1.5/集 | 70-85%降低 |
| 成功率 | ~85% | >95% | 10-15%提升 |
| 并发能力 | 1集/时 | 3-6集/时 | 200-500%提升 |

通过这个新架构，我们将彻底解决现有系统的性能和可维护性问题，为未来的功能扩展奠定坚实的基础。 