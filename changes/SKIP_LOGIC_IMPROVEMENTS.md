# 视频跳过逻辑改进说明

## 问题分析

### 原有逻辑的问题
1. **不完整的跳过检查**：对于通过 `--video_dir` 或 `--video_path` 处理的本地视频文件，如果视频不在数据库中，系统不会检查是否已经处理过
2. **缺乏统一的跳过逻辑**：不同的处理路径（数据库视频 vs 本地文件）有不同的跳过逻辑
3. **信息不够清晰**：用户无法清楚了解哪些视频被跳过，哪些被处理

### 原有的跳过逻辑
- ✅ `--process_all_new_in_db` 模式：只处理没有clips的long_videos
- ✅ 数据库中存在且有clips的视频：会跳过
- ❌ 本地文件路径处理：缺乏完整的跳过检查

## 改进方案

### 1. 增强的跳过检查逻辑

```python
# 对每个视频文件进行全面检查
for video_file_path in videos_to_process:
    existing_video = metadata_db.get_long_video_by_path(video_file_path)
    should_skip = False
    
    if existing_video:
        # 检查是否已有clips
        clip_count = get_clip_count(existing_video['id'])
        if clip_count > 0:
            should_skip = True
    elif current_source_info:
        # 通过source和source_id检查
        lv_info = metadata_db.get_long_video_info(
            source=current_source_info['source'], 
            source_id=current_source_info['source_id']
        )
        if lv_info:
            clip_count = get_clip_count(lv_info['id'])
            if clip_count > 0:
                should_skip = True
    
    if should_skip:
        videos_skipped += 1
        continue
```

### 2. 改进的用户界面

#### 清晰的状态指示
- 🆕 新视频（未在数据库中）
- ✓ 视频已在数据库中
- ⏭️ 跳过已处理的视频
- 📝 需要处理的视频
- 🎬 开始处理
- ✅ 处理完成
- ❌ 处理出错

#### 详细的处理摘要
```
📊 PROCESSING SUMMARY
============================================================
Total videos found: 5
Videos processed: 2
Videos skipped: 3
✅ Successfully processed 2 video(s)
⏭️ Skipped 3 already processed video(s)
============================================================
```

### 3. 递归目录扫描集成

跳过逻辑与新的递归目录扫描功能完美集成：

```bash
python video_retrieval/video_retrieval_main.py process_long_video --video_dir data/long_videos
```

输出示例：
```
Scanning directory: data/long_videos for videos (including subdirectories)...
  Scanning subdirectory: category1
    Found video: category1/movie1.mp4
  Scanning subdirectory: category2/documentaries
    Found video: category2/documentaries/doc1.avi

Found 2 video(s) to process.

--- Processing video: category1/movie1.mp4 ---
🆕 New video not found in database. Will process.
🎬 Starting processing...
✅ Finished processing: category1/movie1.mp4

--- Processing video: category2/documentaries/doc1.avi ---
✓ Video found in database with ID 123
⏭️ Video already processed with 5 clips. Skipping.

📊 PROCESSING SUMMARY
============================================================
Total videos found: 2
Videos processed: 1
Videos skipped: 1
✅ Successfully processed 1 video(s)
⏭️ Skipped 1 already processed video(s)
============================================================
```

## 技术实现细节

### 数据库检查逻辑

1. **按路径检查**：`metadata_db.get_long_video_by_path(video_file_path)`
2. **按source/source_id检查**：`metadata_db.get_long_video_info(source=..., source_id=...)`
3. **clips计数检查**：`SELECT COUNT(*) FROM video_clips WHERE original_long_video_id = ?`

### 错误处理

- 添加了try-catch块来处理处理过程中的异常
- 异常不会中断整个批处理过程
- 提供详细的错误信息和堆栈跟踪

### 性能优化

- 数据库连接在每次检查后正确关闭
- 避免重复的数据库查询
- 清晰的处理流程减少不必要的操作

## 使用场景

### 1. 初次处理大量视频
```bash
python video_retrieval/video_retrieval_main.py process_long_video --video_dir /path/to/videos
```
- 所有视频都会被处理
- 清晰显示处理进度

### 2. 重新运行处理命令
```bash
python video_retrieval/video_retrieval_main.py process_long_video --video_dir /path/to/videos
```
- 已处理的视频会被自动跳过
- 只处理新添加的视频
- 显示跳过统计

### 3. 混合场景（新旧视频混合）
- 自动识别哪些需要处理，哪些需要跳过
- 提供详细的处理报告

## 兼容性

- ✅ 完全向后兼容现有功能
- ✅ 不影响 `--process_all_new_in_db` 模式
- ✅ 不影响单个视频处理 `--video_path`
- ✅ 与递归目录扫描功能完美集成

## 总结

这次改进解决了视频重复处理的问题，提供了：

1. **完整的跳过逻辑**：确保已处理的视频不会被重复处理
2. **清晰的用户反馈**：用户可以清楚了解处理状态
3. **详细的处理统计**：提供处理摘要和统计信息
4. **良好的错误处理**：单个视频的错误不会影响整个批处理
5. **与新功能集成**：与递归目录扫描功能无缝集成

现在用户可以安全地重复运行处理命令，系统会智能地跳过已处理的视频，只处理新的视频文件。
