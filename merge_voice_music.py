import argparse
import subprocess
import os
from config import logger

def add_audio_to_video(args):
    """
    将视频、语音音频和可选的背景音乐合成为一个视频文件。
    如果指定了video_has_voice，则忽略voice输入，直接合并视频和背景音乐。

    参数:
    - args: argparse 解析的参数。
    """

    # 验证输入文件是否存在
    if not os.path.isfile(args.video):
        raise FileNotFoundError(f"视频文件不存在: {args.video}")
    if not args.video_has_voice and not os.path.isfile(args.voice):
        raise FileNotFoundError(f"语音音频文件不存在: {args.voice}")
    if args.music and not os.path.isfile(args.music):
        raise FileNotFoundError(f"背景音乐文件不存在: {args.music}")

    # 获取音频和视频的时长
    def get_duration(file_path):
        """获取媒体文件的持续时间"""
        try:
            result = subprocess.run(
                [
                    "ffprobe", "-v", "error", "-show_entries",
                    "format=duration", "-of",
                    "default=noprint_wrappers=1:nokey=1", file_path
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            return float(result.stdout.strip())
        except Exception as e:
            logger.error(f"无法获取文件的持续时间 {file_path}: {e}")
            return None

    # 获取视频时长
    video_duration = get_duration(args.video)
    if video_duration is None:
        logger.error("无法获取视频的持续时间，无法继续处理。")
        return
    
    # 如果需要添加外部语音，检查语音文件时长
    if not args.video_has_voice:
        voice_duration = get_duration(args.voice)
        if voice_duration is None:
            logger.error("无法获取语音音频的持续时间，无法继续处理。")
            return

    # 准备基本输入参数
    inputs = ["-i", args.video]  # 视频始终是第一个输入
    filter_complex = []
    maps = ["-map", "0:v"]  # 始终使用第一个输入的视频流

    # 根据视频是否已包含语音，确定语音来源
    if not args.video_has_voice:
        # 添加外部语音输入
        inputs.extend(["-i", args.voice])
        voice_stream = "1:a"  # 外部语音音频索引
        voice_index = 1
    else:
        # 视频已包含语音，使用视频的音频轨道
        voice_stream = "0:a"  # 视频中的音频索引
        voice_index = 0

    # 处理背景音乐（如果提供）
    if args.music:
        music_duration = get_duration(args.music)
        if music_duration is None:
            logger.error("无法获取背景音乐的持续时间，无法继续处理。")
            return

        # 计算淡入淡出时间（根据视频时长调整）
        fade_in = min(args.fade_in, video_duration)
        fade_out = min(args.fade_out, video_duration)
        
        # 计算淡出开始时间
        if args.fade_out_start is None:
            fade_out_start = max(0, video_duration - fade_out)
        else:
            fade_out_start = min(args.fade_out_start, video_duration - fade_out)

        # 添加背景音乐输入（循环播放）
        music_index = len(inputs) // 2  # 计算背景音乐的输入索引
        inputs.extend(["-stream_loop", "-1", "-i", args.music])
        music_stream = f"{music_index}:a"  # 背景音乐流的索引

        # 构建背景音乐的音频滤镜
        audio_filters = []
        if args.music_volume is not None:
            audio_filters.append(f"volume={args.music_volume}")
        if fade_in > 0:
            audio_filters.append(f"afade=t=in:st=0:d={fade_in}")
        if fade_out > 0:
            audio_filters.append(f"afade=t=out:st={fade_out_start}:d={fade_out}")

        # 应用音频滤镜
        if audio_filters:
            bgm_filter = ','.join(audio_filters)
            bgm_filter_complex = f"[{music_stream}]{bgm_filter}[bgm];"
            bgm_input = "[bgm]"
        else:
            bgm_filter_complex = ""
            bgm_input = f"[{music_stream}]"

        # 构建音频混合命令
        filter_complex_str = (
            f"{bgm_filter_complex}"
            f"[{voice_stream}]volume={args.voice_volume}[voice];"
            f"[voice]{bgm_input}amix=inputs=2:duration=shortest:dropout_transition=2[aout]"
        )

        filter_complex.extend(["-filter_complex", filter_complex_str])
        maps = ["-map", "0:v", "-map", "[aout]"]
    elif not args.video_has_voice:
        # 只有外部语音音频，调整音量
        filter_complex_str = f"[{voice_stream}]volume={args.voice_volume}[aout]"
        filter_complex.extend(["-filter_complex", filter_complex_str])
        maps = ["-map", "0:v", "-map", "[aout]"]
    # 如果没有背景音乐且视频已包含语音，不需要额外的滤镜操作

    # 构建 FFmpeg 命令
    command = [
        "ffmpeg",
        "-y",                  # 覆盖输出文件
        "-hide_banner",        # 隐藏 FFmpeg 标志
        "-stats",              # 只显示编码统计信息
        "-stats_period", "10", # 每10秒更新一次统计信息
        "-loglevel", "warning",# 只显示警告和错误
        *inputs,               # 添加所有输入源
    ]
    
    # 添加滤镜（如果有）
    if filter_complex:
        command.extend(filter_complex)
    
    # 添加输出参数
    command.extend([
        *maps,                       # 流映射
        "-shortest",                 # 以最短的输入流为准
        "-c:v", args.video_codec,    # 视频编码
        "-b:v", args.video_bitrate,  # 视频比特率
        "-c:a", args.audio_codec,    # 音频编码
        "-b:a", args.audio_bitrate,  # 音频比特率
        args.output                  # 输出文件
    ])

    # 执行命令前记录
    logger.debug(f"执行 FFmpeg 命令: {' '.join(command)}")

    # 执行命令
    try:
        subprocess.run(command, check=True)
        logger.info(f"处理完成，输出保存至: {args.output}")
    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg 处理错误: {e}")
        if e.stderr:
            logger.error(f"错误详情: {e.stderr}")
    except Exception as e:
        logger.error(f"未知错误: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="将视频、语音音频和可选的背景音乐合成为一个视频文件。")

    # 视频和音频输入参数
    parser.add_argument("--video", required=True, help="输入视频文件路径。")
    parser.add_argument("--voice", help="输入语音音频文件路径。")
    parser.add_argument("--music", help="输入背景音乐文件路径（可选）。")
    parser.add_argument("--video-has-voice", action="store_true", 
                        help="指定输入视频是否已包含语音。启用此选项时，将忽略--voice参数。")

    # 输出文件名参数
    parser.add_argument("--output", help="输出视频文件路径。默认基于输入视频名称自动生成。")

    # 音频混合选项
    parser.add_argument("--music-volume", type=float, default=0.5, 
                        help="背景音乐音量 (0.0 到 1.0，默认: 0.5)。")
    parser.add_argument("--voice-volume", type=float, default=1.0, 
                        help="语音音频音量 (0.0 到 1.0，默认: 1.0)。")
    parser.add_argument("--fade-in", type=float, default=5, 
                        help="背景音乐淡入持续时间（秒，默认: 5）。")
    parser.add_argument("--fade-out", type=float, default=5, 
                        help="背景音乐淡出持续时间（秒，默认: 5）。")
    parser.add_argument("--fade-out-start", type=float, default=None, 
                        help="背景音乐淡出开始时间（秒，默认: 视频长度 - 淡出持续时间）。")

    # 编码选项
    parser.add_argument("--audio-codec", default="aac", 
                        help="输出音频编码格式 (默认: aac)。")
    parser.add_argument("--audio-bitrate", default="192k", 
                        help="输出音频比特率 (默认: 192k)。")
    parser.add_argument("--video-codec", default="libx264", 
                        help="输出视频编码格式 (默认: libx264)。")
    parser.add_argument("--video-bitrate", default="1000k", 
                        help="输出视频比特率 (默认: 1000k)。")

    args = parser.parse_args()

    # 参数验证
    if not args.video_has_voice and not args.voice:
        parser.error("如果未指定--video-has-voice，则必须提供--voice参数。")
    
    # 处理音量参数范围
    if args.music_volume is not None and (args.music_volume < 0 or args.music_volume > 1):
        parser.error("背景音乐音量必须在0.0到1.0之间。")
    if args.voice_volume < 0 or args.voice_volume > 1:
        parser.error("语音音频音量必须在0.0到1.0之间。")

    # 修改输出文件名的逻辑
    if not args.output:
        video_path = args.video
        directory = os.path.dirname(video_path)
        basename = os.path.basename(video_path)
        name_without_ext = os.path.splitext(basename)[0]
        
        # 根据处理模式选择输出后缀
        output_suffix = '_music' if args.video_has_voice else '_voice'
        
        if name_without_ext.endswith('_image'):
            # 如果输入视频名称以 _image 结尾，去掉它
            output_name = name_without_ext[:-6] + output_suffix + '.mp4'
        else:
            # 如果不是，添加相应后缀
            output_name = name_without_ext + output_suffix + '.mp4'
        
        args.output = os.path.join(directory, output_name)

    # 调用处理函数
    add_audio_to_video(args)