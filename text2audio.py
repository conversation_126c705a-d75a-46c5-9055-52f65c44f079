import os
import json
import base64
import argparse
import tempfile
import time
import hashlib
import shutil
from typing import List, Dict, Any, Optional, Tuple

from elevenlabs.client import ElevenLabs
from pydub import AudioSegment

ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
eleven_client = ElevenLabs(api_key=ELEVENLABS_API_KEY, timeout=60)

from config import (
    logger,
    AUDIO_DIR,
    VOICE_DELAY_TIME,
    TEMP_FILE_DIR,
    AUDIO_CACHE_DIR,  # 添加缓存目录配置
)
from modules.nlp_model import get_nlp_model, get_language_code
from modules.utils import process_script

# 音频格式相关常量
AUDIO_FORMAT = "mp3"  # 输出格式
AUDIO_SAMPLE_RATE = "44100"  # 采样率
AUDIO_BITRATE = "128"  # 比特率
# 组合成ElevenLabs API需要的格式
AUDIO_OUTPUT_FORMAT = f"{AUDIO_FORMAT}_{AUDIO_SAMPLE_RATE}_{AUDIO_BITRATE}"

# 添加重试相关常量
MAX_RETRY_COUNT = 3
RETRY_INITIAL_DELAY_SECONDS = 10  # 初始等待时间设为10秒
RETRY_BACKOFF_MULTIPLIER = 1.5  # 每次重试延长等待时间的倍数

# ElevenLabs Voice Mapping
ELEVENLABS_VOICE_MAPPING = {
    'English': 'brkaXeg2GVKseQkCHQra',
    'English-Story': 'Adam',
    'Chinese': 'Xiaoxiao',
    'Japanese': 'brkaXeg2GVKseQkCHQra',
    'Spanish': 'Miguel',
    'Portuguese': 'Clara'
}

def calculate_backoff_time(retry_count):
    """计算指数退避等待时间"""
    return RETRY_INITIAL_DELAY_SECONDS * (RETRY_BACKOFF_MULTIPLIER ** (retry_count - 1))

def get_model_id(language: str) -> str:
    """
    Select appropriate model_id per ElevenLabs best-practice:
    English variants → monolingual; all others → multilingual.
    """
    return "eleven_turbo_v2_5" if language.lower().startswith("english") else "eleven_multilingual_v2"

def save_timestamp_file(alignment_data, timestamp_path):
    """保存时间戳数据到文件"""
    with open(timestamp_path, 'w', encoding='utf-8') as f:
        json.dump(alignment_data, f, ensure_ascii=False, indent=2)
    logger.info(f"时间戳数据已保存到 {timestamp_path}")

def process_segments(json_file, audio_dir, theme, language='Chinese'):
    """处理 JSON 文件中的每个段落并生成音频"""
    logger.info(f"使用 ElevenLabs 处理来自 {json_file} 的段落，语言: {language}")
    
    # 获取语言代码
    lang_code = get_language_code(language)
    if not lang_code:
        logger.warning(f"不支持的语言: {language}, 使用默认 'en'")
        lang_code = 'en'
    
    # 读取输入的 JSON 文件
    with open(json_file, 'r', encoding='utf-8') as f:
        segments = json.load(f)
    
    # 创建音频输出目录和时间戳目录
    os.makedirs(audio_dir, exist_ok=True)
    timestamps_dir = os.path.join(os.path.dirname(audio_dir), 'timestamps')
    os.makedirs(timestamps_dir, exist_ok=True)
    
    # 确保缓存目录存在
    os.makedirs(AUDIO_CACHE_DIR, exist_ok=True)
    
    # 提取所有段落文本
    text_segments = [seg['paragraph_text'] for seg in segments]
    
    # 准备输出的 JSON 数据
    output_segments = []
    
    model_id = get_model_id(language)
    voice_id = ELEVENLABS_VOICE_MAPPING.get(language)
    logger.info(f"使用映射中的 voice_id: {voice_id}")
    
    # 确保我们有一个有效的 API key
    api_key = ELEVENLABS_API_KEY
    if not api_key:
        logger.error("未设置 ELEVENLABS_API_KEY 环境变量")
        raise RuntimeError("ElevenLabs API 密钥未设置。请设置 ELEVENLABS_API_KEY 环境变量。")
    
    # 为每个段落创建新的客户端，确保 API key 正确设置
    client = ElevenLabs(api_key=api_key, timeout=60)
    
    # 存储上一次请求的ID，用于连贯性
    previous_request_ids = []
    request_ids = []
    
    # 第一次迭代：生成音频并获取请求ID
    for idx, segment in enumerate(segments):
        # 生成带语言代码的音频文件名
        audio_filename = f"{theme}_segment_{idx+1}_{lang_code}.wav"
        audio_path = os.path.join(audio_dir, audio_filename)
        timestamp_filename = f"{theme}_segment_{idx+1}_{lang_code}_timestamps.json"
        timestamp_path = os.path.join(timestamps_dir, timestamp_filename)
        
        # 获取前一段和后一段的文本（如果存在）
        previous_text = segments[idx-1]['paragraph_text'] if idx > 0 else None
        next_text = segments[idx+1]['paragraph_text'] if idx < len(segments)-1 else None
        
        # 获取前一次和后一次请求的ID（如果存在）
        prev_ids = previous_request_ids[-3:] if previous_request_ids else None
        next_ids = None  # 第一次迭代中，我们还不知道下一次请求的ID
        
        # 检查缓存
        text_hash = hashlib.sha256(segment['paragraph_text'].encode('utf-8')).hexdigest()
        cache_file = os.path.join(AUDIO_CACHE_DIR, f"{voice_id}_{text_hash}.wav")
        
        if os.path.exists(cache_file):
            # 缓存命中：复制缓存的WAV到正确的输出位置
            shutil.copy(cache_file, audio_path)
            logger.info(f"使用缓存的音频用于段落 {idx+1} (ID: {segment['segment_id']})")
            success = True
        else:
            success = None  # 将在合成后设置
        
        if success is None:
            # 合成音频
            success, alignment, request_id = synthesize_segment(
                segment['paragraph_text'],
                audio_path,
                voice_id,
                language,
                model_id,
                client,
                previous_text=previous_text,
                next_text=next_text,
                previous_request_ids=prev_ids,
                next_request_ids=next_ids
            )
            
            # 首次成功合成后，存储一份到缓存
            if success and not os.path.exists(cache_file):
                try:
                    shutil.copy(audio_path, cache_file)
                except Exception as e:
                    logger.warning(f"无法写入缓存文件 {cache_file}: {e}")
        
        if success:
            # 保存请求ID用于后续段落
            if request_id:
                request_ids.append(request_id)
                previous_request_ids.append(request_id)
            
            # 保存时间戳数据
            if alignment:
                save_timestamp_file(alignment, timestamp_path)
            
            # 获取音频时长
            try:
                audio = AudioSegment.from_wav(audio_path)
                duration = len(audio) / 1000.0  # 转换为秒
            except Exception as e:
                logger.error(f"获取段落 {idx+1} 的音频时长失败: {e}")
                duration = 0
                
            # 更新段落信息 - 只保留必要字段
            segment_data = {
                "segment_id": segment["segment_id"],
                "paragraph_text": segment["paragraph_text"],
                "audio": {
                    "path": audio_path,
                    "duration": duration
                }
            }
            output_segments.append(segment_data)
        else:
            logger.error(f"段落 {idx+1} 的音频合成失败")
            # 音频合成失败立即终止处理
            raise RuntimeError(f"段落 {idx+1} 的音频合成失败。停止处理。")
    
    # 保存更新后的 JSON
    output_json = json_file.replace('.json', '_audio.json')
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(output_segments, f, ensure_ascii=False, indent=2)
    
    logger.info(f"处理完成. 输出 JSON 已保存到 {output_json}")
    return output_json

def synthesize_segment(text, output_file, voice_id, language, model_id, client, 
                       previous_text=None, next_text=None, 
                       previous_request_ids=None, next_request_ids=None):
    """使用 ElevenLabs API 合成段落音频，并获取时间戳信息"""
    # 这里不再使用NLP处理和引号移除
    # 直接使用原始文本
    
    # 获取段落索引
    segment_idx = int(output_file.split('_')[-2])  # 假设文件名格式：theme_segment_N_lang.wav
    
    # 添加重试机制
    retry_count = 0
    success = False
    alignment_data = None
    request_id = None
    
    while retry_count < MAX_RETRY_COUNT and not success:
        try:
            # 准备API调用参数
            api_params = {
                "voice_id": voice_id,
                "text": text,
                "model_id": model_id,
                "output_format": AUDIO_OUTPUT_FORMAT  # 使用预定义的输出格式
            }
            
            # 添加连贯性参数（如果提供）
            if previous_text:
                api_params["previous_text"] = previous_text
            if next_text:
                api_params["next_text"] = next_text
            if previous_request_ids:
                # 最多允许3个请求ID
                api_params["previous_request_ids"] = previous_request_ids[-3:] 
            if next_request_ids:
                # 最多允许3个请求ID
                api_params["next_request_ids"] = next_request_ids[-3:]
            
            # 使用 ElevenLabs API 合成带时间戳的音频
            response = client.text_to_speech.convert_with_timestamps(**api_params)
            
            # 尝试获取请求ID（如果可用）
            request_id = getattr(response, 'request_id', None)
            if request_id:
                logger.debug(f"获取到请求ID: {request_id}")
            
            # 检查响应属性，通过dir(response)查看可用属性
            logger.debug(f"响应对象属性: {dir(response)}")
            
            # 将音频数据转换为WAV文件
            if hasattr(response, 'audio'):
                # 新版本API可能使用audio属性
                audio_data = response.audio
            elif hasattr(response, 'audio_base_64'):
                # 新版本API使用audio_base_64属性（注意下划线）
                audio_data = base64.b64decode(response.audio_base_64)
            elif hasattr(response, 'audio_base64'):
                # 旧版本API可能使用audio_base64属性
                audio_data = base64.b64decode(response.audio_base64)
            else:
                # 尝试通过._content获取原始二进制响应
                audio_data = response._content if hasattr(response, '_content') else None
                
                if not audio_data:
                    # 如果找不到音频数据，记录错误
                    logger.error("无法从API响应中获取音频数据")
                    raise ValueError("响应中不包含音频数据")
            
            # 使用临时文件先保存为MP3格式
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_path = temp_file.name
                temp_file.write(audio_data)
            
            # 读取临时MP3文件，然后转换为WAV
            try:
                logger.debug(f"尝试加载MP3格式: {temp_path}")
                audio = AudioSegment.from_mp3(temp_path)
            except Exception as e:
                logger.warning(f"MP3加载失败，尝试其他格式: {e}")
                try:
                    # 尝试作为未知格式加载
                    logger.debug("尝试加载文件作为未知格式")
                    audio = AudioSegment.from_file(temp_path)
                except Exception as e2:
                    logger.error(f"无法作为任何格式加载音频: {e2}")
                    raise ValueError(f"音频格式无法识别: {e2}")
            
            # 只为非第一段添加延迟
            delay_time = int(VOICE_DELAY_TIME) if isinstance(VOICE_DELAY_TIME, str) else VOICE_DELAY_TIME
            if segment_idx > 1:
                silence = AudioSegment.silent(duration=delay_time)
                audio = silence + audio
            
            # 保存最终音频为WAV格式
            audio.export(output_file, format="wav")
            logger.debug(f"音频成功导出为WAV格式: {output_file}")
            
            # 保存对齐数据，检查可能的属性名
            alignment_data = {}
            
            # 处理时间戳信息（如果存在）
            if hasattr(response, 'alignment'):
                alignment_data["alignment"] = response.alignment.dict() if hasattr(response.alignment, 'dict') else response.alignment
            
            if hasattr(response, 'normalized_alignment'):
                alignment_data["normalized_alignment"] = response.normalized_alignment.dict() if hasattr(response.normalized_alignment, 'dict') else response.normalized_alignment
            
            # 清理临时文件
            os.unlink(temp_path)
            
            success = True
            logger.info(f"段落音频合成成功: {output_file}")
            
        except Exception as e:
            logger.error(f"音频合成错误: {e}")
            retry_count += 1
            
            if retry_count < MAX_RETRY_COUNT:
                wait_time = calculate_backoff_time(retry_count)
                logger.warning(f"重试音频合成 {retry_count}/{MAX_RETRY_COUNT}，等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
            
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.unlink(temp_path)
    
    return success, alignment_data, request_id

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="使用 ElevenLabs API 进行文本到语音合成")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--json", type=str, help="输入 JSON 文件路径")
    group.add_argument("--text", type=str, help="输入文本文件路径")
    parser.add_argument("--theme", type=str, required=True, help="用于音频目录的主题名称")
    parser.add_argument("--lang", type=str, default="Chinese", 
                       help="TTS 使用的语言 (默认: Chinese, 选项: English, English-Story, Japanese, Chinese, Spanish, Portuguese)")
    parser.add_argument("--api-key", type=str, help="ElevenLabs API 密钥，如不提供则使用环境变量 ELEVENLABS_API_KEY")
    args = parser.parse_args()
    
    # 如果命令行提供了 API key，则使用它覆盖环境变量
    if args.api_key:
        ELEVENLABS_API_KEY = args.api_key
    else:
        # 检查是否设置了 API key
        ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
    
    if not ELEVENLABS_API_KEY:
        logger.error("未设置 ELEVENLABS_API_KEY。请设置环境变量或使用 --api-key 参数提供")
        exit(1)
    
    eleven_client = ElevenLabs(api_key=ELEVENLABS_API_KEY, timeout=60)

    # 获取语言代码
    lang_code = get_language_code(args.lang)
    
    # 构建音频输出目录路径
    base_audio_dir = AUDIO_DIR.format(theme=args.theme)
    
    # 根据语言决定是否添加语言代码后缀
    theme_audio_dir = (
        base_audio_dir if args.lang.lower() == 'english' 
        else f"{base_audio_dir}_{lang_code}"
    )
    os.makedirs(theme_audio_dir, exist_ok=True)

    # 如果输入是文本文件，先处理成JSON格式
    if args.text:
        # 处理文本文件生成段落
        paragraphs = process_script(args.text)
        
        # 转换为所需的JSON格式 - 只包含必要字段
        segments = []
        for para in paragraphs:
            segment = {
                "segment_id": para["segment_id"],
                "paragraph_text": para["paragraph_text"]
            }
            segments.append(segment)
        
        # 保存为临时JSON文件
        base_name = os.path.splitext(os.path.basename(args.text))[0]
        text_json = os.path.join(os.path.dirname(args.text), f"{base_name}.json")
        with open(text_json, 'w', encoding='utf-8') as f:
            json.dump(segments, f, ensure_ascii=False, indent=2)
        
        input_json = text_json
    else:
        input_json = args.json

    # 处理段落并生成音频
    output_json = process_segments(input_json, theme_audio_dir, args.theme, args.lang)
    logger.info(f"处理完成. 结果已保存到 {output_json}")