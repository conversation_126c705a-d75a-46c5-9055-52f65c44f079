import argparse
import subprocess
import os
import sys
import json
import tempfile
from config import logger

def get_video_info(video_path):
    """Get video information using ffprobe."""
    cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', video_path]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    return json.loads(result.stdout)

def remove_background_and_overlay(image_video_path: str, voice_video_path: str, output_video_path: str):
    """
    Remove green screen background from voice video, keep the subject fully opaque,
    overlay it onto the main video, and use the audio from the voice video.
    """
    logger.info(f"Starting advanced processing: image_video={image_video_path}, voice_video={voice_video_path}")

    # Get video info
    main_info = get_video_info(image_video_path)
    voice_info = get_video_info(voice_video_path)

    # Get video dimensions
    main_height = int(main_info['streams'][0]['height'])
    voice_height = int(voice_info['streams'][0]['height'])

    # Calculate scale factor to make voice video height 1/3 of main video height
    scale_factor = (main_height / 3) / voice_height

    # Green screen removal and overlay with pixel format adjustment
    filter_complex = (
        f"[1:v]scale=iw*{scale_factor}:ih*{scale_factor}[scaled];"
        f"[scaled]chromakey=0x00b140:similarity=0.2:blend=0.0[transparent];"
        f"[transparent]despill=green[despill];"
        f"[0:v][despill]overlay=W-w-10:H-h-0,format=yuv420p[v]"
    )

    # Check for CUDA support
    cuda_supported = True
    try:
        subprocess.run(['ffmpeg', '-hwaccel', 'cuda', '-i', 'dummy.mp4', '-f', 'null', '-'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except subprocess.CalledProcessError:
        cuda_supported = False

    # Build FFmpeg command
    ffmpeg_command = [
        'ffmpeg',
        '-loglevel', 'error',  # Suppress ffmpeg output
        '-i', image_video_path,
        '-i', voice_video_path,
        '-filter_complex', filter_complex,
        '-map', '[v]',
        '-map', '1:a',
        '-c:v', 'h264_nvenc' if cuda_supported else 'libx264',  # Use NVIDIA's NVENC for GPU acceleration if available
        '-preset', 'medium',
        '-crf', '23',
        '-c:a', 'aac',
        '-b:a', '192k',
        '-y',
        output_video_path
    ]

    if cuda_supported:
        ffmpeg_command.insert(2, '-hwaccel')
        ffmpeg_command.insert(3, 'cuda')

    try:
        subprocess.run(ffmpeg_command, check=True)
        logger.info(f"Successfully created {output_video_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error occurred while processing video: {e}")
        sys.exit(1)

def concatenate_videos(video_files: list, output_path: str):
    """
    Concatenates multiple video files into one using the concat demuxer method.
    This method is fast and avoids re-encoding when the input videos have the same codec.
    """
    logger.info(f"Starting fast concatenation process for {len(video_files)} videos using concat demuxer")
    
    # 打印所有输入文件的路径
    for i, video in enumerate(video_files):
        logger.debug(f"Input video {i+1}: {video}")
    
    # 创建临时文件来存储视频文件列表
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False) as temp_file:
        for video in video_files:
            # 使用 shell 转义处理路径
            escaped_path = os.path.abspath(video).replace("'", "'\\''")
            temp_file.write(f"file '{escaped_path}'\n")
        temp_file_path = temp_file.name

    # 构建 FFmpeg 命令
    ffmpeg_command = [
       'ffmpeg',
       '-y',
       '-f', 'concat',
       '-safe', '0',
       '-i', temp_file_path,
       '-c', 'copy',
       output_path
   ]
    try:
        # 执行 FFmpeg 命令
        subprocess.run(ffmpeg_command, check=True, stderr=subprocess.PIPE, universal_newlines=True)
        logger.info(f"Successfully created final video: {output_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error occurred while concatenating videos: {e}")
        logger.error(f"FFmpeg error output: {e.stderr}")
        sys.exit(1)
    finally:
        # 删除临时文件
        os.unlink(temp_file_path)

def concatenate_videos_encode(video_files: list, output_path: str):
    """
    Concatenates multiple video files into one.
    """
    logger.info(f"Starting concatenation process for {len(video_files)} videos")
    with open('temp_file_list.txt', 'w') as f:
        for video in video_files:
            f.write(f"file '{video}'\n")
    
    ffmpeg_command = [
        'ffmpeg',
        '-loglevel', 'error',  # Suppress ffmpeg output
        '-f', 'concat',
        '-safe', '0',
        '-i', 'temp_file_list.txt',
        '-c:v', 'libx264',  # Re-encode video
        '-c:a', 'aac',      # Re-encode audio
        '-strict', 'experimental',
        '-y',
        output_path
    ]
    try:
        subprocess.run(ffmpeg_command, check=True)
        logger.info(f"Successfully created final video: {output_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error occurred while concatenating videos: {e}")
        sys.exit(1)
    finally:
        if os.path.exists('temp_file_list.txt'):
            os.remove('temp_file_list.txt')

def overlay_audio(args):
    base_name = os.path.splitext(args.image_video)[0]
    
    # Check if the base_name contains '_image'
    if '_image' in base_name:
        # Replace '_image' with '_draft'
        base_name = base_name.replace('_image', '_draft')
    else:
        # If '_image' is not in the name, just add '_draft'
        base_name += '_draft'
    
    output_path = f"{base_name}.mp4"
    remove_background_and_overlay(args.image_video, args.voice_video, output_path)
    logger.info(f"Overlay video saved as: {output_path}")

def concat_videos(args):
    # 初始化视频文件列表
    video_files = [args.video]
    
    # 如果提供了 outro_video，则添加到列表中
    if args.outro_video:
        video_files.append(args.outro_video)
    
    # 检查文件是否存在
    for file in video_files:
        if not os.path.exists(file):
            logger.error(f"File not found: {file}")
            sys.exit(1)
    
    # Get the base name of the main video
    base_name = os.path.splitext(args.video)[0]
    
    # Check if the base_name contains '_sub'
    if '_sub' in base_name:
        # Replace '_sub' with '_final'
        output_base = base_name.replace('_sub', '_final')
    else:
        # If '_sub' is not in the name, just add '_final'
        output_base = f"{base_name}_final"
    
    output_path = f"{output_base}.mp4"
    concatenate_videos(video_files, output_path)

def main():
    parser = argparse.ArgumentParser(description="Video processing tool")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Overlay audio command
    overlay_parser = subparsers.add_parser("overlay", help="Overlay audio video onto main video")
    overlay_parser.add_argument("image_video", help="Path to the main video")
    overlay_parser.add_argument("voice_video", help="Path to the voice video")

    # Concat command
    concat_parser = subparsers.add_parser("concat", help="Concatenate videos")
    concat_parser.add_argument("--video", required=True, help="Path to the main video")
    concat_parser.add_argument("--outro_video", help="Path to the outro video (optional)")

    args = parser.parse_args()

    if args.command == "overlay":
        overlay_audio(args)
    elif args.command == "concat":
        concat_videos(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
