import os
import sys
import json
import argparse
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.langchain_interface import call_llm_json_response
from config import logger, LANGUAGE_CODES, get_param
from modules.gpt_parameters import MODEL_KEY_GEMINI_25_PRO, LLM_PROVIDER_GOOGLE

def generate_metadata(script_content: str, language: str = "en") -> dict:
    """生成视频元数据
    
    Args:
        script_content: 脚本内容
        language: 目标语言代码
        
    Returns:
        Dict: 包含标题、描述等元数据的字典
    """
    try:
        data = {
            "script": script_content,
            "LANGUAGE": language
        }
        
        expected_fields = ["title", "description", "tags"]
        metadata = call_llm_json_response(
            api_function="gen-metadata",
            prompt_data=data,
            max_retries=5,
            llm_type=LLM_PROVIDER_GOOGLE,
            model_key=MODEL_KEY_GEMINI_25_PRO,
            using_cache=True,
            expected_fields=expected_fields
        )
        
        return metadata
        
    except Exception as e:
        logger.error(f"生成元数据时出错: {e}")
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate metadata for a text file.")
    parser.add_argument('input_file', help="Path to the input text file.")
    parser.add_argument('--lang', default='English', help="Language for the generated metadata.")
    args = parser.parse_args()

    input_file_path = args.input_file
    language = args.lang
    
    if not os.path.isfile(input_file_path):
        print(f"The file {input_file_path} does not exist.")
        sys.exit(1)
    
    with open(input_file_path, 'r') as file:
        script_content = file.read()
    
    metadata = generate_metadata(script_content, language)
    title = metadata.get("title", "")
    description = metadata.get("description", "")
    tags = ", ".join(metadata.get("tags", []))

    # 获取语言缩写
    lang_code = LANGUAGE_CODES.get(language, "en")  # 默认使用 "en"

    # 构造输出文件路径
    output_file_path = f"{os.path.splitext(input_file_path)[0]}_metadata.txt"

    # Format the output content
    output_content = f"Title:\n{title}\n\nDescription:\n{description}\n\nTags:\n{tags}"

    # Save the metadata to the output file
    with open(output_file_path, 'w') as output_file:
        output_file.write(output_content)

    logger.info(f"Metadata generated and saved to {output_file_path}")