# images2video.py
import subprocess
import os
import tempfile
import logging
import time
import sys
import shutil
from typing import List, Tuple, Optional

from config import (
    VIDEO_RESOLUTION, VIDEO_BITRATE, VIDEO_FPS, VIDEO_PRESET, 
    VIDEO_CRF, VIDEO_CODEC, VIDEO_AUDIO_CODEC
)

logger = logging.getLogger(__name__)

# DepthFlow 运动预设列表
DEPTHFLOW_MOTION_PRESETS = [
    "circle",
    "dolly", 
    "horizontal", 
    "vertical", 
    "zoom"
]

# 默认使用的 DepthFlow 预设
DEFAULT_DEPTHFLOW_MOTION = "zoom"

def run_depthflow(
    input_image: str, 
    output_video: str, 
    motion: str = DEFAULT_DEPTHFLOW_MOTION,
    duration: float = 5.0,
    fps: int = VIDEO_FPS,
    quality: int = 45,  # 降低到合理范围 (30-50)
    resolution: tuple = VIDEO_RESOLUTION  # 添加分辨率参数
) -> bool:
    """运行 DepthFlow 生成视频
    
    Args:
        input_image: 输入图像路径
        output_video: 输出视频路径
        motion: DepthFlow 运动预设名称
        duration: 视频时长（秒）
        fps: 帧率
        quality: 视频质量 (1-100)，建议值30-50
        resolution: 视频分辨率
        
    Returns:
        bool: 是否成功生成视频
    """
    tmp_video = None
    try:
        if motion not in DEPTHFLOW_MOTION_PRESETS:
            logger.warning(f"无效的 DepthFlow 运动预设: {motion}, 使用默认预设: {DEFAULT_DEPTHFLOW_MOTION}")
            motion = DEFAULT_DEPTHFLOW_MOTION
            
        # 使用 VIDEO_RESOLUTION 配置
        width, height = resolution
        
        # 创建临时输出文件，避免直接写入最终文件
        tmp_video_fd, tmp_video = tempfile.mkstemp(suffix='.mp4', prefix='depthflow_tmp_')
        os.close(tmp_video_fd)
            
        # 按照优化后的参数构建命令
        cmd = [
            "depthflow", "input",
            "-i", input_image,
            "depthpro",
            "depthstate",
            "--height", "0.15",
            "--steady", "0.3",
            "--isometric", "0.8",
        ]
        
        # 根据不同motion添加特定参数
        if motion == "zoom":
            # 对于zoom运动
            cmd.extend([motion, "--smooth", "--intensity", "0.75"])
        else:
            # 对于其他运动类型，可以添加适当的缩放参数
            if motion == "dolly":
                # dolly + zoom组合效果更好
                cmd.extend([motion, "--smooth", "--intensity", "0.75"])
            else:
                # 其他类型使用默认参数
                cmd.extend([motion, "--smooth", "--intensity", "0.75"])
        
        # 完成命令行参数
        cmd.extend([
            "main",
            "-o", tmp_video,
            "--width", str(width),
            "--height", str(height),
            "--time", str(duration),
            "--fps", str(fps),
            "--quality", str(quality),
            "--ssaa", "2"
        ])
        
        logger.info(f"运行 DepthFlow 生成视频: {' '.join(cmd)}")
        start_time = time.time()
        
        # 捕获并记录depthflow的输出，以便调试
        process = subprocess.run(cmd, capture_output=True, text=True, check=False)
        process_time = time.time() - start_time

        # 始终记录 stdout 和 stderr
        if process.stdout:
            logger.info(f"DepthFlow 标准输出:\n{process.stdout}")
        if process.stderr:
            logger.warning(f"DepthFlow 标准错误:\n{process.stderr}")

        if process.returncode != 0:
            logger.error(f"DepthFlow执行失败，返回码: {process.returncode}")
            return False
        
        # 检查临时文件是否存在且有效
        if not os.path.exists(tmp_video) or os.path.getsize(tmp_video) < 10000:  # 文件至少应大于10KB
            logger.error(f"DepthFlow生成的视频文件无效或过小: {tmp_video} (大小: {os.path.getsize(tmp_video) if os.path.exists(tmp_video) else '不存在'})")
            return False
            
        # 验证视频文件的有效性
        probe_cmd = ["ffprobe", "-v", "error", tmp_video]
        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True)
        if probe_result.returncode != 0:
            logger.error(f"无效的视频文件: {tmp_video}, ffprobe返回: {probe_result.stderr}")
            return False
        
        # 使用FFmpeg重新编码视频，确保生成有效的MP4文件
        logger.info(f"重新编码视频确保格式兼容: {tmp_video} -> {output_video}")
        transcode_cmd = [
            "ffmpeg", "-y",
            "-i", tmp_video,
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "22",
            "-pix_fmt", "yuv420p",  # 确保兼容性
            output_video
        ]
        
        transcode_result = subprocess.run(transcode_cmd, capture_output=True, text=True)
        if transcode_result.returncode != 0:
            logger.error(f"视频转码失败: {transcode_result.stderr}")
            return False
        
        # 验证输出
        if os.path.exists(output_video):
            file_size = os.path.getsize(output_video)
            logger.info(f"视频生成完成: {output_video} (大小: {file_size/1024/1024:.2f}MB)")
            
            # 再次验证输出文件
            final_probe = subprocess.run(["ffprobe", "-v", "error", output_video], capture_output=True, text=True)
            if final_probe.returncode != 0:
                logger.error(f"最终视频文件无效: {final_probe.stderr}")
                return False
                
            return True
        else:
            logger.error(f"视频未生成: {output_video}")
            return False
            
    except Exception as e:
        logger.error(f"生成视频失败: {str(e)}")
        return False
    finally:
        # 清理临时文件
        if tmp_video and os.path.exists(tmp_video):
            try:
                os.remove(tmp_video)
                logger.info(f"临时视频文件已删除: {tmp_video}")
            except Exception as e:
                logger.warning(f"删除临时文件失败: {tmp_video}, 原因: {str(e)}")

def select_motion_by_index(image_index: int) -> str:
    """根据图片索引选择 DepthFlow 运动预设
    
    Args:
        image_index: 图片索引
        
    Returns:
        str: DepthFlow 运动预设名称
    """
    preset_index = image_index % len(DEPTHFLOW_MOTION_PRESETS)
    return DEPTHFLOW_MOTION_PRESETS[preset_index]

def generate_video_from_image_with_depthflow(
    image_path: str,
    output_path: str,
    duration: float,
    fps: int = VIDEO_FPS,
    resolution: tuple = VIDEO_RESOLUTION,
    audio_path: str = None,
    motion: str = None,
    image_index: int = 0,
    preprocess_image: bool = False
) -> str:
    """使用 DepthFlow 从单个图片和音频生成视频片段
    
    Args:
        image_path: 输入图像路径
        output_path: 输出视频路径
        duration: 视频时长（秒）
        fps: 帧率
        resolution: 分辨率 (宽, 高)
        audio_path: 音频路径（可选）
        motion: DepthFlow 运动预设名称（如果为 None，则根据 image_index 选择）
        image_index: 图片索引（用于在未指定 motion 时选择预设）
        preprocess_image: 是否预处理图片（默认为True）
        
    Returns:
        str: 输出视频路径
    """
    try:
        # 验证输入文件
        if not os.path.isfile(image_path):
            logger.error(f"图片文件不存在: {image_path}")
            raise FileNotFoundError(f"找不到图片文件：{image_path}")
        
        if audio_path and not os.path.isfile(audio_path):
            logger.error(f"音频文件不存在: {audio_path}")
            raise FileNotFoundError(f"找不到音频文件：{audio_path}")
        
        # 如果未指定运动预设，根据图片索引选择
        if motion is None:
            motion = select_motion_by_index(image_index)
        
        try:
            # 准备输入图片和临时视频路径
            width, height = resolution
            temp_video_fd, temp_video = tempfile.mkstemp(suffix='.mp4', prefix='temp_video_')
            os.close(temp_video_fd)  # 关闭文件描述符
            
            # 确定实际使用的图片路径
            input_for_depthflow = image_path
            temp_image = None
            
            # 如果需要预处理图片
            if preprocess_image:
                # 创建临时图片文件
                temp_image_fd, temp_image = tempfile.mkstemp(suffix='.jpg', prefix='processed_image_')
                os.close(temp_image_fd)  # 关闭文件描述符
                
                # 保持原有的预处理滤镜链
                preprocess_filter = (
                    "split[original][for_bg];"
                    f"[for_bg]scale={width*2}:{height*2}:force_original_aspect_ratio=increase,"
                    f"crop={width}:{height},"
                    f"gblur=sigma=20[bg];"
                    f"[original]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                    f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2:color=black@0[fg];"
                    "[bg][fg]overlay=x=(W-w)/2:y=(H-h)/2"
                )
                
                # 预处理图片
                preprocess_command = [
                    'ffmpeg', '-y',
                    '-i', image_path,
                    '-vf', preprocess_filter,
                    '-q:v', '2',
                    '-frames:v', '1',
                    '-update', '1',
                    temp_image
                ]
                
                logger.info(f"预处理图片: {image_path} -> {temp_image}")
                subprocess.run(preprocess_command, check=True, capture_output=True, text=True)
                
                # 使用预处理后的图片
                input_for_depthflow = temp_image
            else:
                logger.info(f"跳过图片预处理，直接使用原始图片: {image_path}")
            
            # 2. 使用 DepthFlow 直接生成视频
            if not run_depthflow(input_for_depthflow, temp_video, motion, duration, fps):
                raise RuntimeError(f"生成视频失败: {temp_video}")
                
            # 3. 如果有音频，合并音频
            if audio_path:
                # 合并音频，使用配置的音频编码器
                merge_command = [
                    'ffmpeg', '-y',
                    '-hide_banner',
                    '-loglevel', 'error',
                    '-stats',
                    '-stats_period', '10',
                    '-i', temp_video,
                    '-i', audio_path,
                    '-c:v', 'copy',
                    '-c:a', VIDEO_AUDIO_CODEC,
                    '-b:v', VIDEO_BITRATE,
                    '-strict', 'experimental',
                    '-shortest',
                    output_path
                ]
                logger.info("开始合并音频...")
                merge_start_time = time.time()
                subprocess.run(merge_command, check=True)
                logger.info(f"音频合并完成，耗时: {time.time() - merge_start_time:.2f}秒")
            else:
                # 如果没有音频，直接复制临时视频
                shutil.copy(temp_video, output_path)
                logger.info(f"无声视频生成完成")
            
            # 验证输出文件
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"输出文件: {output_path} (大小: {file_size/1024/1024:.2f}MB)")
            else:
                logger.error(f"输出文件未生成: {output_path}")
            
            return output_path
            
        finally:
            # 确保临时文件被清理
            tmp_files = [temp_video]
            if temp_image:  # 只有在预处理图片时才添加临时图片文件
                tmp_files.append(temp_image)
                
            for tmp_file in tmp_files:
                if os.path.exists(tmp_file):
                    try:
                        os.remove(tmp_file)
                        logger.info(f"临时文件已删除: {tmp_file}")
                    except Exception as e:
                        logger.warning(f"删除临时文件失败: {tmp_file}, 原因: {str(e)}")
            
    except Exception as e:
        logger.error(f"使用 DepthFlow 生成视频片段时出错: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        if hasattr(e, '__traceback__'):
            import traceback
            logger.error("错误堆栈:\n" + "".join(traceback.format_tb(e.__traceback__)))
        raise
        
# 保持与原始 image2clip.py 函数名称一致，方便集成

if __name__ == "__main__":
    # 示例用法
    image_path = "example.jpg"
    output_path = "output.mp4"
    duration = 10.0
    
    try:
        result = generate_video_from_image_with_depthflow(
            image_path=image_path,
            output_path=output_path,
            duration=duration,
            motion="zoom"
        )
        print(f"视频生成成功：{result}")
    except Exception as e:
        print(f"视频生成失败：{str(e)}")
