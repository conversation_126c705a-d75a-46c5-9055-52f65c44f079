import random
import os
import json
import logging
import argparse
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from config import TEMP_FILE_DIR, logger, LANGUAGE_CODES, LANGUAGE_FONT_MAP
from modules.langchain_interface import call_llm_json_response
from config import VIDEO_RESOLUTION
# Constants
COVER_CANDIDATES = 30
FONT_PATH = "/home/<USER>/.fonts/Anton-Regular.ttf"
FONT_SIZE = 60
FONT_COLOR = (255, 255, 255)  # White color
COVERS_DIR_NAME = "covers"  # Name of the covers directory

# Subtitle positions
SUBTITLE_POSITIONS = [
    {"x": "center", "y": "bottom"},  # Bottom center
    {"x": "center", "y": "top"},     # Top center
]

def preprocess_response(response):
    if response.startswith("```json") and response.endswith("```"):
        response = response[7:-3].strip()
    return response

def generate_cover_summary(full_text, language="English"):
    try:
        data = {
            'full_text': full_text,
            'LANGUAGE': language
        }
        # 修改前:
        # response = call_gpt4('generate-slide-cover', data)
        # return parse_json_response(response, "generate-slide-cover")
        
        # 修改后:
        return call_llm_json_response(
            api_function='generate-slide-cover',
            prompt_data=data,
            expected_fields=['front_cover', 'back_cover']
        )
    except Exception as e:
        logger.error(f"Error generating cover summary: {e}")
        return None

def resize_and_blur_image(image_path, resolution):
    try:
        with Image.open(image_path) as image:
            logger.debug(f"Processing image: {image_path}, mode: {image.mode}")
            logger.debug(f"Original image size: {image.size}")
            
            # 强制转换为 RGBA 模式（包含所有输入类型）
            image = image.convert('RGBA')  # 修改点1：移除条件判断，强制转换
            
            scale = min(resolution[0] / image.width, resolution[1] / image.height)
            new_size = (int(image.width * scale), int(image.height * scale))
            
            resized_image = image.resize(new_size, Image.Resampling.LANCZOS)
            logger.debug(f"Resized image size: {resized_image.size}")
            
            # 确保背景图像模式正确
            background = resized_image.copy().resize(resolution, Image.Resampling.LANCZOS)
            background = background.convert('RGBA')  # 修改点2：显式转换
            background = background.filter(ImageFilter.GaussianBlur(20))
            
            # 确保前景图像模式正确
            foreground = Image.new('RGBA', resolution, (0, 0, 0, 0))
            paste_position = ((resolution[0] - resized_image.width) // 2, (resolution[1] - resized_image.height) // 2)
            foreground.paste(resized_image, paste_position)
            
            return foreground, background, resized_image.size
    except Exception as e:
        logger.error(f"Error processing image {image_path}: {e}")
        raise

def add_text_background(draw, text_x, text_y, text_w, text_h, opacity=179):  # 70% 不透明度对应的值是 179
    background_color = (0, 0, 0, opacity)  # 黑色背景，70% 不透明度
    background = Image.new('RGBA', (text_w, text_h), background_color)
    return background, (text_x, text_y)

def ensure_directory(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)

def choose_subtitle_position():
    return random.choice(SUBTITLE_POSITIONS)

def select_cover_image(image_dir, language):
    # 获取目录中所有图片文件
    images = []
    for img in os.listdir(image_dir):
        if img.lower().endswith(('.png', '.jpg', '.jpeg')):
            full_path = os.path.join(image_dir, img)
            if os.path.isfile(full_path):
                images.append(full_path)
    if not images:
        logger.error(f"在 {image_dir} 中未找到有效的图像文件")
        return None

    # 修改后的筛选逻辑
    language_suffix = f"_{language.lower()}"  # 匹配类似 _es 的后缀
    language_images = []
    for img in images:
        base_name = os.path.splitext(os.path.basename(img))[0].lower()
        
        # 优先匹配完整语言代码结尾的文件名
        if base_name.endswith(language_suffix):
            language_images.append(img)
            logger.debug(f"发现符合语言后缀 {language_suffix} 的图片: {img}")

    # 如果没有找到带后缀的，再尝试完全匹配
    if not language_images:
        logger.debug(f"没有找到带 {language_suffix} 后缀的图片，尝试完全匹配")
        for img in images:
            base_name = os.path.splitext(os.path.basename(img))[0].lower()
            if base_name == language.lower():
                language_images.append(img)

    if language_images:
        selected = random.choice(language_images)
        logger.info(f"选择了符合语言 {language} 的图片（匹配模式：{language_suffix}）: {selected}")
        return selected
    else:
        selected = random.choice(images)
        logger.info(f"没有找到符合语言 {language} 的图片，随机选择: {selected}")
        return selected

def save_cover_image_with_optional_text(image_path, tagline, resolution, output_path, language, padding=60):
    try:
        foreground, background, _ = resize_and_blur_image(image_path, resolution)
        
        # 添加模式验证和强制转换
        logger.debug(f"Foreground mode: {foreground.mode}, Background mode: {background.mode}")
        if foreground.mode != 'RGBA':
            foreground = foreground.convert('RGBA')
        if background.mode != 'RGBA':
            background = background.convert('RGBA')
            
        if tagline:
            draw = ImageDraw.Draw(foreground)
            # 根据语言选择字体
            font_path_used = LANGUAGE_FONT_MAP.get(language, LANGUAGE_FONT_MAP['English'])
            font = ImageFont.truetype(font_path_used, FONT_SIZE)
            # 计算文本大小
            text_bbox = draw.textbbox((0, 0), tagline, font=font)
            text_w, text_h = text_bbox[2], text_bbox[3]
            text_x = (resolution[0] - text_w) // 2
            text_y = resolution[1] - text_h - padding
            # 添加半透明背景
            text_bg, text_position = add_text_background(draw, text_x, text_y, text_w, text_h)
            foreground.alpha_composite(text_bg, text_position)
            draw.text((text_x, text_y), tagline, font=font, fill=FONT_COLOR)
        
        # 合成前再次强制转换模式
        combined_image = Image.alpha_composite(
            background.convert('RGBA'), 
            foreground.convert('RGBA')
        )
        combined_image = combined_image.convert('RGB')
        
        combined_image.save(output_path, format='JPEG', quality=95, subsampling=0)
        logger.info(f"Cover image saved to {output_path}")
    except Exception as e:
        logger.error(f"Error generating cover image: {e}")
        raise

def main():
    parser = argparse.ArgumentParser(description="Generate cover image with optional tagline.")
    parser.add_argument('--image', required=True, help="Directory containing images.")
    parser.add_argument('--script', required=True, help="Script file (used for output file naming and optionally for generating tagline).")
    parser.add_argument('--lang', default='English', help="Language code (e.g., en, es, ja, pt).")
    parser.add_argument('--tagline', action='store_true', help="Generate tagline using LLM and add to cover image.")
    args = parser.parse_args()

    image_dir = args.image
    script_file = args.script
    # 标准化语言参数
    raw_lang = args.lang
    # 使用 LANGUAGE_CODES 进行标准化转换
    language_code = LANGUAGE_CODES.get(raw_lang, raw_lang)
    # 保留原始语言参数用于 LLM 调用
    language_for_llm = raw_lang if raw_lang in LANGUAGE_CODES.values() else next((k for k, v in LANGUAGE_CODES.items() if v == language_code), raw_lang)
    use_tagline = args.tagline

    # 直接使用脚本文件所在目录，不创建子目录
    script_dir = os.path.dirname(os.path.abspath(script_file))
    ensure_directory(TEMP_FILE_DIR)

    # 选取封面图片时使用标准化后的语言代码
    selected_image = select_cover_image(image_dir, language_code)
    if selected_image is None:
        logger.error("未找到有效的封面图片，程序退出。")
        return

    tagline = None
    # 如果设置了 --tagline，则使用原始语言参数生成 tagline
    if use_tagline:
        try:
            with open(script_file, 'r', encoding='utf-8') as f:
                full_text = f.read()
            summary = generate_cover_summary(full_text, language_for_llm)
            if summary and 'front_cover' in summary and 'sentence' in summary['front_cover']:
                tagline = summary['front_cover']['sentence']
            else:
                logger.error("LLM 未返回有效的 tagline，程序退出。")
                return
        except Exception as e:
            logger.error(f"处理 script 文件或生成 tagline 时出错: {e}")
            return

    # 根据 --script 参数生成输出文件名（取文件名前缀）
    script_prefix = os.path.splitext(os.path.basename(script_file))[0]
    # 直接在脚本目录下生成输出文件，添加语言代码到文件名
    output_image_path = os.path.join(script_dir, f"{script_prefix}_final.jpg")

    save_cover_image_with_optional_text(selected_image, tagline, VIDEO_RESOLUTION, output_image_path, language_for_llm)

if __name__ == "__main__":
    main()