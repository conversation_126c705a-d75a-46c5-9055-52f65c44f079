# generate_chapter_summary.py
import os
import re
import json
import argparse
import sys
import time
import numpy as np
from typing import List, Dict, Any, Optional
from tqdm import tqdm
import tiktoken
import unicodedata

from config import logger, get_param
from modules.nlp_model import LANGUAGE_CODES
from modules.gpt4_interface import call_gpt_json_response
from modules.embedding_management import (
    get_embedding_manager,
    get_embeddings
)
from modules.text_preprocessing import split_text_for_analysis
from modules.utils import read_file_with_encoding, load_alias_map
from modules.key_event_spine import extract_spine
from config import ENABLE_SPINE_PIPELINE

CHAPTER_SUMMARY_MAX_LENGTH = 600

def preprocess_chinese_text(text: str) -> str:
    """预处理中文文本"""
    # 统一全角/半角字符
    text = unicodedata.normalize('NFKC', text)

    # 处理特殊引号
    text = re.sub(r'[「『]', '"', text)
    text = re.sub(r'[」』]', '"', text)

    # 处理破折号和省略号
    text = re.sub(r'[—]{2,}', '——', text)
    text = re.sub(r'[.]{3,}|…{2,}', '……', text)

    # 确保标点符号后有适当的空格
    text = re.sub(r'([。！？…）】」』\)])(?!\s)', r'\1\n', text)

    return text


def get_chunk_context(
    current_idx: int,
    chunks: List[str],
    embeddings: List[np.ndarray],
    window_size: int = 2
) -> Dict[str, Any]:
    """获取当前chunk的顺序上下文信息"""
    manager = get_embedding_manager()
    current_chunk = chunks[current_idx]

    # 获取基于顺序的上下文
    _, context_texts = manager.get_embedding_with_context(
        text=current_chunk,
        sequence_position=current_idx,
        window_size=window_size
    )

    # 简化上下文信息
    context = {
        "context_texts": context_texts  # 添加顺序上下文文本
    }

    return context


def validate_summary_quality(summary: Dict[str, Any]) -> bool:
    """验证摘要质量"""
    try:
        # 1. 验证基本结构
        if not isinstance(summary, dict):
            return False

        chapter_summary = summary.get("chapter_summary")
        if not isinstance(chapter_summary, dict):
            return False

        # 2. 验证narrative部分
        narrative = chapter_summary.get("narrative")
        if not isinstance(narrative, dict):
            return False

        # 检查content不为空且内容有意义
        content = narrative.get("content", "")
        if not content or len(content.strip()) < 10:  # 确保内容至少10个字符
            return False

        # 检查themes不为空且格式正确
        themes = narrative.get("themes", [])
        if not themes or not all(isinstance(t, str) and t.strip() for t in themes):
            return False

        # 3. 验证key_events部分
        events = chapter_summary.get("key_events", [])
        if not isinstance(events, list) or not events:
            return False

        required_event_fields = {"event", "details", "location", "scene_description", "characters"}

        # 更严格的事件验证
        for event in events:
            # 检查是否有空字典或无效事件
            if not event or not isinstance(event, dict):
                return False

            # 检查是否存在空键
            if any(key == "" for key in event.keys()):
                return False

            # 检查字段名称的大小写是否正确
            if not all(field in event for field in required_event_fields):
                return False

            # 确保所有字段值都是正确的类型且不为空（除了scene_description）
            if not all([
                isinstance(event["event"], str) and event["event"].strip(),
                isinstance(event["details"], str) and event["details"].strip(),
                isinstance(event["location"], str) and event["location"].strip(),
                isinstance(event["scene_description"], str),  # 只检查类型，允许空字符串
                isinstance(event["characters"], list) and len(event["characters"]) > 0
            ]):
                return False

        return True

    except Exception:
        return False


def save_chapter_summary_incrementally(summary: Dict[str, Any], output_file: str) -> None:
    """增量保存单个章节摘要到JSON文件

    Args:
        summary: 单个章节摘要
        output_file: 输出文件路径
    """
    try:
        # 读取现有数据
        existing_summaries = []
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_summaries = json.load(f)

        # 检查是否已存在相同章节
        chapter_number = summary['basic_info']['chapter_number']
        chapter_exists = False
        for i, existing_summary in enumerate(existing_summaries):
            if existing_summary['basic_info']['chapter_number'] == chapter_number:
                chapter_exists = True
                break

        # 如果章节不存在，则添加
        if not chapter_exists:
            existing_summaries.append(summary)
            # 按章节号排序
            existing_summaries.sort(key=lambda x: x['basic_info']['chapter_number'])
            # 保存更新后的数据
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(existing_summaries, f, ensure_ascii=False, indent=2)
            logger.info(f"章节 {chapter_number} 已保存到 {output_file}")
        else:
            logger.info(f"章节 {chapter_number} 已存在，跳过保存")

    except Exception as e:
        logger.error(f"保存章节摘要到JSON时出错: {e}")
        raise


def generate_chapter_summaries_json(
    input_file: str,
    output_file: str,
    style: str = 'engaging',
    language: str = 'Chinese',
    window_size: int = 2,
    max_tokens: int = 8000,
    max_retries: int = 5
) -> List[Dict[str, Any]]:
    """生成章节摘要的JSON结构"""
    try:
        # 读取现有的摘要文件
        existing_summaries = []
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_summaries = json.load(f)

        # 修改：使用实际章节序号作为检查依据
        existing_chapter_numbers = {
            summary['basic_info']['chapter_number']
            for summary in existing_summaries
        }

        # 读取和预处理文本
        novel_text = read_file_with_encoding(input_file)
        if language == 'Chinese':
            novel_text = preprocess_chinese_text(novel_text)

        # 分割文本并识别章节
        text_chunks = split_text_for_analysis(novel_text, max_tokens)
        chapter_pattern = re.compile(r'^第[零一二三四五六七八九十百千万\d]+[章节回集卷].*?(?=\n)', re.MULTILINE)

        # 获取EmbeddingManager实例
        embedding_manager = get_embedding_manager()
        chunk_embeddings = embedding_manager.get_batch_embeddings(text_chunks, maintain_sequence=True)

        chapter_summaries = []
        chunk_count = 0
        current_chapter = {
            "basic_info": {
                "chapter_number": 1,  # 初始章节号
                "title": "开篇"
            },
            "narrative": {
                "content": "",
                "themes": []
            },
            "key_events": [],
            "key_characters": []
        }

        for i, chunk in enumerate(tqdm(text_chunks, desc="生成章节摘要")):
            # 更新 chunk 计数
            chunk_count += 1

            # 使用 chunk_count 作为章节号
            current_chapter_number = chunk_count
            if current_chapter_number in existing_chapter_numbers:
                logger.info(f"章节 {current_chapter_number} 已存在，跳过处理")
                continue

            # 获取上下文信息
            context_info = get_chunk_context(i, text_chunks, chunk_embeddings, window_size)

            # 准备数据
            data = {
                "text": chunk,
                "max_length": CHAPTER_SUMMARY_MAX_LENGTH,
                "style": style,
                "language": language,
                "context_info": context_info["context_texts"]
            }

            valid_summary = False
            retry_count = 0
            last_response = None  # 添加变量存储最后一次响应

            while not valid_summary and retry_count < max_retries:
                try:
                    # 第一次尝试使用缓存，后续重试禁用缓存
                    using_cache = (retry_count == 0)

                    logger.debug(f"正在生成第 {i} 块的摘要 (尝试 {retry_count + 1}/{max_retries})")
                    response = call_gpt_json_response(
                        api_function="summarize_chapter",
                        prompt_data=data,
                        using_cache=using_cache
                    )
                    last_response = response  # 保存最后一次响应

                    # 验证响应格式和质量
                    if response and validate_summary_quality(response):
                        valid_summary = True
                        summary = response["chapter_summary"]

                        # 修改：强制使用我们计算的章节号，而不是响应中的章节号
                        if "basic_info" in summary and isinstance(summary["basic_info"], dict):
                            basic_info = summary["basic_info"]
                            # 使用我们的章节号覆盖响应中的章节号
                            basic_info["chapter_number"] = current_chapter_number
                            current_chapter["basic_info"].update(basic_info)

                        # 更新叙事内容
                        if "narrative" in summary and isinstance(summary["narrative"], dict):
                            narrative = summary["narrative"]
                            if all(k in narrative for k in ["content", "themes"]):
                                current_chapter["narrative"].update(narrative)

                        # 更新关键事件
                        if "key_events" in summary and isinstance(summary["key_events"], list):
                            current_chapter["key_events"].extend([
                                event for event in summary["key_events"]
                                if isinstance(event, dict) and all(k in event for k in [
                                    "event", "details", "location", "scene_description", "characters"
                                ])
                            ])

                        # 更新关键角色
                        if "key_characters" in summary and isinstance(summary["key_characters"], list):
                            current_chapter["key_characters"].extend([
                                char for char in summary["key_characters"]
                                if isinstance(char, dict) and all(k in char for k in [
                                    "name", "role", "actions", "development", "interactions"
                                ]) and isinstance(char["actions"], list)
                                and isinstance(char["interactions"], list)
                                and all(isinstance(interaction, dict)
                                       and "with" in interaction
                                       and "description" in interaction
                                       for interaction in char["interactions"])
                            ])
                    else:
                        logger.warning(f"摘要验证失败，重试中 ({retry_count + 1}/{max_retries})")
                        retry_count += 1

                except Exception as e:
                    logger.error(f"生成摘要时发生错误: {e}")
                    retry_count += 1

            # 如果所有重试都失败
            if not valid_summary:
                logger.error(f"在 {max_retries} 次尝试后仍未能生成有效摘要，最后一次结果是: {last_response}")
                continue

            # 在验证通过后，立即保存当前章节
            if valid_summary:
                # 如果启用Spine流水线，添加spine_events
                if ENABLE_SPINE_PIPELINE:
                    try:
                        alias_map = load_alias_map("assets/alias_map.json")
                        spine_events = extract_spine(current_chapter, alias_map)
                        current_chapter["spine_events"] = spine_events
                        logger.info(f"为章节 {current_chapter_number} 添加了 {len(spine_events)} 个spine事件")
                    except Exception as e:
                        logger.error(f"添加spine事件失败: {str(e)}")
                        current_chapter["spine_events"] = []

                if current_chapter["basic_info"]["title"] != "开篇":
                    save_chapter_summary_incrementally(current_chapter, output_file)
                    chapter_summaries.append(current_chapter)

            # 检查是否有新章节标题
            chapter_match = chapter_pattern.search(chunk)
            if chapter_match:
                if current_chapter["basic_info"]["title"] != "开篇":
                    # 使用 chunk_count 作为章节号
                    current_chapter["basic_info"]["chapter_number"] = chunk_count
                    save_chapter_summary_incrementally(current_chapter, output_file)
                    chapter_summaries.append(current_chapter)

                current_chapter = {
                    "basic_info": {
                        "chapter_number": chunk_count,  # 使用 chunk_count
                        "title": chapter_match.group() if chapter_match else f"第{chunk_count}章"
                    },
                    "narrative": {
                        "content": "",
                        "themes": []
                    },
                    "key_events": [],
                    "key_characters": []
                }

        # 处理最后一章
        if current_chapter["basic_info"]["title"] != "开篇":
            current_chapter["basic_info"]["chapter_number"] = chunk_count  # 确保最后一章也使用正确的 chunk_count
            save_chapter_summary_incrementally(current_chapter, output_file)
            chapter_summaries.append(current_chapter)

        return chapter_summaries

    except Exception as e:
        logger.error(f"生成章节摘要JSON时出错: {e}")
        raise

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='生成小说章节摘要')

    parser.add_argument(
        'input_file',
        help='输入小说文本文件的路径'
    )

    parser.add_argument(
        '--output',
        help='输出JSON文件的路径',
        default=None
    )

    parser.add_argument(
        '--window_size',
        type=int,
        default=get_param('WINDOW_SIZE', 2),
        help='上下文窗口大小'
    )

    parser.add_argument(
        '--style',
        default=get_param('SUMMARY_STYLE', 'engaging'),
        help='摘要风格'
    )

    parser.add_argument(
        '--language',
        default=get_param('LANGUAGE', 'Chinese'),
        help='文本语言'
    )

    parser.add_argument(
        '--max_tokens',
        type=int,
        default=get_param('MAX_TOKENS', 8000),
        help='每个块的最大token���'
    )

    return parser.parse_args()

def main():
    """主函数"""
    try:
        args = parse_arguments()

        # 设置输出文件路径：如果没有指定，使用输入文件名相同的前缀 + .json
        output_file = args.output
        if output_file is None:
            output_file = f"{os.path.splitext(args.input_file)[0]}.json"

        # 生成章节摘要JSON
        chapter_summaries = generate_chapter_summaries_json(
            input_file=args.input_file,
            output_file=output_file,  # 使用处理后的 output_file
            style=args.style,
            language=args.language,
            window_size=args.window_size,
            max_tokens=args.max_tokens
        )

    except Exception as e:
        logger.error(f"Error processing novel: {e}")
        sys.exit(1)
    finally:
        # 确保嵌入管理器保存
        embedding_manager = get_embedding_manager()
        embedding_manager.save()

if __name__ == "__main__":
    main()