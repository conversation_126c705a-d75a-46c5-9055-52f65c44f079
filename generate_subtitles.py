import subprocess
import sys
from pathlib import Path
import argparse
import os
import tempfile
import shutil
import platform
from config import logger, TEMP_FILE_DIR
from modules.nlp_model import get_nlp_model
import json
from pydub import AudioSegment
import re
from moviepy.video.io.VideoFileClip import VideoFileClip

# 添加MPS支持代码
import torch
try:
    import whisper
    _orig_load_model = whisper.load_model
    
    def _patched_load_model(name, device=None, **kw):
        if device is None and torch.backends.mps.is_available():
            device = "mps"
            logger.info(f"[MPS] Whisper 将使用 MPS 加速 (model: {name})")
        return _orig_load_model(name, device=device, **kw)
    
    whisper.load_model = _patched_load_model
    
    # 设置MPS回落，确保兼容性
    if torch.backends.mps.is_available() and os.environ.get("PYTORCH_ENABLE_MPS_FALLBACK") != "1":
        os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
        logger.info("已启用MPS回落支持")
        
    logger.info(f"Whisper MPS加速已设置，设备状态: MPS可用={torch.backends.mps.is_available()}")
except ImportError:
    logger.info("未检测到whisper模块，跳过MPS配置")

MAX_CHARS_RATIO = 1.2
MIN_CHARS_RATIO = 0.8

# Language configuration
SUBTITLE_CONFIG = {
    'English': {
        'code': 'en-US',
        'max_chars_per_line': 42,
        'time_format': '{:02d}:{:02d}:{:02d},{:03d}'
    },
    'Spanish': {
        'code': 'es-ES',
        'max_chars_per_line': 42,
        'time_format': '{:02d}:{:02d}:{:02d},{:03d}'
    },
    'Portuguese': {
        'code': 'pt-PT',
        'max_chars_per_line': 42,
        'time_format': '{:02d}:{:02d}:{:02d},{:03d}'
    },
    'Japanese': {
        'code': 'ja-JP',
        'max_chars_per_line': 25,  
        'time_format': '{:02d}:{:02d}:{:02d},{:03d}'
    },
    'Chinese': {
        'code': 'zh-CN',
        'max_chars_per_line': 25,  # 与日语保持一致
        'time_format': '{:02d}:{:02d}:{:02d},{:03d}'
    },
}

SUBTITLE_DEBUG = False

def check_subaligner():
    try:
        subprocess.run(['subaligner', '--version'], check=True, capture_output=True, text=True)
        logger.info("Subaligner is installed and available.")
    except subprocess.CalledProcessError:
        logger.error("Subaligner is not installed or not working properly.")
        sys.exit(1)
    except FileNotFoundError:
        logger.error("Subaligner command not found. Please install it with: pip install subaligner")
        sys.exit(1)

def get_char_limits(max_chars: int) -> (int, int):
    min_chars = int(max_chars * MIN_CHARS_RATIO)
    max_chars = int(max_chars * MAX_CHARS_RATIO)
    return min_chars, max_chars


def split_long_lines(transcript: str, max_chars: int, language: str) -> str:
    # 使用 spacy 进行句子分割
    nlp = get_nlp_model(lang=language)
    doc = nlp(transcript)
    sentences = [sent.text.strip() for sent in doc.sents]
    
    lines = []
    for sentence in sentences:
        words = sentence.split()
        current_line = ''
        for word in words:
            if len(current_line) + len(word) + 1 <= max_chars:
                current_line += ' ' + word if current_line else word
            else:
                if len(current_line) >= int(max_chars * MIN_CHARS_RATIO):
                    lines.append(current_line.strip())
                    current_line = word
                else:
                    # 当前行长度不足，尝试将当前行与下一个单词合并
                    if lines:
                        lines[-1] += ' ' + word
                    else:
                        lines.append(current_line + ' ' + word)
                    current_line = ''
        if current_line:
            lines.append(current_line.strip())
    
    logger.debug(f"文本分割后的行数: {len(lines)}")
    
    return '\n\n'.join(lines)


def save_transcript(transcript: str, file_path: Path) -> None:
    try:
        with file_path.open('w', encoding='utf-8') as f:
            f.write(transcript)
        logger.info(f"转录文本已保存到 {file_path}")
    except IOError as e:
        logger.error(f"保存转录文本失败: {e}")
        sys.exit(1)

def run_command(command: list, error_message: str) -> None:
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        logger.info(result.stdout)
    except subprocess.CalledProcessError as e:
        logger.error(f"{error_message}: {e}")
        logger.error(f"Command output: {e.output}")
        sys.exit(1)
    except FileNotFoundError:
        logger.error(f"Command not found: {command[0]}. Please ensure it is installed and in your PATH.")
        sys.exit(1)

def run_subaligner(audio_file: Path, transcript_file: Path, output_file: Path, language: str) -> None:
    config = SUBTITLE_CONFIG.get(language, SUBTITLE_CONFIG['English'])
    max_chars = config['max_chars_per_line']
    language_code = config['code']  # 获取语言代码

    # 检测是否为 macOS 系统
    is_macos = platform.system() == 'Darwin'
    
    # 为 macOS 系统设置优化的资源配置
    batch_size = '4'
    num_workers = '2'
    
    if is_macos:
        batch_size = '1'     # 增加批处理大小
        num_workers = '1'    # 增加工作进程数
        logger.info("检测到 macOS 系统，启用优化的资源配置")

    with tempfile.TemporaryDirectory() as temp_output_dir:
        temp_output_dir_path = Path(temp_output_dir)
        temp_output_file = temp_output_dir_path / output_file.name

        try:
            # 步骤 1: 使用脚本模式生成初始字幕文件
            initial_command = [
                'subaligner',
                '-m', 'script',
                '-s', str(transcript_file),
                '-v', str(audio_file),
                '-o', str(temp_output_file),
                '--segment-duration-tolerance', '2.0',
                '--batch-size', batch_size,
                '--num-workers', num_workers,
                '--media_process_timeout', '600',
                '--keep-extracted-audio',
                '--max-chars', str(max_chars),
                '--debug',
                '--reference-language', language_code,
                '--hypothesis-language', language_code
            ]

            logger.debug(f"运行初始命令: {' '.join(initial_command)}")
            
            result = subprocess.run(initial_command, check=True, capture_output=True, text=True, env=os.environ)
            logger.info(result.stdout)
            logger.info(f"Subaligner (单通道模式) 成功完成。初始输出保存到 {temp_output_file}")

            # 步骤2：使用双通道模式进行精细对齐
            final_command = [
                'subaligner',
                '-m', 'dual',
                '-s', str(temp_output_file),
                '-v', str(audio_file),
                '-o', str(temp_output_file.with_suffix('.final.srt')),
                '--segment-duration-tolerance', '2.0',  # 增加容忍度
                '--batch-size', batch_size,
                '--num-workers', num_workers,
                '--media_process_timeout', '600',
                '--keep-extracted-audio',
                '--max-chars', str(max_chars),
                '--reference-language', language_code,
                '--hypothesis-language', language_code,
                '--debug'
            ]

            logger.debug(f"运行精细对齐命令: {' '.join(final_command)}")

            result = subprocess.run(final_command, check=True, capture_output=True, text=True)
            logger.info(result.stdout)
            logger.info(f"Subaligner (双通道模式) 成功完成。最终输出保存到 {temp_output_file.with_suffix('.final.srt')}")

            # 复制生成的字幕文件到最终目标位置
            shutil.copy(temp_output_file.with_suffix('.final.srt'), output_file)

        except subprocess.CalledProcessError as e:
            logger.error(f"Subaligner 运行失败: {e}")
            logger.error(f"命令输出: {e.stdout}")
            logger.error(f"错误输出: {e.stderr}")
            # 可能的回退策略，例如使用简单的时间均分方法生成字幕

    logger.info(f"Subaligner 处理完成。最终输出保存到 {output_file}")

def run_subaligner_cjk(audio_file: Path, output_file: Path, language: str) -> None:
    """
    使用二步流程处理日语字幕：先用Whisper转写，再用dual模式精调时间轴
    
    Args:
        audio_file: 音频文件路径
        output_file: 输出字幕文件路径
        language: 语言代码
    """
    config = SUBTITLE_CONFIG.get(language, SUBTITLE_CONFIG['Japanese'])
    max_chars = config['max_chars_per_line']
    logger.debug(f"max_chars,max_chars_per_line: {max_chars}")
    language_code = config['code']  # 获取语言代码，日语为ja-JP

    # 检测是否为 macOS 系统，优化资源配置
    is_macos = platform.system() == 'Darwin'
    batch_size = '1' if is_macos else '4'
    num_workers = '1' if is_macos else '2'
    
    logger.info(f"开始使用Subaligner处理日语字幕，使用转写+精细对齐模式")
    
    with tempfile.TemporaryDirectory() as temp_output_dir:
        temp_output_dir_path = Path(temp_output_dir)
        temp_output_file = temp_output_dir_path / f"{output_file.stem}_initial.srt"
        final_output_file = temp_output_dir_path / f"{output_file.stem}_final.srt"
        
        try:
            # 第一步：使用转写模式生成初步字幕
            logger.info("第一步：使用Whisper转写模式生成初步字幕...")
            transcribe_command = [
                'subaligner',
                '-m', 'transcribe',
                '-v', str(audio_file),
                '-ml', 'jpn',  # 日语语言代码
                '-mr', 'whisper',  # 使用Whisper模型
                '-mf', 'medium',   # 
                '-o', str(temp_output_file),
                '--batch-size', batch_size,
                '--num-workers', num_workers,
                '--media_process_timeout', '600',
                '--max_char_length', str(max_chars),
                '--debug'
            ]
            
            logger.debug(f"运行转写命令: {' '.join(transcribe_command)}")
            result = subprocess.run(transcribe_command, check=True, capture_output=True, text=True)
            logger.info(f"Whisper转写完成，初始字幕保存到 {temp_output_file}")
            
            # 第二步：使用dual模式进行精细对齐
            logger.info("第二步：使用双通道模式进行精细对齐...")
            if not temp_output_file.exists():
                raise FileNotFoundError(f"转写生成的字幕文件不存在: {temp_output_file}")
                
            align_command = [
                'subaligner',
                '-m', 'dual',
                '-v', str(audio_file),
                '-s', str(temp_output_file),
                '-o', str(final_output_file),
                '--segment-duration-tolerance', '2.0',  # 增加容忍度
                '--batch-size', batch_size,
                '--num-workers', num_workers,
                '--media_process_timeout', '600',
                '--max_char_length', str(max_chars),
                '--reference-language', 'jpn',          
                '--hypothesis-language', 'jpn',  
                '--debug'
            ]
            
            logger.debug(f"运行精细对齐命令: {' '.join(align_command)}")
            result = subprocess.run(align_command, check=True, capture_output=True, text=True)
            logger.info(f"双通道对齐完成，最终字幕保存到 {final_output_file}")
            
            # 复制生成的字幕文件到最终目标位置
            if final_output_file.exists():
                shutil.copy(final_output_file, output_file)
            else:
                # 如果精细对齐失败，使用初始转写结果
                logger.warning("精细对齐未生成文件，使用初始转写结果")
                shutil.copy(temp_output_file, output_file)
                
        except subprocess.CalledProcessError as e:
            logger.error(f"Subaligner运行失败: {e}")
            logger.error(f"命令输出: {e.stdout}")
            logger.error(f"错误输出: {e.stderr}")
            raise
        except Exception as e:
            logger.error(f"处理失败: {e}")
            raise
            
    logger.info(f"日语字幕处理完成，最终输出保存到 {output_file}")

def generate_aligned_srt(transcript_file: Path, audio_file: Path, subtitle_output_file: Path, language: str, low_memory: bool = False, disable_quantization: bool = False) -> None:
    config = SUBTITLE_CONFIG.get(language, SUBTITLE_CONFIG['English'])
    
    if language != 'Japanese':
        check_subaligner()  # 仅在非日语情况下检查 subaligner
    
    if language == 'Japanese':
        try:
            # 直接使用run_subaligner_cjk处理日语字幕
            run_subaligner_cjk(audio_file, subtitle_output_file, language)
            logger.info(f"日语字幕生成完成。最终输出保存到 {subtitle_output_file}")
        except Exception as e:
            logger.error(f"处理失败: {e}")
            raise
    else:
        # 其他语言使用原有的处理方式
        try:
            with transcript_file.open('r', encoding='utf-8') as f:
                transcript_text = f.read()
            logger.info(f"已从 {transcript_file} 加载转录文本")
        except IOError as e:
            logger.error(f"读取转录文本失败: {e}")
            raise

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir_path = Path(temp_dir)
            formatted_transcript_file = temp_dir_path / f'formatted_transcript_{audio_file.stem}.txt'

            formatted_transcript = split_long_lines(transcript_text, config['max_chars_per_line'], language)
            save_transcript(formatted_transcript, formatted_transcript_file)
                        
            run_subaligner(audio_file, formatted_transcript_file, subtitle_output_file, language)

            logger.info(f"临时文件存储在 {temp_dir}")

    logger.info("字幕生成成功完成。")

def extract_text_from_json(json_file: Path) -> str:
    """从JSON文件中提取narration和dialogues的content文本"""
    try:
        with json_file.open('r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"JSON文件解析失败: {e}")
        sys.exit(1)
    except IOError as e:
        logger.error(f"读取JSON文件失败: {e}")
        sys.exit(1)

    text_parts = []
    
    # 遍历所有场景
    for scene in data.get('scenes', []):
        # 添加narration（如果不为空）
        if scene.get('narration'):
            text_parts.append(scene['narration'])
        
        # 添加dialogues中的content（如果存在）
        if scene.get('dialogues'):
            for dialogue in scene['dialogues']:
                if dialogue.get('content'):
                    text_parts.append(dialogue['content'])
    
    # 用换行符连接所有文本
    return '\n'.join(text_parts)

def process_input_file(input_file: Path, is_json: bool) -> str:
    """处理输入文件，返回文本内容"""
    if is_json:
        return extract_text_from_json(input_file)
    else:
        try:
            with input_file.open('r', encoding='utf-8') as f:
                return f.read()
        except IOError as e:
            logger.error(f"读取文件失败: {e}")
            sys.exit(1)

def get_clean_filename(file_path: Path, script_path: Path) -> Path:
    """从脚本文件路径生成输出的 srt 文件路径
    
    Args:
        file_path: 音频文件路径（用于兼容旧代码）
        script_path: 输入脚本文件路径
    Returns:
        Path: srt 文件输出路径
    """
    script_dir = script_path.parent
    script_basename = script_path.stem
    
    # 检查并移除已知后缀
    suffixes = ['_prompts_images_timing', '_prompts_timing_images', '_prompts_images']
    clean_name = script_basename
    
    for suffix in suffixes:
        if script_basename.endswith(suffix):
            clean_name = script_basename[:-len(suffix)]
            break
    
    return script_dir / f"{clean_name}.srt"

def extract_audio_from_video(video_path: Path) -> Path:
    """
    从视频文件中提取音频
    
    Args:
        video_path: 视频文件路径
    
    Returns:
        Path: 提取的音频文件路径
    """
    # 确保临时目录存在
    temp_dir = Path(TEMP_FILE_DIR)
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置输出音频路径
    output_path = temp_dir / f"{video_path.stem}_audio.mp3"
    
    try:
        # 加载视频文件
        video = VideoFileClip(str(video_path))
        # 提取音频并保存
        video.audio.write_audiofile(str(output_path))
        video.close()
        
        logger.info(f"音频提取完成，保存至: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"从视频提取音频失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Generate SRT subtitles for an audio file.")
    
    # 修改输入参数，使script和json互斥
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("--script", type=str, help="Path to the transcript file")
    input_group.add_argument("--json", type=str, help="Path to the JSON file containing scene data")
    
    # 修改音频输入互斥组
    audio_group = parser.add_mutually_exclusive_group(required=True)
    audio_group.add_argument("--audio", type=str, help="Path to the audio file")
    audio_group.add_argument("--video", type=str, help="Path to the video file to extract audio from")
    
    parser.add_argument("--lang", type=str, default="Chinese", help="Language of the audio (default: Chinese)")
    parser.add_argument("--low-memory", action="store_true", help="启用低内存模式，更积极地释放内存资源")
    parser.add_argument("--no-quantize", action="store_true", help="禁用模型量化，可能提高精度但会增加内存使用")
    
    args = parser.parse_args()

    # 处理音频输入
    if args.video:
        video_path = Path(args.video)
        audio_path = extract_audio_from_video(video_path)
    else:
        audio_path = Path(args.audio)

    # 处理文本输入
    input_path = Path(args.json if args.json else args.script)
    
    if args.json:
        prefix = input_path.stem
        temp_dir = Path(TEMP_FILE_DIR)
        transcript_path = temp_dir / f'{prefix}_transcript.txt'
        transcript_text = process_input_file(input_path, True)
        
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        with transcript_path.open('w', encoding='utf-8') as f:
            f.write(transcript_text)
    else:
        transcript_path = input_path

    # 确定输出路径
    if args.script:
        # 直接使用脚本路径，仅更改后缀
        script_path_str = str(input_path).strip().replace('\n', '').replace('\r', '')
        output_file = Path(os.path.splitext(script_path_str)[0] + '.srt')
    else:
        # 对于JSON文件，使用原有的处理逻辑
        output_file = get_clean_filename(audio_path, input_path)

    # 生成字幕，传递参数
    generate_aligned_srt(
        transcript_path, 
        audio_path, 
        output_file, 
        args.lang, 
        low_memory=args.low_memory,
        disable_quantization=args.no_quantize
    )

    logger.info(f"字幕已成功生成并保存到: {output_file}")
