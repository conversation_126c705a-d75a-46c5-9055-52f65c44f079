#!/bin/bash

# 设置区域设置和字符编码
#export LANG=zh_CN.UTF-8
#export LC_ALL=zh_CN.UTF-8

# 基础配置
BASE_DIR="/home/<USER>/ai-video/"  # Python 脚本运行的基础目录
WORK_DIR="${1:-$(pwd)}"              # 文件处理的工作目录   
UTILS_DIR="/home/<USER>/ai-video/utils"
LOG_DIR="/home/<USER>/ai-video/logs"

# 日志函数
log_info() {
    echo "[INFO] $1" | tee -a "$LOG_DIR/process_$(date +%Y%m%d_%H%M).log"
}

log_error() {
    echo "[ERROR] $1" | tee -a "$LOG_DIR/process_$(date +%Y%m%d_%H%M).log"
}

# 命令执行函数
run_command() {
    log_info "Running command: $1"
    cd "$BASE_DIR"  # 确保在基础目录执行
    eval "$1"
    if [ $? -ne 0 ]; then
        log_error "Error running command: $1"
        return 1
    fi
    return 0
}

# 先切换到基础目录
log_info "切换到基础目录: $BASE_DIR"
cd "$BASE_DIR"

# 初始化 conda 环境
log_info "初始化 conda 环境..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate base

# 处理音频文件
process_audio_files() {
    log_info "查找音频文件命令: find \"$WORK_DIR\" -type f -name \"*.mp3\" -print0"
    found_files=$(find "$WORK_DIR" -type f -name "*.mp3" | wc -l)
    log_info "找到 $found_files 个 MP3 文件"
    
    while IFS= read -r -d '' mp3_file; do
        # 检查两种可能的文本文件名
        txt_file="${mp3_file%.*}.txt"
        txt_processed="${mp3_file%.*}_processed.txt"
        
        if [ -f "$txt_file" ] || [ -f "$txt_processed" ]; then
            log_info "跳过已处理的音频文件: $mp3_file"
            continue
        fi
        
        log_info "处理音频文件: $mp3_file"
        if run_command "python audio2text.py \"$mp3_file\""; then
            log_info "音频转文本成功: $mp3_file"
        else
            log_error "音频转文本失败: $mp3_file"
            echo "$mp3_file" >> "$LOG_DIR/audio_failed_$(date +%Y%m%d_%H%M).log"
        fi
    done < <(find "$WORK_DIR" -type f -name "*.mp3" -print0)
}

# 处理文本文件
process_text_files() {
    log_info "查找文本文件命令: find \"$WORK_DIR\" -type f -name \"*.txt\" -print0"
    found_files=$(find "$WORK_DIR" -type f -name "*.txt" | wc -l)
    log_info "找到 $found_files 个 TXT 文件"
    
    while IFS= read -r -d '' txt_file; do
        # 跳过已处理的文件
        if [[ "$txt_file" == *_processed.txt ]]; then
            log_info "跳过已处理的文本文件: $txt_file"
            continue
        fi
        
        log_info "处理文本文件: $txt_file"
        
        # 先生成主题名称
        theme_output=$(python -c "from modules.describe_images import generate_keyword; from modules.utils import normalize_words; print(normalize_words(generate_keyword('$txt_file', 'English')))")
        theme=$(echo "$theme_output" | tail -n 1)
        log_info "生成的主题名称: $theme"
        
        # 使用生成的主题名称调用 fetch_process_images.py
        if run_command "python fetch_process_images.py --script \"$txt_file\" --theme \"$theme\""; then
            # 设置目标目录和文件
            target_dir="$BASE_DIR/1-theme-talker/results/${theme}"
            target_file="$target_dir/${theme}_cn.txt"
            
            # 检查目标目录是否存在，如果不存在则创建
            if [ ! -d "$target_dir" ]; then
                log_info "创建目标目录: $target_dir"
                mkdir -p "$target_dir"
            else
                log_info "目标目录已存在: $target_dir"
            fi
            
            # 复制文件
            log_info "复制文件到: $target_file"
            if cp "$txt_file" "$target_file"; then
                log_info "文件复制成功: $target_file"
            else
                log_error "文件复制失败: $txt_file -> $target_file"
                echo "$txt_file" >> "$LOG_DIR/copy_failed_$(date +%Y%m%d_%H%M).log"
            fi
            
            # 处理成功后，重命名文件添加 _processed 后缀
            processed_file="${txt_file%.txt}_processed.txt"
            if mv "$txt_file" "$processed_file"; then
                log_info "文件处理完成并重命名: $processed_file"
            else
                log_error "文件重命名失败: $txt_file"
            fi
        else
            log_error "下载图片失败: $txt_file"
            echo "$txt_file" >> "$LOG_DIR/image_failed_$(date +%Y%m%d_%H%M).log"
        fi
    done < <(find "$WORK_DIR" -type f -name "*.txt" -print0)
}

# 主流程
main() {
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    # 处理音频文件
    process_audio_files
    
    # 处理文本文件
    process_text_files
    
    log_info "处理完成"
}

main
