#!/bin/bash
#set -e

# 初始化变量
BATCH_FILE=""
podcast_mode=false

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--batch)
            BATCH_FILE="$2"
            shift 2
            ;;
        --podcast)
            podcast_mode=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# 检查批处理文件是否提供
if [ -z "$BATCH_FILE" ]; then
    echo "Error: Batch file not provided. Use -b or --batch to specify the batch file."
    exit 1
fi

# 转换 BATCH_FILE 为绝对路径
BATCH_FILE=$(realpath "$BATCH_FILE")

# 检查文件是否存在
if [ ! -f "$BATCH_FILE" ]; then
    echo "Error: Batch file does not exist: $BATCH_FILE"
    exit 1
fi

# 获取当前时间戳，格式为 YYYYMMDD_HHMM
CURRENT_TIMESTAMP=$(date '+%Y%m%d_%H%M')

# 获取文件名（不含后缀）和后缀
FILENAME_NO_EXT=$(basename "$BATCH_FILE" .txt)
BATCH_DATE="${FILENAME_NO_EXT}_${CURRENT_TIMESTAMP}"

# 读取批处理文件到数组
topics=()
while IFS= read -r line || [ -n "$line" ]; do
    # 判断是否为空行，非空行才加入数组
    if [ -n "$line" ]; then
        topics+=("$line")
    fi
done < "$BATCH_FILE"

# 打印处理信息
echo "Processing batch file: $BATCH_FILE"
echo "Number of topics: ${#topics[@]}"

# Define server info and paths
SERVER_USER="azureuser"
SERVER_HOST="**************"
REMOTE_RESULT_BASE_DIR="/home/<USER>/ai-video/1-theme-talker/results"
LOCAL_DIR_BASE="/Users/<USER>/theme-talker/generated_video"
REMOTE_LOG_DIR="/home/<USER>/ai-video/logs"
REMOTE_SCRIPT="/home/<USER>/ai-video/utils/_run_video_generation.sh"
REMOTE_PODCAST_SCRIPT="/home/<USER>/ai-video/utils/_run_video_podcast.sh"
REMOTE_TOPICS_DIR="/home/<USER>/ai-video/1-theme-talker/topics"

# Function to check directory contents
check_directory_contents() {
    local dir=$1
    local is_remote=$2
    echo "Checking contents of directory: $dir"
    if [ "$is_remote" = true ]; then
        ssh -i ~/.ssh/videoadmin.pem "$SERVER_USER@$SERVER_HOST" << EOF
        set -x
        cd "$dir"
        pwd
        ls -la
EOF
    else
        pwd
        ls -la "$dir"
    fi
}

# Iterate over the topics array
for TOPIC in "${topics[@]}"; do
    LOCAL_DIR="${LOCAL_DIR_BASE}/${TOPIC}"
    LOCAL_IMAGES_DIR="${LOCAL_DIR}/images"
    LOCAL_COVERS_DIR="${LOCAL_DIR}/covers"
    REMOTE_TOPIC_DIR="${REMOTE_RESULT_BASE_DIR}/${TOPIC}"
    REMOTE_IMAGES_DIR="${REMOTE_TOPIC_DIR}/images"
    REMOTE_COVERS_DIR="${REMOTE_TOPIC_DIR}/covers"
    
    # Check if the remote directory exists
    echo "Checking if remote directory exists: $REMOTE_TOPIC_DIR"
    if ! ssh -i ~/.ssh/videoadmin.pem "$SERVER_USER@$SERVER_HOST" "[ -d \"$REMOTE_TOPIC_DIR\" ]"; then
        echo "Error: Remote directory '$REMOTE_TOPIC_DIR' does not exist."
        exit 1
    fi
    
    # Sync the local images directory back to the remote server
    echo "Syncing local images back to remote server: $REMOTE_IMAGES_DIR"
    rsync -avz --checksum --delete --progress -e "ssh -i ~/.ssh/videoadmin.pem" "${LOCAL_IMAGES_DIR}/" "$SERVER_USER@$SERVER_HOST:$REMOTE_IMAGES_DIR"
    
    # Sync the local covers directory back to the remote server
    echo "Syncing local covers back to remote server: $REMOTE_COVERS_DIR"
    rsync -avz --checksum --delete --progress -e "ssh -i ~/.ssh/videoadmin.pem" "${LOCAL_COVERS_DIR}/" "$SERVER_USER@$SERVER_HOST:$REMOTE_COVERS_DIR"
    
    # Sync txt files from the topic directory
    echo "Syncing local txt files back to remote server: $REMOTE_TOPIC_DIR"
    rsync -avz --checksum --progress -e "ssh -i ~/.ssh/videoadmin.pem" --include="*.txt" --exclude="*" "${LOCAL_DIR}/" "$SERVER_USER@$SERVER_HOST:$REMOTE_TOPIC_DIR"
    
done

# Set up remote log file
REMOTE_LOG_FILE="${REMOTE_LOG_DIR}/${FILENAME_NO_EXT}_processing_${CURRENT_TIMESTAMP}.log"

# Copy BATCH_FILE to remote server
REMOTE_BATCH_FILE="${REMOTE_TOPICS_DIR}/${BATCH_DATE}.txt"
scp -i ~/.ssh/videoadmin.pem "$BATCH_FILE" "${SERVER_USER}@${SERVER_HOST}:${REMOTE_BATCH_FILE}"

# SSH to the server and run the script using nohup
echo "SSH login command: ssh -t -i ~/.ssh/videoadmin.pem ${SERVER_USER}@${SERVER_HOST}"
ssh -t -i ~/.ssh/videoadmin.pem ${SERVER_USER}@${SERVER_HOST} << EOF
    # Check and create logs directory
    if [ ! -d "$REMOTE_LOG_DIR" ]; then
        echo "Creating logs directory: $REMOTE_LOG_DIR"
        mkdir -p $REMOTE_LOG_DIR
    fi
    
    # 选择要执行的脚本
    SCRIPT_TO_RUN="$REMOTE_SCRIPT"
    if [ "$podcast_mode" = true ]; then
        SCRIPT_TO_RUN="$REMOTE_PODCAST_SCRIPT"
    fi
    
    # Print the command to be executed
    echo "Executing remote script command: nohup bash \$SCRIPT_TO_RUN '$REMOTE_BATCH_FILE' > '$REMOTE_LOG_FILE' 2>&1 &"
    
    # Use nohup to run the script and redirect output to the log file
    nohup bash \$SCRIPT_TO_RUN '$REMOTE_BATCH_FILE' > '$REMOTE_LOG_FILE' 2>&1 &
    
    echo "Remote script is running in the background. Log output is directed to $REMOTE_LOG_FILE"
EOF

echo "All topics have been processed, synchronized, and the remote script has been initiated!"
