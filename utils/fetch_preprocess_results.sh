#!/bin/bash

# Check if batch file is provided
if [ -z "${1:-}" ]; then
    echo "Error: Batch file not provided" >&2
    exit 1
fi

# Parameters: Batch file and date
BATCH_FILE="$1"
BATCH_DATE=$(basename "$BATCH_FILE" .txt)

# Define server info and paths
SERVER_USER="azureuser"
SERVER_HOST="**************"
STATUS_DIR="/home/<USER>/ai-video/1-theme-talker/topics/"
REMOTE_RESULT_BASE_DIR="/home/<USER>/ai-video/1-theme-talker/results/"
LOCAL_DIR_BASE="/Users/<USER>/theme-talker/generated_video/"
TEMP_DIR="/Users/<USER>/theme-talker/temp/"

# Create local temp directory if it doesn't exist
mkdir -p "$TEMP_DIR"

# Read the batch file into an array manually
topics=()
while IFS= read -r line || [ -n "$line" ]; do
    topics+=("$line")
done < "$BATCH_FILE"

# Output file contents for verification
echo "Contents of $BATCH_FILE:"
for topic in "${topics[@]}"; do
    echo "$topic"
done
echo "---End of file contents---"

# Process each topic
echo "Starting to process individual topics..."
process_count=0
for TOPIC in "${topics[@]}"; do
    [[ -z "$TOPIC" ]] && continue
    ((process_count++))
    echo "Processing topic $process_count: $TOPIC"

    LOCAL_DIR="${LOCAL_DIR_BASE}${TOPIC}/"
    LOCAL_IMAGES_DIR="${LOCAL_DIR}images/"
    mkdir -p "$LOCAL_IMAGES_DIR"

    REMOTE_TOPIC_DIR="${REMOTE_RESULT_BASE_DIR}${TOPIC}"
    REMOTE_IMAGES_DIR="${REMOTE_TOPIC_DIR}/images/"
    REMOTE_TEXT_FILE="${REMOTE_TOPIC_DIR}/${TOPIC}_cn.txt"

    # Check if the remote images directory exists
    echo "Checking if remote directory exists: $REMOTE_IMAGES_DIR"
    if ! ssh -i ~/.ssh/videoadmin.pem "$SERVER_USER@$SERVER_HOST" "[ -d \"$REMOTE_IMAGES_DIR\" ]"; then
        echo "Error: Remote directory '$REMOTE_IMAGES_DIR' does not exist. Skipping this topic." >&2
        continue
    fi

    # List the contents of the remote directory
    echo "Listing contents of remote directory: $REMOTE_IMAGES_DIR"
    ssh -i ~/.ssh/videoadmin.pem "$SERVER_USER@$SERVER_HOST" << EOF
        set -x
        cd "$REMOTE_IMAGES_DIR"
        pwd
        ls -la
EOF

    # Sync the remote images directory to the local images directory
    echo "Syncing images from remote server to local directory: $LOCAL_IMAGES_DIR"
    if rsync -avz -e "ssh -i ~/.ssh/videoadmin.pem" "$SERVER_USER@$SERVER_HOST:$REMOTE_IMAGES_DIR" "$LOCAL_IMAGES_DIR"; then
        echo "Successfully synced images for: $TOPIC"
    else
        echo "Failed to sync images for: $TOPIC" >&2
        continue
    fi

    # Sync the .txt file from remote server to local directory
    echo "Syncing text file from remote server to local directory: $LOCAL_DIR"
    if scp -i ~/.ssh/videoadmin.pem "$SERVER_USER@$SERVER_HOST:$REMOTE_TEXT_FILE" "$LOCAL_DIR"; then
        echo "Successfully synced text file for: $TOPIC"
    else
        echo "Failed to sync text file for: $TOPIC" >&2
    fi
done

echo "Total topics processed: $process_count"
echo "All topics have been processed!"