#!/bin/bash

# 新增参数：指定目录
SYNC_DIR=""
GEN_SCRIPT=true
PREPROCESS_SCRIPT="/home/<USER>/theme-talker/utils/_run_podcast_preprocess.sh"

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --sync_dir)
            SYNC_DIR="$2"
            shift 2
            ;;
        --gen_script)
            GEN_SCRIPT="$2"
            shift 2
            ;;
        *)
            echo "错误: 无效的参数 '$1'"
            exit 1
            ;;
    esac
done

# 检查是否提供了同步目录
if [ -z "$SYNC_DIR" ]; then
    echo "错误：未提供同步目录"
    exit 1
fi

# 定义服务器信息和路径
SERVER_USER="azureuser"
SERVER_HOST="**************"
TOPIC_DIR="/home/<USER>/theme-talker/topics/"
REMOTE_LOG_DIR="/home/<USER>/theme-talker/logs/"
UTILS_DIR="/home/<USER>/theme-talker/utils/"
REMOTE_LOG_FILE="${REMOTE_LOG_DIR}podcast_preprocess_$(date +%Y%m%d_%H%M).log"

# 定义远程同步目录
REMOTE_SYNC_DIR="${TOPIC_DIR}$(basename "$SYNC_DIR")/"

# 检查远程目录是否存在，如果不存在则创建
echo "检查远程目录是否存在: ssh ${SERVER_USER}@${SERVER_HOST} 'if [ ! -d ${REMOTE_SYNC_DIR} ]; then mkdir -p ${REMOTE_SYNC_DIR}; fi'"
ssh ${SERVER_USER}@${SERVER_HOST} "if [ ! -d ${REMOTE_SYNC_DIR} ]; then mkdir -p ${REMOTE_SYNC_DIR}; fi"

# 使用 rsync 将指定目录中的文件同步到服务器的 topics 目录下
echo "同步命令: rsync -avz $SYNC_DIR ${SERVER_USER}@${SERVER_HOST}:${REMOTE_SYNC_DIR}"
rsync -avz "$SYNC_DIR" "${SERVER_USER}@${SERVER_HOST}:${REMOTE_SYNC_DIR}"

if [ $? -eq 0 ]; then
    echo "目录同步成功。"
else
    echo "目录同步失败。" >&2
    exit 1
fi

# 登录到服务器并调用 PREPROCESS_SCRIPT 处理文件
echo "SSH 登录命令: ssh -t -i ~/.ssh/videoadmin.pem ${SERVER_USER}@${SERVER_HOST}"
ssh -t -i ~/.ssh/videoadmin.pem ${SERVER_USER}@${SERVER_HOST} << EOF
    echo "调用 PREPROCESS_SCRIPT 处理文件"
    nohup bash "$PREPROCESS_SCRIPT" --remote_sync_dir "$REMOTE_SYNC_DIR" > "$REMOTE_LOG_FILE" 2>&1 &
EOF

if [ $? -eq 0 ]; then
    echo "远程文件处理已成功完成。"
else
    echo "远程文件处理失败。" >&2
    exit 1
fi