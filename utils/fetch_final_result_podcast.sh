#!/bin/bash
set -x

# Check if batch file is provided
if [ -z "$1" ]; then
    echo "Error: Batch file not provided"
    exit 1
fi

# Parameters: Batch file, date, and theme-talker version
BATCH_FILE=$1
BATCH_DATE=$(basename "$BATCH_FILE" .txt)
THEME_TALKER_VERSION=${2:-"theme-talker"}

# Define server info and paths
SERVER_USER="azureuser"
SERVER_HOST="**************"
REMOTE_RESULT_BASE_DIR="/home/<USER>/${THEME_TALKER_VERSION}/results/"
LOCAL_DIR_BASE="/Users/<USER>/theme-talker/generated_video/"

# Function to download files using rsync
download_files() {
    local TOPIC="$1"
    local REMOTE_DIR="${REMOTE_RESULT_BASE_DIR}${TOPIC}/"
    local LOCAL_DIR="${LOCAL_DIR_BASE}${TOPIC}/"

    # Create local directory if it doesn't exist
    mkdir -p "$LOCAL_DIR"

    # Files to download
    local FILES=(
        "${TOPIC}.txt"
        "${TOPIC}_sub.mp4"
        "${TOPIC}_metadata_en.txt"        
    )

    # Download each file using rsync
    for FILE in "${FILES[@]}"; do
        echo "Downloading $FILE for $TOPIC..."
        if rsync -avz -e "ssh -i ~/.ssh/videoadmin.pem" "$SERVER_USER@$SERVER_HOST:$REMOTE_DIR$FILE" "$LOCAL_DIR"; then
            echo "Successfully downloaded $FILE"
        else
            echo "Warning: Failed to download $FILE for $TOPIC"
        fi
    done

    # Download all images from the 'covers' directory using rsync
    local COVERS_DIR="${REMOTE_DIR}covers/"
    local LOCAL_COVERS_DIR="${LOCAL_DIR}covers/"
    echo "Downloading all images from $COVERS_DIR..."
    if rsync -avz -e "ssh -i ~/.ssh/videoadmin.pem" "$SERVER_USER@$SERVER_HOST:$COVERS_DIR" "$LOCAL_COVERS_DIR"; then
        echo "Successfully downloaded all images from $COVERS_DIR"
    else
        echo "Warning: Failed to download images from $COVERS_DIR"
    fi
}

# Process each topic in the batch file
while IFS= read -r TOPIC || [ -n "$TOPIC" ]; do
    if [ -n "$TOPIC" ]; then
        echo "Processing $TOPIC..."
        download_files "$TOPIC"
    else
        echo "Warning: Empty line or invalid topic in $BATCH_FILE"
    fi
done < "$BATCH_FILE"

echo "All files have been processed!"