#!/bin/bash

# Check if batch file is provided
if [ -z "$1" ]; then
    echo "Error: Batch file not provided"
    exit 1
fi

# Convert BATCH_FILE to absolute path
BATCH_FILE=$(realpath "$1")

# Parameters: Batch file and date
BATCH_DATE=$(basename "$BATCH_FILE" .txt)

# Define server info
SERVER_USER="azureuser"
SERVER_HOST="**************"
REMOTE_LOG_DIR="/home/<USER>/theme-talker/logs/"
REMOTE_LOG_FILE="${REMOTE_LOG_DIR}${BATCH_DATE}_processing.log"

# SSH to the server and tail the log file
ssh -t -i ~/.ssh/gpu.pem ${SERVER_USER}@${SERVER_HOST} "tail -f $REMOTE_LOG_FILE"