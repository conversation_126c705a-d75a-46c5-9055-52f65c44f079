#!/bin/bash

# 脚本：下载、处理和索引以 "crime" 为主题的视频
source ~/miniconda3/etc/profile.d/conda.sh
conda activate aidev

export PYTHONPATH=$(pwd)

PYTHON_EXECUTABLE="python" # 或者 python3，取决于您的环境

# 检查 video_retrieval_main.py 是否存在
if [ ! -f "video_retrieval/video_retrieval_main.py" ]; then
    echo "错误: video_retrieval/video_retrieval_main.py 未找到。"
    echo "请确保脚本相对于项目根目录运行，或者调整 VIDEO_RETRIEVAL_MAIN 变量。"
    # 尝试上一级目录查找
    if [ -f "../video_retrieval/video_retrieval_main.py" ]; then
        echo "在上一级目录中找到 video_retrieval_main.py。将使用该路径。"
        VIDEO_RETRIEVAL_MAIN="${PYTHON_EXECUTABLE} video_retrieval/video_retrieval_main.py"
    else
        exit 1
    fi
else
    VIDEO_RETRIEVAL_MAIN="${PYTHON_EXECUTABLE} video_retrieval/video_retrieval_main.py"
fi


# 关键词
KEYWORDS="victorian Era"

# 每次运行时，每个关键词从每个源下载的视频数量
# Pexels: 200 req/h. Pixabay: 100 req/min.
# 假设一次搜索+下载X个视频大约是 X+1 个请求。
# 如果 MAX_PER_KEYWORD=20, 每次源的调用大约 21 次请求。
# 两个源就是 ~42 次请求。
# 您可以根据需要调整此值。较小的值更安全，但下载较慢。
MAX_PER_KEYWORD=4000

echo "=============================================================================="
echo "开始视频处理流程: 下载 -> 处理与切分 -> 构建索引"
echo "关键词: ${KEYWORDS}"
echo "每个来源每次最大下载量: ${MAX_PER_KEYWORD}"
echo "=============================================================================="
echo ""

# -----------------------------------------------------------------------------
echo "阶段 1: 下载视频 (来源: all, 关键词: ${KEYWORDS})"
# -----------------------------------------------------------------------------
# 使用 --source all 从 Pexels 和 Pixabay 下载
# 项目中的 token_bucket.py 应该处理各自服务的短期速率限制。
# 此脚本本身不处理跨多小时/多天的下载以符合月度限制（如Pexels的20,000次/月）。
# 用户需要根据API总用量，在适当间隔后重复运行此脚本以获取更多视频。

$VIDEO_RETRIEVAL_MAIN download_videos --source all --keywords "${KEYWORDS}" --max_per_keyword ${MAX_PER_KEYWORD}

# 检查上一个命令的退出状态
if [ $? -ne 0 ]; then
  echo "错误: 视频下载失败。正在中止脚本。"
  exit 1
fi

echo "视频下载阶段完成。"
echo ""
sleep 2 # 短暂暂停，增加可读性

# -----------------------------------------------------------------------------
echo "阶段 2: 处理与切分所有新视频"
# -----------------------------------------------------------------------------
# 处理数据库中所有通过下载或手动导入但尚未处理的视频
# (这些视频应位于 video_config.py 中定义的 LONG_VIDEOS_DIR)
$VIDEO_RETRIEVAL_MAIN process_long_video --process_all_new_in_db

if [ $? -ne 0 ]; then
  echo "错误: 视频处理与切分失败。正在中止脚本。"
  exit 1
fi

echo "视频处理与切分阶段完成。"
echo ""
sleep 2

# -----------------------------------------------------------------------------
echo "阶段 3: 构建/更新索引"
# -----------------------------------------------------------------------------
# 对 video_config.py 中定义的 CLIPS_DIR 中的所有片段
# (或其中尚未建立索引的片段) 进行特征提取和索引构建
$VIDEO_RETRIEVAL_MAIN build_index

if [ $? -ne 0 ]; then
  echo "错误: 构建/更新索引失败。正在中止脚本。"
  exit 1
fi

echo "索引构建/更新阶段完成。"
echo ""

echo "=============================================================================="
echo "所有阶段成功完成！"
echo "=============================================================================="
echo ""
echo "重要提示:"
echo " - 此脚本已尝试下载一批视频。要下载更多视频，请在适当的间隔后再次运行此脚本。"
echo " - 请关注您的API使用情况，特别是Pexels每月20,000次请求的总限制。"
echo " - 您可以通过修改脚本中的 MAX_PER_KEYWORD 变量来调整单次下载的数量。"
echo ""