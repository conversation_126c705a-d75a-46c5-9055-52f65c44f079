#!/bin/bash
# 删除 data/logs 和 data/temp 目录（包括所有子目录）中修改时间早于昨天 00:00 的文件，
# 并删除清理后产生的空子目录

if [[ "$(uname)" == "Darwin" ]]; then
    # macOS 系统：使用 marker 文件来比较
    yesterday=$(date -v -1d "+%Y-%m-%d")
    marker_yesterday=$(mktemp)
    # 设置 marker 文件的时间为昨天 00:00
    touch -t $(date -j -f "%Y-%m-%d" "$yesterday" "+%Y%m%d0000") "$marker_yesterday"
    
    for dir in data/logs data/temp; do
        if [ -d "$dir" ]; then
            echo "正在清理目录 \"$dir\"（包括所有子目录）中修改时间早于 $yesterday 00:00 的文件..."
            find "$dir" -type f ! -newer "$marker_yesterday" -exec rm -f {} \;
        else
            echo "目录 \"$dir\" 不存在。"
        fi
    done
    
    rm -f "$marker_yesterday"
else
    # Linux 系统：直接使用 -newermt 参数
    yesterday=$(date -d "yesterday 00:00" "+%Y-%m-%d %H:%M:%S")
    
    for dir in data/logs data/temp; do
        if [ -d "$dir" ]; then
            echo "正在清理目录 \"$dir\"（包括所有子目录）中修改时间早于 $yesterday 的文件..."
            find "$dir" -type f ! -newermt "$yesterday" -exec rm -f {} \;
        else
            echo "目录 \"$dir\" 不存在。"
        fi
    done
fi

# 删除空的子目录（保留主目录本身）
for dir in data/logs data/temp; do
    if [ -d "$dir" ]; then
         echo "正在删除目录 \"$dir\" 下的空子目录..."
         find "$dir" -mindepth 1 -type d -empty -delete
    fi
done

echo "清理完成。"