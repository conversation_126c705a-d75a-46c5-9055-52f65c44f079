import os
import shutil
import logging
from typing import List, Optional, Tuple
import argparse

import torch
from transformers import CLIPProcessor, CLIPModel
from PIL import Image
from config import FILTERED_IMAGE_DIR
# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 文件头部添加配置常量
SIMILARITY_THRESHOLD = 0.95  # 图片相似度阈值：0.95表示非常相似的图片才会被认为是重复

def validate_keyword(keyword: str) -> bool:
    """
    验证关键词是否有效。

    :param keyword: 输入的关键词
    :return: 如果关键词有效则返回 True，否则返回 False
    """
    if not keyword.strip():
        logger.error("关键词不能为空或仅包含空白字符。")
        return False
    return True


class CLIPImageMatcher:
    """
    使用 CLIP 模型进行图像-文本匹配的工具类。
    """

    def __init__(self, model_name: str = "openai/clip-vit-base-patch32", device: Optional[str] = None):
        """
        初始化 CLIP 模型和处理器。

        :param model_name: 使用的 CLIP 模型名称
        :param device: 计算设备（'cuda' 或 'cpu'）。默认为自动检测。
        """
        self.device = device if device else ("cuda" if torch.cuda.is_available() else "cpu")
        #logger.debug(f"使用设备: {self.device}")
        try:
            self.model = CLIPModel.from_pretrained(model_name)
            self.processor = CLIPProcessor.from_pretrained(model_name)
            self.model.to(self.device)
            self.model.eval()
            #logger.debug("CLIP 模型加载成功。")
        except Exception as e:
            logger.error(f"加载 CLIP 模型时出错: {e}")
            raise

    def process_images_batch(self, image_paths: List[str]) -> Tuple[torch.Tensor, List[str], List[int]]:
        """
        批量处理图片，返回特征、有效路径和图片尺寸
        """
        batch_features = []
        valid_paths = []
        image_sizes = []

        for image_path in image_paths:
            try:
                with Image.open(image_path).convert("RGB") as img:
                    width, height = img.size
                    total_pixels = width * height
                    
                    image_input = self.processor(images=img, return_tensors="pt").to(self.device)
                    with torch.no_grad():
                        image_features = self.model.get_image_features(**image_input)
                        image_features = image_features / image_features.norm(p=2, dim=-1, keepdim=True)
                        
                    batch_features.append(image_features)
                    valid_paths.append(image_path)
                    image_sizes.append(total_pixels)
            except Exception as e:
                logger.error(f"处理图片 '{image_path}' 时出错: {e}")
                continue

        if not batch_features:
            return None, [], []

        return torch.cat(batch_features, dim=0), valid_paths, image_sizes

    def filter_and_deduplicate(
        self, 
        image_paths: List[str], 
        theme: str, 
        relevance_threshold: float = 0.5,
        similarity_threshold: float = 0.95,
        batch_size: int = 16
    ) -> Tuple[List[str], List[str]]:
        """
        同时进行相关性过滤和去重，返回保留的图片和移除的图片
        
        Args:
            image_paths: 输入图片路径列表
            theme: 主题关键词
            relevance_threshold: 相关性判断阈值
            similarity_threshold: 图片相似度判断阈值
            batch_size: 批处理大小
        
        Returns:
            Tuple[List[str], List[str]]: (保留的图片路径, 移除的图片路径)
        """
        if not image_paths:
            return [], []

        # 计算主题文本特征
        text_inputs = self.processor(text=[theme], return_tensors="pt", padding=True).to(self.device)
        with torch.no_grad():
            text_features = self.model.get_text_features(**text_inputs)
            text_features = text_features / text_features.norm(p=2, dim=-1, keepdim=True)

        # 批量处理所有图片
        all_features = []
        all_valid_paths = []
        all_image_sizes = []

        for i in range(0, len(image_paths), batch_size):
            batch_paths = image_paths[i:i + batch_size]
            features, valid_paths, image_sizes = self.process_images_batch(batch_paths)
            
            if features is not None:
                all_features.append(features)
                all_valid_paths.extend(valid_paths)
                all_image_sizes.extend(image_sizes)

        if not all_features:
            return [], image_paths

        # 合并所有特征
        all_features = torch.cat(all_features, dim=0)

        # 计算与主题的相关性
        relevance_scores = torch.matmul(all_features, text_features.T).squeeze()
        relevant_mask = relevance_scores >= relevance_threshold

        # 获取相关图片的索引
        relevant_indices = torch.where(relevant_mask)[0].cpu().tolist()
        
        if not relevant_indices:
            return [], image_paths

        # 提取相关图片的特征和路径
        relevant_features = all_features[relevant_mask]
        relevant_paths = [all_valid_paths[i] for i in relevant_indices]
        relevant_sizes = [all_image_sizes[i] for i in relevant_indices]

        # 计算相关图片之间的相似度矩阵
        similarity_matrix = torch.matmul(relevant_features, relevant_features.T)

        # 去重处理
        used_indices = set()
        retained_images = []
        
        # 按图片尺寸排序，优先保留大尺寸图片
        size_sorted_indices = sorted(range(len(relevant_paths)), 
                                   key=lambda k: relevant_sizes[k], 
                                   reverse=True)

        for i in size_sorted_indices:
            if i in used_indices:
                continue
                
            # 找出相似图片
            similar_indices = torch.where(similarity_matrix[i] >= similarity_threshold)[0].cpu().tolist()
            
            # 保留当前图片（最大尺寸）
            retained_images.append(relevant_paths[i])
            
            # 标记所有相似图片
            used_indices.update(similar_indices)

        # 确定移除的图片
        removed_images = [path for path in image_paths 
                         if path not in retained_images]

        # 记录处理结果
        total_images = len(image_paths)
        retained_count = len(retained_images)
        removed_count = len(removed_images)
        
        logger.info(f"""
        图片处理完成:
        - 总图片数: {total_images}
        - 保留图片数: {retained_count}
        - 移除图片数: {removed_count}
        - 移除率: {(removed_count/total_images)*100:.2f}%
        """)

        return retained_images, removed_images


def filter_images_by_clip(theme: str, image_paths: List[str], threshold: float = 0.5) -> Tuple[List[str], List[str]]:
    """
    使用 CLIP 模型过滤和去重图片
    """
    if not validate_keyword(theme):
        raise ValueError("无效的关键词")
    
    if not image_paths:
        logger.warning("没有找到需要过滤的图片")
        return [], []
    
    try:
        clip_matcher = CLIPImageMatcher()
        return clip_matcher.filter_and_deduplicate(
            image_paths=image_paths,
            theme=theme,
            relevance_threshold=threshold,
            similarity_threshold=SIMILARITY_THRESHOLD  # 使用全局配置的相似度阈值
        )
    except Exception as e:
        logger.error(f"处理图片时出错: {e}")
        return [], []


def parse_arguments():
    """
    解析命令行参数。

    :return: 解析后的参数对象
    """
    parser = argparse.ArgumentParser(
        description="根据主题过滤图像，将不相关的图片移动到 filtered_images 目录。"
    )
    parser.add_argument(
        '--theme',
        type=str,
        required=True,
        help='输入的主题关键词，用于图像匹配。'
    )
    parser.add_argument(
        '--image_dir',
        type=str,
        required=True,
        help='图像目录路径，包含需要过滤的图片。'
    )
    parser.add_argument(
        '--threshold',
        type=float,
        default=0.5,
        help='相似度阈值，默认为 0.5。'
    )
    return parser.parse_args()


def main():
    """
    主函数，仅处理命令行参数，并调用相应的功能。
    """
    args = parse_arguments()
    logger.info(f"开始处理主题: {args.theme}")
    logger.info(f"图片目录: {args.image_dir}")
    logger.info(f"相似度阈值: {args.threshold}")
    
    try:
        if not os.path.exists(args.image_dir):
            logger.error(f"目录不存在: {args.image_dir}")
            return
            
        files = os.listdir(args.image_dir)
        logger.info(f"目录中的文件数量: {len(files)}")
        
        move_irrelevant_images(
            theme=args.theme,
            image_dir=args.image_dir,
            threshold=args.threshold
        )
    except Exception as e:
        logger.error(f"程序执行过程中出错: {e}")
        logger.exception(e)  # 这会打印完整的堆栈跟踪

if __name__ == "__main__":
    main()
