import sys
import os
import requests
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
from tavily import <PERSON>ly<PERSON>lient
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import urllib3
import re  
from functools import partial
import time 

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import TAVILY_API_KEY, IMAGE_DIR,TAVILY_RESULTS,TAVILY_REFERENCE_RESULTS
from modules.image_context import extract_context_text, optimize_context_text
from modules.image_preprocessing import normalize_and_enhance_image_url, is_valid_image, get_image_size
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

os.environ["OMP_NUM_THREADS"] = "1"

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Cache-Control": "max-age=0",
    "Referer": "https://www.google.com"
}

# 优化现有的 session 对象
retry_strategy = Retry(
    total=2,
    backoff_factor=0.1,
    status_forcelist=[429, 500, 502, 503, 504],
    allowed_methods=["HEAD", "GET", "OPTIONS"]
)
adapter = HTTPAdapter(max_retries=retry_strategy)
session = requests.Session()
session.mount("https://", adapter)
session.mount("http://", adapter)
session.max_redirects = 5  # 限制最大重定向次数
session.request = partial(session.request, timeout=10)  # 设置10秒超时

def is_valid_url(url):
    parsed = urlparse(url)
    return bool(parsed.netloc) and bool(parsed.scheme)

def clean_filename(filename):
    # Split the filename and extension
    name, ext = os.path.splitext(filename)
    # Remove unwanted characters but retain hyphens and underscores
    cleaned_name = re.sub(r'[^A-Za-z0-9-_]', '', name)[:40]
    # Combine the cleaned name with the original extension
    return cleaned_name + ext

def search_tavily(theme, retries=3, delay=30, wiki_only=False):
    client = TavilyClient(api_key=TAVILY_API_KEY)
    attempt = 0
    while attempt < retries:
        try:
            logger.info(f"Searching for theme: {theme}, attempt {attempt + 1}")
            search_params = {
                "query": theme,
                "search_depth": "advanced",
                "include_images": True,
                "include_raw_content": True,
                "max_results": TAVILY_RESULTS
            }
            
            if wiki_only:
                search_params["include_domains"] = ['wikipedia.org']
                search_params["max_results"] = TAVILY_REFERENCE_RESULTS
                
            response = client.search(**search_params)

            results = response.get('results', [])
            images = response.get('images', [])
            
            
            if results:
                for item in results:
                    logger.debug(f"Found result URL: {item.get('url')}")
                    #if 'image_url' in item:
                    #    logger.debug(f"Found image URL in result: {item['image_url']}")
                        
            return results, images
        except Exception as e:
            logger.error(f"Failed to search Tavily API: {e}", exc_info=False)
            attempt += 1
            if attempt < retries:
                logger.info(f"Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                logger.error(f"All {retries} attempts to search Tavily API failed.")
                return [], []

def extract_images_and_context_from_url(page_url, seen_urls):
    try:
        #logger.debug(f"Starting to extract images and context from {page_url}")
        response = session.get(page_url, headers=HEADERS, verify=False, timeout=10)  # 保持原有的超时设置
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')
        images = []

        meta_images = extract_image_urls_from_meta(soup, page_url)
        images.extend(meta_images)

        for img in soup.find_all('img'):
            img_url = img.get('src')
            if img_url:
                img_url = normalize_and_enhance_image_url(img_url, page_url)
                if img_url.lower().endswith('.svg'):
                    #logger.debug(f"Skipping SVG image: {img_url}")
                    continue
                if is_valid_image(img_url) and img_url not in seen_urls:
                    context = extract_context_text(img, soup, page_url)
                    context = optimize_context_text(context)  # Optimize context text
                    #logger.debug(f"Extracted image URL: {img_url} with context: {context}")
                    images.append({'url': img_url, 'context': context})
                    seen_urls.add(img_url)

        return images
    except requests.exceptions.RequestException as e:
        # 改进错误处理，不再假设 e.response 总是存在
        #logger.debug(f"Failed to extract images and context from {page_url}: {str(e)}", exc_info=False)
        return []  # 返回空列表，表示没有提取到图片
    except Exception as e:
        logger.error(f"An unexpected error occurred while extracting images and context from {page_url}: {e}", exc_info=False)
        return []  # 同样返回空列表

def extract_image_urls_from_meta(soup, base_url):
    images = []
    meta_tags = soup.find_all('meta', property='og:image') + \
                soup.find_all('meta', property='twitter:image') + \
                soup.find_all('meta', itemprop='image')
    for tag in meta_tags:
        img_url = tag.get('content')
        if img_url:
            img_url = normalize_and_enhance_image_url(img_url, base_url)
            if img_url.lower().endswith('.svg'):
                #logger.debug(f"Skipping SVG image: {img_url}")
                continue
            if is_valid_image(img_url):
                images.append({'url': img_url, 'context': f"Extracted from: {base_url}"})
            # else:
            #     logger.debug(f"Invalid meta image URL: {img_url}")
    return images

def ensure_url_scheme(url, base_url=None):
    parsed_url = urlparse(url)
    if not parsed_url.scheme:
        if url.startswith('//'):
            return f"https:{url}"
        if base_url:
            return urljoin(base_url, url)
        return f"https://{url}"
    return url

def save_image_and_context(image_data, theme):
    filtered_images = []
    seen_urls = set()

    for image in image_data:
        url = ensure_url_scheme(image['url'])
        
        if url in seen_urls or not is_valid_url(url):
            #logger.debug(f"Skipping invalid or duplicate URL: {url}")
            continue

        try:
            #logger.debug(f"Attempting to download image from {url}")
            response = session.get(url, headers=HEADERS, verify=False)
            response.raise_for_status()

            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                #logger.debug(f"Skipping non-image content type: {content_type} for URL: {url}")
                continue

            image_content = response.content
            size = get_image_size(image_content, url)
            if size is None or size[0] < 300 or size[1] < 300:
                #logger.debug(f"Skipping small or invalid image from {url}")
                continue

            context = image.get('context', '')
            image_filename = image.get('filename', os.path.basename(url.split('?')[0]))
            
            if not image_filename:
                #logger.debug(f"Image filename is empty for URL: {url}, skipping...")
                continue
            
            image_filename = clean_filename(image_filename)  # Clean the filename
            
            # Ensure the filename has a valid image extension
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            if not any(image_filename.lower().endswith(ext) for ext in valid_extensions):
                image_filename += '.jpg'  # Default to .jpg if no valid extension is found
            
            # Add 'cover_' prefix for images from initial `images` list
            if "Tavily search result" in context:
                image_filename = "cover_" + image_filename
                
            image_path = os.path.join(IMAGE_DIR.format(theme=theme), image_filename)
            #context_path = os.path.join(IMAGE_DIR.format(theme=theme), os.path.splitext(image_filename)[0] + '_context.txt')

            os.makedirs(os.path.dirname(image_path), exist_ok=True)
            with open(image_path, 'wb') as f:
                f.write(image_content)
            # if context:
            #     with open(context_path, 'w') as f:
            #         f.write(context)

            filtered_images.append({
                'url': url,
                'context': context,
                'filename': image_filename,
                'filepath': image_path
            })

            seen_urls.add(url)
            #logger.debug(f"Downloaded image from {url} to {image_path}")

        except requests.exceptions.RequestException as e:
            #logger.debug(f"Failed to download image from {url}: {str(e)}", exc_info=False)
            continue
        except Exception as e:
            #logger.debug(f"Unexpected error processing image from {url}: {str(e)}", exc_info=False)
            continue
    logger.info(f"Extracted {len(filtered_images)} images from Tavily search results of {theme} ")
    return filtered_images

def process_results(theme, results, images):
    all_images = []
    seen_urls = set()  # Track seen URLs to avoid duplicates

    for item in results:
        page_url = item.get('url', '')
        if page_url and page_url not in seen_urls:
            images_from_url = extract_images_and_context_from_url(page_url, seen_urls)
            all_images.extend(images_from_url)
            seen_urls.add(page_url)

    for img_url in images:
        if is_valid_image(img_url):
            high_res_img_url = normalize_and_enhance_image_url(img_url)
            if high_res_img_url not in seen_urls:
                all_images.append({'url': high_res_img_url, 'context': f"Extracted from: Tavily search result for topic '{theme}'"})
                seen_urls.add(high_res_img_url)

    filtered_images = save_image_and_context(all_images, theme)
    return filtered_images  # Return list of dicts with 'url', 'context', 'filename', and 'filepath'


def main():
    import argparse
    parser = argparse.ArgumentParser(description="Fetch and process Tavily API results for a given theme.")
    parser.add_argument("theme", type=str, help="The theme to search for")
    args = parser.parse_args()
    theme = args.theme
    results, images = search_tavily(theme)
    if results:
        filtered_images = process_results(theme, results, images)
        save_results(theme, filtered_images)
        logger.info(f"Successfully processed theme: {theme}")
    else:
        logger.error(f"No results found for theme: {theme}")

def save_results(theme, filtered_images):
    """
    Save the filtered images and their contexts to a JSON file for further processing or analysis.
    """
    result_path = os.path.join(IMAGE_DIR.format(theme=theme), f"{theme}_results.json")
    try:
        os.makedirs(os.path.dirname(result_path), exist_ok=True)
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(filtered_images, f, ensure_ascii=False, indent=4)
        logger.info(f"Saved results to {result_path}")
    except Exception as e:
        logger.error(f"Failed to save results for theme {theme}: {e}", exc_info=True)

if __name__ == "__main__":
    main()