import os
import logging
from typing import List, Dict, Any, <PERSON><PERSON>
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import json
import sys
import re

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.embedding_management import get_embedding_manager
from modules.langchain_interface import (
    call_llm_json_response,
    call_llm
)
from modules.langchain_interface import generate_embedding
from modules.text_optimizer import (
    smart_paragraph_split,
    deduplicate_texts
)
from modules.utils import (
    read_file_with_encoding,
    clean_text,
    split_into_chunks,
    calculate_token_count,
    write_temp_file,
    normalize_language
)
from modules.source_internet import (
    get_external_content,
    extract_keyword
)
from config import logger, TEXT_ANALYTICS_KEY, TEXT_ANALYTICS_ENDPOINT

# Azure Text Analytics
from azure.ai.textanalytics import TextAnalyticsClient
from azure.core.credentials import AzureKeyCredential

AZURE_ENTITY_LINKING_BATCH_SIZE = 5  # Max 5 documents per call for Azure Text Analytics
AZURE_CORRECTION_THRESHOLD = 0.9  # 如果Azure翻译结果与原始结果差异超过(1-0.9)，则认为需要修正

logger = logging.getLogger(__name__)

# 辅助函数：按段落或近似字符数拆分文本
def split_text_blocks(text: str, max_chars: int = 4800) -> List[str]: # Azure限制为5120，留一些余量
    """
    按段落或近似字符数拆分，避免超长。
    """
    if not text:
        return []
    paragraphs = text.split("\\n\\n")
    blocks = []
    current_block = ""
    for p in paragraphs:
        if len(current_block) + len(p) + 2 < max_chars: # +2 for \\n\\n
            current_block = (current_block + "\\n\\n" + p).strip()
        else:
            if current_block: # Append the block if it's not empty
                blocks.append(current_block)
            # If the paragraph itself is too long, it becomes a block.
            # Further splitting for extremely long paragraphs might be needed if they exceed API limits.
            if len(p) >= max_chars:
                # Simple split for oversized paragraphs, could be improved
                for i in range(0, len(p), max_chars):
                    blocks.append(p[i:i+max_chars])
                current_block = "" # Reset current_block
            else:
                current_block = p
    
    if current_block: # Add the last block
        blocks.append(current_block)
    return [block for block in blocks if block] # Ensure no empty blocks are returned

# 辅助函数：调用API，生成实体映射
def build_entity_map(blocks: List[str], client: TextAnalyticsClient, language: str) -> Dict[str, str]:
    """
    调用 API，生成 {原译名: 标准化名称} 映射。
    """
    entity_map = {}
    # Azure 同次调用最多支持 5 个文档 (documents)
    for i in range(0, len(blocks), 5):
        batch = blocks[i:i+5]
        try:
            response = client.recognize_linked_entities(documents=batch, language=language)
            for doc in response:
                if not doc.is_error:
                    for ent in doc.entities: # ent is a LinkedEntity
                        # 如果同一个原译名映射到多个标准名，可根据置信度或上下文再筛选
                        # 当前简单处理：后者覆盖前者
                        if ent.name: # ent.name is the standardized name
                            for match in ent.matches: # match is a LinkedEntityMatch
                                if match.text: # match.text is the original text in the document
                                    # 使用 match.text 作为键 (原始文本中的写法)
                                    # 使用 ent.name 作为值 (标准化的名称)
                                    entity_map[match.text] = ent.name
                else:
                    logger.error(f"Azure Text Analytics: 文档 {doc.id} 处理出错: {doc.error.code} - {doc.error.message}")
        except Exception as e:
            logger.error(f"Azure Text Analytics:调用 recognize_linked_entities API 时出错: {str(e)}")
    return entity_map

# 辅助函数：精确替换所有实体提及
def apply_corrections(text: str, entity_map: Dict[str, str]) -> str:
    """
    精确替换所有实体提及。使用 word boundary 避免误替换。
    """
    if not entity_map:
        return text

    # 按长度对key进行排序（降序），以优先匹配更长的实体名称
    # 例如，如果 entity_map 有 "New York City" 和 "New York"，我们希望先匹配 "New York City"
    sorted_keys = sorted(entity_map.keys(), key=len, reverse=True)

    def repl(match):
        orig = match.group(0)
        # 使用原始匹配到的文本作为key从entity_map获取标准名
        # 因为 sorted_keys 中的每个 key 都是一个独立的词或短语
        return entity_map.get(orig, orig)

    # 构造正则：\\b(Name1|Name2|...)\\b
    # 使用 re.escape 来处理实体名称中可能存在的特殊正则字符
    # 确保只匹配完整的单词/短语
    try:
        # 过滤掉空字符串的key，以避免无效的正则表达式模式
        valid_keys = [key for key in sorted_keys if key]
        if not valid_keys:
            return text # 如果没有有效的key，则不进行替换
        pattern = r"\\b(" + "|".join(map(re.escape, valid_keys)) + r")\\b"
        corrected_text = re.sub(pattern, repl, text)
        return corrected_text
    except re.error as e:
        logger.error(f"创建正则表达式时出错: {e}. Entity keys: {valid_keys}")
        return text # 正则表达式错误时返回原文本

# 重构的事实检查（实体链接校正）流程
def fact_check_script_pipeline(
    translated_text: str,
    language: str = "en"  # 默认语言为英语
) -> str:
    """
    使用 Azure 实体链接服务校正翻译后文本中的实体名称。
    
    Args:
        translated_text: 翻译后的脚本内容。
        language: 文本的语言代码 (例如 "en", "zh-hans")。
                 Azure 支持的语言列表: https://learn.microsoft.com/azure/ai-services/language-service/entity-linking/language-support
                 
    Returns:
        str: 校正后的脚本内容。
    """
    logger.info(f"开始使用 Azure 实体链接对文本进行实体名称校正。语言: {language}")

    if not TEXT_ANALYTICS_KEY or not TEXT_ANALYTICS_ENDPOINT:
        logger.warning("Azure Text Analytics Key 或 Endpoint 未配置。跳过实体链接校正。")
        return translated_text

    if not translated_text or not translated_text.strip():
        logger.info("输入文本为空，跳过实体链接校正。")
        return translated_text
        
    # Azure API 要求语言代码，我们使用 normalize_language 确保格式正确
    # 注意：Azure 可能有自己的一套语言代码，需要确保映射正确
    # 例如，Azure 可能用 "zh-Hans" 代表简体中文，而我们内部用 "zh"
    # 此处假设 normalize_language 返回的格式是 Azure 兼容的，或在此处进行转换
    try:
        # 示例中 Azure 使用 "en", "es" 等。
        # 我们项目中的 normalize_language 可能返回 "english", "spanish"。
        # 需要一个映射或调整 normalize_language
        # 暂时直接使用传入的 language 参数，并依赖调用者提供 Azure 兼容的格式
        # 或者，我们可以基于 Azure 文档维护一个映射
        # https://learn.microsoft.com/en-us/azure/ai-services/language-service/language-support?tabs=data-classification
        # 简体中文: zh-Hans, 繁体中文: zh-Hant
        # 为了简单起见，我们先假设传入的 language 是 Azure 可接受的。
        # 更好的做法是进行校验和转换。
        azure_language_code = language # 假设传入的是 "en", "zh-Hans" 等
        logger.info(f"规范化后的语言代码 (供 Azure 使用): {azure_language_code}")

    except ValueError as e:
        logger.warning(f"语言代码处理失败: {e}。将尝试使用原始语言代码 '{language}'.")
        azure_language_code = language


    try:
        client = TextAnalyticsClient(
            endpoint=TEXT_ANALYTICS_ENDPOINT,
            credential=AzureKeyCredential(TEXT_ANALYTICS_KEY)
        )
    except Exception as e:
        logger.error(f"初始化 Azure TextAnalyticsClient 失败: {str(e)}")
        return translated_text

    logger.info("拆分文本块...")
    blocks = split_text_blocks(translated_text)
    if not blocks:
        logger.info("文本拆分后为空，无需处理。")
        return translated_text
    
    logger.info(f"文本被拆分为 {len(blocks)} 个块。开始构建实体映射...")
    entity_map = build_entity_map(blocks, client, azure_language_code)
    # Log detailed Azure corrections
    if logger.isEnabledFor(logging.DEBUG):
        for orig, std in entity_map.items():
            logger.debug(f"Azure correction mapping: '{orig}' -> '{std}'")
    
    if not entity_map:
        logger.info("未能从 Azure API 获取任何实体映射。返回原始文本。")
        return translated_text
        
    logger.info(f"实体映射构建完成，共找到 {len(entity_map)} 个唯一实体。开始应用校正...")
    # 打印部分entity_map以供调试
    # count = 0
    # for k, v in entity_map.items():
    #    logger.debug(f"Map: '{k}' -> '{v}'")
    #    count +=1
    #    if count >10:
    #        break

    corrected_text = apply_corrections(translated_text, entity_map)
    
    logger.info("实体名称校正完成。")
    
    # 比较原文和校正后文本的差异
    # if translated_text != corrected_text:
    #    logger.info("检测到文本更改。")
    #    # 可以添加更详细的差异对比日志，如果需要
    # else:
    #    logger.info("校正后文本与原文无差异。")

    return corrected_text


def fact_check_json_segments_pipeline(
    segments_to_el: List[Dict[str, Any]], 
    azure_language_code: str
) -> List[Dict[str, Any]]:
    """
    Performs Azure Entity Linking on a list of translated segments.
    Each segment in segments_to_el should have at least 'segment_id' and 'translated_text'.
    Adds 'el_corrected_text' to each segment dictionary.
    """
    logger.info(f"开始对 {len(segments_to_el)} 个JSON段落进行 Azure 实体链接校正。语言: {azure_language_code}")

    # Only support English and Spanish for entity linking
    if azure_language_code.lower() not in ('en', 'es'):
        logger.warning(f"Language {azure_language_code} not supported for entity linking. Skipping EL pipeline.")
        for segment in segments_to_el:
            segment['el_corrected_text'] = segment.get('translated_text', '')
        return segments_to_el

    if not TEXT_ANALYTICS_KEY or not TEXT_ANALYTICS_ENDPOINT:
        logger.warning("Azure Text Analytics Key 或 Endpoint 未配置。跳过实体链接校正。")
        for segment in segments_to_el:
            segment['el_corrected_text'] = segment.get('translated_text', '')
        return segments_to_el

    if not segments_to_el:
        logger.info("输入段落列表为空，跳过实体链接校正。")
        return []

    try:
        text_analytics_client = TextAnalyticsClient(
            endpoint=TEXT_ANALYTICS_ENDPOINT,
            credential=AzureKeyCredential(TEXT_ANALYTICS_KEY)
        )
    except Exception as e:
        logger.error(f"初始化 Azure TextAnalyticsClient 失败: {str(e)}")
        for segment in segments_to_el:
            segment['el_corrected_text'] = segment.get('translated_text', '')
        return segments_to_el

    el_corrected_json_segments = []
    
    for i in range(0, len(segments_to_el), AZURE_ENTITY_LINKING_BATCH_SIZE):
        batch_of_segments = segments_to_el[i:i + AZURE_ENTITY_LINKING_BATCH_SIZE]
        
        azure_input_docs = []
        segment_map_for_batch = {} # To map azure doc id back to original segment

        for idx, segment_data in enumerate(batch_of_segments):
            segment_id_str = str(segment_data['segment_id']) # Azure ID must be string
            segment_map_for_batch[segment_id_str] = segment_data
            translated_text = segment_data.get('translated_text', '')
            if translated_text and translated_text.strip(): # Only process if there's text
                 azure_input_docs.append({
                    "id": segment_id_str,
                    "text": translated_text,
                    "language": azure_language_code
                })
            else: # If no text, carry forward and set el_corrected_text to empty
                segment_data['el_corrected_text'] = ''
                el_corrected_json_segments.append(segment_data)

        # 如果没有文档，跳过当前批次
        if not azure_input_docs:
            logger.warning("当前批次没有生成任何文档，跳过。")
            continue

        try:
            num_original_segments_in_this_azure_batch = 0
            if azure_input_docs:
                for doc_to_send in azure_input_docs:
                    doc_id_to_send = doc_to_send['id']
                    original_segments_for_this_doc = segment_map_for_batch.get(doc_id_to_send, [])
                    num_original_segments_in_this_azure_batch += len(original_segments_for_this_doc)
                
                logger.debug(f"向 Azure 发送 {len(azure_input_docs)} 个合并文档进行实体链接，这些文档共包含 {num_original_segments_in_this_azure_batch} 个原始 JSON 段落。")
            else:
                logger.debug("Azure 实体链接批次中没有文档可发送。") # Add a log for empty azure_input_docs case
            
            azure_response = text_analytics_client.recognize_linked_entities(documents=azure_input_docs)

            processed_segment_ids_in_batch = set()

            for doc_result in azure_response:
                original_segment_data = segment_map_for_batch.get(doc_result.id)
                if not original_segment_data:
                    logger.error(f"无法从 Azure 响应ID '{doc_result.id}' 映射回原始段落。")
                    continue
                
                processed_segment_ids_in_batch.add(original_segment_data['segment_id'])
                current_translated_text = original_segment_data.get('translated_text', '')

                if doc_result.is_error:
                    logger.error(f"Azure实体链接错误 (段落ID {original_segment_data['segment_id']}): {doc_result.error.code} - {doc_result.error.message}")
                    original_segment_data['el_corrected_text'] = current_translated_text
                else:
                    entity_map_for_segment = {}
                    for entity in doc_result.entities:
                        for match in entity.matches:
                            entity_map_for_segment[match.text] = entity.name
                    
                    if entity_map_for_segment:
                        corrected_text_for_segment = apply_corrections(current_translated_text, entity_map_for_segment)
                        
                        # Check for significant reduction
                        original_len = len(current_translated_text)
                        corrected_len = len(corrected_text_for_segment)
                        if corrected_len < original_len * AZURE_CORRECTION_THRESHOLD and original_len > 0:
                            logger.warning(f"Azure实体链接校正后的文本长度 ({corrected_len}) "
                                           f"显著小于原始翻译文本长度 ({original_len}) for segment {original_segment_data['segment_id']}. "
                                           "将使用原始翻译文本。")
                            original_segment_data['el_corrected_text'] = current_translated_text
                        else:
                            original_segment_data['el_corrected_text'] = corrected_text_for_segment
                            logger.info(f"段落 {original_segment_data['segment_id']} 的实体链接校正应用成功。")
                    else:
                        original_segment_data['el_corrected_text'] = current_translated_text # No entities found or no corrections made
                
                el_corrected_json_segments.append(original_segment_data)

            # Handle segments in batch that might not have been processed by Azure (e.g. if azure_input_docs was filtered)
            # This should ideally be caught by the initial check for empty text
            for seg_data_in_batch in batch_of_segments:
                if seg_data_in_batch['segment_id'] not in processed_segment_ids_in_batch and 'el_corrected_text' not in seg_data_in_batch:
                    logger.warning(f"Segment {seg_data_in_batch['segment_id']} was in batch but not in Azure response and not pre-handled. Using translated text.")
                    seg_data_in_batch['el_corrected_text'] = seg_data_in_batch.get('translated_text', '')
                    el_corrected_json_segments.append(seg_data_in_batch)


        except Exception as e:
            logger.error(f"Azure实体链接API调用失败 (批次 {i // AZURE_ENTITY_LINKING_BATCH_SIZE + 1}): {str(e)}")
            for segment_data in batch_of_segments: # Fallback for the entire batch
                if 'el_corrected_text' not in segment_data: # If not already handled (e.g. empty text)
                    segment_data['el_corrected_text'] = segment_data.get('translated_text', '')
                    el_corrected_json_segments.append(segment_data)
    
    # Ensure all original segments are accounted for, even if errors occurred.
    # Sort by original order if necessary, though appending should maintain it if batching is sequential.
    # This part might be complex if segments were skipped. A safer way is to update a copy of segments_to_el.
    
    # Create a dictionary for quick lookup
    output_map = {seg['segment_id']: seg for seg in el_corrected_json_segments}
    final_output_list = []
    for original_segment in segments_to_el:
        sid = original_segment['segment_id']
        if sid in output_map:
            final_output_list.append(output_map[sid])
        else: # Should not happen if logic is correct, but as a safeguard
            logger.error(f"Segment {sid} was lost during EL pipeline. Adding with original translated text.")
            original_segment['el_corrected_text'] = original_segment.get('translated_text', '')
            final_output_list.append(original_segment)
            
    return final_output_list


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="校对翻译后的脚本文件")
    parser.add_argument("translated_script_file", help="翻译后的脚本文件路径")
    parser.add_argument("--lang", default="en", help="脚本语言 (en/zh/ja 等)")
    parser.add_argument("--threshold", type=float, default=0.5, help="相似度阈值")
    parser.add_argument("--chunk_size", type=int, default=FACT_CHECK_TRUNK_TOKENS, help="每块最大token数")
    
    args = parser.parse_args()
    
    try:
        # 读取脚本文件
        script_content = read_file_with_encoding(args.translated_script_file)
        
        # 调用处理函数
        processed_content = fact_check_script_pipeline(
            translated_text=script_content,
            language=args.lang
        )
        
        # 保存处理后的内容
        output_file = os.path.splitext(args.translated_script_file)[0] + "_factchecked.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(processed_content)
            
        logger.info(f"校对完成，输出文件: {output_file}")
            
    except Exception as e:
        logger.error(f"处理文件时发生错误: {str(e)}")