import argparse
import subprocess
import os
import sys
import json
import tempfile
from config import logger

def get_video_info(video_path):
    """Get video information using ffprobe."""
    cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', video_path]
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    return json.loads(result.stdout)

def concatenate_videos(video_files: list, output_path: str):
    """
    Concatenates multiple video files into one using the concat demuxer method.
    This method is fast and avoids re-encoding when the input videos have the same codec.
    """
    logger.info(f"开始合并 {len(video_files)} 个视频片段")
    
    # 验证所有输入文件
    for i, video in enumerate(video_files, 1):
        logger.debug(f"验证视频文件 {i}/{len(video_files)}: {video}")
        if not os.path.exists(video):
            raise FileNotFoundError(f"视频文件不存在: {video}")
            
        # 获取视频信息验证文件是否有效
        try:
            info = get_video_info(video)
            if not info.get('streams'):
                raise ValueError(f"无效的视频文件: {video}")
            logger.debug(f"视频 {i} 信息: {json.dumps(info, indent=2)}")
        except Exception as e:
            logger.error(f"验证视频文件失败 {video}: {str(e)}")
            raise
    
    # 创建临时文件来存储视频文件列表
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False) as temp_file:
        for video in video_files:
            # 使用规范化的绝对路径
            abs_path = os.path.abspath(video)
            temp_file.write(f"file '{abs_path}'\n")
        temp_file_path = temp_file.name
        
    logger.debug(f"创建的临时文件列表内容:")
    with open(temp_file_path, 'r') as f:
        logger.debug(f.read())

    # 构建 FFmpeg 命令
    ffmpeg_command = [
        'ffmpeg',
        '-y',                # 覆盖输出文件
        '-f', 'concat',      # 使用concat格式
        '-safe', '0',        # 允许绝对路径
        '-i', temp_file_path,# 输入文件列表
        '-c', 'copy',        # 直接复制流
        '-vsync', '2',       # 处理时间戳
        output_path
    ]
    
    logger.debug(f"FFmpeg命令: {' '.join(ffmpeg_command)}")
    
    try:
        # 执行 FFmpeg 命令
        result = subprocess.run(
            ffmpeg_command, 
            check=True, 
            stderr=subprocess.PIPE, 
            stdout=subprocess.PIPE,
            universal_newlines=True
        )
        logger.debug(f"FFmpeg输出: {result.stdout}")
        logger.debug(f"FFmpeg错误: {result.stderr}")
        logger.info(f"成功创建最终视频: {output_path}")
        
        # 验证输出文件
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"输出文件未创建: {output_path}")
            
        # 验证输出视频是否有效
        output_info = get_video_info(output_path)
        if not output_info.get('streams'):
            raise ValueError(f"生成的视频文件无效: {output_path}")
            
    except subprocess.CalledProcessError as e:
        logger.error(f"执行FFmpeg命令时出错: {e}")
        logger.error(f"FFmpeg错误输出: {e.stderr}")
        raise
    finally:
        # 删除临时文件
        try:
            os.unlink(temp_file_path)
            logger.debug(f"已删除临时文件: {temp_file_path}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")

def concatenate_videos_encode(video_files: list, output_path: str):
    """
    Concatenates multiple video files into one.
    """
    logger.info(f"Starting concatenation process for {len(video_files)} videos")
    with open('temp_file_list.txt', 'w') as f:
        for video in video_files:
            f.write(f"file '{video}'\n")
    
    ffmpeg_command = [
        'ffmpeg',
        '-loglevel', 'error',  # Suppress ffmpeg output
        '-f', 'concat',
        '-safe', '0',
        '-i', 'temp_file_list.txt',
        '-c:v', 'libx264',  # Re-encode video
        '-c:a', 'aac',      # Re-encode audio
        '-strict', 'experimental',
        '-y',
        output_path
    ]
    try:
        subprocess.run(ffmpeg_command, check=True)
        logger.info(f"Successfully created final video: {output_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error occurred while concatenating videos: {e}")
        sys.exit(1)
    finally:
        if os.path.exists('temp_file_list.txt'):
            os.remove('temp_file_list.txt')
