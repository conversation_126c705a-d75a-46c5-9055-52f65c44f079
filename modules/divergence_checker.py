# modules/divergence_checker.py
"""
漂移检查器模块

主要功能：
1. 计算剧本事件与spine事件的漂移分数
2. 动态阈值计算
3. SQLite向量缓存管理
4. Yellow队列处理
"""

import os
import sqlite3
import json
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from config import logger, ANIMATION_TEMP_DIR
from modules.embedding_management import get_embedding_manager


class VectorCache:
    """向量缓存管理器"""
    
    def __init__(self, cache_path: str = None):
        if cache_path is None:
            cache_path = os.path.join(ANIMATION_TEMP_DIR, "vector_cache.sqlite")
        self.cache_path = cache_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        os.makedirs(os.path.dirname(self.cache_path), exist_ok=True)
        
        with sqlite3.connect(self.cache_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS event_vectors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_text TEXT UNIQUE,
                    vector BLOB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_event_text ON event_vectors(event_text)
            """)
    
    def get_vector(self, event_text: str) -> Optional[np.ndarray]:
        """获取缓存的向量"""
        try:
            with sqlite3.connect(self.cache_path) as conn:
                cursor = conn.execute(
                    "SELECT vector FROM event_vectors WHERE event_text = ?",
                    (event_text,)
                )
                result = cursor.fetchone()
                if result:
                    return np.frombuffer(result[0], dtype=np.float32)
                return None
        except Exception as e:
            logger.error(f"获取缓存向量时出错: {str(e)}")
            return None
    
    def set_vector(self, event_text: str, vector: np.ndarray):
        """缓存向量"""
        try:
            vector_bytes = vector.astype(np.float32).tobytes()
            with sqlite3.connect(self.cache_path) as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO event_vectors (event_text, vector) VALUES (?, ?)",
                    (event_text, vector_bytes)
                )
        except Exception as e:
            logger.error(f"缓存向量时出错: {str(e)}")
    
    def get_batch_vectors(self, event_texts: List[str]) -> Dict[str, np.ndarray]:
        """批量获取向量"""
        vectors = {}
        missing_texts = []
        
        # 先从缓存获取
        for text in event_texts:
            vector = self.get_vector(text)
            if vector is not None:
                vectors[text] = vector
            else:
                missing_texts.append(text)
        
        # 为缺失的文本生成向量
        if missing_texts:
            try:
                embedding_manager = get_embedding_manager()
                new_vectors = embedding_manager.get_batch_embeddings(missing_texts)
                
                for text, vector in zip(missing_texts, new_vectors):
                    self.set_vector(text, vector)
                    vectors[text] = vector
                    
            except Exception as e:
                logger.error(f"批量生成向量时出错: {str(e)}")
        
        return vectors


def divergence_score(script_events: List[str], spine_events: List[Dict[str, Any]]) -> float:
    """
    计算剧本事件与spine事件的漂移分数
    
    Args:
        script_events: 从剧本中提取的事件列表
        spine_events: spine事件列表
        
    Returns:
        float: 漂移分数 (0-1，越高表示漂移越大)
    """
    try:
        if not script_events or not spine_events:
            logger.warning("事件列表为空，返回高漂移分数")
            return 1.0
        
        # 提取spine事件文本
        spine_texts = [event.get("event", "") for event in spine_events if event.get("event")]
        if not spine_texts:
            logger.warning("spine事件文本为空，返回高漂移分数")
            return 1.0
        
        # 使用向量缓存
        cache = VectorCache()
        
        # 获取所有事件的向量
        all_texts = script_events + spine_texts
        vectors = cache.get_batch_vectors(all_texts)
        
        if len(vectors) < len(all_texts):
            logger.warning("部分事件向量获取失败")
            return 0.8  # 返回较高的漂移分数
        
        # 分离script和spine向量
        script_vectors = [vectors[text] for text in script_events if text in vectors]
        spine_vectors = [vectors[text] for text in spine_texts if text in vectors]
        
        if not script_vectors or not spine_vectors:
            return 1.0
        
        # 计算相似度矩阵
        similarities = []
        for script_vec in script_vectors:
            max_sim = 0.0
            for spine_vec in spine_vectors:
                sim = np.dot(script_vec, spine_vec) / (
                    np.linalg.norm(script_vec) * np.linalg.norm(spine_vec)
                )
                max_sim = max(max_sim, sim)
            similarities.append(max_sim)
        
        # 计算平均相似度
        avg_similarity = np.mean(similarities)
        
        # 漂移分数 = 1 - 平均相似度
        divergence = 1.0 - avg_similarity
        
        logger.info(f"漂移分数计算完成: {divergence:.3f} (平均相似度: {avg_similarity:.3f})")
        return max(0.0, min(1.0, divergence))  # 确保在[0,1]范围内
        
    except Exception as e:
        logger.error(f"计算漂移分数时出错: {str(e)}")
        return 0.8  # 返回较高的漂移分数作为安全值


def compute_dynamic_threshold(score_history: List[float]) -> float:
    """
    计算动态阈值
    
    Args:
        score_history: 历史漂移分数列表
        
    Returns:
        float: 动态阈值
    """
    try:
        if not score_history:
            return 0.5  # 默认阈值
        
        if len(score_history) < 3:
            return 0.5  # 样本太少，使用默认阈值
        
        scores = np.array(score_history)
        
        # 计算中位数和IQR
        median = np.median(scores)
        q75 = np.percentile(scores, 75)
        q25 = np.percentile(scores, 25)
        iqr = q75 - q25
        
        # 动态阈值 = median + 1.5 * IQR
        threshold = median + 1.5 * iqr
        
        # 限制在[0.45, 0.55]范围内
        threshold = max(0.45, min(0.55, threshold))
        
        logger.info(f"动态阈值计算: median={median:.3f}, IQR={iqr:.3f}, threshold={threshold:.3f}")
        return threshold
        
    except Exception as e:
        logger.error(f"计算动态阈值时出错: {str(e)}")
        return 0.5


def generate_yellow_report(
    episode_number: int,
    divergence_score: float,
    threshold: float,
    script_events: List[str],
    spine_events: List[Dict[str, Any]],
    output_dir: str = None
) -> str:
    """
    生成Yellow队列报告
    
    Args:
        episode_number: 集数
        divergence_score: 漂移分数
        threshold: 阈值
        script_events: 剧本事件
        spine_events: spine事件
        output_dir: 输出目录
        
    Returns:
        str: 报告文件路径
    """
    try:
        if output_dir is None:
            output_dir = ANIMATION_TEMP_DIR
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成报告内容
        report = {
            "episode": episode_number,
            "timestamp": datetime.now().isoformat(),
            "divergence_score": divergence_score,
            "threshold": threshold,
            "severity": "HIGH" if divergence_score > threshold + 0.1 else "MEDIUM",
            "missing_events": _find_missing_events(script_events, spine_events),
            "contradicted_events": _find_contradicted_events(script_events, spine_events),
            "suggest_fix": _generate_fix_suggestions(script_events, spine_events),
            "raw_data": {
                "script_events": script_events,
                "spine_events": [event.get("event", "") for event in spine_events]
            }
        }
        
        # 保存报告
        filename = f"YELLOW_ep{episode_number:02d}.md"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Yellow Queue Report - Episode {episode_number}\n\n")
            f.write(f"**Generated:** {report['timestamp']}\n")
            f.write(f"**Divergence Score:** {divergence_score:.3f}\n")
            f.write(f"**Threshold:** {threshold:.3f}\n")
            f.write(f"**Severity:** {report['severity']}\n\n")
            
            f.write("## Missing Events\n")
            for event in report['missing_events']:
                f.write(f"- {event}\n")
            f.write("\n")
            
            f.write("## Contradicted Events\n")
            for event in report['contradicted_events']:
                f.write(f"- {event}\n")
            f.write("\n")
            
            f.write("## Suggested Fixes\n")
            for fix in report['suggest_fix']:
                f.write(f"- {fix}\n")
            f.write("\n")
            
            f.write("## Raw Data\n")
            f.write("```json\n")
            f.write(json.dumps(report['raw_data'], ensure_ascii=False, indent=2))
            f.write("\n```\n")
        
        logger.info(f"Yellow报告已生成: {filepath}")
        return filepath
        
    except Exception as e:
        logger.error(f"生成Yellow报告时出错: {str(e)}")
        return ""


def _find_missing_events(script_events: List[str], spine_events: List[Dict[str, Any]]) -> List[str]:
    """查找缺失的事件"""
    spine_texts = [event.get("event", "") for event in spine_events]
    # 简化版本：返回spine中但script中相似度低的事件
    return spine_texts[:3]  # 暂时返回前3个作为示例


def _find_contradicted_events(script_events: List[str], spine_events: List[Dict[str, Any]]) -> List[str]:
    """查找矛盾的事件"""
    # 简化版本：返回可能矛盾的事件
    return script_events[:2] if len(script_events) > 5 else []


def _generate_fix_suggestions(script_events: List[str], spine_events: List[Dict[str, Any]]) -> List[str]:
    """生成修复建议"""
    suggestions = [
        "检查剧本是否包含所有关键spine事件",
        "确保事件的因果关系与原文一致",
        "调整剧本以更好地反映原文的情节发展"
    ]
    return suggestions
