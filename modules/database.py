import sqlite3
import os
import numpy as np
import uuid
from typing import List, Tuple, Optional, Dict
import logging
from config import logger
from modules.langchain_interface import generate_embedding

logger = logging.getLogger(__name__)

def connect_sqlite(db_path):
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    conn = sqlite3.connect(db_path)
    return conn

def create_tables(conn):
    cursor = conn.cursor()
    cursor.execute('''CREATE TABLE IF NOT EXISTS raw_content (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        theme TEXT,
                        url TEXT,
                        raw_content TEXT
                      )''')
    cursor.execute('''CREATE TABLE IF NOT EXISTS images (
                        id TEXT PRIMARY KEY,
                        theme TEXT,
                        url TEXT,
                        image_path TEXT
                      )''')
    cursor.execute('''CREATE TABLE IF NOT EXISTS embeddings (
                        id TEXT PRIMARY KEY,
                        theme TEXT,
                        url TEXT UNIQUE,
                        filepath TEXT UNIQUE NOT NULL,
                        embedding BLOB
                      )''')
    conn.commit()

def insert_raw_content(conn, theme, url, raw_content):
    cursor = conn.cursor()
    cursor.execute('''INSERT INTO raw_content (theme, url, raw_content) VALUES (?, ?, ?)''',
                    (theme, url, raw_content))
    conn.commit()

def insert_images(conn, theme, images):
    cursor = conn.cursor()
    for unique_id, url, path in images:
        cursor.execute('''SELECT COUNT(*) FROM images WHERE id=?''', (unique_id,))
        if cursor.fetchone()[0] == 0:  # Only insert if not already present
            cursor.execute('''INSERT INTO images (id, theme, url, image_path) VALUES (?, ?, ?, ?)''',
                           (unique_id, theme, url, path))
    conn.commit()

def store_embedding(conn, theme: str, url: str, filepath: str, embedding: np.ndarray):
    cursor = conn.cursor()
    embedding_array = np.array(embedding, dtype=np.float32)
    unique_id = str(uuid.uuid4())
    
    cursor.execute('''INSERT INTO embeddings (id, theme, url, filepath, embedding) 
                     VALUES (?, ?, ?, ?, ?)''',
                   (unique_id, theme, url, filepath, embedding_array.tobytes()))
    conn.commit()
    conn.commit()

def get_embeddings_by_theme(conn, theme):
    cursor = conn.cursor()
    cursor.execute('''
        SELECT e.image_url, i.image_path, e.embedding
        FROM embeddings e
        JOIN images i ON e.image_url = i.url
        WHERE e.theme=?
    ''', (theme,))
    data = cursor.fetchall()
    embeddings = [(url, path, np.frombuffer(embed, dtype=np.float32)) for url, path, embed in data]
    # for emb in embeddings:
    #     print(f"URL: {emb[0]}, Path: {emb[1]}, Embedding: {emb[2][:5]}...")  # Print first 5 elements for brevity
    return embeddings

def get_embeddings_for_images(conn: sqlite3.Connection, images_dir: str, valid_images: List[str], debug: bool = True) -> Dict[str, np.ndarray]:
    """
    获取或生成图片的 embeddings
    
    Args:
        conn: 数据库连接
        images_dir: 图片目录
        valid_images: 有效图片列表
        debug: 是否打印调试信息
    
    Returns:
        Dict[str, np.ndarray]: 图片路径到 embedding 的映射
    """
    embeddings_dict = {}
    missing_images = []
    
    # 遍历所有有效图片
    for image_path in valid_images:
        # 尝试从数据库获取 embedding
        embedding = get_embedding_by_filepath(conn, image_path)
        
        if embedding is not None:
            embeddings_dict[image_path] = embedding
        else:
            missing_images.append(image_path)
    

    logger.debug(f"从数据库找到 {len(embeddings_dict)} 个 embeddings")
    logger.debug(f"需要生成 {len(missing_images)} 个新的 embeddings")
    
    # 为缺失的图片生成 embedding
    if missing_images:
        for img_path in missing_images:
            try:
                # 生成新的 embedding
                embedding = generate_embedding(img_path)
                if embedding is not None:
                    # 存储到数据库
                    insert_or_update_embedding(
                        conn=conn,
                        theme='auto_generated',
                        url=None,
                        filepath=img_path,
                        embedding=embedding
                    )
                    embeddings_dict[img_path] = embedding
                    if debug:
                        logger.info(f"成功生成并存储 embedding: {img_path}")
                else:
                    logger.warning(f"无法为图片生成 embedding: {img_path}")
            except Exception as e:
                logger.error(f"处理图片时出错 {img_path}: {str(e)}")
                continue
    
    if debug:
        logger.info(f"最终获取到 {len(embeddings_dict)} 个有效的 embeddings")
        
    return embeddings_dict

def get_embedding_by_url(conn: sqlite3.Connection, url: str) -> Optional[np.ndarray]:
    """
    通过 URL 获取 embedding。

    Args:
        conn (sqlite3.Connection): 数据库连接对象。
        url (str): 图片的 URL。

    Returns:
        Optional[np.ndarray]: 如果找到，返回 embedding，否则返回 None。
    """
    cursor = conn.cursor()
    cursor.execute('SELECT embedding FROM embeddings WHERE url = ?', (url,))
    result = cursor.fetchone()
    if result:
        embedding = np.frombuffer(result[0], dtype=np.float32)
        logger.debug(f"通过 URL 获取 embedding 成功: URL={url}")
        return embedding
    logger.debug(f"通过 URL 未找到 embedding: URL={url}")
    return None

def get_embedding_by_filepath(conn: sqlite3.Connection, filepath: str) -> Optional[np.ndarray]:
    """
    通过 filepath 获取 embedding。

    Args:
        conn (sqlite3.Connection): 数据库连接对象。
        filepath (str): 图片的文件路径。

    Returns:
        Optional[np.ndarray]: 如果找到，返回 embedding，否则返回 None。
    """
    cursor = conn.cursor()
    cursor.execute('SELECT embedding FROM embeddings WHERE filepath = ?', (filepath,))
    result = cursor.fetchone()
    if result:
        embedding = np.frombuffer(result[0], dtype=np.float32)
        logger.debug(f"通过 filepath 获取 embedding 成功: filepath={filepath}")
        return embedding
    logger.debug(f"通过 filepath 未找到 embedding: filepath={filepath}")
    return None

def insert_or_update_embedding(conn: sqlite3.Connection, theme: str, url: Optional[str], filepath: str, embedding: np.ndarray):
    """
    插入新的 embedding 或根据 URL 更新现有记录的 filepath。

    Args:
        conn (sqlite3.Connection): 数据库连接对象。
        theme (str): 主题名称。
        url (Optional[str]): 图片的 URL，可以为空。
        filepath (str): 图片的文件路径，不能为空。
        embedding (np.ndarray): 图片的嵌入向量。
    """
    cursor = conn.cursor()
    unique_id = str(uuid.uuid4())
    embedding_blob = embedding.astype(np.float32).tobytes()

    if url:
        # 尝试通过 URL 获取已有的 embedding
        cursor.execute('SELECT id FROM embeddings WHERE url = ?', (url,))
        result = cursor.fetchone()
        if result:
            # 如果存在，通过 URL 更新 filepath
            existing_id = result[0]
            cursor.execute('''
                UPDATE embeddings
                SET filepath = ?, embedding = ?
                WHERE id = ?
            ''', (filepath, embedding_blob, existing_id))
            logger.info(f"通过 URL 更新 embedding: URL={url}, filepath={filepath}")
        else:
            # 如果不存在，插入新的记录
            cursor.execute('''
                INSERT INTO embeddings (id, theme, url, filepath, embedding)
                VALUES (?, ?, ?, ?, ?)
            ''', (unique_id, theme, url, filepath, embedding_blob))
            logger.info(f"插入新的 embedding: URL={url}, filepath={filepath}")
    else:
        # 如果没有 URL，仅通过 filepath 插入
        cursor.execute('SELECT id FROM embeddings WHERE filepath = ?', (filepath,))
        result = cursor.fetchone()
        if result:
            # 如果 filepath 已存在，跳过插入
            logger.info(f"嵌入已存在，跳过插入: filepath={filepath}")
            return
        else:
            # 插入新的记录
            cursor.execute('''
                INSERT INTO embeddings (id, theme, url, filepath, embedding)
                VALUES (?, ?, ?, ?, ?)
            ''', (unique_id, theme, None, filepath, embedding_blob))
            logger.info(f"插入新的 embedding: filepath={filepath}")
    
    conn.commit()