# modules/character_voice_generator.py
"""
面向LLM可执行指令的动态角色特征注入模块

该模块负责：
1. 加载和解析角色核心档案
2. 生成角色演绎指南
3. 为剧本生成提供动态角色特征
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

# 延迟导入LLM接口，避免在模块导入时就加载依赖
# from modules.langchain_interface import call_llm
from config import logger

# 角色语音生成的prompt模板
CHARACTER_VOICE_GENERATION_PROMPT = """
您是一位资深的剧本编剧和角色塑造专家。根据以下提供的角色核心档案，请为该角色生成一段简洁、具体的"演绎指南"。这份指南将用于指导另一个AI语言模型在创作剧本时，如何塑造该角色的对话风格和语音特点。

指南应包含：
1. 对角色整体语言风格的概括性描述
2. 该角色在不同情绪或情境下（可基于其核心特质推断）可能使用的典型句式、口头禅或表达倾向
3. 一两个简短的对话示例，以体现其风格
4. 应避免的语言模式或表达方式

角色核心档案：
角色名：{character_name}
别名：{aliases}
核心身份：{core_identity}
主要性格特点：{dominant_traits}
沟通风格关键词：{communication_style_keywords}

请输出针对角色"{character_name}"的演绎指南：
"""

NATURAL_SPEECH_PATTERN_PROMPT = """
基于以下角色的演绎指南，请生成该角色的自然语言模式示例。这些示例将用于指导剧本创作中的对话生成。

角色名：{character_name}
演绎指南：{voice_guide}

请提供该角色的典型语言模式，格式如下：
- {character_name}: "典型表达方式1" (使用场景), "典型表达方式2" (使用场景)

要求：
1. 提供2-3个具体的语言模式示例
2. 每个示例都要包含使用场景说明
3. 体现角色的独特语言风格
4. 适合在剧本对话中直接使用

请输出：
"""


def load_character_profiles(story_id: str) -> Optional[Dict[str, Any]]:
    """
    加载指定故事的角色核心档案
    
    Args:
        story_id: 故事ID
        
    Returns:
        角色档案字典，如果文件不存在则返回None
    """
    try:
        profile_path = Path(f"2-animation-drama/episodes/{story_id}/character_profiles.json")
        
        if not profile_path.exists():
            logger.warning(f"角色档案文件不存在: {profile_path}")
            return None
            
        with open(profile_path, 'r', encoding='utf-8') as f:
            profiles = json.load(f)
            
        logger.info(f"成功加载故事 {story_id} 的角色档案，包含 {len(profiles.get('characters', {}))} 个角色")
        return profiles
        
    except Exception as e:
        logger.error(f"加载角色档案失败: {str(e)}")
        return None


def generate_character_voice_guide(character_name: str, character_profile: Dict[str, Any]) -> Optional[str]:
    """
    为单个角色生成演绎指南

    Args:
        character_name: 角色名称
        character_profile: 角色档案信息

    Returns:
        角色演绎指南文本，失败时返回None
    """
    try:
        # 延迟导入LLM接口
        from modules.langchain_interface import call_llm

        # 构建prompt数据
        prompt_data = {
            "character_name": character_name,
            "aliases": ", ".join(character_profile.get("aliases", [])) or "无",
            "core_identity": character_profile.get("core_identity", ""),
            "dominant_traits": ", ".join(character_profile.get("dominant_traits", [])),
            "communication_style_keywords": ", ".join(character_profile.get("communication_style_keywords", []))
        }

        # 调用LLM生成演绎指南
        response = call_llm(
            api_function="character_voice_generation",
            prompt_data=prompt_data,
            using_cache=True
        )
        
        if response:
            logger.info(f"成功生成角色 {character_name} 的演绎指南")
            return response.strip()
        else:
            logger.error(f"生成角色 {character_name} 的演绎指南失败：空响应")
            return None
            
    except Exception as e:
        logger.error(f"生成角色 {character_name} 的演绎指南时出错: {str(e)}")
        return None


def generate_natural_speech_patterns(character_name: str, voice_guide: str) -> Optional[str]:
    """
    基于演绎指南生成自然语言模式

    Args:
        character_name: 角色名称
        voice_guide: 角色演绎指南

    Returns:
        自然语言模式文本，失败时返回None
    """
    try:
        # 延迟导入LLM接口
        from modules.langchain_interface import call_llm

        prompt_data = {
            "character_name": character_name,
            "voice_guide": voice_guide
        }

        response = call_llm(
            api_function="natural_speech_pattern_generation",
            prompt_data=prompt_data,
            using_cache=True
        )
        
        if response:
            logger.info(f"成功生成角色 {character_name} 的自然语言模式")
            return response.strip()
        else:
            logger.error(f"生成角色 {character_name} 的自然语言模式失败：空响应")
            return None
            
    except Exception as e:
        logger.error(f"生成角色 {character_name} 的自然语言模式时出错: {str(e)}")
        return None


def generate_dynamic_character_features(story_id: str, character_names: List[str]) -> Dict[str, str]:
    """
    为指定角色列表生成动态角色特征
    
    Args:
        story_id: 故事ID
        character_names: 需要生成特征的角色名称列表
        
    Returns:
        包含动态角色特征的字典，包含以下键：
        - dynamic_character_voice_requirements: 角色语音要求
        - dynamic_natural_speech_patterns: 自然语言模式
    """
    try:
        # 加载角色档案
        profiles = load_character_profiles(story_id)
        if not profiles:
            logger.warning(f"无法加载故事 {story_id} 的角色档案，使用默认特征")
            return _get_default_character_features(character_names)
        
        characters_data = profiles.get("characters", {})
        
        # 生成角色演绎指南
        voice_guides = {}
        speech_patterns = {}
        
        for char_name in character_names:
            # 查找角色档案，支持别名匹配
            matched_profile = None
            matched_main_name = None

            # 首先尝试直接匹配主名称
            if char_name in characters_data:
                matched_profile = characters_data[char_name]
                matched_main_name = char_name
            else:
                # 尝试别名匹配
                for main_name, profile in characters_data.items():
                    aliases = profile.get("aliases", [])
                    if char_name in aliases:
                        matched_profile = profile
                        matched_main_name = main_name
                        break

                # 如果仍未匹配，尝试模糊匹配（处理 "罗兰（程岩）" 这种格式）
                if not matched_profile:
                    for main_name, profile in characters_data.items():
                        # 检查角色名称是否包含在输入字符串中
                        if main_name in char_name:
                            matched_profile = profile
                            matched_main_name = main_name
                            break

                        # 检查别名是否包含在输入字符串中
                        aliases = profile.get("aliases", [])
                        for alias in aliases:
                            if alias in char_name:
                                matched_profile = profile
                                matched_main_name = main_name
                                break

                        if matched_profile:
                            break

            if matched_profile:
                # 生成演绎指南
                voice_guide = generate_character_voice_guide(matched_main_name, matched_profile)
                if voice_guide:
                    voice_guides[char_name] = voice_guide
                    logger.info(f"成功生成角色 {char_name} 的演绎指南")

                    # 生成自然语言模式
                    speech_pattern = generate_natural_speech_patterns(char_name, voice_guide)
                    if speech_pattern:
                        speech_patterns[char_name] = speech_pattern
                        logger.info(f"成功生成角色 {char_name} 的自然语言模式")
            else:
                logger.warning(f"角色 {char_name} 不在档案中，跳过")
        
        # 组装最终的动态特征
        voice_requirements = _format_voice_requirements(voice_guides)
        natural_patterns = _format_natural_speech_patterns(speech_patterns)
        
        return {
            "dynamic_character_voice_requirements": voice_requirements,
            "dynamic_natural_speech_patterns": natural_patterns
        }
        
    except Exception as e:
        logger.error(f"生成动态角色特征时出错: {str(e)}")
        return _get_default_character_features(character_names)


def _format_voice_requirements(voice_guides: Dict[str, str]) -> str:
    """格式化角色语音要求"""
    if not voice_guides:
        return "       - 请根据角色在剧集结构中的描述来塑造其独特的语音特征"
    
    formatted_lines = []
    for char_name, guide in voice_guides.items():
        # 将指南格式化为缩进的文本
        indented_guide = "\n         ".join(guide.split("\n"))
        formatted_lines.append(f"       - {char_name}: {indented_guide}")
    
    return "\n".join(formatted_lines)


def _format_natural_speech_patterns(speech_patterns: Dict[str, str]) -> str:
    """格式化自然语言模式"""
    if not speech_patterns:
        return "       - 请根据角色特征创造独特的语言模式和表达方式"
    
    formatted_lines = []
    for char_name, pattern in speech_patterns.items():
        formatted_lines.append(f"       {pattern}")
    
    return "\n".join(formatted_lines)


def _get_default_character_features(character_names: List[str]) -> Dict[str, str]:
    """获取默认的角色特征（当无法加载档案时使用）"""
    return {
        "dynamic_character_voice_requirements": "       - 请根据角色在剧集结构中的描述来塑造其独特的语音特征和对话风格",
        "dynamic_natural_speech_patterns": "       - 请为每个角色创造独特的语言模式，体现其身份、背景和性格特点"
    }
