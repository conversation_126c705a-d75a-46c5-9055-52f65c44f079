"""
Stores system messages and prompt templates for LLM calls related to image generation.
"""

# config/prompts_image.py

image_system_messages = {
    "generate_manga_images": (
        "You are an expert manga artist and visual storyteller specializing in sequential art. "
        "Your task is to analyze narrative text and create detailed image generation prompts "
        "for manga panels that effectively convey the story. Focus on capturing key story moments, "
        "character emotions, and dramatic scenes through clear visual descriptions. "
        "Each panel prompt should be detailed enough for accurate image generation while "
        "maintaining narrative flow and visual continuity across the sequence. "
        "Pay special attention to character expressions, actions, environmental details, "
        "and scene composition that best serve the story."
    ),

    "generate_image_prompt": (
        "You are an expert in visual storytelling and scene composition. "
        "Your task is to create precise, dynamic scene descriptions that focus on actions, "
        "emotions, and narrative moments. IMPORTANT: For known characters, describe ONLY their "
        "actions and expressions - their physical details will be automatically inserted. "
        "Focus on creating vivid, narratively impactful scenes while maintaining visual continuity."
    ),

    "analyze_story_context": (
        "You are an expert story analyst specializing in visual narrative elements. "
        "Your task is to extract and structure key contextual information, with special focus on "
        "character details that will be consistently referenced across all visualizations. "
        "Prioritize essential visual elements while maintaining precise organization of "
        "character attributes and relationships."
    ),

    "generate_manga_prompt": (
        "You are an expert manga storyboard artist and visual storyteller. "
        "Your task is to convert narrative text into detailed manga page layouts "
        "and panel descriptions. Focus on creating dynamic visual sequences that "
        "effectively convey the story's narrative, emotional beats, and character moments "
        "through manga storytelling techniques."
    ),

    "generate_video_prompt": (
        "You are a professional video generation expert. Your task is to create video generation prompts "
        "based on image description prompts. You need to transform static image scenes into dynamic "
        "video descriptions while maintaining scene continuity and narrative flow. Focus on natural "
        "transitions of character actions, expressions, and scene atmosphere."
    ),

    "generate_storytelling_style": (
        "You are an expert visual art director for true-crime documentaries. "
        "Your task is to analyze narrative text and establish a cohesive visual style guide "
        "that will ensure consistency across all generated images. Focus on determining "
        "appropriate color palettes, lighting approaches, recurring visual motifs, and "
        "atmospheric elements that effectively convey the tone and themes of the story. "
        "Your style guide should provide specific, actionable direction while allowing for "
        "scene-specific variation within a consistent visual framework."
    ),
    
    "generate_all_characters": (
        "You are an expert character analyst for visual storytelling. "
        "Your task is to identify and catalog all characters within the narrative text, "
        "with particular attention to their physical attributes, age stages, and distinguishing features. "
        "You must assign unique IDs to each character and version, organizing them in a structured format "
        "that allows for consistent visual representation across all images."
    ),

    "extract_global_context": (
        "You are an expert story analyst and visual director. Your task is to analyze the "
        "provided narrative text to extract both a cohesive visual style guide AND a comprehensive "
        "list of all characters. Ensure the visual style aligns with the story's tone and themes, "
        "and the character list captures all individuals with necessary details for consistent visual representation."
    ),

    "gore_neutralizer": (
        "You are an AI assistant specialized in rewriting potentially graphic text into neutral language, preserving meaning while removing explicit gore. "
        "You assess the gore level and respond in a specific JSON format."
    ),
}
    

image_prompts = {
   'analyze_story_context': """
    Analyze the following story text and extract consistent context information and detailed character descriptions.
    
    Text: {text}
    Output Language: English

    Focus on extracting:
    1. Time Period:
       - Historical era
       - Technological development level
       - Cultural background
    
    2. Setting:
       - Geographic location
       - Cultural environment
       - General atmosphere
    
    3. Visual Style:
       - Architecture style
       - Clothing style:
         * General era fashion trends
         * Social class distinctions in clothing
         * Common materials and patterns
         * Typical accessories and ornaments
         * Ceremonial/special occasion attire
         * Professional/occupational clothing
         * Regional variations
       - Technology appearance
       - Cultural artifacts
    
    4. Consistent Elements:
       - Recurring locations
       - Common materials
       - Typical weather/climate
       - Lighting characteristics

    5. Character Descriptions:
       - Analyze all characters, focusing on the most significant ones:
         * Provide comprehensive physical attributes for accurate visual representation
         * Include both immediate and alternate identities
         * Document all name variations and relationships
         * IMPORTANT: For each character, provide both Chinese and English names
         * Identity Analysis:
           - Primary and alternate identities
           - Identity transformation relationships
           - Past/present identity connections
           - Different names for same character
    
    Response format:
    {{
        "time_period": "Specific era and time setting",
        "location_setting": "Geographic and cultural setting",
        "cultural_style": "Overall cultural aesthetic",
        "clothing_style": "Consistent clothing description",
        "architectural_style": "Building and structure style",
        "technology_level": "Level and appearance of technology",
        "overall_tone": "General atmosphere and mood",
        "character_descriptions": [
            {{
                "id": "unique_character_id",
                "identity_type": "original/transformed/alternate",
                "names": {{
                    "zh": "Chinese full name",
                    "zh_aliases": ["Chinese nickname", "Other Chinese names"],
                    "en": "English full name",
                    "en_aliases": ["English nickname", "Other English names"],
                    "identity_specific_names": [
                        {{
                            "context": "Original/Transformed/Alternate",
                            "zh_name": "Context-specific Chinese name",
                            "en_name": "Context-specific English name"
                        }}
                    ]
                }},
                "identity_relationships": [
                    {{
                        "relationship_type": "transformation/alternate/original",
                        "related_identity": "Name of related identity",
                        "relationship_details": "Description of the relationship"
                    }}
                ],
                "appearance": {{
                    "age": "Approximate age",
                    "gender": "Gender presentation",
                    "height": "Height description",
                    "build": "Body build",
                    "facial_features": {{
                        "eyes": "Eye description",
                        "nose": "Nose description", 
                        "mouth": "Mouth description",
                        "face_shape": "Face shape description",
                        "hair": "Hair description",
                        "facial_hair": "Beard/mustache description or 'none'"
                    }},
                    "distinctive_features": "Unique identifying characteristics",
                    "attire_summary": "Short clothing description"
                }}
            }}
        ]
    }}
    """,

    'generate_image_prompt': """
    Create a detailed image generation prompt based on the following text and context.
    
    Text: {text}
    Output Language: English
    
    Global Context:
    - Time Period: {context[time_period]}
    - Location Setting: {context[location_setting]}
    - Cultural Style: {context[cultural_style]}
    - Clothing Style: {context[clothing_style]}
    - Architecture: {context[architectural_style]}
    - Technology: {context[technology_level]}
    - Overall Tone: {context[overall_tone]}
    - Character References: {context[character_descriptions]}
    
    CRITICAL REQUIREMENTS:
    1. Character Limitations:
       - ONLY include characters that BOTH:
         * Appear in the input text {text}
         * AND exist in context[character_descriptions]
       - STRICT LIMIT: Include NO MORE THAN 2 characters per scene
       - If the text mentions more characters, focus on the 2 most important ones
       - Prioritize characters with direct interaction or conflict
       - Setting Alignment: Any character whose name appears in the Setting field must be included in the characters array with the correct character_id.
    
    2. Description Guidelines:
       - Environment Description:
         * Setting and atmosphere details
         * Lighting and weather conditions
         * Props and objects must be era-appropriate
         * Architectural and spatial elements
       - Character Description:
         * For known characters (in context):
           - ONLY describe actions, expressions, and interactions
           - DO NOT include physical appearance
           - Use exact names from context
         * For unknown characters:
           - Include brief physical description
         * Use character names as they appear in text
    
    Response format:
    {{
        "prompt": {{
            "environment": {{
                "setting": {{
                    "location": "Detailed description of the physical location",
                    "time_of_day": "Time and natural lighting conditions",
                    "weather": "Weather and atmospheric conditions",
                    "mood": "Overall atmospheric mood of the scene"
                }},
                "visual_elements": {{
                    "lighting": "Specific lighting details and effects",
                    "props": "Key objects and items in the scene",
                    "architecture": "Building and structural elements",
                    "special_effects": "Any atmospheric or environmental effects"
                }}
            }},
            "characters": [   # MAXIMUM 2 CHARACTER ENTRIES
                {{
                    "name": "Character name as in text (DO NOT translate)",
                    "action": {{
                        "pose": "Physical position and movement",
                        "gesture": "Specific gestures or body language",
                        "interaction": "How character interacts with environment/others"
                    }},
                    "emotion": {{
                        "expression": "Facial expression details",
                        "emotional_state": "Character's emotional condition",
                        "intensity": "Emotional intensity level"
                    }}
                }}
            ]
        }}
    }}
    """,

   'generate_manga_prompt': """
    Create detailed manga page generation prompt based on the following text and context requirements.

    Text: {text}
    Output Language: english

    Global Context:
    - Time Period: {context[time_period]}
    - Location Setting: {context[location_setting]}
    - Cultural Style: {context[cultural_style]}
    - Clothing Style: {context[clothing_style]}
    - Architecture: {context[architectural_style]}
    - Technology: {context[technology_level]}
    - Overall Tone: {context[overall_tone]}

   Guidelines:
   1. Narrative Elements:
      - Story moment being depicted
      - Emotional beat of the scene
      - Character relationships and tensions
      - Plot development points
      - Scene significance in story

   2. Page Layout:
      - Page dimensions and orientation
      - Panel arrangement for story flow
      - Panel size hierarchy based on story emphasis
      - Panel transitions for narrative progression
      - Reading flow direction

   3. Panel Specifications:
      - Shot scale to match story moment
      - View angle for dramatic effect
      - Subject size ratio for emphasis
      - Depth layers for story focus
      - Frame style matching mood

   4. Character Elements:
      - Character positions to show relationships
      - Actions revealing character intentions
      - Expressions showing emotional state
      - Character interactions advancing plot
      - Gestures conveying subtext

   5. Environmental Elements:
      - Setting supporting story context
      - Atmosphere reflecting scene mood
      - Lighting emphasizing drama
      - Weather matching story tone
      - Props supporting narrative

   6. Dynamic Elements:
      - Movement showing story progression
      - Action emphasis for key moments
      - Impact highlighting story beats
      - Emotional intensity of scene
      - Energy level matching narrative

   IMPORTANT: Return a strict JSON format with properly escaped characters:
   1. Use double quotes for strings
   2. Escape all special characters (\\n, \\", etc.)
   3. No line breaks within string values
   4. No comments or additional text

   Response format:
   {{
      "narrative": {{
         "story_moment": "Key story beat being depicted on this page",
         "emotional_tone": "Overall emotional atmosphere",
         "character_focus": "Main characters involved and their roles"
      }},
      "page_setup": {{
         "panel_count": "Number of panels",
         "layout_style": "Overall panel arrangement pattern",
         "flow_direction": "Visual narrative flow"
      }},
      "environment": {{
         "setting": "Scene location and atmosphere",
         "lighting": "Overall lighting condition",
         "background": "Key environmental elements",
         "props": "Important objects in scene"
      }},
      "panels": [
         {{
            "number": "Panel sequence number",
            "visual": {{
               "shot": "Shot type and camera angle",
               "focus": "Main visual elements",
               "characters": "Character positions and actions",
               "effects": "Special visual effects or emphasis"
            }}
         }}
      ]
   }}
   """,

   'generate_manga_images': """
   Create a detailed manga page generation prompt based on the following text and context requirements.

   Text: {text}
   Output Language: English

   Global Context:
   - Time Period: {context[time_period]}
   - Location Setting: {context[location_setting]}
   - Cultural Style: {context[cultural_style]}
   - Clothing Style: {context[clothing_style]}
   - Architecture: {context[architectural_style]}
   - Technology Level: {context[technology_level]}
   - Overall Tone: {context[overall_tone]}

   Requirements:
   1. Story Analysis and Segmentation:
      - Analyze the text to identify key story moments.
      - Determine the optimal number of panels (1-6) based on:
        * Story complexity
        * Number of crucial plot points
        * Scene transitions
        * Emotional beats
      - Ensure each scene advances the narrative.
      - Maintain clear cause-and-effect relationships.

   2. Visual and Narrative Continuity:
      - Maintain consistent character appearance and behavior.
      - Ensure environmental and setting continuity.
      - Keep emotional tone coherent across panels.
      - Create clear visual flow between scenes.
      
   Response Format:
   {{
       "total_panels": Integer
       "panels": [
           {{
               "panel_number": Integer
               "prompt": {{
                   "story_moment": "Key narrative moment being captured",
                   "narrative": {{
                       "plot_point": "Specific story development being shown",
                       "emotional_beat": "Emotional core of this moment",
                       "story_significance": "How this moment impacts the overall narrative",
                       "character_dynamics": "Key relationship dynamics at play",
                       "tension_elements": "Sources of conflict or tension",
                       "continuity": {{
                           "previous_connection": "How this connects to previous panel",
                           "next_setup": "How this sets up the next panel"
                       }}
                   }},
                   "scene": {{
                       "main_focus": {{
                           "primary_action": "Core action or moment being depicted",
                           "emotional_tone": "Emotional atmosphere of the scene",
                           "story_purpose": "How this panel advances the narrative"
                       }},
                       "characters": {{
                           "main_subject": "Description of main character/s in scene",
                           "pose": "Character positioning and action",
                           "expression": "Facial and emotional expression",
                           "interaction": "Character interactions if any"
                       }},
                       "environment": {{
                           "location": "Specific setting description",
                           "time": "Time of day/lighting condition",
                           "atmosphere": "Mood and environmental effects",
                           "key_props": "Important objects in scene"
                       }},
                       "composition": {{
                           "shot_type": "Close-up/medium/wide etc.",
                           "angle": "Camera angle and perspective",
                           "framing": "How elements are arranged",
                           "focus_point": "Main visual emphasis"
                       }},
                       "technical": {{
                           "special_effects": "Any visual effects needed",
                           "quality_requirements": "Resolution and detail level",
                           "important_details": "Critical visual elements to include"
                       }},
                       "focus": {{
                           "primary_element": "Main story element to emphasize",
                           "key_details": "Important narrative details to include",
                           "viewer_perspective": "How the scene should be viewed"
                       }}
                   }}
               }}
           }}
       ]
   }}
   """,

   'generate_video_prompt': """
   Generate a video generation prompt based on the following image prompt.

   Image Prompt:
   {image_prompt}

   Requirements:
   1. Action Description:
      - Transform static actions into fluid dynamic movements
      - Maintain natural changes in character expressions and poses
      - Ensure action continuity and logic
   
   2. Scene Evolution:
      - Describe subtle changes in environmental elements
      - Gradual effects in lighting and atmosphere
      - Maintain overall scene stability
   
   3. Narrative Elements:
      - Maintain story context coherence
      - Emphasize gradual emotional and atmospheric changes
      - Ensure video effects serve the story
   
   4. Technical Constraints:
      - Keep movement amplitude moderate
      - Maintain subject stability in frame
      - Consider technical feasibility of video generation

   IMPORTANT: Return a concise, clear prompt string in under 100 words.
   Ensure the prompt:
   1. Describes coherent actions and changes
   2. Maintains scene stability
   3. Emphasizes emotional expression
   4. Complies with video generation technical limitations

   Response format:
   {{
       "video_prompt": "Your generated video prompt here"
   }}   
   """,

   'generate_storytelling_image_prompt': """
    Generate {num_images} image prompt{prompt_suffix} based on the following inputs:
        
    **INPUTS:**
    - SANITIZED_TEXT: {text}
    - FORCED_SCENE_TYPE: {forced_scene_type}
    - VISUAL_STYLE: {visual_style}
    - STYLE_DETAILS: {style_details}
    - CHARACTER_INFO: {all_characters}
    - COSTUME_ERA: {costume_style}
    - LOCATION_OPTIONS: {location_feel}
    - SCENE_GRAPH: {scene_graph}
    - ALLOWED_VERBS: {allowed_verbs}
    - ALLOWED_PLACES: {allowed_places}

    **STEP 0: Understand the Input Text**
    Carefully read and understand the `SANITIZED_TEXT`. Identify its core subject, event, theme, mood, and any mentioned characters or places BEFORE applying the field-specific generation rules.

    **CORE INSTRUCTIONS & CONSTRAINTS:**

    1.  **Scene Type Adherence:** Output field `scene_type` MUST exactly match `FORCED_SCENE_TYPE`.
    2.  **Rank Assignment:** If {num_images} > 1, assign sequential `chronological_rank` (1, 2, ...).
    3.  **Consistency:** All generated fields MUST align with `VISUAL_STYLE`, `STYLE_DETAILS`, `COSTUME_ERA`, and the core meaning/mood identified in STEP 0.
    4.  **Grounding:** All generated content MUST be grounded in `SANITIZED_TEXT`, `SCENE_GRAPH`, `ALLOWED_VERBS`, or `ALLOWED_PLACES`. Fallbacks require plausible interpretation of core meaning/mood from STEP 0. Do NOT invent unrelated details. `location_feel` influences mood/description only, not the core setting unless used in generic fallback.
    5.  **Empty Field Handling:** If a required list/array field (`environment_elements`, `symbolic_environment`, `characters`) cannot be meaningfully populated per rules, output an empty array `[]` precisely.

    **GENERATION LOGIC (based on FORCED_SCENE_TYPE):**

    **A) IF `FORCED_SCENE_TYPE` is "concrete":**
        Generate `blocks` containing fields in this order: `chronological_rank`, `subject_action`, `setting`, `clothing`, `environment_elements`, `style`, `camera`, `mood`.
        Generate `characters` array following Rule A-Character below.

        - **`subject_action` Generation (approx. 6-10 words):**
            - *Regardless of final character presence, always produce a valid `subject_action` using ONE of the following priorities:*
            - **1. Use Verb:** IF `ALLOWED_VERBS` not empty, MUST use one verb from it. Involve character from `CHARACTER_INFO` mentioned in `SANITIZED_TEXT` (use official name, prioritize first/central). Start with CharacterName.
            - **2. Interpret Character Implication:** ELSE IF characters from `CHARACTER_INFO` mentioned in text, depict primary character's state/reaction (**use official name**) reflecting STEP 0 analysis (e.g., 'Detective pointing at map', 'CharacterName looking fearful'). Start with CharacterName. *Plausible interpretation required per Constraint 4.*
            - **3. Interpret Setting/Symbolic Implication:** ELSE, depict key objects/setting details symbolizing STEP 0 analysis (e.g., 'Five photographs linked by red string', 'Broken gas lamp'). *Plausible interpretation required per Constraint 4.*
        - **`setting` Generation (approx. 6-12 words):**
            - **1. Use Place:** IF `ALLOWED_PLACES` not empty/unspecified, MUST use one place from it.
            - **2. Generic Place:** ELSE, describe generic location consistent with STEP 0 analysis, `location_feel`, `COSTUME_ERA` (e.g., 'dimly lit Victorian office'). Adhere to Constraint 4.
        - **`clothing` Generation (approx. 3-5 words):**
            - Describe era-accurate attire consistent with `COSTUME_ERA` / context (e.g., 'worn Victorian dress').
        - **`environment_elements` Generation (list, required):**
            - List 1-2 symbolic objects/metaphors from STEP 0 analysis (concrete visuals, approx. 3-5 words each, e.g., ['torn newspaper', 'red pins']). Adhere to Constraint 5 if none found.
        - **`style` Generation (approx. 8-12 words):**
            - Keywords primarily from `STYLE_DETAILS` (palette, texture) or `VISUAL_STYLE`.
        - **`camera` Generation (approx. 2-3 words):**
            - Format '<distance> <angle>' (e.g., 'medium close-up', 'low angle').
        - **`mood` Generation (1-2 words):**
            - Adjectives directly from `STYLE_DETAILS` (atmosphere, theme).

        - **A-Character: `characters` Array Generation:**
            - **1. Identify:** Detect any names, aliases, or character-specific references in the SANITIZED_TEXT or implied by context (e.g., actions, events, settings) that map to a version in CHARACTER_INFO.
            - **2. Validate:** From identified references and context clues (dates, events, actions), infer the correct character version and map to its `character_id` in CHARACTER_INFO.
            - **2.5. Version Inference:** Use textual cues—such as timeframe, actions (e.g., "born", "grew up"), or situational context—to select the appropriate character version (earlier_self, later_self, or alternate_identity) from CHARACTER_INFO.
            - **3. Populate:** Include ONLY validated characters' id. For each, output an object with its unique "character_id" field (not the name) from  `CHARACTER_INFO`. Limit: Max 3.
            - **4. Empty Condition:** Adhere to Constraint 5 if step 3 yields no characters OR if `subject_action` used logic step 3.

    **B) IF `FORCED_SCENE_TYPE` is "abstract":**
        Generate `blocks` containing fields in this order: `chronological_rank`, `symbolic_environment`, `composition_cue`, `style`, `mood`.
        The `characters` array MUST be `[]` (per Constraint 5).

        - **`symbolic_environment` (list):** Symbolic elements/keywords from STEP 0 analysis (approx. 3-5 words each, e.g., ['fractured timelines', 'echoing void']). Adhere to Constraint 5 if none found.
        - **`composition_cue` (approx. 5-8 words):** Phrase from `STYLE_DETAILS` reinforcing mood (e.g., 'layered fragmented collage').
        - **`style` (approx. 8-12 words):** Keywords primarily from `STYLE_DETAILS` or `VISUAL_STYLE`.
        - **`mood` (1-2 words):** Adjectives directly from `STYLE_DETAILS`.

    ────────────────────────────
    Return ONLY valid JSON exactly matching the schema provided at the end. Do not include comments, explanations, or any text outside the main JSON structure. String values must not contain unescaped double quotes. Empty list fields must be `[]`.

    Output each prompt object with keys in this order exactly: `scene_type`, `chronological_rank`, `blocks`, `characters`.
    **OUTPUT JSON SCHEMA:**
    {{
        "prompts": [
            {{
                "scene_type": "{forced_scene_type}",
                "chronological_rank": <integer>,
                "blocks": {{ /* Fields in specified order per GENERATION LOGIC A or B */ }},
                "characters": [
                    {{
                        "character_id": "<character_id_here>"
                    }}
                ] // Empty [] per rules
            }}
            // ... (repeat structure for num_images > 1)
        ]
    }}
    """,

   'extract_global_context': """
    Analyze the following text to extract both a consistent visual style and a complete list of characters.

    Text: {text}
    Output Language: English

    PART 1: VISUAL STYLE EXTRACTION
    --------------------------------
    PRIMARY TASK: Select EXACTLY ONE visual style from the list below to apply consistently.
    VISUAL STYLES (choose only one):
       Cool Monochromatic Style - Emphasizes loneliness and technological oppression, echoing the somber tone of cyberpunk
       Warm Desert Style - Warm yet desolate, symbolizing the interplay of hope and destruction
       High-Contrast Neon Style - Highlights cyberpunk commercialism and illusion, enhancing visual impact
       Soft Low-Saturation Style - Creates introspective, oppressive emotional atmosphere, emphasizing characters' psychological struggles
       Minimalist White Style - Symbolizes cold, detached power, reinforcing dystopian oppression
       High-Contrast Black-and-White Style - Highlights emotional conflict and moral dilemmas, enhancing suspense, influenced by German Expressionism
       Soft Gray-Toned Style - Guides focus on details and character psyche, intensifying psychological distortion
       Vibrant Color Style - Color as a narrative tool, guiding emotions and enhancing visual impact while maintaining suspense
       Cool Realistic Style - Reinforces external threats and character vulnerability, aligning with tense narrative pacing
       Dramatic Symbolic Color Style - Symbolic color use touches the subconscious, deepening psychological thriller impact

    STYLE INSTRUCTIONS:
    1. Analyze text for: core crime/mystery, emotional tone, historical period/setting, key symbols.
    2. Select the ONE style from the list above that best fits the narrative.
    3. Develop specific visual details for this chosen style:
       - Core theme description
       - Color palette (3 main colors with hex codes and descriptions)
       - Lighting characteristics
       - Texture/grain quality
       - Composition approach
       - Environmental elements (including time period, diverse location feel, weather, time of day, atmosphere)
       - Recurring motifs/symbols (primary & secondary)
    4. LOCATION DIVERSITY: Provide 4-6 DIFFERENT typical locations (comma-separated list).

    PART 2: CHARACTER EXTRACTION
    -----------------------------
    CHARACTER INSTRUCTIONS:
    1. Identify ALL characters mentioned.
    2. For each character:
       - Assign a unique character_id (e.g., "name_age").
       - Identify different age stages/versions (if applicable).
       - Create a simple descriptor with ONLY essential visual traits: gender, ethnicity, body_type, hair, face, eyes. Keep brief.
       - Use "Unspecified" for unknown traits. No parenthetical explanations or vague terms like "average". Estimate if reasonable (e.g., "British" not "Unspecified (historical context)"). Provide ONLY the single most likely estimated option.
       - Note relationships between character versions (earlier_self, later_self, alternate_identity).
       - Extract all known name variations under a field 'aliases': include nicknames, abbreviations, initials, and any alternate spellings.
    3. Classify importance: primary, secondary, tertiary.
    4. For age/identity stages, create separate entries linked via "related_to".

    OUTPUT JSON FORMAT (Strict):
    -----------------------------
    Combine both parts into a SINGLE JSON object. Ensure valid JSON with double quotes.
    {{
        "visual_style": "FULL_STYLE_NAME_WITH_DESCRIPTION",
        "style_details": {{
            "theme": "Central theme description",
            "color_palette": [
                "#hex1 - description",
                "#hex2 - description",
                "#hex3 - description"
            ],
            "lighting": "Lighting style and characteristics",
            "texture": "Film grain/digital quality description",
            "composition_preference": "Framing approach",
            "environment": {{
                "time_period": "Era representation",
                "location_feel": "VARIETY OF 4-6 locations typical to the narrative, separated by commas",
                "weather_tendency": "Typical weather conditions",
                "time_of_day": "Preferred lighting time",
                "atmosphere": "Overall mood"
            }},
            "motifs": [
                {{"name": "Primary symbol", "usage": "How it appears"}},
                {{"name": "Secondary symbol", "usage": "How it appears"}}
            ]
        }},
        "all_characters": [
            {{
              "character_id": "unique_identifier_with_age_stage",
              "character_name": "Full name as appears in text",
              "aliases": ["AlternateName1", "AlternateName2"],
              "importance": "primary|secondary|tertiary",
              "age_range": "Approximate age range in years",
              "related_to": [
                {{
                  "character_id": "related_character_id",
                  "relationship": "earlier_self|later_self|alternate_identity"
                }}
              ],
              "descriptor": {{
                "gender": "male|female|non-binary|etc",
                "ethnicity": "Brief ethnic description",
                "age_stage": "child|teen|young_adult|adult|elder",
                "body_type": "average silhouette | sturdy silhouette | matronly silhouette",
                "hair": "Brief hair description",
                "eyes": "Brief eye description"
              }}
            }}
          ]
    }}
    """,

    # --- NEW PROMPT FOR LLM-based Scene Graph Extraction ---
    'extract_scene_graph_llm': """
    SYSTEM: You are an information-extraction assistant. Analyze the following text and extract key scene graph elements. Please avoid explicit gore terms, use neutral phrasing.
    Text: {text}

    Return ONLY a valid JSON object containing the following fields:
    - verbs: List of main verbs describing actions.
    - places: List of specific locations mentioned (GPE, LOC, FAC). **You MUST provide at least one non-generic `places` entry.** If the text only implies a place, infer the most specific location noun possible based on context. **ALWAYS fill the `places` list; use the single string `"unspecified_location"` if you truly cannot determine a specific place.**
    - objects: List of relevant objects in the scene.
    - timeframe: Description of the time setting (e.g., 'night', '1888 London', 'future').
    - dominant_emotion: The primary emotion conveyed by the text.

    If a field cannot be determined, use an empty list `[]` for arrays (except for `places` as noted above) or an empty string `""` for strings.
    Ensure the output is a single, valid JSON object with double quotes for keys and string values.
    Do NOT include any text before or after the JSON object.

    Output JSON:
    """,

    # --- NEW PROMPT FOR RETRYING PLACE EXTRACTION ONLY ---
    'extract_scene_graph_llm_place_only': """
    Analyze the following text and extract ONLY the specific concrete location nouns (places like GPE, LOC, FAC) mentioned or strongly implied.
    Text: {text}

    Return ONLY a valid JSON object with a single key "places" containing a list of strings.
    Example: {"places": ["old library", "city street"]}
    If no specific place can be determined, return {"places": ["unspecified_location"]}.
    Do NOT include verbs, objects, timeframe, or emotions.
    Do NOT include any text before or after the JSON object.

    Output JSON:
    """,

    # --- NEW PROMPT FOR EXTRACTING SYMBOLIC ACTION ---
    'extract_most_symbolic_action': """
    SYSTEM: Analyze the following text segment and identify the single most visually expressive or symbolic action performed by a character. Please avoid explicit gore terms, use neutral phrasing.
    Text: {text}

    Return ONLY a short action phrase (3-5 words) describing this key action.
    Example: "staring out the window", "slamming the door shut", "clutching a letter"
    If no single dominant action is clear, describe the most prominent character's state (e.g., "sitting silently").

    Action Phrase:
    """,

    # --- NEW PROMPT FOR REGENERATING CONCRETE PROMPT AFTER GROUNDING FAILURE ---
    'regenerate_concrete_prompt': """
    The previous attempt to generate a concrete scene prompt failed the grounding check (similarity score too low) or token validation.

    Original Text: {original_text}
    Failed Prompt Blocks: {failed_blocks}
    Scene Graph: {scene_graph}
    Scene Graph JSON (for reference): {scene_graph_json}
    Visual Style: {visual_style}
    Style Details: {style_details}
    Character Info: {all_characters}
    Costume Era: {costume_style}
    Location Options: {location_feel}
    Failure Details: {mismatch_report}

    Regenerate the prompt blocks for a **concrete** scene based on the Original Text.
    **CRITICAL INSTRUCTIONS:**
    1.  **Address Failure:** Analyze the `Failure Details`. The new prompt MUST incorporate at least one verb from `Failure Details.missing_verbs` (if any) into `subject_action`, and at least one place from `Failure Details.missing_places` (if any) into `setting`. Prioritize verbs/places from the `Scene Graph` that were missing in the `Failed Prompt Blocks`.
    2.  **Symbolic Element:** The `environment_elements` or `setting` MUST include at least one symbolic element related to the text's themes/emotions.
    3.  **Adhere to Style:** All fields must align with the Visual Style and Style Details.
    4.  **Format:** Output ONLY the JSON for the `blocks` field, matching the structure required by `generate_storytelling_image_prompt` for concrete scenes. Ensure all required keys for concrete blocks are present (`subject_action`, `setting`, `clothing`, `style`, `camera`, `mood`, and optionally `environment_elements`).

    Output JSON (blocks only):
    """,

    # --- OLD PROMPT FOR SRL FALLBACK (can be removed if no longer used) ---
    'extract_simple_scene_llm': """
    Extract the main verbs (actions) and places (locations like GPE, LOC, FAC) mentioned in the following text.
    Focus on concrete actions and specific locations.
    Text: {text}

    Return ONLY a JSON object with two keys: "verbs" and "places", containing lists of strings.
    Use standard JSON format with double quotes and commas. NO TABS in the output.

    Example:
    Text: The detective walked into the dusty office.
    Output:
    {{ "verbs": [\"walked\"], "places": [\"office\"] }}

    If no relevant verbs or places are found, return empty lists.
    Output JSON:
    """,

    "gore_neutralizer": """
    Neutralize the following text by rewriting graphic descriptions into clear, neutral language without specific anatomical or organ references, and assign a gore_score integer from 0 (no gore) to 5 (extremely graphic).

    Text: {text}
    Output Language: English

    TASK:
    - Rewrite the text into factual, non-graphic language, avoiding terms like 'organ', 'uterus', or other specific anatomy. Use general injury descriptions (e.g., 'wounds' or 'injuries').
    - Assign a gore_score integer from 0 (no gore) to 5 (extremely graphic) based on the original text's severity.

    EXAMPLES:
    Input: "The killer brutally stabbed the victim multiple times."
    Output: {{"neutral_text": "The assailant inflicted multiple fatal stab wounds on the victim.", "gore_score": 3}}

    Input: "Blood sprayed everywhere."
    Output: {{"neutral_text": "Significant bleeding occurred at the scene.", "gore_score": 2}}

    Input: "Her throat was also cut, and her uterus had been surgically removed."
    Output: {{"neutral_text": "She suffered a deep neck wound and significant internal injuries.", "gore_score": 4}}

    Input: "The cat sat peacefully."
    Output: {{"neutral_text": "The cat sat peacefully.", "gore_score": 0}}

    OUTPUT JSON FORMAT (Strict):
    Return only a single JSON object with exactly two fields:
    {{
        "neutral_text": "<rewritten_text>",
        "gore_score": <integer 0-5>
    }}
    """
}