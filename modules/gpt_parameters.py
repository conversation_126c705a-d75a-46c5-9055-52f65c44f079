# 添加必要的导入
import os
import yaml
import logging
from typing import Tu<PERSON>, Dict, Any
from config import logger
# 设置日志记录
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 模型配置常量
# LLM 提供商
LLM_PROVIDER_AZURE = "azure"
LLM_PROVIDER_OPENAI = "openai"
LLM_PROVIDER_ANTHROPIC = "anthropic"
LLM_PROVIDER_DEEPSEEK = "deepseek"
LLM_PROVIDER_LOCAL_DEEPSEEK = "local_deepseek"  # 添加本地 DeepSeek 提供商
LLM_PROVIDER_GOOGLE = "google"  # 新增 Google 提供商

# 模型键名
# OpenAI 模型
MODEL_KEY_GPT41 = "gpt-4.1"  # 更新为实际API调用名称
MODEL_KEY_O3_MINI = "o4-mini"
MODLE_KEY_GPT_NANO = "gpt-4.1-nano"

# Anthropic 模型
MODEL_KEY_CLAUDE_OPUS = "claude-3-opus-20240229"
MODEL_KEY_CLAUDE_SONNET = "claude-3-sonnet-20240229"
MODEL_KEY_CLAUDE_HAIKU = "claude-3-haiku-20240307"

# DeepSeek 模型
MODEL_KEY_DEEPSEEK_CHAT = "deepseek-chat"  # 实际API调用名称
MODEL_KEY_DEEPSEEK_REASONER = "deepseek-reasoner"  # 保留兼容性
MODEL_KEY_ARK_DEEPSEEK_R1 = "ep-20250226164618-9cq2r"
DEEPSEEK_ARK_API_BASE = "https://ark.cn-beijing.volces.com/api/v3"

# 本地 DeepSeek 模型
MODEL_KEY_LOCAL_DEEPSEEK_R1 = "huihui_ai/deepseek-r1-abliterated:32b"  # 本地模型名称

MODEL_KEY_GEMINI_25_PRO = "gemini-2.5-pro-preview-05-06"

# 环境变量名称
ENV_AZURE_OPENAI_ENDPOINT = "AZURE_OPENAI_ENDPOINT"
ENV_AZURE_OPENAI_API_KEY = "AZURE_OPENAI_API_KEY"
ENV_OPENAI_API_KEY = "OPENAI_API_KEY"
ENV_ANTHROPIC_API_KEY = "ANTHROPIC_API_KEY"
ENV_DEEPSEEK_API_KEY = "DEEPSEEK_API_KEY"
ENV_DEEPSEEK_API_BASE = "DEEPSEEK_API_BASE"
ENV_DEEPSEEK_ARK_API_KEY = "DEEPSEEK_ARK_API_KEY"  # 新增 ARK API 密钥环境变量
ENV_GEMINI_API_KEY = "GEMINI_API_KEY"  # 新增 Gemini API 密钥环境变量
ENV_LOCAL_DEEPSEEK_API_BASE = "LOCAL_DEEPSEEK_API_BASE"  # 本地 DeepSeek API 基础 URL
ENV_CACHE_DIR = "LANGCHAIN_CACHE_DIR"
ENV_CACHE_EXPIRY_DAYS = "LANGCHAIN_CACHE_EXPIRY_DAYS"

# 默认值
DEFAULT_AZURE_ENDPOINT = "https://o1-mini-mcn.openai.azure.com/"
DEFAULT_DEEPSEEK_API_BASE = "https://api.deepseek.com/v1"
DEFAULT_LOCAL_DEEPSEEK_API_BASE = "http://localhost:11434/v1"  # 默认本地 Ollama 地址
DEFAULT_CACHE_DIR = "data/cache/langchain_responses"
DEFAULT_CACHE_EXPIRY_DAYS = 7

# 模型参数白名单配置
MODEL_CONFIGS = {
    LLM_PROVIDER_AZURE: {
        MODEL_KEY_O3_MINI: {
            "deployment_name": MODEL_KEY_O3_MINI,
            "api_version": "2024-12-01-preview",
            "allowed_params": ["deployment_name", "api_version", "max_completion_tokens"],
            "default_max_tokens": 100000
        },
        MODEL_KEY_GPT41: {
            "deployment_name": "gpt-4.1",  # 使用实际的部署名称
            "api_version": "2025-01-01-preview",  # 使用新的API版本
            "allowed_params": ["temperature", "api_version", "top_p", "max_tokens"],
            "default_max_tokens": 32000  # 设置一个适当的默认token限制
        },
        MODLE_KEY_GPT_NANO: {
            "deployment_name": MODLE_KEY_GPT_NANO,
            "api_version": "2025-03-01-preview",  # 更新为新API版本
            "allowed_params": ["deployment_name", "api_version", "max_completion_tokens"],
            "default_max_tokens": 32000
        }
    },
    LLM_PROVIDER_DEEPSEEK: {
        MODEL_KEY_DEEPSEEK_REASONER: {
            "model_name": MODEL_KEY_DEEPSEEK_REASONER,
            "base_url": DEFAULT_DEEPSEEK_API_BASE,
            "allowed_params": ["model", "temperature", "top_p", "max_tokens"],
            "default_max_tokens": 8192
        },
        MODEL_KEY_DEEPSEEK_CHAT: {
            "model_name": MODEL_KEY_DEEPSEEK_CHAT,
            "base_url": DEFAULT_DEEPSEEK_API_BASE,
            "allowed_params": ["model", "temperature", "top_p", "frequency_penalty"],
            "default_max_tokens": 4096
        },
        MODEL_KEY_ARK_DEEPSEEK_R1: {  # 添加 ARK 模型配置
            "model_name": MODEL_KEY_ARK_DEEPSEEK_R1,
            "base_url": DEEPSEEK_ARK_API_BASE,
            "allowed_params": ["model", "temperature", "top_p", "max_tokens"],
            "default_max_tokens": 8192,
            "use_ark_api": True  # 标记使用 ARK API
        }
    },
    LLM_PROVIDER_LOCAL_DEEPSEEK: {  # 添加本地 DeepSeek 模型配置
        MODEL_KEY_LOCAL_DEEPSEEK_R1: {
            "model_name": MODEL_KEY_LOCAL_DEEPSEEK_R1,
            "base_url": DEFAULT_LOCAL_DEEPSEEK_API_BASE,
            "allowed_params": ["model", "temperature", "top_p", "max_tokens"],
            "default_max_tokens": 8192,
            "use_local_api": True  # 标记使用本地 API
        }
    },
    LLM_PROVIDER_GOOGLE: {
        MODEL_KEY_GEMINI_25_PRO: {
            "model_name": MODEL_KEY_GEMINI_25_PRO,
            "api_version": "v1",
            "allowed_params": [
                "model", "messages",
                "max_tokens", "temperature", "top_p",
                "frequency_penalty", "presence_penalty",
                "logit_bias", "stream",
                "stop", "tools", "tool_choice"
            ],
            "default_max_tokens": 65536
        }
    }
}

# 模型参数兼容性配置
MODEL_PARAM_COMPATIBILITY = {
    LLM_PROVIDER_AZURE: {
        "gpt4": {
            "supports_temperature": True,
            "supports_top_p": True,
        },
        MODEL_KEY_O3_MINI: {
            "supports_temperature": False,  # o3-mini 不支持 temperature 参数
            "supports_top_p": False,
        },
        MODEL_KEY_GPT41: {
            "supports_temperature": True,
            "supports_top_p": True,
        }
    },
    LLM_PROVIDER_OPENAI: {
        "gpt4": {
            "supports_temperature": True,
            "supports_top_p": True,
        }
    },
    LLM_PROVIDER_ANTHROPIC: {
        MODEL_KEY_CLAUDE_OPUS: {
            "supports_temperature": True,
            "supports_top_p": False,
        },
        MODEL_KEY_CLAUDE_SONNET: {
            "supports_temperature": True,
            "supports_top_p": False,
        },
        MODEL_KEY_CLAUDE_HAIKU: {
            "supports_temperature": True,
            "supports_top_p": False,
        }
    },
    LLM_PROVIDER_DEEPSEEK: {
        MODEL_KEY_DEEPSEEK_REASONER: {
            "supports_temperature": True,
            "supports_top_p": True,
        },
        MODEL_KEY_DEEPSEEK_CHAT: {
            "supports_temperature": True,
            "supports_top_p": True,
            "supports_frequency_penalty": True
        },
        MODEL_KEY_ARK_DEEPSEEK_R1: {  # 添加 ARK 模型兼容性配置
            "supports_temperature": True,
            "supports_top_p": True,
        }
    },
    LLM_PROVIDER_LOCAL_DEEPSEEK: {  # 添加本地 DeepSeek 模型兼容性配置
        MODEL_KEY_LOCAL_DEEPSEEK_R1: {
            "supports_temperature": True,
            "supports_top_p": True,
        }
    }
}

# GPT参数配置
GPT_PARAMETERS = {
    # Novel Rewrite相关 - 基于Gemini 2.5 Pro最佳实践优化
    "extract_spine_events": {
        "temperature": 0.4,  # 优化：事件提取需要准确性和洞察力的平衡
        "top_p": 0.85,       # 适度提高以发现重要事件
        "frequency_penalty": 0.2,  # 降低以保持重要事件的识别
        "presence_penalty": 0.3    # 鼓励发现新的关键事件
    },
    "generate_episode_structure": {
        "temperature": 1.0,  # 优化：结构生成需要创意性和逻辑性
        "top_p": 0.9,        # 保持结构的合理性
        "frequency_penalty": 0.3,  # 避免重复的结构模式
        "presence_penalty": 0.4    # 鼓励创新的结构设计
    },
    "generate_full_script": {
        "temperature": 1.5,  # 优化：完整剧本需要高创意性
        "top_p": 0.92,       # 平衡创意性和连贯性
        "frequency_penalty": 0.4,  # 避免重复表达
        "presence_penalty": 0.5    # 强烈鼓励新颖的表达
    },

    "summarize_chapter": {
        "temperature": 0.5,  # 优化：摘要需要平衡准确性和洞察力
        "top_p": 0.9,        # 降低以提高摘要的一致性
        "frequency_penalty": 0.3,  # 减少以保持重要信息的重复提及
        "presence_penalty": 0.2    # 适度鼓励发现新的重要元素
    },
    "summarize_group": {
        "temperature": 0.6,  # 优化：组摘要需要更多综合分析能力
        "top_p": 0.9,        # 保持一致性
        "frequency_penalty": 0.3,  # 减少以允许重要主题的重复
        "presence_penalty": 0.3    # 鼓励发现组级别的新模式
    },
    "generate_final_summary_part": {
        "temperature": 0.7,  # 优化：最终摘要需要更高的综合能力
        "top_p": 0.92,       # 适度提高以支持更好的综合表达
        "frequency_penalty": 0.2,  # 降低以保持关键信息的完整性
        "presence_penalty": 0.3    # 鼓励发现整体层面的新洞察
    },

    # 剧本生成相关 - 基于Gemini 2.5 Pro最佳实践优化
    "generate_episode_script": {
        "temperature": 1.4,  # 优化：创意写作最佳范围1.2-1.6，提高创意性和多样性
        "top_p": 0.95,       # 保持高多样性以支持创意表达
        "frequency_penalty": 0.3,  # 适度减少重复，但不抑制创意
        "presence_penalty": 0.4    # 鼓励新概念和表达方式
    },
    "generate_full_script": {
        "temperature": 1.5,  # 优化：长剧集需要更高创意性，支持复杂叙事
        "top_p": 0.92,       # 平衡创意性和连贯性
        "frequency_penalty": 0.4,  # 避免重复但保持表达丰富性
        "presence_penalty": 0.5    # 强烈鼓励新颖表达和概念
    },
    "convert_script_to_json": {
        "temperature": 0.2,  # 优化：结构化转换需要高精确性，降低随机性
        "top_p": 0.85,       # 降低以提高准确性和一致性
        "frequency_penalty": 0.1,  # 最小化以保持结构完整性
        "presence_penalty": 0.0    # 避免引入不必要的新元素
    },
    "generate_episode_structure": {
        "temperature": 1.0,  # 优化：结构设计需要平衡创意和逻辑性
        "top_p": 0.9,        # 适中的多样性，保持结构合理性
        "frequency_penalty": 0.3,  # 适度避免重复结构模式
        "presence_penalty": 0.4    # 鼓励创新的结构元素
    },
    "allocate_content": {
        "temperature": 0.4,
        "top_p": 0.85,
        "frequency_penalty": 0.3,
        "presence_penalty": 0.2
    },
    "generate_episode_outline": {
        "temperature": 0.6,
        "top_p": 0.9,
        "frequency_penalty": 0.2,
        "presence_penalty": 0.1
    },

    # 剧本审查和优化 - 基于任务特性优化
    "review_script": {
        "temperature": 0.4,  # 优化：审查需要一定客观性但允许洞察力
        "top_p": 0.9,        # 降低以提高分析的一致性和准确性
        "frequency_penalty": 0.2,  # 减少以避免遗漏重要问题
        "presence_penalty": 0.1    # 轻微鼓励发现新问题
    },
    "refine_script": {
        "temperature": 1.2,  # 优化：改进需要创意性但保持原有结构
        "top_p": 0.88,       # 平衡创意改进和结构保持
        "frequency_penalty": 0.4,  # 避免重复改进模式
        "presence_penalty": 0.3    # 鼓励创新的改进方法
    },

    # 分析相关
    "analyze_chapter": {
        "temperature": 0.4,
        "top_p": 0.9,
        "frequency_penalty": 0.4,
        "presence_penalty": 0.2
    },
    "analyze_story_characters": {
        "temperature": 0.3,
        "top_p": 0.95,
        "frequency_penalty": 0.3,
        "presence_penalty": 0.1
    },
    "analyze_group_density": {
        "temperature": 0.3,
        "top_p": 0.9,
        "frequency_penalty": 0.2,
        "presence_penalty": 0.0
    },

    # 故事理解和规划 - 基于任务复杂度优化
    "generate_global_outline": {
        "temperature": 0.8,  # 优化：全局大纲需要创意性和结构性的平衡
        "top_p": 0.9,        # 保持适度多样性
        "frequency_penalty": 0.2,  # 降低以保持重要主题的连贯性
        "presence_penalty": 0.3,   # 鼓励发现新的故事元素
        # 模型特定参数
        LLM_PROVIDER_DEEPSEEK: {
            "temperature": 0.6,  # DeepSeek优化
            "frequency_penalty": 0.3
        }
    },
    "generate_story_understanding": {
        "temperature": 0.6,  # 优化：故事理解需要分析深度和洞察力
        "top_p": 0.88,       # 平衡准确性和洞察力
        "frequency_penalty": 0.2,  # 降低以保持分析的完整性
        "presence_penalty": 0.2    # 适度鼓励新的理解角度
    },
    "determine_total_episodes": {
        "temperature": 0.7,  # 优化：集数分配需要创意性规划能力
        "top_p": 0.9,        # 保持规划的多样性
        "frequency_penalty": 0.2,  # 降低以保持规划逻辑的一致性
        "presence_penalty": 0.3    # 鼓励创新的分配策略
    },

    # 图像生成相关
    "generate_storytelling_image_prompt": {
        "temperature": 0.3,
        "top_p": 0.8,
        "frequency_penalty": 0.2,
        "presence_penalty": 0.1
    },
    "generate_storytelling_style": {
        "temperature": 0.3,
        "top_p": 0.8,
        "frequency_penalty": 0.2,
        "presence_penalty": 0.1
    },
    "generate_all_characters": {
        "temperature": 0.3,
        "top_p": 0.8,
        "frequency_penalty": 0.2,
        "presence_penalty": 0.1
    },

    "identify_entities": {
        "temperature": 0.3,
        "max_tokens": 1000,
        "top_p": 0.95
    },
    "map_validate_entities": {
        "temperature": 0.2,
        "max_tokens": 800,
        "top_p": 0.9
    },
    "validate_subtitle": {
        "temperature": 0.2,
        "max_tokens": 500,
        "top_p": 0.9
    },
    "entity_replacement_validation": {
        "temperature": 0.2,
        "max_tokens": 500,
        "top_p": 0.9
    },
    "context_validation": {
        "temperature": 0.2,
        "max_tokens": 500,
        "top_p": 0.9
    },
    # 新增 gore_neutralizer 配置
    "gore_neutralizer": {
        "temperature": 0.1,  # 极低温以获得事实性重写
        "top_p": 0.7,        # 允许少量灵活性
        "max_tokens": 512,   # 足够替换后的文本和JSON开销
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
        # 指定使用 DeepSeek (使用基础 chat 模型)
        LLM_PROVIDER_DEEPSEEK: {
            "model_key": MODEL_KEY_DEEPSEEK_CHAT # 强制使用此模型
        }
    },
    # 默认参数配置
    "default": {
        "temperature": 0.6,
        "top_p": 0.9,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0
    }
}

def get_gpt_parameters(api_function: str, llm_type: str = None, model_key: str = None) -> Dict[str, Any]:
    """获取指定API函数的GPT参数配置，支持模型特定参数"""
    # 获取基础参数
    params = GPT_PARAMETERS.get(api_function, {}).copy()
    used_default = False
    if not params: # 如果没有找到 api_function 的特定参数，使用默认值
        params = GPT_PARAMETERS.get("default", {}).copy()
        used_default = True

    # 合并模型特定参数
    if llm_type and model_key:
        model_params = MODEL_CONFIGS.get(llm_type, {}).get(model_key, {})
        allowed_params = model_params.get("allowed_params", {})

        # 处理allowed_params是列表的情况 - 不再忽略，而是创建一个空字典
        # allowed_params现在只是表示哪些参数是允许的，不再直接作为参数值
        if isinstance(allowed_params, list):
            # 从GPT_PARAMETERS或默认参数中获取列表中指定的参数值
            default_params = GPT_PARAMETERS.get("default", {})
            api_specific_params = GPT_PARAMETERS.get(api_function, {})

            # 记录用于调试的信息
            #logger.debug(f"从列表格式的allowed_params创建参数字典: {allowed_params}")

            # 为列表中的每个参数设置值
            for param_name in allowed_params:
                # 跳过特殊处理的参数如deployment_name, api_version等
                if param_name in ["deployment_name", "api_version", "model"]:
                    continue

                # 优先使用API特定参数，其次使用默认参数
                if param_name in api_specific_params:
                    params[param_name] = api_specific_params[param_name]
                elif param_name in default_params:
                    params[param_name] = default_params[param_name]
        elif isinstance(allowed_params, dict):
            # 如果是字典，直接更新
            params.update(allowed_params)

    # 添加默认 max_tokens
    if "max_tokens" not in params:
        default_max = MODEL_CONFIGS.get(llm_type, {}).get(model_key, {}).get("default_max_tokens")
        if default_max:
            params["max_tokens"] = default_max

    # 移除模型特定参数字典（保持不变）
    for provider in [LLM_PROVIDER_AZURE, LLM_PROVIDER_OPENAI,
                    LLM_PROVIDER_ANTHROPIC, LLM_PROVIDER_DEEPSEEK, LLM_PROVIDER_LOCAL_DEEPSEEK]:
        if provider in params:
            del params[provider]

    logger.debug(f"获取参数 for api_function='{api_function}', llm_type='{llm_type}', model_key='{model_key}'. 参数: {params}")

    return params

# 辅助函数
def get_model_param_compatibility(llm_type: str, model_key: str) -> dict:
    """获取模型参数兼容性配置"""
    if llm_type not in MODEL_PARAM_COMPATIBILITY:
        return {
            "supports_temperature": True,
            "supports_top_p": True
        }

    if model_key not in MODEL_PARAM_COMPATIBILITY[llm_type]:
        # 返回该 llm_type 的第一个模型的兼容性配置作为默认值
        return next(iter(MODEL_PARAM_COMPATIBILITY[llm_type].values()))

    return MODEL_PARAM_COMPATIBILITY[llm_type][model_key]

from modules.prompts_episodes import episodes_prompts, episodes_system_messages
from modules.prompts_image import image_prompts, image_system_messages
from modules.prompts_utils import utils_prompts, utils_system_messages
from modules.prompts_story import story_prompts, story_system_messages
from modules.prompts_translation import translation_prompts, translation_system_messages
from modules.prompts_novel_rewrite import novel_rewrite_prompts, novel_rewrite_system_messages

# 创建一个统一的提示词集合
PROMPT_COLLECTIONS = {
    'episodes': {
        'prompts': episodes_prompts,
        'system_messages': episodes_system_messages
    },
    'image': {
        'prompts': image_prompts,
        'system_messages': image_system_messages
    },
    'utils': {
        'prompts': utils_prompts,
        'system_messages': utils_system_messages
    },
    'story': {
        'prompts': story_prompts,
        'system_messages': story_system_messages
    },
    'translation': {
        'prompts': translation_prompts,
        'system_messages': translation_system_messages
    },
    'novel_rewrite': {
        'prompts': novel_rewrite_prompts,
        'system_messages': novel_rewrite_system_messages
    }
}

# 从配置文件加载模型设置
def load_model_settings(settings_path: str = None) -> Tuple[str, str]:
    """
    从配置文件加载模型设置

    Args:
        settings_path: 配置文件路径，默认为 'setting/settings.yaml'

    Returns:
        Tuple[str, str]: (llm_type, model_key) 元组
    """
    try:
        # 设置默认值
        default_llm_type = LLM_PROVIDER_AZURE
        default_model_key = MODEL_KEY_GPT41

        # 使用默认路径或指定路径
        if settings_path is None:
            settings_path = os.path.join('setting', 'settings.yaml')

        # 尝试从 settings.yaml 加载配置
        if os.path.exists(settings_path):
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = yaml.safe_load(f)

            # 获取 LLM 类型和模型键名
            llm_type = settings.get('LLM_TYPE', default_llm_type)
            model_key = settings.get('MODEL_KEY', default_model_key)

            # 验证 LLM 类型
            valid_llm_types = [
                LLM_PROVIDER_AZURE,
                LLM_PROVIDER_OPENAI,
                LLM_PROVIDER_ANTHROPIC,
                LLM_PROVIDER_DEEPSEEK,
                LLM_PROVIDER_LOCAL_DEEPSEEK,
                LLM_PROVIDER_GOOGLE  # 添加 Google 提供商
            ]
            if llm_type not in valid_llm_types:
                logger.warning(f"无效的 LLM 类型: {llm_type}，使用默认值: {default_llm_type}")
                llm_type = default_llm_type

            # 验证模型键名与 LLM 类型的兼容性
            if llm_type == LLM_PROVIDER_AZURE and model_key not in [MODEL_KEY_O3_MINI, MODEL_KEY_GPT41]:
                logger.warning(f"Azure 提供商不支持所选模型 {model_key}，将使用默认模型 {MODEL_KEY_O3_MINI}")
                model_key = MODEL_KEY_O3_MINI
            elif llm_type == LLM_PROVIDER_DEEPSEEK and model_key not in [MODEL_KEY_DEEPSEEK_CHAT, MODEL_KEY_DEEPSEEK_REASONER, MODEL_KEY_ARK_DEEPSEEK_R1]:
                logger.warning(f"DeepSeek 提供商不支持所选模型 {model_key}，将使用默认模型 {MODEL_KEY_DEEPSEEK_CHAT}")
                model_key = MODEL_KEY_DEEPSEEK_CHAT
            elif llm_type == LLM_PROVIDER_LOCAL_DEEPSEEK and model_key not in [MODEL_KEY_LOCAL_DEEPSEEK_R1]:
                logger.warning(f"本地 DeepSeek 提供商不支持所选模型 {model_key}，将使用默认模型 {MODEL_KEY_LOCAL_DEEPSEEK_R1}")
                model_key = MODEL_KEY_LOCAL_DEEPSEEK_R1
            elif llm_type == LLM_PROVIDER_GOOGLE and model_key not in [MODEL_KEY_GEMINI_25_PRO]:
                logger.warning(f"Google 提供商不支持所选模型 {model_key}，将使用默认模型 {MODEL_KEY_GEMINI_25_PRO}")
                model_key = MODEL_KEY_GEMINI_25_PRO

            logger.info(f"从配置文件加载模型设置: LLM 类型={llm_type}, 模型键名={model_key}")
            return llm_type, model_key

        else:
            logger.info(f"未找到配置文件，使用默认模型设置: LLM 类型={default_llm_type}, 模型键名={default_model_key}")
            return default_llm_type, default_model_key

    except Exception as e:
        logger.error(f"加载模型设置失败: {str(e)}，使用默认值")
        return LLM_PROVIDER_AZURE, MODEL_KEY_O3_MINI

# 预加载默认模型设置
DEFAULT_LLM_TYPE, DEFAULT_MODEL_KEY = load_model_settings()

# 获取提示词和系统消息
def get_prompt_and_system_message(api_function: str) -> Tuple[str, str]:
    """获取指定API函数的提示词和系统消息"""
    # 从 PROMPT_COLLECTIONS 中查找提示词和系统消息
    for collection_name, collection in PROMPT_COLLECTIONS.items():
        if api_function in collection['prompts']:
            prompt_template = collection['prompts'][api_function]
            # 如果有对应的系统消息，使用它，否则使用通用系统消息
            system_message = collection['system_messages'].get(
                api_function,
                collection['system_messages'].get('default', "You are a helpful assistant.")
            )
            return prompt_template, system_message

    # 没有找到对应的提示词和系统消息，返回默认值
    logger.warning(f"未找到API函数 {api_function} 的提示词和系统消息，使用默认值")
    return "{content}", "You are a helpful assistant."
