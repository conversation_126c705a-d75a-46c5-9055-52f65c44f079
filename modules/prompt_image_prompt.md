# 图像提示词生成——完整流程说明

下面的文档按顺序描述从文本输入到最终提交给 ComfyUI/Flux 工作流的每一个步骤，重点说明各字段的组织、拼接与注入方式。

---

## 1. 全局分析阶段

1. **输入**  
   - `full_text`：整个故事的完整文本。

2. **调用 LLM 提取风格与角色**  
   ```python
   global_data = call_llm_json_response(
       api_function="generate_storytelling_style",
       prompt_data={"text": full_text},
       expected_fields=["visual_style", "style_details"]
   )
   character_data = call_llm_json_response(
       api_function="generate_all_characters",
       prompt_data={"text": full_text},
       expected_fields=["all_characters"]
   )

	3.	输出结果
	•	visual_style：整体视觉风格，比如 “High-Contrast Black-and-White Style”。
	•	style_details：包含
	•	environment.time_period
	•	environment.location_feel
	•	color_palette、lighting、composition_preference、theme…
	•	all_characters：角色列表，每项含
	•	character_id
	•	character_name
	•	descriptor 或 appearance

⸻

2. 对每个段落生成初始 Prompt

针对每个段落 segment_texts[i]（共 N 段）执行以下步骤：

2.1 构建 Prompt 输入字典

num_images      = num_images_list[i]                   # 本段需要的图片数
prompt_suffix   = "" if num_images == 1 else "s"
prompt_input = {
  "text":           segment_texts[i],
  "visual_style":   visual_style,
  "style_details":  style_details,
  "all_characters": simplified_characters,            # 只含名称的角色列表
  "num_images":     num_images,
  "prompt_suffix":  prompt_suffix,
  "costume_style":  f"Clothing appropriate for the {style_details['environment']['time_period']} era",
  "location_feel":  style_details['environment']['location_feel']
}

	•	costume_style 用来指示时代特征
	•	location_feel 与 style_details.environment.location_feel 保持一致

2.2 调用 LLM 生成原始提示词

raw_image_prompt = call_llm_json_response(
    api_function="generate_storytelling_image_prompt",
    prompt_data=prompt_input,
    expected_fields=["prompts"]
)

	•	raw_image_prompt 结构

{
  "prompts": [
    {
      "scene_type": "concrete",           // concrete 或 abstract
      "chronological_rank": 1,            // 如果 num_images >1，则有序号
      "blocks": {
        "subject_action": "...",          // ≤10 字，以角色名开头
        "setting": "...",                 // 地点描述，含隐喻替换
        "environment_elements": [...],    // 隐喻列表（可选）
        "style": "...",                   // 风格描述，≤12 字
        "camera": "...",                  // 机位镜头
        "mood": "..."                     // 情绪 1–2 词
      },
      "characters": [
        { "name": "角色A" }               // 最多 2 人
      ]
    },
    …（可能有多条）
  ]
}



⸻

3. 程序化角色信息增强

将 raw_image_prompt 与 all_characters 结合，为每个角色注入详细 descriptor：

enhanced_prompt = enhance_prompts_with_character_info(
    image_prompt=raw_image_prompt,
    all_characters=all_characters
)

	•	行为
	•	遍历每个 prompt_item
	•	对 prompt_item["characters"] 中出现的角色，根据 all_characters 找到对应描述
	•	增加/更新 character["descriptor"] 字段

⸻

4. Flux Prompt 构建与 Seed

对 enhanced_prompt["prompts"] 中的每一项 prompt_item：
	1.	提取 Anchors

anchors = [ ch["descriptor"] for ch in prompt_item["characters"][:2] ]


	2.	提取 Scene Bits

if prompt_item["scene_type"] == "concrete":
    scene_bits = [
      prompt_item["blocks"]["subject_action"],
      prompt_item["blocks"]["setting"],
      prompt_item["blocks"].get("costume", "")
    ]
else:  # abstract
    scene_bits = prompt_item["blocks"]["symbolic_environment"]


	3.	提取 Style Bits

style_bits = [
  prompt_item["blocks"].get("style", ""),
  prompt_item["blocks"].get("mood", ""),
  prompt_item["blocks"].get("camera", "")
]


	4.	拼接 Flux Prompt

prompt_item["flux_prompt"] = build_flux_prompt(
  anchors, scene_bits, style_bits,
  prompt_item["scene_type"],
  with_style=True
)

	•	拼接规则
	1.	Anchors 用 ,  连接，置于最前
	2.	Scene Bits 用 ;  连接，追加其后
	3.	Style Bits 再次用 ,  连接，放在末尾
	•	示例格式

anchor1, anchor2; action 描述; setting 描述; style 描述, mood, camera


	5.	计算随机种子

prompt_item["seed"] = compute_seed_from_characters(prompt_item["characters"])



⸻

5. 注入 ComfyUI/Flux 工作流
	1.	加载工作流 JSON

workflow_data = load_workflow(workflow_path)


	2.	调用参数注入函数

workflow_data = set_workflow_parameters(
  workflow_data,
  prompt_data=prompt_item,
  workflow_config=WORKFLOW_CONFIG["storytelling-api"]
)

	•	正向提示词节点 (action)
	•	将 prompt_item["flux_prompt"] 写入 nodes[node_id].inputs["text"]（或 prompt）
	•	样式节点 (style)
	•	如需兼容旧流程，写入风格文本
	•	无负面提示词节点

⸻

6. 提交生成与结果获取
	1.	提交到服务器

prompt_id = queue_prompt(server_address, prompt_data=prompt_item["flux_prompt"])


	2.	轮询查询

img_info = check_queue_status(
  server_address,
  prompt_id,
  workflow_config=WORKFLOW_CONFIG["storytelling-api"]
)

	•	返回：{"filename": "...", "subfolder": "...", "type": "..."}

	3.	保存映射
	•	将获得的 filename 路径赋值给对应段落的 paragraph["generated_image"] 或 generated_images

⸻

至此，整个流程完成：
文本 → LLM(风格+角色) → 构建 Prompt 输入 → LLM(原始提示词) → 程序化增强 → Flux Prompt 拼接 → 工作流注入 → 提交生成 → 获取结果。
所有字段的拼接与处理均按上述规则逐步执行，确保每一步输入输出格式一致。