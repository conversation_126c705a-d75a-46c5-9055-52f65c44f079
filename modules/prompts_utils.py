"""
This module contains prompts for subtitle correction and validation.
"""

# System messages configuration
utils_system_messages = {
    "identify_entities": (
        "You are an expert in named entity recognition, specializing in identifying "
        "and classifying entities in text. Focus on accurate detection of people, "
        "locations, organizations, dates, and other significant entities while "
        "maintaining contextual understanding."
    ),
    "map_validate_entities": (
        "You are an expert in entity validation and mapping, specializing in "
        "comparing entities between source text and subtitles. Focus on identifying "
        "discrepancies and suggesting accurate corrections while maintaining "
        "contextual accuracy."
    ),
    "batch_subtitle_validation": (
        "You are an expert in subtitle validation, specializing in checking grammar, "
        "context accuracy, and consistency. Focus on ensuring subtitle quality while "
        "maintaining natural language flow and accurate meaning."
    ),
    "describe_video_frame": (
        "You are an expert in visual analysis and description, specializing in "
        "providing detailed, accurate, and contextual descriptions of video frames. "
        "Focus on identifying key elements, actions, composition, and overall context "
        "while maintaining natural language flow."
    ),
    "gen_keywords": (
        "You are an expert in keyword generation, specializing in creating "
        "multiple keywords to capture different aspects of provided script content. "
        "These keywords will be used for searching images and articles relevant to the script."
    ),
}

utils_prompts = {
    'identify_entities': """
    Analyze and identify all named entities in the following text with precise classification.
    
    Text: {text}
    Output Language: English
    
    Requirements:
    1. Entity Recognition:
       - Identify all named entities with high accuracy
       - Apply correct classification categories
       - Consider contextual relationships
       - Maintain consistency across similar entities
    
    2. Classification Guidelines:
       - Person:
         * Individual names (full names, nicknames)
         * Professional titles
         * Historical figures
       - Location:
         * Geographic places
         * Buildings and landmarks
         * Administrative regions
       - Organization:
         * Companies and businesses
         * Government bodies
         * Institutions and groups
       - Date:
         * Specific dates and times
         * Time periods and eras
         * Recurring events
       - Other:
         * Products and brands
         * Events
         * Cultural references
    
    3. Quality Requirements:
       - Ensure complete entity capture
       - Maintain classification accuracy
       - Consider cultural context
       - Handle ambiguous cases appropriately
    
    Response Format:
    {{
        "result": [
            {{
                "entity": "Exact entity text",
                "type": "classification_category",
                "context": "Brief context note if needed"
            }}
        ]
    }}
    """,

    'map_validate_entities': """
    Compare and validate named entities between transcript and subtitles, with special focus on phonetic similarity.
    
    Source Data:
    - Transcript Entities: {transcript_entities}
    - Subtitle Entities: {subtitle_entities}
    
    Validation Requirements:
    1. Phonetic Matching Priority:
       - ONLY map entities that sound similar when pronounced
       - Reject mappings where pronunciations differ significantly
       - Consider regional accent variations
       - Pay attention to similar-sounding characters in Chinese/Japanese
    
    2. Mapping Rules:
       - Primary criterion: Must have similar pronunciation
       - Secondary: Consider meaning and context
       - For Chinese: Consider both pinyin and tones
       - For Japanese: Consider both on-yomi and kun-yomi readings
    
    3. Quality Standards:
       - High confidence: Nearly identical pronunciation
       - Medium confidence: Similar pronunciation with minor variations
       - Low confidence: Do not map if pronunciations are different
       - Reject mappings where only meaning matches but sound differs
    
    Response Format (JSON):
    {{
        "mappings": [
            {{
                "source": "original_entity",
                "target": "correct_form",
                "confidence": "high/medium/low",
                "notes": "Explanation focusing on pronunciation similarity"
            }}
        ]
    }}
    
    Important Notes:
    - ONLY suggest replacements where pronunciation is similar
    - Reject matches based purely on meaning without phonetic similarity
    - Include pronunciation details in notes for verification
    - Mark confidence as 'low' if unsure about pronunciation match
    - Response MUST be valid JSON format
    """,

    'batch_subtitle_validation': """
    Validate and assess the quality of subtitle replacements for accuracy and naturalness.
    
    Content:
    {batch_subtitles}
    
    Validation Requirements:
    1. Language Quality:
       - Grammar accuracy
       - Natural expression
       - Proper punctuation
       - Consistent style
    
    2. Content Accuracy:
       - Meaning preservation
       - Context alignment
       - Cultural appropriateness
       - Technical terminology
    
    3. Subtitle Standards:
       - Length appropriateness
       - Reading flow
       - Timing compatibility
       - Format consistency
    
    Response Format:
    {{
        "subtitles": [
            {{
                "index": "subtitle_number",
                "validation": {{
                    "is_valid": boolean,
                    "issues": [
                        {{
                            "type": "grammar/context/flow/other",
                            "description": "Issue description",
                            "suggestion": "Correction suggestion"
                        }}
                    ]
                }}
            }}
        ]
    }}
    """,

    "generate_ner": """
    Analyze and identify named entities requiring standardized translation from the following text pair.

    Source text: {source_text}
    Translated text: {translated_text}
    Source language: {source_lang}
    Target language: {target_lang}

    Requirements:
    1. Identify entities requiring standardized translation:
       - Person names (historical figures, people)
       - Location names (countries, cities, regions)
       - Organization names (companies, institutions)
       - Terms (brands, works, proper nouns)

    2. For each entity:
       - Identify both source and translated forms
       - Categorize entity type
       - Ensure accurate pairing between source and translation

    IMPORTANT: Return a strict JSON format with properly escaped characters:
        1. Use double quotes for strings
        2. Escape all special characters (\\n, \\", etc.)
        3. No line breaks within string values
        4. No comments or additional text

    [Response Format]
    {{
        "entities": [
            {{
                "name": "<entity_name_in_source>",
                "translated_name": "<entity_name_in_translation>",
                "category": "<PERSON|LOCATION|ORGANIZATION|TERM>"
            }}
        ]
    }}
    """,

    "check_ner": """
    Evaluate the accuracy and standardization of entity translation.

    Input:
    - Source text: {source_text}
    - Translated text: {translated_text}
    - Original entity: {entity_name}
    - Current translation: {translated_name}
    - Wikidata standard translation: {wikidata_translation}
    - Entity type: {entity_category}
    - Language pair: {source_lang} -> {target_lang}

    Evaluation criteria:
    1. Translation accuracy
    2. Standardization level
    3. Target language conventions
    4. Domain-specific requirements

    IMPORTANT: Return a strict JSON format with properly escaped characters:
        1. Use double quotes for strings
        2. Escape all special characters (\\n, \\", etc.)
        3. No line breaks within string values
        4. No comments or additional text

    [Response Format]
    {{
        "is_correct": "boolean",
        "suggested_correction": "string"
    }}
    """,

    "describe_video_frame": """
    Analyze and provide a detailed description of the video frame from the video titled: {video_title}
    Video context: {filename}
    The image is provided as a base64-encoded string in the following format:
    data:image/jpeg;base64,{image}

    Requirements:
    1. Content Analysis:
       - Main subjects and their actions
       - Setting and environment
       - Visual composition and layout
       - Notable objects and elements
       - Lighting and atmosphere
       - Relevance to video title context

    2. Description Guidelines:
       - Start with the most prominent elements
       - Include relevant context from video title
       - Describe spatial relationships
       - Note significant details
       - Maintain objective perspective
       - Keep description concise (maximum 150 words)

    3. Quality Standards:
       - Clear and concise language
       - Logical flow of information
       - Accurate technical terms
       - Natural descriptive style
       - Comprehensive yet brief coverage
       - Context-aware description
       - Strict word limit: 150 words maximum

    IMPORTANT: Return a strict JSON format with properly escaped characters:
        1. Use double quotes for strings
        2. Escape all special characters (\\n, \\", etc.)
        3. No line breaks within string values
        4. No comments or additional text
        5. Description must not exceed 150 words

    [Response Format]
    {{
        "description": "Concise description of the video frame (max 150 words)"
    }}
    """,

    'gen_keywords': """
    Context: Generate multiple keywords to capture different aspects of the provided script content. These keywords will be used for searching images and articles relevant to the script.

    Script: {script}
    Language: The keywords should be generated in {language}

    Instructions:
    1. First, analyze the entire script thoroughly to understand its complete content.
    2. Create one primary topic keyword that represents the overall theme or subject (similar to a title).
    3. Then generate 2 additional keywords that capture different aspects or perspectives of the content.
    4. Each keyword should:
       - Be specific enough for targeted searches
       - Focus on a distinct angle or subtopic
       - Be concise (2-5 words each)
       - Collectively cover the breadth of the script's content

    Response Format:
    {{
        "topic": "The primary topic or theme keyword",
        "keywords": [
            "First additional keyword",
            "Second additional keyword"
        ]
    }}

    Note: Ensure the keywords represent significantly different aspects of the content, not just synonyms or minor variations of the same concept.
    """,

    'summarize_image_caption': """
    Context: Summarize the combined image descriptions (auto-generated caption, tags, writer's description) into a concise text focusing on concrete visual elements and key entities relevant for image matching.

    Combined Descriptions:
    - Auto-Caption: {auto_caption}
    - Tags: {tags}
    - Writer Caption: {writer_caption}

    Instructions:
    1. Identify the most important visual elements, objects, actions, and named entities present in the descriptions.
    2. Synthesize these key elements into a single, coherent summary.
    3. The summary should prioritize information useful for matching this image to a paragraph describing a scene.
    4. Keep the summary concise, ideally under 60 tokens.
    5. Output Language: English

    IMPORTANT: Return a strict JSON format with properly escaped characters:
        1. Use double quotes for strings
        2. Escape all special characters (\\n, \\", etc.)
        3. No line breaks within string values
        4. No comments or additional text

    [Response Format]
    {{
        "summary": "Concise summary focusing on visual elements and entities (max 60 tokens)"
    }}
    """,
}

