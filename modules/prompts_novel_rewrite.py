# modules/prompts_novel_rewrite.py
"""
小说重写相关的提示词模板

用于novel_rewrite功能的系统消息和提示词
"""

# 系统消息
novel_rewrite_system_messages = {
    "extract_spine_events": (
        "You are a story structure analyst. Extract 1-4 key events from the chapter summary that form the narrative spine. "
        "Each event should have importance (0-1) and tension (0-1) scores. Focus on events that drive the plot forward "
        "and create emotional impact."
    ),
    
    "extract_events": (
        "You are a narrative parser. Given a full episode script, list EVERY plot event that "
        "changes a character's goal, reveals new information, or shifts power."
    ),
    
    "default": (
        "You are a helpful AI assistant specialized in novel analysis and rewriting."
    )
}

# 提示词模板
novel_rewrite_prompts = {
    "extract_spine_events": """
    Analyze the provided chapter summary and extract 1-4 key events that form the narrative spine.
    
    Input:
    - Chapter Summary: {chapter_summary}
    - Alias Map: {alias_map}
    - Max Events: {max_events}
    
    Requirements:
    1. Extract events that drive the plot forward
    2. Focus on events that create emotional impact
    3. Assign importance score (0-1): how crucial this event is to the overall story
    4. Assign tension score (0-1): how much emotional/dramatic tension this event creates
    5. Use character names from the alias map when possible
    
    Return JSON format:
    {{
        "spine_events": [
            {{
                "event": "Brief description of the event",
                "importance": 0.8,
                "tension": 0.7
            }}
        ]
    }}
    """,
    
    "extract_events": """
    Parse the provided episode script and extract ALL plot events that change the story state.
    
    Input:
    - Script: {script}
    
    Requirements:
    1. List every event that changes a character's goal
    2. Include events that reveal new information
    3. Include events that shift power dynamics
    4. Format as "<actor><verb><object>" when possible
    5. Be comprehensive but concise
    
    Return JSON format:
    {{
        "events": [
            "Character A discovers secret",
            "Character B makes decision",
            "Conflict escalates between X and Y"
        ]
    }}
    """
}

# 默认配置
DEFAULT_NOVEL_REWRITE_CONFIG = {
    "max_spine_events": 4,
    "importance_threshold": 0.3,
    "tension_threshold": 0.3,
    "enable_character_mapping": True
}
