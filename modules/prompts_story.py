#modules/story_prompt.py

story_system_messages = {
    "gen-story": (
        "You are an expert scriptwriter for audio programs. Your task is to create, "
        "validate, and refine scripts based on multiple articles to ensure they are clear, "
        "engaging, and suitable for text-to-speech generation. Focus on character development, "
        "plot structure, setting, and tension while ensuring factual accuracy and coherence."
    ),
    "describe-image": (
        "You are an expert image describer. Your task is to generate detailed and contextual "
        "descriptions of images based on the provided context and image URL. Ensure that "
        "the description is precise, comprehensive, and takes the context into account."
    ),
    "generate-slide-cover": (
        "You are an expert in summarizing texts. Your task is to summarize the following paragraph "
        "into a concise title and one sentence."
    ),
    "gen-metadata": (
        "You are an expert video metadata generator. Your task is to create detailed and engaging "
        "metadata for YouTube videos based on the provided script. This includes the title, "
        "description, tags, and appropriate category."
    ),
    "gen-short-descriptions": (
        "You are an expert in generating concise and relevant short descriptions. Your task is to "
        "create a list of unique and context-specific descriptions for each paragraph of an article. "
        "Ensure that each description is tailored to the content of the paragraph, capturing its main "
        "themes and concepts. Avoid repetition across descriptions and ensure that each is distinct "
        "and relevant to the specific context of the paragraph."
    ),
    "review-scripts": (
        "You are a top-tier author. Your task is to critically review the provided story script, "
        "identifying its shortcomings and suggesting necessary improvements. Focus on narrative "
        "coherence, character development, plot structure, pacing, and detail richness to ensure the "
        "script is engaging and captivating for the audience."
    ),
    "refine-final-scripts": (
        "You are a top-tier author. Your task is to refine the provided story script based on review feedback. "
        "Focus on implementing the suggested improvements to create an engaging, detail-rich narrative that captures "
        "and holds the audience's attention, ensuring the script is polished and ready for its intended audience."
    ),
    "extract-topics": (
        "You are an expert in content analysis. Your task is to extract 2-5 broad themes from the provided web content. "
        "These themes should encapsulate the core ideas, emotions, or motifs present in the source material, serving as "
        "the foundation for developing a structured narrative across multiple acts."
    ),
    "find-relevant-theme": (
        "You are an expert in thematic analysis. Your task is to determine the most relevant theme for each of the given "
        "content blocks based on the identified themes provided. Every content block must be assigned to a theme, ensuring "
        "a comprehensive thematic structure for the narrative."
    ),
    "generate-act-structure": (
        "You are an expert in narrative structure. Your task is to use the extracted themes and their corresponding content "
        "blocks to create a well-structured sequence of Acts that form a cohesive and balanced narrative. Each Act should "
        "align with one or more themes and reference the relevant content blocks that support these themes."
    ),
    "gen_keyword": (
        "You are an expert in keyword generation. Your task is to generate a highly relevant keyword or short phrase from the provided script, capturing its core theme and main concepts. This keyword will be used for searching articles most pertinent to the script's overall content."
    )
}

story_prompts = {
    'describe-image': """
    Context: The image description is needed for generating accurate embeddings to enable image search functionality. 
    The image data is provided as a URL.

    Objective: Generate a detailed and accurate description of the provided image (Image_url) for creating embeddings 
    used in image retrieval systems, referencing the context (Image_context).

    Image_context: {context}
    Image_url: {image_url}
    Style: Descriptive and precise, focusing on key visual elements.
    Tone: Professional and clear.
    Audience: Developers who will use the description for embedding and search purposes.

    Response:
    Generate a detailed description of the image, considering the provided context. Ensure the description is 
    precise, comprehensive, and provides a vivid portrayal of the image content.
    """,

    'optimize-content': """
    Context: Optimize the content of a single act within a larger narrative, considering global analysis and act sequence.

    Act Content: {act_content}
    Unique and Similar Content: {unique_and_similar_content}
    Global Analysis: {global_analysis}
    Act Index: {act_index}

    Instructions:
    1. Review the Act Content, focusing on its unique elements and how it fits into the overall narrative.
    2. Consider the Unique and Similar Content to emphasize distinctive aspects and handle repetitive elements appropriately.
    3. Use the Global Analysis to ensure consistency with overarching themes and narrative progression.
    4. Take into account the Act Index to maintain proper story flow and context relative to other acts.
    5. Optimize the content by:
       - Enhancing unique elements identified for this act
       - Rephrasing or removing repetitive content
       - Ensuring smooth transitions and narrative flow
       - Maintaining consistency with global themes and overall story structure
       - Adjusting the content to fit its position in the overall narrative (based on Act Index)
    6. Preserve the core narrative and key information while improving clarity, engagement, and uniqueness.
    7. Ensure the optimized content builds upon previous acts and sets up for subsequent acts.

    Guidelines:
    1. Use clear, understandable vocabulary; avoid obscure or overly formal expressions.
    2. Maintain objective narration, presenting both positive and negative aspects.
    3. Provide multi-perspective descriptions for controversial topics.
    4. Preserve factual accuracy and content depth.
    5. Use concise, direct sentence structures.
    6. Use descriptive language moderately, avoiding excessive embellishment.
    7. Maintain narrative coherence with appropriate transitions.
    8. Adjust language style according to the theme, maintaining overall consistency.

    Output:
    Provide an optimized version of the act content that:
    - Emphasizes unique elements
    - Reduces redundancy
    - Improves narrative flow and engagement
    - Aligns with the global themes and overall story structure
    - Fits seamlessly into the narrative progression based on its act index
    - Maintains the essence of the original content while enhancing its quality and relevance
    - Include only the narrative itself. Avoid any non-story elements, including act titles, markers, explanations, summaries, or meta-commentary.

    Your response should be the optimized act content, ready for integration into the larger narrative structure.
    """,
    
    'decide-keep-or-delete': """
    Context: Given a list of similar sentences, decide which sentence to keep and which to delete based on their contextual importance.
    
    Similar Sentences: {similar_sentences}
    Context: {context}
    
    Instructions:
    1. Analyze the context and the list of similar sentences.
    2. Determine which sentence has the most logical importance and should be kept.
    3. Identify sentences that are less important and can be deleted without affecting the overall understanding.
    4. Provide a clear decision on which sentence to keep and which to delete.
    
    Output:
    Provide a JSON object with the decision, specifying the sentence to keep and the sentences to delete. Use the following format:
    {{"keep": "string", "delete": ["string"]}}
    
    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,
    
   'extract-topics': """
    Context: Analyze the provided web content to extract 2-5 broad themes that encapsulate the core ideas, emotions, or motifs present in the source material. These themes will serve as the foundation for developing a structured narrative across multiple acts, ensuring depth and diversity in storytelling.

    Web Content: {web_content}

    Structure:
    - Focus on identifying key subjects, emotions, or recurring motifs that are prevalent throughout the content.
    - Ensure themes are broad enough to support the development of multiple acts, yet specific enough to provide clear direction for narrative creation.
    - Include a range of themes that reflect the diverse perspectives and tones present in the content.

    Style: 
    - Objective and comprehensive, capturing the full spectrum of ideas and emotions present in the content.
    
    Tone:
    - Neutral and analytical, presenting themes without bias towards positive or negative interpretations. Ensure balanced representation of both positive and negative aspects, especially for controversial topics.

    Audience:
    - Content analysts and narrative designers developing a balanced and comprehensive story structure.

    Response:
    - Provide 2-5 broad themes extracted from the web content.
    - Each theme should be a concise phrase that captures a key aspect of the content, regardless of its positive or negative connotations.
    - List the themes as a comma-separated string, ensuring they are distinct and represent the full range of perspectives in the content.

    Example Output:
    "Theme 1, Theme 2, Theme 3, Theme 4, Theme 5"
    """,

    'find-relevant-theme': """
    Context: Determine the most relevant theme for each of the given content blocks based on the identified themes provided in the list below. Every content block must be assigned to a theme.

    Content Blocks: {content_clusters}
    Themes: {themes}
    Style: Natural and engaging, ensuring the themes connect with the content in a way that feels intuitive to listeners.
    Tone: Conversational and insightful, as if discussing the themes with an interested friend. Ensure balanced representation of both positive and negative aspects, especially for controversial topics.
    Audience: Storytellers and content strategists aligning narrative content with themes.

    Response:
    - Provide a JSON object mapping each content block index to the most relevant theme by selecting exactly one theme from the provided list.
    - Every content block MUST be assigned to a theme. If a content block doesn't seem to fit any theme perfectly, assign it to the most closely related theme.
    - Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    - Ensure the selected themes are in English and match the language of the themes provided in the 'Themes' list.

    Example Output:
    {{
        "block-0": "Theme 1",
        "block-1": "Theme 2",
        "block-2": "Theme 1"
    }}
    """,

    'generate-act-structure': """
    Context: Use the extracted themes and their corresponding content blocks to create a well-structured sequence of Acts that form a cohesive and balanced narrative. Each Act should align with one or more themes and reference the relevant content blocks that support these themes. Ensure the structure reflects a nuanced portrayal of the character, especially where controversies or conflicting views arise, without speculative interpretation.

    Themes: {themes}
    Theme to Content Map: {theme_to_content_map}

    Style: Ensure smooth transitions between Acts while maintaining a clear and engaging narrative arc that reflects both the thematic progression and the complexity of the character's actions, especially in the face of controversy.

    Tone: Neutral and balanced. Each Act must present multiple perspectives where applicable, especially for controversial topics. Avoid favoring positive or negative interpretations, and base all content on verifiable actions or events without speculating on internal motivations.

    Audience: Storytellers and narrative designers seeking to construct a cohesive story structure that reflects both positive and negative aspects of the character.

    Response:
    Create a structured list of Acts based on the provided themes and content blocks. Each Act MUST represent a significant part of the narrative and MUST contain at least one content block.

    Important Instructions:
    1. Each Act MUST include at least one content block, directly related to the specified themes. Do not create Acts without content blocks.
    2. Focus on factual, observable actions and avoid speculative psychological interpretations unless explicitly mentioned in the source material.
    3. Present a balanced view by integrating contrasting viewpoints on controversial actions or decisions within the narrative structure.
    4. Use only the themes and content blocks provided in the Theme to Content Map.
    5. Distribute all content blocks across the Acts.
    6. Create as many Acts as necessary to include all content blocks, ideally between 3 and 5 Acts, to balance depth and narrative flow.
    7. Ensure that each Act summary not only captures the main events and ideas but also reflects how the Act connects to the previous and subsequent Acts in terms of narrative and thematic development.
    8. Prioritize narrative coherence, thematic relevance, and the interrelation of Acts, with attention to the complexity and multi-dimensional nature of the character, especially in handling controversies.
    9. Provide a brief summary for each Act, capturing the main events, developments, or ideas to be covered. This summary will guide the generation of detailed act content.

    Output Format:
    - Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    - The JSON object should have the following structure:
      * "act_structure": An array of objects, where each object represents an Act with the following fields:
        - "act_number": The number of the Act in the sequence (integer)
        - "title": A brief title for the Act (string)
        - "themes": The themes associated with this Act (array of strings)
        - "content_blocks": The content block identifiers associated with this Act (array of strings, must not be empty)
        - "summary": A brief summary of the main events, developments, or ideas to be covered in this Act (string), including how this Act transitions from the previous one and sets up the next.

    Example Output:
    {{
      "act_structure": [
        {{
          "act_number": 1,
          "title": "Act One - The Introduction",
          "themes": ["Theme 1", "Theme 2"],
          "content_blocks": ["block-1", "block-2"],
          "summary": "Introduce the main character and setting, establishing the initial conflict and key relationships. Sets up the foundation for the themes explored in the following acts, while subtly introducing controversial aspects."
        }},
        {{
          "act_number": 2,
          "title": "Act Two - Rising Tension",
          "themes": ["Theme 3"],
          "content_blocks": ["block-3", "block-4"],
          "summary": "Explore the challenges faced by the character, deepening the conflict and revealing new aspects of the themes, including contrasting viewpoints on key actions or decisions. Connects to themes introduced in Act One and leads to the climax."
        }},
        {{
          "act_number": 3,
          "title": "Act Three - The Climax",
          "themes": ["Theme 1"],
          "content_blocks": ["block-5", "block-6"],
          "summary": "Bring the main conflict to a head, resolving key issues while showing the impact of the character's actions on different stakeholders, presenting both positive and negative perspectives. Concludes the narrative threads developed in the previous acts."
        }}
      ]
    }}
    """,

    'gen-act-content': """
    Context:
    Create an engaging and factual narrative script for a specific act, drawing from the Act Source and guided by the provided Act Summary. The story should feel like it's being shared by a skilled storyteller, focusing on facts while remaining captivating for listeners.

    Theme: {theme}
    Act Title: {act_title}
    Act Source: {act_source}
    Previous Acts: {previous_acts}
    Act Summary: {act_summary}

    Instructions:
    1. **Narrative Style**:
       - Tell the story naturally and engagingly, highlighting key moments from the Act Source.
       - Use concise, factual descriptions to help listeners understand scenes and characters.
       - Focus on facts and events, minimizing non-factual scene descriptions.
       - Maintain a natural flow, as if narrating an exciting tale.
       - End the act leaving listeners eager for more, without cliché cliffhangers.

    2. **Tone and Language**:
       - Adopt a neutral, engaging tone.
       - Use everyday language and expressions, avoiding formal or academic words.
       - Keep the tone conversational and relatable, but not overly casual.
       - Vary pacing naturally, mirroring engaging storytelling.
       - Build interest organically, not forcefully.

    3. **Content Guidelines**:
       - Follow the provided Act Summary to guide the content and ensure it covers the main events, developments, or ideas.
       - Ensure narrative flows naturally from previous acts.
       - Aim for at least 2000 tokens, letting the story dictate its length.
       - Create an immersive experience without non-story elements or explicit narration cues.
       - Make full use of the unique details from the Act Source to enrich the narrative and bring characters to life.       
       - Align closely with the Act Source, minimizing non-factual descriptions. Use conversational, relatable language, avoiding overly formal or academic terms.
       - **Emphasize Uniqueness**: Ensure the content is unique within the act and does not repeat information from Previous Acts.
       - **Balanced Representation**: Ensure balanced representation of both positive and negative aspects, especially for controversial topics. Present different perspectives to provide a comprehensive view.

    Guidelines:
    1. Use clear, understandable vocabulary; avoid overly formal or colloquial expressions.
    2. Balance professionalism and readability; explain complex concepts when necessary.
    3. Objectively present positive and negative aspects; provide multi-angle descriptions for controversies.
    4. Use concise, direct sentence structures; maintain narrative flow.
    5. Use descriptive language and emotional expression moderately, without over-dramatization.
    6. Maintain factual accuracy; balance details and summaries.

    ### Output Guidelines:
    - **Do not include any part of the 'Act Source' or 'Previous Acts' in your output.**
    - Focus solely on creating the narrative.
    - Provide **only** the narrative itself, without any additional explanations, comments, or symbols.
    - Ensure continuity without obvious breaks or markers.
    - **Emphasize Uniqueness**: Ensure the content is unique within the act and does not repeat information from Previous Acts.
    - **Balanced Representation**: Ensure balanced representation of both positive and negative aspects, especially for controversial topics. Present different perspectives to provide a comprehensive view.
    """,

    'identify-unique-content': """
    Context: Analyze the given act content and compare it with other acts to identify unique elements, including similar but not identical content.

    Act Content: {act_content}
    Other Acts: {other_acts}

    Instructions:
    1. Thoroughly analyze the Act Content and compare it with the content of Other Acts.
    2. Identify elements that are unique to this act, including:
       - Specific events, information, or character developments
       - Themes or motifs not prominently featured in other acts
       - Distinctive narrative elements or stylistic features
    3. Also identify content that is similar but not identical to content in other acts.
    4. For similar content, briefly describe how it differs from related content in other acts.

    Output Format:
    Provide a JSON object with two keys:
    1. "unique_content": An array of strings, each representing a unique element found only in this act.
    2. "similar_content": An array of objects, each containing:
       - "content": The similar content found in this act
       - "difference": How this content differs from related content in other acts

    Example Output:
    {{
      "unique_content": [
        "Detailed description of the protagonist's childhood home",
        "Introduction of a new character, Dr. Smith"
      ],
      "similar_content": [
        {{
          "content": "Discussion of the economic impact of the war",
          "difference": "This act provides specific statistics, while other acts only mention it generally"
        }}
      ]
    }}

    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,

    'global-content-analysis': """
    Context: Analyze all acts collectively to identify common themes, repetitive content, and the overall narrative structure.

    All Act Contents: {all_act_contents}

    Instructions:
    1. Identify overarching themes that appear across multiple acts.
    2. Detect any repetitive content or information that appears in more than one act.
    3. Analyze the progression of the narrative across all acts.
    4. Identify key events, character developments, and turning points in each act.

    Output Format:
    Provide a JSON object with the following structure:
    {{
      "common_themes": [
        {{
          "theme": "Theme description",
          "appearances": ["Act 1", "Act 3", ...]
        }},
        ...
      ],
      "repetitive_content": [
        {{
          "content": "Brief description of repetitive content",
          "locations": ["Act 1", "Act 2", ...]
        }},
        ...
      ],
      "narrative_progression": [
        {{
          "act": "Act 1",
          "key_events": ["Event 1", "Event 2", ...],
          "character_development": ["Character 1 development", "Character 2 development"],
          "role_in_story": "e.g., Introduction of characters and setting"
        }},
        ...
      ]
    }}

    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,

    'extract-relevant-details': """
    Context: Extract relevant and unique details from the act source that are not present in the act content or previous extractions.
    Act Content: {act_content}
    Act Source: {act_source}
    Previously Extracted Details: {previous_details}
    Act Number: {act_number}

    Instructions:
    1. Analyze the Act Source and identify important details that are not already present in the Act Content.
    2. Consider the context of this specific act (Act Number: {act_number}) when determining relevance.
    3. Avoid extracting details that are similar to those in the Previously Extracted Details list.
    4. Focus on unique, act-specific information that adds depth to the narrative.
    5. Prioritize details that are crucial for understanding the events, character development, or thematic elements of this particular act.
    6. Identify any discrepancies between the Act Content and the Act Source. Highlight details in the Act Content that do not align with the Act Source.

    Output:
    Provide a JSON object containing relevant and unique details, ensuring they are:
    - Not redundant with the Act Content or Previously Extracted Details
    - Specific to this act and its context
    - Important for enhancing the narrative depth
    - Highlight any discrepancies between the Act Content and the Act Source

    The JSON object should have two keys:
    - "unique_details": An array of strings, each representing a unique and relevant detail.
    - "discrepancies": An array of strings, each representing a detail in the Act Content that does not align with the Act Source.

    Example Output:
    {{
        "unique_details": [
            "The protagonist's childhood fear of water stems from a near-drowning incident at age 5.",
            "The antagonist has a hidden tattoo that reveals their true identity.",
            "The story's setting experiences an unexpected climate change, affecting all characters' plans."
        ],
        "discrepancies": [
            "The Act Content mentions the protagonist having a dog, but the Act Source states they have a cat.",
            "The Act Content describes the main event happening in summer, while the Act Source clearly states it occurs in winter."
        ]
    }}

    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,

    'refine-act-with-details': """
    Context: Refine the given act content by incorporating the provided relevant details. The goal is to enhance the narrative without disrupting its flow or introducing redundancy.
    Act Content: {act_content}
    Relevant Details: {relevant_details}
    Discrepancies: {discrepancies}

    Instructions:
    1. Carefully read the Act Content, the list of Relevant Details, and the list of Discrepancies.
    2. Identify appropriate places within the Act Content where each relevant detail can be seamlessly integrated.
    3. Incorporate the relevant details in a way that enhances the narrative without disrupting its flow.
    4. Ensure that the integration feels natural and doesn't create redundancy.
    5. Address the discrepancies by aligning the Act Content with the Act Source. Correct any factual inaccuracies found in the Act Content.
    6. Maintain the original tone and style of the Act Content.
    7. If a detail doesn't fit naturally or would create redundancy, it's okay to omit it.

    Guidelines:
    1. Seamlessly incorporate new details, maintaining narrative flow.
    2. Use clear, understandable language; avoid obscure vocabulary.
    3. Maintain objectivity, balancing presentation of all aspects.
    4. Correct inconsistencies, ensuring factual accuracy.
    5. Maintain consistency with original tone and style.
    6. Adjust sentence structures appropriately to improve readability.

    Output:
    - Provide the refined act content with the relevant details integrated and discrepancies corrected. 
    - The output should be a cohesive, flowing narrative that incorporates the new details seamlessly and aligns with the Act Source.
    - Include only the narrative itself. Avoid any non-story elements, including act titles, markers, explanations, summaries, or meta-commentary.
    """,

    'analyze-coherence': """
    Context: Analyze the coherence of the given narrative.

    Narrative: {narrative}

    Instructions:
    1. Evaluate the logical flow and consistency of the narrative.
    2. Identify any gaps, inconsistencies, or areas that need improvement.
    3. Provide a coherence score as a number (for example, 8.5).
    4. Provide specific suggestions for improvement as a single string.

    Output Format:
    Provide a valid JSON object with the following structure:
    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    {{
      "score": coherence_score, 
      "suggestions": "Specific suggestions for improving coherence" 
    }}

    Ensure that your suggestions, even if they are long or contain multiple points, are enclosed in a single string and properly formatted for JSON. For example, use escaped characters for newlines or quotes if needed. Avoid extra formatting, explanations, or any comments outside of the JSON structure.
    """,

    'improve-coherence': """
    Context: Improve the coherence of the given narrative based on the provided suggestions.

    Narrative: {narrative}
    Suggestions: {suggestions}

    Instructions:
    1. Implement the suggestions to improve the logical flow and consistency of the narrative.
    2. Ensure the narrative is coherent and engaging.
    3. When optimizing the content:
       - Enhance unique elements
       - Rephrase or remove repetitive content
       - Ensure smooth transitions and narrative flow
       - Maintain consistency with global themes and overall story structure
    4. Preserve the core narrative and key information while improving clarity, engagement, and uniqueness.

    Guidelines:
    1. Use clear, understandable vocabulary; avoid obscure or overly formal expressions.
    2. Maintain objective narration, presenting both positive and negative aspects.
    3. Provide multi-perspective descriptions for controversial topics.
    4. Preserve factual accuracy and content depth.
    5. Use concise, direct sentence structures.
    6. Use descriptive language moderately, avoiding excessive embellishment.
    7. Maintain narrative coherence with appropriate transitions.
    8. Adjust language style according to the theme, maintaining overall consistency.

    Output Format:
    Provide the improved narrative as a plain text string. Include only the narrative itself, avoiding any non-story elements, such as act titles, markers, explanations, summaries, or meta-commentary.

    Your response should be the optimized narrative content, ready for integration into the larger narrative structure.
    """,

    # -------------------------------------------------------------------------
    # Prompt for generating slide cover content
    'generate-slide-cover': """
    Given the full text of a story, generate compelling and attractive content for the cover and back cover. 
    The cover should include a catchy and engaging story title (less than 6 words) and a subtitle 
    (less than 10 words) that complements the title. The back cover should feature a summary title 
    (exactly 6 words) and a concluding subtitle (exactly 10 words) that reflects the story's conclusion. 
    Ensure the titles and subtitles are distinct, relevant, and highly attractive to potential readers.

    Full text: {full_text}
    Language: The summary should be generated in {LANGUAGE}.

    Response format:
    {{
        "front_cover": {{
            "title": "cover title (max 6 words)",
            "sentence": "cover subtitle (max 10 words)"
        }},
        "back_cover": {{
            "title": "back cover title (6 words)",
            "sentence": "back cover subtitle (10 words)"
        }}
    }}
    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,

    # -------------------------------------------------------------------------
    # Prompt for generating metadata for a YouTube video
    'gen-metadata': """
    Context: Generate metadata for a YouTube video based on the provided script.
 
    Script: {script}
 
    Structure:
    - Title: Create a powerful and attention-grabbing title (recommended length: 100 characters). 
      Ensure your title contains the primary keywords related to your video content. 
      Use compelling and enticing words to make your title stand out. 
      Make sure it's catchy enough to hook the viewers instantly.
    - Description: Write a detailed and captivating description (up to 1000 characters, 
      focusing on key content in the first 100-160 characters). Use natural language, 
      incorporate relevant keywords. Add relevant links, call-to-actions, and a few hashtags 
      to boost engagement.
    - Tags: List relevant tags that help in discovering your video.
    - Topic: Provide a concise description (up to 5 words) that best captures the theme of the script.
 
    Language: The summary should be generated in {LANGUAGE}.

    Response format:
    {{
        "title": "Generated title",
        "description": "Generated description",
        "tags": ["tag1", "tag2"],
        "topic": "Generated topic"
    }}
    
    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,
 # -------------------------------------------------------------------------
    # Prompt for generating theme for a transcript 
    'gen-theme': """
    Context: Extract a concise, searchable theme that encapsulates the entire script's core message.
    
    Script: {script}

    Task:
    1. Read and analyze the full text thoroughly, paying equal attention to all sections.
    2. Identify overarching concepts and arguments that persist throughout the entire document.
    3. Note any shifts in focus or conclusions drawn towards the end of the text.
    4. Synthesize a theme that represents the text's complete message, not just its opening paragraphs.

    Guidelines:
    - Theme length: 3-5 words
    - Ensure the theme reflects the text's overall content, including key points from the middle and conclusion
    - Use precise, descriptive language that captures the text's main focus across its entirety
    - Balance specificity with searchability for both accuracy and discoverability
    - Prioritize concepts that are central to the entire text, not just the introduction

    Language: Generate the theme in {LANGUAGE}.

    Response format:
    {{
        "theme": "Comprehensive 3-5 word theme"
    }}

    Important: Analyze the full text before formulating the theme. Return only a valid JSON object.

    Note: Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """,
    # -------------------------------------------------------------------------
    # Prompt for generating short descriptions for an article
    'gen-short-descriptions': """
    Context: Generate {num_descriptions} unique and relevant summarized short descriptions 
    for the provided paragraph of an article. Each description should be concise, relevant to the content 
    of the paragraph, and should not repeat across sets.

    Paragraph: {paragraph_text}
    
    Language: The summary should be generated in {LANGUAGE}.

    Structure:
    - Generate {num_descriptions} summarized short descriptions.
    - Each description should contain up to 6 words.
    - Ensure the descriptions are distinct from each other and capture the main themes and concepts of the paragraph.
    - Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.

    Response format:
    {{
        "descriptions": [
            "description1",
            "description2",
            ...,
            "description{num_descriptions}"
        ]
    }}
    """,

   'revise_with_source_sentence': """
    Context: Process multiple target sentences with their possible original content from web searches to ensure accuracy, thematic consistency, and detail retention. The original content may not always perfectly match the target sentences. Ensure that the number of sentences in the output matches the number of sentences in the input.

    Input JSON: {input_json}
    Language: All processing and output should be in {LANGUAGE}.

    Instructions:
    For each sentence pair in the input:
    1. First, check if the target sentence and the possible original content are identical.
      If they are exactly the same, output the sentence as is without any processing.
    2. If not identical, evaluate whether they refer to the same event, idea, or topic.
    3. If they refer to the same event/topic:
      a. Prioritize the original content for factual accuracy.
      b. Adjust the target sentence to match the meaning of the original content, ensuring that all critical details and nuances are preserved.
      c. Maintain the tone and style of the target sentence, while integrating accurate information from the original content.
      d. Use clear, concise language without omitting significant details.
      e. Avoid unnecessary embellishments, but ensure meaningful details that contribute to context are retained.
      f. Adapt idiomatic expressions to fit the context and target language if necessary.
    4. If they do not refer to the same event/topic:
       a. Ignore the possible original content.
       b. Focus on optimizing the target sentence to make it more natural and native-sounding, without making it too colloquial.
       c. Maintain the original meaning and key information of the target sentence.
       d. Adjust the language to be less formal and more conversational, but still appropriate for a general audience.
       e. Ensure the optimized sentence flows well with the surrounding context.
    5. For quotes or precise content:
      a. If referring to the same content, use the original without rewriting it.
      b. Only adjust transitions to ensure smooth integration into the text.

    Additional Requirements:
    - Ensure the number of output sentences matches the number of input sentences. Each input sentence must correspond to one output sentence.
    - No critical details or meaningful content should be lost during the revision process.
    - All output must fully comply with openai content guidelines, avoiding any potentially sensitive or inappropriate content.
    - All processing and output must be in the specified language ({LANGUAGE}).

    Output format:
    - Return a valid JSON object containing an array of processed texts. Each entry should be:
      a) The original sentence if it's identical to the original content and already complies with guidelines,
      b) An improved version, ensuring all critical details are retained and full compliance with content guidelines, or
      c) A combination of the target sentence and original content if needed, while maintaining guideline compliance.
    - Ensure that each processed text is free from any content that might trigger content filters or violate usage policies.
    - Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.

    {{ 'results': [
      {{ 'processed_text': "Final version of sentence 1, ensuring full compliance with content guidelines" }},
      {{ 'processed_text': "Final version of sentence 2, ensuring full compliance with content guidelines" }},
      ...
    ] 
    }}

    Note: It is crucial that the output strictly adheres to content guidelines while preserving the essential meaning and details of the original text. Any potentially sensitive topics should be handled with care and neutrality. All output must be in {LANGUAGE}.
    """,

    'refine_style_and_flow': """
    Context: Refine the style of multiple paragraphs to improve readability and narrative flow while strictly preserving all critical content details. Each paragraph is provided with its surrounding context to help maintain coherence and consistency across the text.

    Input JSON: {input_json}
    Language: All processing and output should be in {LANGUAGE}.

    Input Structure:
    - The input JSON contains an array of paragraphs. Each entry includes:
      1. "paragraph": The paragraph text to be refined.
      2. "context": The surrounding text (up to 3 paragraphs before and after) to provide additional context for refining the paragraph while ensuring consistency and smooth transitions.

    Instructions:
    For each paragraph:
    1. **Preserve all critical details**: Ensure no key information, nuances, or important content is lost during the refinement process.
    2. Improve readability and narrative flow while retaining all significant content and meaning.
    3. Simplify complex expressions to make the text more accessible, but **do not omit any important details**.
    4. Use the provided context to ensure smooth transitions between ideas, maintaining continuity and consistency in the narrative.
    5. Use clear, concise sentence structures, while preserving the original tone and ensuring all essential information is retained.
    6. Adjust for style consistency across paragraphs without altering or removing critical information.
    7. Refine sentence structures and phrasing to ensure the paragraph text feels natural and engaging for {LANGUAGE} readers.
    8. **IMPORTANT**: You MUST return exactly the same number of paragraphs as provided in the input. Each input paragraph must have one corresponding output paragraph.

    Output format:
    - Return a JSON object containing an array of refined paragraphs. Each paragraph should retain all original details, ensuring no information is lost, but presented with improved clarity and flow.
    - The number of paragraphs in the output MUST match the number of paragraphs in the input exactly.
    - Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.

    Example output structure for an input with exactly 3 paragraphs:
    {{ 'results': [
      {{ 'optimized_text': "Improved version of paragraph 1" }},
      {{ 'optimized_text': "Improved version of paragraph 2" }},
      {{ 'optimized_text': "Improved version of paragraph 3" }}
    ] }}
    """,

    'final_review_and_proofread': """
    Context: Perform a final review and proofreading to ensure clarity, accuracy, and smooth transitions while strictly preserving all critical details. Absolutely no important information or nuances should be lost during this process. Retaining 100% of the original content is the highest priority.

    Full Text: {full_text}
    Language: All processing and output should be in {LANGUAGE}.

    Instructions:
    1. **Absolutely preserve all critical details**: Ensure that no key information, nuances, or significant content is lost during the review process. Every fact, number, date, and name must be retained.
    2. Review for any grammatical or contextual inconsistencies while keeping all details fully intact. Never sacrifice any information for the sake of grammar.
    3. Ensure terminology and key concepts are consistently applied without altering or omitting any important information. If inconsistencies are found, retain the original phrasing.
    4. Adjust for clarity and smooth transitions, but ensure that all essential details remain in place. Prefer retaining slightly awkward phrasing over losing any information.
    5. Maintain tone and narrative structure, ensuring that every critical piece of information and nuance remains intact. Do not simplify any content for the sake of fluency.
    6. If you identify any potential improvements but are unsure if they might affect the original meaning, keep the original text unchanged.
    7. Throughout the process, prioritize preserving the original information above any considerations of style or fluency.

    Output format:
    - Provide the refined text in its final form, preserving paragraph structure and maintaining the integrity of the content. Ensure that no details or significant content are lost in the process.
    - If any changes are made, mark the changed sections with square brackets [] immediately after, briefly explaining the change, e.g., [grammar correction] or [wording adjusted for clarity].
    - If a section might need improvement but you choose not to change it to preserve the original meaning, mark it with curly braces {}, e.g., {potential clarification needed, but original text retained for accuracy}

    Remember: Retaining 100% of the original content and meaning is absolutely essential. Always err on the side of preserving the original text rather than risking the loss of any information or detail. All output must be in {LANGUAGE}.
    """,

    'gen_keyword': """
    Context: Generate a highly relevant keyword or short phrase from the provided script, capturing its core theme and main concepts. This keyword will be used for searching articles most pertinent to the script's overall content.

    Script: {script}
    Language: The keyword should be generated in {LANGUAGE}.

    Instructions:
    1. Thoroughly analyze the entire script, paying equal attention to all sections.
    2. Identify the overarching themes, key concepts, and main arguments that persist throughout the script.
    3. Consider any shifts in focus or conclusions drawn towards the end of the text.
    4. Synthesize a keyword or short phrase that encapsulates the script's central message or theme.
    5. Ensure the keyword accurately represents the text's complete content, not just its opening paragraphs.
    6. The keyword should be concise (not exceeding 5 words) yet specific enough for effective article searches.
    7. Prioritize concepts that are central to the entire text, not just those mentioned prominently at the beginning.

    Response format:
    {{
        "keyword": "Generated keyword or short phrase"
    }}

    Important: Analyze the full text before formulating the keyword. The keyword should reflect the script's overall content, including key points from the middle and conclusion.

    Return a valid JSON object only. Do not include any other text, comments, or Markdown formatting.
    """
}