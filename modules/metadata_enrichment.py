#!/usr/bin/env python3
# modules/metadata_enrichment.py - Functions for enriching image metadata

import os
import io
import json
import logging
import hashlib
from PIL import Image
import torch
from functools import lru_cache
from botocore.exceptions import ClientError
import shutil

# Attempt to import necessary libraries, handle gracefully if missing
try:
    from transformers import Blip2Processor, Blip2ForConditionalGeneration
except ImportError:
    Blip2Processor = None
    Blip2ForConditionalGeneration = None
    logging.warning("transformers not found. BLIP-2 captioning will be unavailable.")

try:
    import boto3
except ImportError:
    boto3 = None
    logging.warning("boto3 not found. AWS Rekognition tagging will be unavailable.")

# Import local modules
from modules.langchain_interface import call_llm_json_response # Assumes this is in thePYTHONPATH or sibling dir

# Setup logger for this module
logger = logging.getLogger(__name__)

# Module-level cache for image bytes (used by Rekognition)
_IMAGE_BYTES_CACHE = {}
# Module-level globals for models (lazy loaded)
_blip_processor = None
_blip_model = None


# --- Helper Functions ---

def _to_jpeg_bytes(img: Image.Image) -> bytes:
    """Converts a PIL Image object (potentially WEBP) to JPEG bytes."""
    buf = io.BytesIO()
    # Ensure image is RGB before saving as JPEG
    img.convert('RGB').save(buf, format='JPEG')
    return buf.getvalue()

# --- Model Initializers ---

def get_blip_models(device: str):
    """Lazy initialization of BLIP-2 models."""
    global _blip_processor, _blip_model
    if Blip2Processor is None or Blip2ForConditionalGeneration is None:
        logger.error("transformers library not installed, cannot load BLIP models.")
        return None, None

    if _blip_processor is None or _blip_model is None:
        logger.info("Loading BLIP-2 models...")
        model_name = "Salesforce/blip2-opt-2.7b"
        try:
            logger.debug(f"Attempting to load BLIP model {model_name} with device_map='auto' and float16")
            _blip_processor = Blip2Processor.from_pretrained(model_name)
            _blip_model = Blip2ForConditionalGeneration.from_pretrained(
                 model_name,
                 device_map="auto",
                 torch_dtype=torch.float16 if device != "cpu" else torch.float32
             )
            logger.info(f"BLIP-2 models loaded with device_map='auto'.")
        except Exception as e1:
            logger.warning(f"Failed loading BLIP-2 with device_map='auto' ({e1}). Trying manual placement on {device}.")
            try:
                 _blip_processor = Blip2Processor.from_pretrained(model_name)
                 _blip_model = Blip2ForConditionalGeneration.from_pretrained(
                     model_name,
                     torch_dtype=torch.float16 if device != "cpu" else torch.float32
                 ).to(device)
                 logger.info(f"BLIP-2 models loaded manually onto device: {device}.")
            except Exception as e2:
                 logger.error(f"Failed loading BLIP-2 model ({model_name}) completely: {e2}", exc_info=True)
                 _blip_processor = None
                 _blip_model = None
    return _blip_processor, _blip_model

# --- Image Metadata Enrichment Functions ---

def generate_blip_caption(image_path: str, device: str) -> str:
    """Generate captions using BLIP-2."""
    try:
        logger.debug(f"Generating BLIP-2 caption for {image_path}")
        processor, model = get_blip_models(device)
        if model is None or processor is None:
            logger.error("BLIP models not loaded, cannot generate caption.")
            return ""

        img = Image.open(image_path).convert("RGB")
        try:
            model_device = next(model.parameters()).device
        except StopIteration:
             logger.error("Could not determine BLIP model device.")
             return ""
        logger.debug(f"BLIP model is on device: {model_device}")
        input_dtype = torch.float16 if model_device != torch.device("cpu") else torch.float32

        with torch.no_grad():
            prompt = "Question: What is happening in this image? Answer:"
            inputs = processor(images=img, text=prompt, return_tensors="pt").to(model_device, dtype=input_dtype)
            try:
                 generated_ids = model.generate(**inputs, max_new_tokens=50)
                 caption = processor.batch_decode(generated_ids, skip_special_tokens=True)[0].strip()
            except Exception as gen_e:
                 logger.error(f"BLIP-2 generation failed for {image_path}: {gen_e}")
                 return ""

        caption = caption.replace("Answer:", "").strip()
        logger.debug(f"BLIP-2 caption for {os.path.basename(image_path)}: {caption}")
        return caption
    except FileNotFoundError:
        logger.error(f"File not found for BLIP captioning: {image_path}")
        return ""
    except Exception as e:
        logger.error(f"Error generating BLIP-2 caption for {image_path}: {str(e)}")
        logger.debug(f"Detailed BLIP-2 error:", exc_info=True)
        return ""

@lru_cache(maxsize=2048)
def _rekognition_cached(key: str, max_labels: int = 10) -> str:
    """Cached implementation of AWS Rekognition for image labels."""
    if boto3 is None:
        logger.warning("boto3 not installed, skipping Rekognition.")
        return ""
    global _IMAGE_BYTES_CACHE

    aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    aws_region = os.getenv('AWS_DEFAULT_REGION', 'ap-northeast-1')

    if not aws_access_key_id or not aws_secret_access_key:
        logger.warning("AWS credentials not found, skipping Rekognition.")
        return ""

    try:
        if key not in _IMAGE_BYTES_CACHE:
            logger.error(f"Image bytes not found in cache for key: {key}. This shouldn't happen.")
            return ""
        image_bytes = _IMAGE_BYTES_CACHE[key]

        rekognition = boto3.client('rekognition', aws_access_key_id=aws_access_key_id,
                                   aws_secret_access_key=aws_secret_access_key, region_name=aws_region)
        response = rekognition.detect_labels(Image={'Bytes': image_bytes}, MaxLabels=max_labels)
        labels = [label['Name'] for label in response['Labels']]
        return " ".join(labels)
    except ClientError as ce:
        # ... (error handling as before) ...
        error_code = ce.response.get('Error', {}).get('Code')
        if error_code == 'UnrecognizedClientException': logger.error("AWS Rekognition: Invalid security token.")
        elif error_code == 'RequestEntityTooLargeException': logger.warning(f"Rekognition skipped: Image payload too large ({key}).")
        elif error_code == 'AccessDeniedException': logger.error("AWS Rekognition: Access Denied.")
        else: logger.error(f"AWS Rekognition ClientError for key {key}: {str(ce)}")
        return ""
    except Exception as e:
        logger.error(f"Error in cached Rekognition for key {key}: {str(e)}")
        return ""

def extract_tags_rekognition(image_path: str, dry_run: bool, max_labels: int = 10) -> str:
    """Extract object tags using AWS Rekognition with caching and WEBP conversion."""
    global _IMAGE_BYTES_CACHE
    if dry_run:
        logger.info(f"Dry run: Skipping AWS Rekognition for {os.path.basename(image_path)}")
        return ""
    if boto3 is None:
        logger.warning("boto3 not installed, skipping Rekognition.")
        return ""

    try:
        img = Image.open(image_path)
        img_format = img.format
        if img_format == 'WEBP':
            image_bytes = _to_jpeg_bytes(img)
        else:
            with open(image_path, 'rb') as f: image_bytes = f.read()
        img.close()
    except FileNotFoundError: logger.error(f"File not found for Rekognition: {image_path}"); return ""
    except Image.UnidentifiedImageError: logger.error(f"Cannot identify image file: {image_path}"); return ""
    except Exception as img_err: logger.error(f"Error opening image {image_path}: {img_err}"); return ""

    key = hashlib.md5(image_bytes).hexdigest()
    if key not in _IMAGE_BYTES_CACHE:
        _IMAGE_BYTES_CACHE[key] = image_bytes

    tags = _rekognition_cached(key, max_labels)
    if tags: logger.debug(f"Rekognition tags for {os.path.basename(image_path)}: {tags}")
    else: logger.warning(f"Rekognition tags empty for {os.path.basename(image_path)} (Format: {img_format})")
    return tags

def summarize_image_text_with_llm(auto_caption: str, tags: str, writer_caption: str) -> str:
    """Uses LLM to summarize image text sources for better BGE embedding."""
    logger.debug("Summarizing image text with LLM...")
    try:
        prompt_data = {
            "auto_caption": auto_caption or "N/A",
            "tags": tags or "N/A",
            "writer_caption": writer_caption or "N/A"
        }
        response_json = call_llm_json_response(
            api_function='summarize_image_caption',
            prompt_data=prompt_data,
            expected_fields=['summary']
        )
        if response_json and 'summary' in response_json:
            summary = response_json['summary']
            logger.debug(f"LLM Summary generated: {summary}")
            return summary
        else:
            logger.warning("LLM summarization failed or returned invalid format.")
            fallback_text = f"{auto_caption} {tags} {writer_caption}".strip()
            return fallback_text[:200]
    except Exception as e:
        logger.error(f"Error during LLM summarization (potential timeout or other issue): {str(e)}")
        fallback_text = f"{auto_caption} {tags} {writer_caption}".strip()
        return fallback_text[:200]

def enrich_image_metadata(attribute_json_path: str, image_dir: str, theme: str, device: str, dry_run: bool) -> None:
    """
    Main function to enrich image metadata in the attribute JSON file.
    Reads existing metadata, generates missing captions/tags/summaries, and saves back.
    """
    logger.info(f"Starting metadata enrichment for theme '{theme}'")
    processed_count = 0
    metadata = []
    partial_save_path = attribute_json_path + '.partial'

    try:
        # Read existing metadata
        logger.debug(f"Attempting to read existing metadata from: {attribute_json_path}")
        try:
            if os.path.exists(attribute_json_path):
                with open(attribute_json_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                logger.debug(f"Successfully loaded {len(metadata)} existing metadata entries.")
            else:
                logger.warning(f"Metadata file not found: {attribute_json_path}. Starting fresh.")
        except json.JSONDecodeError as jde:
             logger.error(f"Error decoding JSON from {attribute_json_path}: {jde}")
             logger.warning("Proceeding with empty metadata list.")

        # Make filepaths absolute (handle potential relative paths in existing JSON)
        for entry in metadata:
            if 'filepath' in entry and entry['filepath'] and not os.path.isabs(entry['filepath']):
                 # Attempt to make absolute relative to JSON file location? Or workspace root?
                 # Safest might be relative to JSON location if possible.
                 json_dir = os.path.dirname(attribute_json_path)
                 potential_path = os.path.join(json_dir, entry['filepath'])
                 if os.path.exists(potential_path):
                     entry['filepath'] = os.path.abspath(potential_path)
                 else: # Fallback to assuming it's relative to image_dir
                     potential_path_img = os.path.join(image_dir, entry['filepath'])
                     if os.path.exists(potential_path_img):
                         entry['filepath'] = os.path.abspath(potential_path_img)
                     else: # Last resort, make absolute from current working dir? Risky.
                          entry['filepath'] = os.path.abspath(entry['filepath'])


        # Import get_valid_images locally to avoid circular dependency if moved later
        from modules.image_assignment import get_valid_images
        valid_image_paths = get_valid_images(image_dir)
        valid_image_paths = list(dict.fromkeys([os.path.abspath(img) for img in valid_image_paths]))
        logger.info(f"Valid images found initially: {len(valid_image_paths)}")

        # Track images needing enrichment
        images_to_enrich = []
        metadata_by_path = {entry.get('filepath'): entry for entry in metadata if entry.get('filepath')}

        for abs_img_path in valid_image_paths:
            img_entry = metadata_by_path.get(abs_img_path)
            needs_enrichment = False
            if not img_entry:
                needs_enrichment = True
            else:
                if not img_entry.get('auto_caption'): needs_enrichment = True
                if not img_entry.get('tags') and not dry_run: needs_enrichment = True
                if not img_entry.get('image_summary'): needs_enrichment = True
                if 'clip_embedding' in img_entry: del img_entry['clip_embedding'] # Deprecated

            if needs_enrichment:
                images_to_enrich.append(abs_img_path) # Use absolute path

        if not images_to_enrich:
            logger.info("All valid images already have enhanced metadata.")
            return

        logger.info(f"Enriching metadata for {len(images_to_enrich)} images...")

        # Process images needing enrichment
        modified = False
        for i, abs_img_path in enumerate(images_to_enrich):
            logger.info(f"Processing image {i+1}/{len(images_to_enrich)}: {os.path.basename(abs_img_path)}")
            img_entry = metadata_by_path.get(abs_img_path)

            if not img_entry:
                img_entry = {'filepath': abs_img_path, 'url': f"file://{abs_img_path}"}
                metadata.append(img_entry) # Add new entry to the main list
                metadata_by_path[abs_img_path] = img_entry # Add to lookup dict
                modified = True

            entry_modified = False
            # Generate BLIP caption
            if not img_entry.get('auto_caption'):
                logger.debug(f"Generating BLIP caption...")
                auto_caption = generate_blip_caption(abs_img_path, device)
                if auto_caption: img_entry['auto_caption'] = auto_caption; entry_modified = True

            # Handle writer caption
            if 'description' in img_entry and not img_entry.get('writer_caption'):
                img_entry['writer_caption'] = img_entry['description']; entry_modified = True

            # Extract Rekognition tags
            if not img_entry.get('tags') and not dry_run:
                logger.debug(f"Extracting Rekognition tags...")
                tags = extract_tags_rekognition(abs_img_path, dry_run)
                if tags: img_entry['tags'] = tags; entry_modified = True

            # Generate LLM Summary (only if essential fields exist or were just generated)
            if not img_entry.get('image_summary'):
                 # Check if needed inputs are present before calling LLM
                 current_auto_caption = img_entry.get('auto_caption', '')
                 current_tags = img_entry.get('tags', '')
                 current_writer_caption = img_entry.get('writer_caption', '') or img_entry.get('description', '')
                 # Only summarize if there's something to summarize
                 if current_auto_caption or current_tags or current_writer_caption:
                     logger.info(f"Generating LLM summary...")
                     summary = summarize_image_text_with_llm(current_auto_caption, current_tags, current_writer_caption)
                     if summary: img_entry['image_summary'] = summary; entry_modified = True
                 else:
                     logger.warning(f"Skipping summary for {os.path.basename(abs_img_path)}: no captions or tags available.")

            if entry_modified: modified = True
            processed_count += 1

        # Save if modified
        if modified:
            logger.info("Saving updated metadata...")
            backup_path = attribute_json_path + '.backup'
            if os.path.exists(attribute_json_path):
                try: shutil.copy2(attribute_json_path, backup_path); logger.debug("Backup created.")
                except Exception as backup_err: logger.warning(f"Failed to backup metadata: {backup_err}")

            try:
                with open(attribute_json_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                logger.info(f"Metadata enrichment complete. Processed {processed_count} entries. Saved to {attribute_json_path}")
                # Clean up partial file on successful save
                if os.path.exists(partial_save_path):
                    try: os.remove(partial_save_path); logger.debug("Partial save file removed.")
                    except OSError as e: logger.warning(f"Could not remove partial save file: {partial_save_path}, error: {e}")
            except Exception as save_e:
                logger.error(f"Failed to save final metadata: {save_e}")
                # Attempt to restore from backup on save failure?
                if os.path.exists(backup_path):
                     try: shutil.copy2(backup_path, attribute_json_path); logger.info("Restored metadata from backup.")
                     except Exception as restore_err: logger.error(f"Failed to restore metadata from backup: {restore_err}")
                raise # Re-raise save error
        else:
             logger.info("No metadata entries were modified.")

    except KeyboardInterrupt:
        logger.warning("KeyboardInterrupt during metadata enrichment.")
        # Save partial progress if any modifications were made
        if 'modified' in locals() and modified and processed_count > 0:
             logger.debug(f"Attempting to save partial metadata to: {partial_save_path}")
             try:
                 with open(partial_save_path, 'w', encoding='utf-8') as f:
                     json.dump(metadata, f, ensure_ascii=False, indent=2)
                 logger.info(f"Partially processed metadata saved to: {partial_save_path}")
             except Exception as save_err: logger.error(f"Failed to save partial metadata: {save_err}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unhandled error during metadata enrichment: {str(e)}", exc_info=True)
        raise # Re-raise other critical errors
