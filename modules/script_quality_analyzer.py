# modules/script_quality_analyzer.py
import re
import json
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from config import logger

@dataclass
class QualityMetrics:
    """脚本质量指标"""
    character_depth_score: float = 0.0
    emotional_impact_score: float = 0.0
    dialogue_sophistication_score: float = 0.0
    narrative_engagement_score: float = 0.0
    audio_optimization_score: float = 0.0
    overall_score: float = 0.0
    detailed_analysis: Dict[str, Any] = None

class ScriptQualityAnalyzer:
    """脚本质量分析器"""
    
    def __init__(self):
        self.psychological_keywords = [
            '内心', '情感', '感受', '心理', '动机', '恐惧', '渴望', '矛盾',
            'internal', 'emotion', 'feeling', 'psychology', 'motivation', 'fear', 'desire', 'conflict'
        ]
        
        self.dialogue_quality_indicators = [
            '潜台词', '暗示', '讽刺', '犹豫', '停顿', '未完成',
            'subtext', 'implication', 'irony', 'hesitation', 'pause', 'unfinished'
        ]
        
        self.emotional_impact_words = [
            '震撼', '感动', '紧张', '悲伤', '愤怒', '恐惧', '惊讶', '温暖',
            'shocking', 'moving', 'tense', 'sad', 'angry', 'fearful', 'surprised', 'warm'
        ]

    def analyze_script_quality(self, script_text: str, episode_structure: Dict[str, Any] = None) -> QualityMetrics:
        """分析脚本质量并返回详细指标"""
        try:
            # 分析各个质量维度
            character_depth = self._analyze_character_depth(script_text, episode_structure)
            emotional_impact = self._analyze_emotional_impact(script_text)
            dialogue_quality = self._analyze_dialogue_sophistication(script_text)
            narrative_engagement = self._analyze_narrative_engagement(script_text)
            audio_optimization = self._analyze_audio_optimization(script_text)
            
            # 计算总体分数
            overall_score = (
                character_depth * 0.25 +
                emotional_impact * 0.25 +
                dialogue_quality * 0.20 +
                narrative_engagement * 0.20 +
                audio_optimization * 0.10
            )
            
            detailed_analysis = {
                "character_analysis": self._get_character_analysis_details(script_text, episode_structure),
                "emotional_analysis": self._get_emotional_analysis_details(script_text),
                "dialogue_analysis": self._get_dialogue_analysis_details(script_text),
                "narrative_analysis": self._get_narrative_analysis_details(script_text),
                "audio_analysis": self._get_audio_analysis_details(script_text),
                "improvement_suggestions": self._generate_improvement_suggestions(
                    character_depth, emotional_impact, dialogue_quality, 
                    narrative_engagement, audio_optimization
                )
            }
            
            return QualityMetrics(
                character_depth_score=character_depth,
                emotional_impact_score=emotional_impact,
                dialogue_sophistication_score=dialogue_quality,
                narrative_engagement_score=narrative_engagement,
                audio_optimization_score=audio_optimization,
                overall_score=overall_score,
                detailed_analysis=detailed_analysis
            )
            
        except Exception as e:
            logger.error(f"脚本质量分析失败: {str(e)}")
            return QualityMetrics()

    def _analyze_character_depth(self, script_text: str, episode_structure: Dict[str, Any] = None) -> float:
        """分析角色深度"""
        score = 0.0
        
        # 检查内心独白和心理描写
        internal_thoughts = len(re.findall(r'内心|心想|思考|感到|意识到', script_text))
        psychological_descriptions = len(re.findall(r'心理|情感|动机|恐惧|渴望', script_text))
        
        # 检查角色成长和变化
        character_development = len(re.findall(r'成长|变化|领悟|突破|转变', script_text))
        
        # 检查角色关系动态
        relationship_dynamics = len(re.findall(r'关系|互动|冲突|和解|理解', script_text))
        
        # 基于文本长度标准化分数
        text_length = len(script_text)
        if text_length > 0:
            score = min(1.0, (internal_thoughts + psychological_descriptions * 2 + 
                             character_development * 3 + relationship_dynamics) / (text_length / 500))
        
        return score

    def _analyze_emotional_impact(self, script_text: str) -> float:
        """分析情感影响力"""
        score = 0.0
        
        # 情感词汇密度
        emotional_words = 0
        for word in self.emotional_impact_words:
            emotional_words += len(re.findall(word, script_text, re.IGNORECASE))
        
        # 情感变化和转折
        emotional_transitions = len(re.findall(r'突然|忽然|然而|但是|可是|却|竟然', script_text))
        
        # 感官描写
        sensory_descriptions = len(re.findall(r'看到|听到|感受到|闻到|触摸|声音|光线|温度', script_text))
        
        text_length = len(script_text)
        if text_length > 0:
            score = min(1.0, (emotional_words * 2 + emotional_transitions * 3 + 
                             sensory_descriptions) / (text_length / 300))
        
        return score

    def _analyze_dialogue_sophistication(self, script_text: str) -> float:
        """分析对话复杂程度"""
        score = 0.0
        
        # 对话中的潜台词指标
        subtext_indicators = 0
        for indicator in self.dialogue_quality_indicators:
            subtext_indicators += len(re.findall(indicator, script_text, re.IGNORECASE))
        
        # 对话的自然性（停顿、未完成句子等）
        natural_dialogue = len(re.findall(r'\.\.\.|\-\-|（停顿）|（犹豫）|（沉默）', script_text))
        
        # 角色语言差异性
        dialogue_lines = re.findall(r'"[^"]*"', script_text)
        unique_speech_patterns = len(set(dialogue_lines)) / max(1, len(dialogue_lines))
        
        if len(script_text) > 0:
            score = min(1.0, (subtext_indicators * 2 + natural_dialogue * 3 + 
                             unique_speech_patterns * 100) / (len(script_text) / 400))
        
        return score

    def _analyze_narrative_engagement(self, script_text: str) -> float:
        """分析叙事吸引力"""
        score = 0.0
        
        # 悬念和钩子
        suspense_elements = len(re.findall(r'突然|意外|神秘|秘密|未知|即将|将要', script_text))
        
        # 节奏变化
        pacing_changes = len(re.findall(r'快速|缓慢|加速|放慢|紧张|放松', script_text))
        
        # 冲突层次
        conflict_layers = len(re.findall(r'冲突|矛盾|对立|争执|分歧|斗争', script_text))
        
        if len(script_text) > 0:
            score = min(1.0, (suspense_elements * 2 + pacing_changes * 2 + 
                             conflict_layers * 3) / (len(script_text) / 350))
        
        return score

    def _analyze_audio_optimization(self, script_text: str) -> float:
        """分析音频优化程度"""
        score = 0.0
        
        # 音效提示
        sound_cues = len(re.findall(r'音效|声音|背景音|环境音|音乐|静默', script_text))
        
        # 语音指导
        vocal_directions = len(re.findall(r'轻声|大声|低语|呼喊|颤抖|坚定', script_text))
        
        # 空间音频元素
        spatial_audio = len(re.findall(r'远处|近处|回声|空旷|密闭|传来', script_text))
        
        if len(script_text) > 0:
            score = min(1.0, (sound_cues * 3 + vocal_directions * 2 + 
                             spatial_audio * 2) / (len(script_text) / 400))
        
        return score

    def _get_character_analysis_details(self, script_text: str, episode_structure: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取角色分析详情"""
        return {
            "psychological_depth_indicators": len(re.findall(r'内心|心理|情感|动机', script_text)),
            "character_growth_moments": len(re.findall(r'成长|变化|领悟|突破', script_text)),
            "relationship_complexity": len(re.findall(r'关系|互动|冲突|理解', script_text)),
            "internal_monologue_presence": len(re.findall(r'心想|思考|感到|意识到', script_text))
        }

    def _get_emotional_analysis_details(self, script_text: str) -> Dict[str, Any]:
        """获取情感分析详情"""
        emotional_word_count = 0
        for word in self.emotional_impact_words:
            emotional_word_count += len(re.findall(word, script_text, re.IGNORECASE))
            
        return {
            "emotional_vocabulary_richness": emotional_word_count,
            "emotional_transitions": len(re.findall(r'突然|然而|但是|却', script_text)),
            "sensory_descriptions": len(re.findall(r'看到|听到|感受到|闻到', script_text)),
            "emotional_peaks": len(re.findall(r'高潮|激动|震撼|感动', script_text))
        }

    def _get_dialogue_analysis_details(self, script_text: str) -> Dict[str, Any]:
        """获取对话分析详情"""
        dialogue_lines = re.findall(r'"[^"]*"', script_text)
        
        return {
            "total_dialogue_lines": len(dialogue_lines),
            "subtext_indicators": len(re.findall(r'潜台词|暗示|讽刺', script_text)),
            "natural_speech_patterns": len(re.findall(r'\.\.\.|\-\-|（停顿）', script_text)),
            "dialogue_diversity": len(set(dialogue_lines)) / max(1, len(dialogue_lines))
        }

    def _get_narrative_analysis_details(self, script_text: str) -> Dict[str, Any]:
        """获取叙事分析详情"""
        return {
            "suspense_elements": len(re.findall(r'突然|意外|神秘|未知', script_text)),
            "pacing_indicators": len(re.findall(r'快速|缓慢|紧张|放松', script_text)),
            "conflict_complexity": len(re.findall(r'冲突|矛盾|对立|斗争', script_text)),
            "narrative_hooks": len(re.findall(r'即将|将要|预感|暗示', script_text))
        }

    def _get_audio_analysis_details(self, script_text: str) -> Dict[str, Any]:
        """获取音频分析详情"""
        return {
            "sound_design_cues": len(re.findall(r'音效|声音|背景音|环境音', script_text)),
            "vocal_direction_notes": len(re.findall(r'轻声|大声|低语|呼喊', script_text)),
            "spatial_audio_elements": len(re.findall(r'远处|近处|回声|传来', script_text)),
            "silence_and_pacing": len(re.findall(r'静默|停顿|沉默|间歇', script_text))
        }

    def _generate_improvement_suggestions(self, character_depth: float, emotional_impact: float, 
                                        dialogue_quality: float, narrative_engagement: float, 
                                        audio_optimization: float) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if character_depth < 0.7:
            suggestions.append("增加角色内心独白和心理描写，深化角色的内在动机和情感层次")
            suggestions.append("设计更多角色成长和转变的关键时刻")
            suggestions.append("丰富角色间的关系动态和互动复杂性")
            
        if emotional_impact < 0.7:
            suggestions.append("增强情感词汇的使用，提高情感表达的丰富性")
            suggestions.append("设计更多情感转折和高潮时刻")
            suggestions.append("加入更多感官描写来增强沉浸感")
            
        if dialogue_quality < 0.7:
            suggestions.append("在对话中加入更多潜台词和隐含意义")
            suggestions.append("使用停顿、犹豫等自然语言特征")
            suggestions.append("为每个角色设计独特的语言风格和说话方式")
            
        if narrative_engagement < 0.7:
            suggestions.append("增加悬念元素和意外转折")
            suggestions.append("优化节奏变化，在紧张和缓和之间切换")
            suggestions.append("设计多层次的冲突结构")
            
        if audio_optimization < 0.7:
            suggestions.append("增加音效和环境音的设计说明")
            suggestions.append("为语音表演提供更详细的情感指导")
            suggestions.append("利用空间音频技术增强沉浸感")
            
        return suggestions

def evaluate_script_quality(script_text: str, episode_structure: Dict[str, Any] = None) -> QualityMetrics:
    """评估脚本质量的便捷函数"""
    analyzer = ScriptQualityAnalyzer()
    return analyzer.analyze_script_quality(script_text, episode_structure) 