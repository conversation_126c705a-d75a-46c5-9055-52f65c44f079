# image_context.py
import nltk
from bs4 import BeautifulSoup
from config import logger

nltk.download('punkt')

def extract_context_text(img, soup, page_url):
    """提取图片的上下文信息，优化常见场景下的文本提取"""
    
    def get_direct_context():
        """获取图片直接相关的文本信息"""
        elements = []
        # 获取基本属性
        alt_text = img.get('alt', '').strip()
        title_text = img.get('title', '').strip()
        
        # 检查最近的figcaption
        figure = img.find_parent('figure')
        caption_text = ''
        if figure and figure.find('figcaption'):
            caption_text = figure.find('figcaption').get_text(strip=True)
        
        # 只添加非空且有效的文本
        for text in [alt_text, title_text, caption_text]:
            if is_valid_text(text):
                elements.append(text)
        
        return elements

    def is_valid_text(text):
        """检查文本是否有效"""
        if not text or len(text.strip()) < 5:
            return False
        
        # 过滤常见的无用文本
        ignore_patterns = {'cookie', 'accept', 'close', 'menu', 'navigation', 
                         'search', 'skip', 'advertisement'}
        text_lower = text.lower()
        if any(pattern in text_lower for pattern in ignore_patterns):
            return False
            
        return True

    def get_nearby_text():
        """获取图片附近的文本内容"""
        elements = []
        max_elements = 3  # 限制收集的元素数量
        
        # 获取图片所在段落的文本
        parent_p = img.find_parent('p')
        if parent_p:
            text = parent_p.get_text(strip=True)
            if is_valid_text(text):
                elements.append({
                    'text': text,
                    'weight': 1.5,
                    'type': 'parent_paragraph'
                })

        # 获取最近的标题
        header = img.find_previous(['h1', 'h2', 'h3', 'h4'])
        if header:
            text = header.get_text(strip=True)
            if is_valid_text(text):
                elements.append({
                    'text': text,
                    'weight': 2.0,
                    'type': 'header'
                })

        # 获取前后相邻段落
        for sibling in img.find_previous_siblings(['p', 'div']):
            if len(elements) >= max_elements:
                break
            text = sibling.get_text(strip=True)
            if is_valid_text(text):
                elements.append({
                    'text': text,
                    'weight': 1.0,
                    'type': 'previous'
                })

        for sibling in img.find_next_siblings(['p', 'div']):
            if len(elements) >= max_elements:
                break
            text = sibling.get_text(strip=True)
            if is_valid_text(text):
                elements.append({
                    'text': text,
                    'weight': 1.0,
                    'type': 'next'
                })

        return elements

    def remove_duplicates(elements):
        """移除重复文本"""
        seen = set()
        unique_elements = []
        for elem in elements:
            text = elem['text']
            if text not in seen:
                seen.add(text)
                unique_elements.append(elem)
        return unique_elements

    try:
        # 1. 获取直接相关文本
        direct_texts = get_direct_context()
        
        # 2. 获取周边文本
        nearby_elements = get_nearby_text()
        
        # 3. 组合所有文本
        all_elements = ([{
            'text': text,
            'weight': 2.0,
            'type': 'direct'
        } for text in direct_texts] + nearby_elements)
        
        # 4. 去重和排序
        unique_elements = remove_duplicates(all_elements)
        sorted_elements = sorted(unique_elements, key=lambda x: x['weight'], reverse=True)
        
        # 5. 组合最终文本
        context_text = "\n".join(item['text'] for item in sorted_elements)
        if page_url:
            context_text = f"Adapted from: {page_url}\n{context_text}"
        
        # 6. 控制文本长度
        context_text = optimize_context_text(context_text, max_tokens=200)
        
        return context_text
        
    except Exception as e:
        logger.error(f"Error extracting context: {str(e)}")
        return f"Image from: {page_url}"

def optimize_context_text(context_text, max_tokens=200):
    fragments = nltk.sent_tokenize(context_text)
    optimized_text = ''
    current_length = 0

    for fragment in fragments:
        fragment_length = len(fragment.split())
        if current_length + fragment_length > max_tokens:
            break
        optimized_text += fragment + ' '
        current_length += fragment_length

    optimized_text = optimized_text.strip()
    #logger.debug(f"Optimized context text: {optimized_text}")
    return optimized_text