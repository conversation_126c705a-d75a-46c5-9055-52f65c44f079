"""
Manages loading and access to spaCy NLP models for various languages.
Implements lazy loading to improve startup time and memory usage.
"""
import spacy
from spacy.cli import download
import langdetect
import logging
from config import LANGUAGE_CODES, logger
from typing import Optional
import sys # Added for checking if langdetect is installed

# 配置: 语言代码到 spaCy 模型名称的映射
LANGUAGE_MODELS = {
    'en': 'en_core_web_sm',
    'es': 'es_core_news_sm',
    'pt': 'pt_core_news_sm',
    'ja': 'ja_core_news_sm',
    'zh': 'zh_core_web_sm'
}

# 支持的语言列表 (从配置生成)
SUPPORTED_LANGUAGES = list(LANGUAGE_MODELS.keys())

# --- 惰性加载实现 ---
_loaded_nlp_models = {} # 缓存已加载的模型
_langdetect_available = 'langdetect' in sys.modules # 检查 langdetect 是否已导入

def load_or_download_model(model_name: str) -> Optional[spacy.Language]:
    """Lazily loads or downloads a spaCy model."""
    try:
        return spacy.load(model_name)
    except IOError:
        logger.info(f"spaCy model '{model_name}' not found locally. Attempting download...")
        try:
            download(model_name)
            logger.info(f"Model '{model_name}' downloaded successfully.")
            # Important: Load after download
            return spacy.load(model_name)
        except SystemExit as e:
            # spacy.cli.download uses sys.exit on error
            logger.error(f"Failed to download spaCy model '{model_name}'. spaCy download command exited with code: {e.code}. Please ensure the model name is correct and you have internet access.")
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred during spaCy model download or loading for '{model_name}': {e}", exc_info=True)
            return None
    except Exception as e:
        logger.error(f"An unexpected error occurred while trying to load spaCy model '{model_name}': {e}", exc_info=True)
        return None

def get_nlp_model(text: Optional[str] = None, lang: Optional[str] = None) -> Optional[spacy.Language]:
    """
    Gets the appropriate spaCy NLP model based on text or language code.
    Implements lazy loading: models are loaded only when first requested.

    Args:
        text: Text sample for language detection (if lang is not provided).
        lang: Language name (e.g., 'English', 'Chinese') or code (e.g., 'en', 'zh').

    Returns:
        The loaded spaCy Language object, or None if loading fails or language is unsupported.
    """
    detected_lang_code = 'en' # Default to English

    if lang:
        # Normalize provided lang name/code
        lang_lower = lang.lower()
        detected_lang_code = LANGUAGE_CODES.get(lang_lower, lang_lower)
        if detected_lang_code not in SUPPORTED_LANGUAGES:
            logger.warning(f"Provided language '{lang}' (code: {detected_lang_code}) is not supported or mapped. Defaulting to English ('en'). Supported codes: {SUPPORTED_LANGUAGES}")
            detected_lang_code = 'en'
    elif text and _langdetect_available:
        try:
            # Limit detection text size for performance and accuracy
            detected_lang_code = langdetect.detect(text[:1500])
            if detected_lang_code not in SUPPORTED_LANGUAGES:
                logger.warning(f"Detected language code '{detected_lang_code}' is not directly supported. Defaulting to English ('en'). Supported codes: {SUPPORTED_LANGUAGES}")
                detected_lang_code = 'en'
            else:
                logger.debug(f"Detected language code: {detected_lang_code}")
        except langdetect.LangDetectException as lde:
            logger.warning(f"Language detection failed: {lde}. Defaulting to English ('en'). Text snippet: '{text[:100]}...'" )
            detected_lang_code = 'en'
        except Exception as e:
             logger.error(f"Unexpected error during language detection: {e}. Defaulting to English ('en').", exc_info=True)
             detected_lang_code = 'en'
    elif text and not _langdetect_available:
         logger.warning("Language code not provided and 'langdetect' is not installed. Defaulting to English ('en').")
         detected_lang_code = 'en'
    else:
        # No lang provided, no text provided, default to English
        logger.debug("No language or text provided, defaulting to English ('en').")
        detected_lang_code = 'en'

    # --- Lazy Loading Logic --- 
    if detected_lang_code in _loaded_nlp_models:
        logger.debug(f"Returning cached spaCy model for language: {detected_lang_code}")
        return _loaded_nlp_models[detected_lang_code]
    elif detected_lang_code in LANGUAGE_MODELS:
        model_name = LANGUAGE_MODELS[detected_lang_code]
        logger.info(f"Loading spaCy model for language '{detected_lang_code}': {model_name}...")
        nlp_model = load_or_download_model(model_name)
        if nlp_model:
            _loaded_nlp_models[detected_lang_code] = nlp_model
            logger.info(f"Successfully loaded model for {detected_lang_code}.")
            return nlp_model
        else:
            logger.error(f"Failed to load or download model '{model_name}' for language '{detected_lang_code}'.")
            # Cache the failure? Or allow retry next time?
            # For now, don't cache failure, return None.
            return None
    else:
        # This case should technically not be reached due to earlier checks
        logger.error(f"Language code '{detected_lang_code}' is not mapped to a spaCy model in LANGUAGE_MODELS.")
        return None

def get_language_code(language_name: str) -> str:
    """Gets the language code (e.g., 'en') from a language name (e.g., 'English')."""
    return LANGUAGE_CODES.get(language_name.lower(), language_name.lower()) # Return lowercased name if not found
