#modules/text_optimizer.py
import sys
import os
import numpy as np
import tiktoken
import re
from sklearn.metrics.pairwise import cosine_similarity
import logging
from typing import List, Union, Dict, Any, Optional
from nltk.tokenize import sent_tokenize

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.langchain_interface import (
    call_llm, 
    call_llm_json_response,
    GPTResponseError
)
from modules.langchain_interface import generate_embedding
from modules.embedding_management import get_embedding_manager
from modules.utils import normalize_language, clean_text
from modules.nlp_model import get_nlp_model
from config import (
    logger,
    LANGUAGE_CODES
)

logger = logging.getLogger(__name__)


MAX_RETRIES = 5


def chunk_text(text, max_tokens=3000):
    """
    Chunk the input text into smaller pieces based on token count.
    Args:
    text (str): The input text to be chunked.
    max_tokens (int): The maximum number of tokens per chunk.
    Returns:
    list: A list of text chunks.
    """
    tokenizer = tiktoken.get_encoding("cl100k_base")
    tokens = tokenizer.encode(text)
    chunks = []
    for i in range(0, len(tokens), max_tokens):
        chunk_tokens = tokens[i:i + max_tokens]
        chunk_text = tokenizer.decode(chunk_tokens, clean_up_tokenization_spaces=True)
        chunks.append(chunk_text)
    return chunks

def generate_combined_embedding(chunks):
    """
    Generate a combined embedding for the given text chunks.
    Args:
    chunks (list): A list of text chunks.
    Returns:
    np.ndarray: The combined embedding.
    """
    embeddings = []
    for chunk in chunks:
        try:
            embedding = generate_embedding(chunk)
            embeddings.append(embedding)
        except Exception as e:
            logger.error(f"Error generating embedding for chunk: {e}")
    
    if embeddings:
        combined_embedding = np.mean(embeddings, axis=0)
        return combined_embedding / np.linalg.norm(combined_embedding)
    else:
        logger.error("No embeddings were generated.")
        return None

def refine_chunk(chunk, feedback=None, embedding=None, refined_chunks=None):
    """
    Refine the script based on feedback, optional embedding, and previously refined chunks.
    Args:
    chunk (str): The text chunk to be refined.
    feedback (str): Feedback for refining the script.
    embedding (np.ndarray): Optional embedding to ensure coherence.
    refined_chunks (list): List of previously refined chunks.
    Returns:
    str: The refined text.
    """
    prompt_data = {
        'feedback': feedback,
        'chunk': chunk,
        'refined_chunks': refined_chunks or []
    }
    if embedding is not None:
        prompt_data['context_embedding'] = embedding.tolist()

    try:
        return call_llm('refine_by_chunk', prompt_data)
    except Exception as e:
        logger.error(f"Error refining script: {e}")
        return chunk

def refine_script_with_context(text, feedback, context=None):
    """
    Refine the input text based on feedback, handling long texts by chunking.
    Args:
    text (str): The input text to be refined.
    feedback (str): Feedback for refining the text.
    raw_contents : Optional context text for generating embedding.
    Returns:
    str: The refined and combined text.
    """
    if context is not None:
        #context_embedding = total_embedding  # Use sum_all_embeddings instead of generate_combined_embedding
        context_chunks = chunk_text(context)
        context_embedding = generate_combined_embedding(context_chunks)
    else:
        context_embedding = None
    
    chunks = chunk_text(text)
    refined_chunks = []
    
    for chunk in chunks:
        refined_chunk = refine_chunk(chunk, feedback=feedback, embedding=context_embedding, refined_chunks=refined_chunks)
        refined_chunks.append(refined_chunk)
    
    return " ".join(refined_chunks)

def review_script(script, source_contents):
    """
    Validate the generated script for coherence and engagement.

    Parameters:
    script (str): The script to validate.
    source_contents (str): The original collected articles for context.

    Returns:
    str: The combined feedback from the validation process.
    """
    logger.info("Entering review_script")
    
    prompt_data = {
        'script': script,
        'collected_articles': source_contents
    }
    logger.info("Validate the generated script for coherence and engagement.")
    feedback = call_llm('review-script', prompt_data)

    logger.debug("Exiting review_script")
    return feedback

def write_to_file(directory, filename, content):
    """
    Writes the content to a file in the specified directory.

    Parameters:
    directory (str): The directory where the file will be written.
    filename (str): The name of the file.
    content (str): The content to be written.
    """
    # Ensure the directory exists
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Directory {directory} created.")

    filepath = os.path.join(directory, filename)
    with open(filepath, 'w', encoding='utf-8') as file:
        file.write(content)
    logger.info(f"Script saved to {filepath}")

def smart_paragraph_split(
    text: str,
    min_sentences: int = 2,
    max_sentences: int = 5,
    language: str = 'English'
) -> List[str]:
    # 使用新的 normalize_language 函数
    normalized_lang = normalize_language(language)
    # 使用 get_nlp_model 函数获取适当的 NLP 模型
    nlp = get_nlp_model(lang=normalized_lang)
    
    doc = nlp(text)

    sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]

    result_paragraphs = []
    current_paragraph = []

    for sentence in sentences:
        current_paragraph.append(sentence)
        if len(current_paragraph) >= max_sentences:
            paragraph = ''.join(current_paragraph) if normalized_lang in ['chinese', 'japanese'] else ' '.join(current_paragraph)
            result_paragraphs.append(paragraph)
            current_paragraph = []

    if current_paragraph:
        paragraph = ''.join(current_paragraph) if normalized_lang in ['chinese', 'japanese'] else ' '.join(current_paragraph)
        result_paragraphs.append(paragraph)

    return result_paragraphs


def clean_text(text: str) -> str:
    """
    清理文本，去除不必要的换行符和标点符号后的空格。
    """
    # 去除换行符
    text = text.replace('\n', '').replace('\r', '').strip()

    # 去除标点符号后的空格
    punctuation_marks = "。！？、，．：；｝"
    for mark in punctuation_marks:
        text = text.replace(f"{mark} ", mark)

    return text

def extract_entities(text: str, using_cache: bool = True) -> List[Dict]:
    """
    从文本中提取命名实体
    
    Args:
        text: 输入文本
        using_cache: 是否使用缓存
        
    Returns:
        包含实体信息的字典列表
        
    Raises:
        Exception: 当实体提取失败时
    """
    try:
        # 调用GPT进行实体识别
        response = call_llm_json_response(
            api_function="ner",
            prompt_data={"text": text},
            using_cache=using_cache
        )
        
        # 验证并返回实体列表
        if response and "entities" in response:
            return response["entities"]
            
        raise ValueError("Invalid response format: missing entities field")
        
    except Exception as e:
        raise Exception(f"实体提取失败: {str(e)}")


def deduplicate_texts(
    texts: List[str], 
    similarity_threshold: float = 0.8,
    language: str = "en"
) -> List[str]:
    """去除重复或高度相似的文本"""
    if not texts:
        return []
        
    initial_count = len(texts)
    logger.debug(f"开始去重处理，初始文本数量: {initial_count}")
    
    # 基本文本清理和规范化
    cleaned_texts = [re.sub(r'\s+', ' ', text.strip()) for text in texts]
    cleaned_texts = [text for text in cleaned_texts if text]
    
    after_cleaning_count = len(cleaned_texts)
    logger.debug(f"清理空文本后的数量: {after_cleaning_count}, 移除了 {initial_count - after_cleaning_count} 个空文本")
    
    if not cleaned_texts:
        return []
        
    # 获取 EmbeddingManager 实例，使用指定的语言
    embedding_manager = get_embedding_manager(language=language)
    
    # 生成文本向量
    try:
        vectors = []
        unique_texts = []
        duplicate_count = 0
        
        for idx, text in enumerate(cleaned_texts, 1):
            logger.debug(f"处理第 {idx}/{after_cleaning_count} 个文本，当前长度: {len(text)} 字符")
            
            # 使用智能段落分割处理长文本，传入语言参数
            text_chunks = smart_paragraph_split(text, language=language)
            
            try:
                # 为每个段落生成向量并计算平均值作为文本的整体表示
                chunk_vectors = []
                for chunk_idx, chunk in enumerate(text_chunks, 1):
                    # 使用 EmbeddingManager 获取 embedding
                    chunk_vector = embedding_manager.get_cached_embedding(chunk, skip_cache=True)
                    if chunk_vector is not None:
                        chunk_vectors.append(chunk_vector)
                    else:
                        logger.debug(f"第 {chunk_idx} 个段落生成向量失败")
                
                if not chunk_vectors:
                    continue
                    
                # 计算所有段落向量的平均值
                current_vector = np.mean(chunk_vectors, axis=0)
                
                # 计算与已有文本的相似度
                if vectors:
                    similarities = cosine_similarity(
                        [current_vector], 
                        vectors
                    )[0]
                    
                    max_similarity = max(similarities) if similarities.size > 0 else 0
                    
                    # 如果与任何已有文本相似度都低于阈值，则保留
                    if not any(sim > similarity_threshold for sim in similarities):
                        vectors.append(current_vector)
                        unique_texts.append(text)
                    else:
                        duplicate_count += 1
                else:
                    # 第一个文本直接添加
                    vectors.append(current_vector)
                    unique_texts.append(text)
                    
            except Exception as e:
                logger.warning(f"处理文本 {idx} 时发生错误: {str(e)}")
                continue
                
        final_count = len(unique_texts)
        logger.info(f"去重完成: 初始文本数 {initial_count}, 清理后数量 {after_cleaning_count}, "
                   f"最终保留 {final_count}, 移除重复 {duplicate_count}, "
                   f"去重率 {(duplicate_count/after_cleaning_count*100):.2f}%")
        
        return unique_texts
        
    except Exception as e:
        logger.error(f"文本去重过程中发生错误: {str(e)}")
        return cleaned_texts
        return cleaned_texts