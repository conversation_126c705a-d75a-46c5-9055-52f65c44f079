# modules/embedding_management.py

import os
import numpy as np
import time
import logging
import faiss
import hashlib
import pickle
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from openai import AzureOpenAI
from tqdm import tqdm
import torch
from sentence_transformers import SentenceTransformer
from modules.nlp_model import get_nlp_model
from config import (
    logger,
    EMBEDDINGS_PATH
)
from modules.utils import read_file_with_encoding, clean_text, smart_paragraph_split
import warnings
from transformers import logging as transformers_logging
from dataclasses import dataclass
import atexit
import json

logger = logging.getLogger(__name__)

@dataclass
class Episode:
    """剧集数据类"""
    episode_number: int
    title: str
    summary: str
    characters: List[Dict[str, Any]]
    scenes: List[Dict[str, Any]]
    
    @classmethod
    def from_script(cls, script: Dict[str, Any]) -> 'Episode':
        """从剧本字典创建Episode实例"""
        ep_data = script.get('ep', {})
        return cls(
            episode_number=ep_data.get('n', 0),
            title=ep_data.get('t', ''),
            summary=script.get('summary', ''),  # 假设剧本中包含summary字段
            characters=ep_data.get('c', []),
            scenes=ep_data.get('s', [])
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'ep': {
                'n': self.episode_number,
                't': self.title,
                'c': self.characters,
                's': self.scenes
            },
            'summary': self.summary
        }

class EmbeddingManager:
    """管理embedding的生成、缓存和检索"""
    
    # 语言代码映射
    LANGUAGE_CODES = {
        'zh': 'chinese',
        'cn': 'chinese',
        'chinese': 'chinese',
        'ja': 'japanese',
        'jp': 'japanese',
        'japanese': 'japanese',
        'ko': 'korean',
        'kr': 'korean',
        'korean': 'korean',
        'en': 'english',
        'english': 'english'
    }
    
    # 语言专用模型映射
    LANGUAGE_MODELS = {
        'chinese': [
            'shibing624/text2vec-base-chinese',     # 专门针对中文优化
            'moka-ai/m3e-base',                     # 新一代中文模型
        ],
        'japanese': [
            'cl-tohoku/bert-base-japanese',         # 专门针对日语优化
            'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'  # 备选多语言模型
        ],
        'korean': [
            'snunlp/KR-BERT-char16424',            # 专门针对韩语优化
            'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'  # 备选多语言模型
        ],
        'english': [
            'sentence-transformers/all-mpnet-base-v2',  # 英语性能最好的模型
            'sentence-transformers/all-MiniLM-L6-v2'   # 轻量级英语模型
        ]
    }
    
    def __init__(self, dimension: int = 768, language: str = 'chinese'):
        try:
            # 初始化路径
            self.paths = {
                'index': os.path.join(EMBEDDINGS_PATH, 'embeddings.index'),
                'map': os.path.join(EMBEDDINGS_PATH, 'text_map.pkl'),
                'sequence': os.path.join(EMBEDDINGS_PATH, 'sequence_list.pkl'),
                'hash_index': os.path.join(EMBEDDINGS_PATH, 'hash_index.pkl')
            }
            
            # 标准化语言代码
            self.language = self.normalize_language_code(language)
            
            # 初始化保存时间和计数器
            self.last_save_time = time.time()
            self.new_embeddings_count = 0
            self.SAVE_THRESHOLD = 100  # 每100个新embedding保存一次
            self.MAX_INDEX_SIZE = 1000000  # 最大索引大小
            
            # 加载或创建索引和映射
            self.index, self.text_map, self.sequence_list, self.text_hash_to_index = \
                self.load_or_create_index(dimension)
            
            # 初始化模型
            self.model = self.get_model()
            
            self.episodes: List[Episode] = []
            
        except Exception as e:
            logger.error(f"EmbeddingManager初始化失败: {str(e)}")
            raise

    @classmethod
    def normalize_language_code(cls, language: str) -> str:
        """
        标准化语言代码
        
        Args:
            language: 输入的语言代码或名称
            
        Returns:
            str: 标准化后的语言代码
            
        Raises:
            ValueError: 当不支持该语言时
        """
        normalized = cls.LANGUAGE_CODES.get(language.lower())
        if normalized is None:
            logger.warning(f"不支持的语言: {language}，使用默认多语言模型")
            return 'english'  # 默认使用英语（多语言）模型
        return normalized

    def load_or_create_index(self, dimension: int) -> Tuple[faiss.Index, Dict[str, str], List[str], Dict[str, int]]:
        """加载或创建Faiss索引及相关映射"""
        logger.debug(f"使用索引路径: {self.paths['index']}")
        
        if all(os.path.exists(path) for path in self.paths.values()):
            try:
                index = faiss.read_index(self.paths['index'])
                with open(self.paths['map'], 'rb') as f:
                    text_map = pickle.load(f)
                with open(self.paths['sequence'], 'rb') as f:
                    sequence_list = pickle.load(f)
                with open(self.paths['hash_index'], 'rb') as f:
                    text_hash_to_index = pickle.load(f)
                logger.info(f"从缓存加载了 {index.ntotal} 个embeddings")
                return index, text_map, sequence_list, text_hash_to_index
            except Exception as e:
                logger.error(f"加载缓存时出错: {e}")
        
        # 创建新索引
        logger.info("创建新的 Faiss 索引")
        index = faiss.IndexFlatIP(dimension)  # 使用内积索引，更适合余弦相似度
        text_map = {}
        sequence_list = []
        text_hash_to_index = {}
        return index, text_map, sequence_list, text_hash_to_index

    def get_model(self) -> SentenceTransformer:
        """获取或初始化模型"""
        try:
            # 设置环境变量来控制 torch.load 行为
            os.environ["TORCH_LOAD_WEIGHTS_ONLY"] = "1"
            
            # 获取当前语言的模型列表
            model_list = self.LANGUAGE_MODELS.get(
                self.language, 
                ['sentence-transformers/paraphrase-multilingual-mpnet-base-v2']
            )
            
            # 尝试加载列表中的模型，直到成功
            last_error = None
            for model_name in model_list:
                try:
                    model = SentenceTransformer(
                        model_name,
                        device="cuda" if torch.cuda.is_available() else "cpu"
                    )
                    logger.info(f"成功加载模型: {model_name}")
                    return model
                except Exception as e:
                    last_error = e
                    logger.warning(f"加载模型 {model_name} 失败: {e}")
                    continue
            
            # 如果所有模型都加载失败，使用默认多语言模型
            if last_error:
                logger.error(f"所有专用模型加载失败，使用默认多语言模型: {last_error}")
            
            default_model = 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'
            logger.info(f"使用默认多语言模型: {default_model}")
            return SentenceTransformer(
                default_model,
                device="cuda" if torch.cuda.is_available() else "cpu"
            )
            
        except Exception as e:
            logger.error(f"初始化 SentenceTransformer 模型时出错: {e}")
            raise
    
    def get_embedding_with_context(
        self,
        text: str,
        sequence_position: int,
        window_size: int = 2
    ) -> Tuple[np.ndarray, List[str]]:
        """获取embedding及其顺序上下文"""
        current_embedding = self.get_cached_embedding(text, sequence_position)
        
        # 基于顺序获取上下文
        start_pos = max(0, sequence_position - window_size)
        end_pos = min(len(self.sequence_list), sequence_position + window_size + 1)
        
        context_texts = []
        for pos in range(start_pos, end_pos):
            if pos != sequence_position and self.sequence_list[pos]:
                context_texts.append(self.sequence_list[pos][:100])  # 文本预览
        
        return current_embedding, context_texts

    def get_cached_embedding(self, text: str, sequence_position: Optional[int] = None, skip_cache: bool = False) -> Optional[np.ndarray]:
        """获取或生成embedding
        
        Args:
            text: 输入文本
            sequence_position: 序列位置（可选）
            skip_cache: 是否跳过缓存（用于 fact_checker）
        """
        if not text.strip():
            logger.warning("遇到空文本，跳过embedding生成")
            return None
        
        if skip_cache:
            # 直接生成新的 embedding，不使用缓存
            try:
                embedding = self.model.encode(text, normalize_embeddings=True)
                return embedding
            except Exception as e:
                logger.error(f"生成文本embedding时出错: {e}")
                return None
            
        # 原有的缓存逻辑
        text_hash = get_text_hash(text)
        if text_hash in self.text_hash_to_index:
            idx = self.text_hash_to_index[text_hash]
            try:
                embedding = self.index.reconstruct(idx)
                return embedding
            except Exception as e:
                logger.error(f"重构索引 {idx} 的embedding失败: {e}")
        
        try:
            embedding = self.model.encode(text, normalize_embeddings=True)
            self.index.add(np.array([embedding]))
            self.text_map[text_hash] = text
            self.text_hash_to_index[text_hash] = self.index.ntotal - 1
            
            if sequence_position is not None:
                while len(self.sequence_list) <= sequence_position:
                    self.sequence_list.append(None)
                self.sequence_list[sequence_position] = text
            
            # 更新计数器并检查是否需要保存
            self.new_embeddings_count += 1
            if self.new_embeddings_count >= self.SAVE_THRESHOLD:
                self.save()
                self.new_embeddings_count = 0
                
            # 检查索引大小
            if self.index.ntotal > self.MAX_INDEX_SIZE:
                logger.warning("检测到大型索引，清理旧条目")
                self._cleanup_old_entries()
            
            return embedding
        except Exception as e:
            logger.error(f"生成文本embedding时出错: {e}")
            return None

    def get_batch_embeddings(
        self,
        texts: List[str],
        maintain_sequence: bool = True
    ) -> List[Optional[np.ndarray]]:
        """批量获取embeddings"""
        MAX_RETRIES = 3
        
        for attempt in range(MAX_RETRIES):
            try:
                logger.info(f"处理 {len(texts)} 个文本块的embeddings")
                embeddings = []
                new_texts = []
                positions = []
                
                # 检查和准备文本
                for i, text in enumerate(texts):
                    if not isinstance(text, str):
                        logger.error(f"无效的文本类型 at index {i}: {type(text)}")
                        embeddings.append(None)
                        continue
                    if not text.strip():
                        logger.warning(f"空文本 at index {i}")
                        embeddings.append(None)
                        continue
                        
                    text_hash = get_text_hash(text)
                    if text_hash in self.text_hash_to_index:
                        idx = self.text_hash_to_index[text_hash]
                        try:
                            embedding = self.index.reconstruct(idx)
                            embeddings.append(embedding)
                        except Exception as e:
                            logger.error(f"重构索引 {idx} 的embedding失败: {e}")
                            new_texts.append(text)
                            positions.append(i)
                            embeddings.append(None)
                    else:
                        new_texts.append(text)
                        positions.append(i)
                        embeddings.append(None)
                
                # 生成新的embeddings
                if new_texts:
                    logger.info(f"生成 {len(new_texts)} 个新的embeddings")
                    try:
                        normalized_texts = [text.strip() for text in new_texts]
                        new_embeddings = self.model.encode(
                            normalized_texts,
                            normalize_embeddings=True,
                            show_progress_bar=True,
                            batch_size=32
                        )
                        
                        # 添加到FAISS索引
                        self.index.add(new_embeddings)
                        
                        # 更新映射
                        for pos, (text, embedding) in enumerate(zip(new_texts, new_embeddings)):
                            text_hash = get_text_hash(text)
                            self.text_map[text_hash] = text
                            self.text_hash_to_index[text_hash] = self.index.ntotal - len(new_embeddings) + pos
                            
                            if maintain_sequence:
                                orig_pos = positions[pos]
                                while len(self.sequence_list) <= orig_pos:
                                    self.sequence_list.append(None)
                                self.sequence_list[orig_pos] = text
                            
                            embeddings[positions[pos]] = embedding
                            
                        # 更新计数器
                        self.new_embeddings_count += len(new_texts)
                        if self.new_embeddings_count >= self.SAVE_THRESHOLD:
                            self.save()
                            self.new_embeddings_count = 0
                    
                    except Exception as e:
                        logger.error(f"生成新embeddings时出错: {e}")
                        raise
                
                return embeddings
                
            except Exception as e:
                if attempt == MAX_RETRIES - 1:
                    logger.error(f"批量生成embeddings失败，已重试{MAX_RETRIES}次: {e}")
                    raise
                logger.warning(f"重试 {attempt + 1}/{MAX_RETRIES}, 错误: {e}")
                time.sleep(1)  # 重试间隔

    def _cleanup_old_entries(self):
        """清理旧的embedding条目"""
        try:
            # 保留最新的70%的条目
            keep_count = int(self.index.ntotal * 0.7)
            new_index = faiss.IndexFlatIP(self.index.d)
            
            # 获取最新的embeddings
            recent_embeddings = []
            recent_texts = []
            recent_hashes = []
            
            for i in range(self.index.ntotal - keep_count, self.index.ntotal):
                embedding = self.index.reconstruct(i)
                recent_embeddings.append(embedding)
            
            # 重建索引
            new_index.add(np.array(recent_embeddings))
            
            # 更新映射
            new_text_map = {}
            new_hash_index = {}
            new_sequence_list = [None] * len(self.sequence_list)
            
            for i, (text_hash, text) in enumerate(self.text_map.items()):
                if i >= len(self.text_map) - keep_count:
                    new_idx = i - (len(self.text_map) - keep_count)
                    new_text_map[text_hash] = text
                    new_hash_index[text_hash] = new_idx
            
            # 更新实例变量
            self.index = new_index
            self.text_map = new_text_map
            self.text_hash_to_index = new_hash_index
            
            logger.info(f"清理完成，当前索引大小: {self.index.ntotal}")
            
        except Exception as e:
            logger.error(f"清理旧条目时出错: {e}")

    def save(self):
        """保存索引和映射"""
        try:
            faiss.write_index(self.index, self.paths['index'])
            with open(self.paths['map'], 'wb') as f:
                pickle.dump(self.text_map, f)
            with open(self.paths['sequence'], 'wb') as f:
                pickle.dump(self.sequence_list, f)
            with open(self.paths['hash_index'], 'wb') as f:
                pickle.dump(self.text_hash_to_index, f)
                
            logger.info(f"已保存 {self.index.ntotal} 个embeddings到缓存")
        except Exception as e:
            logger.error(f"保存缓存时出错: {e}")

    def retrieve_enhanced_context(
        self,
        current_text: str,
        episode_number: int,
        global_outline: Dict[str, Any],
        window_size: int = 2
    ) -> Dict[str, Any]:
        """增强的上下文检索，集成全局大纲信息"""
        try:
            # 获取当前文本的embedding
            current_embedding = self.get_cached_embedding(current_text)
            
            # 相关性搜索结果
            context_results = []
            
            # 1. 检索相邻章节
            start_pos = max(0, episode_number - window_size)
            end_pos = min(len(self.sequence_list), episode_number + window_size + 1)
            
            for pos in range(start_pos, end_pos):
                if pos != episode_number and self.sequence_list[pos]:
                    neighbor_text = self.sequence_list[pos]
                    neighbor_embedding = self.get_cached_embedding(neighbor_text)
                    similarity = self.compute_similarity(current_embedding, neighbor_embedding)
                    context_results.append({
                        "text": neighbor_text[:200],  # 截取预览
                        "position": pos,
                        "similarity": float(similarity),
                        "type": "neighbor"
                    })
            
            # 2. 从全局大纲提取相关信息
            relevant_events = []
            relevant_characters = []
            
            if global_outline:
                # 处理关键事件
                for event in global_outline.get("main_storyline", {}).get("key_points", []):
                    event_text = event["point"]
                    event_embedding = self.get_cached_embedding(event_text)
                    similarity = self.compute_similarity(current_embedding, event_embedding)
                    
                    if similarity > 0.6:  # 相似度阈值
                        relevant_events.append({
                            "event": event,
                            "similarity": float(similarity)
                        })
                
                # 处理主要角色
                for char in global_outline.get("main_characters", []):
                    if char["importance"] == "high":
                        char_text = f"{char['name']} {char['background']}"
                        char_embedding = self.get_cached_embedding(char_text)
                        similarity = self.compute_similarity(current_embedding, char_embedding)
                        
                        if similarity > 0.5:  # 角色相关性阈值
                            relevant_characters.append({
                                "character": char,
                                "similarity": float(similarity)
                            })
            
            # 3. 整合结果
            return {
                "context_results": sorted(
                    context_results,
                    key=lambda x: x["similarity"],
                    reverse=True
                ),
                "relevant_events": sorted(
                    relevant_events,
                    key=lambda x: x["similarity"],
                    reverse=True
                ),
                "relevant_characters": sorted(
                    relevant_characters,
                    key=lambda x: x["similarity"],
                    reverse=True
                )
            }
            
        except Exception as e:
            logger.error(f"增强上下文检索失败: {str(e)}")
            return {
                "context_results": [],
                "relevant_events": [],
                "relevant_characters": []
            }
            
    def compute_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return np.dot(vec1, vec2) / (norm1 * norm2)

    def add_episode(self, episode: Episode) -> None:
        """加新的剧集并更新embedding索引"""
        self.episodes.append(episode)
        self.get_cached_embedding(episode.summary)
    
    def get_episode(self, episode_number: int) -> Optional[Episode]:
        """获取指定编号的剧集"""
        for episode in self.episodes:
            if episode.episode_number == episode_number:
                return episode
        return None

# 全局embedding管理器实例
_embedding_manager: Optional[EmbeddingManager] = None

def get_embedding_manager(language: str = 'chinese') -> EmbeddingManager:
    """获取或创建embedding管理器实例"""
    global _embedding_manager
    if _embedding_manager is None:
        _embedding_manager = EmbeddingManager(language=language)
    elif _embedding_manager.language != language.lower():
        # 如果语言设置不同，创建新的实例
        _embedding_manager.save()  # 保存当前实例的数据
        _embedding_manager = EmbeddingManager(language=language)
    return _embedding_manager

def get_embedding(text: str) -> np.ndarray:
    """获取单个文本的embedding"""
    manager = get_embedding_manager()
    return manager.get_cached_embedding(text)

def get_embeddings(texts: List[str]) -> List[np.ndarray]:
    """批量获取embeddings"""
    manager = get_embedding_manager()
    return manager.get_batch_embeddings(texts)

# 程序退出时保存
@atexit.register
def cleanup():
    if _embedding_manager is not None:
        _embedding_manager.save()

def get_text_hash(text: str) -> str:
    """计算文本的哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()