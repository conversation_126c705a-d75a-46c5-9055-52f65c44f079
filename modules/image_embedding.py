# image_embedding.py
import argparse
import os
import json
import nltk
from langdetect import detect, LangDetectException
from typing import Optional
import logging
import shutil
import datetime
import sys


sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.langchain_interface import generate_embedding
from modules.describe_images import describe_images_with_context
from modules.vector_store import VectorStore
from modules.describe_images import describe_images_with_context, generate_keyword
from config import (
    DATABASE_PATH, EXPECTED_EMBEDDING_DIMENSION, IMAGE_DIR, 
    SCRIPT_DIR, LANGUAGE_CODES, TOPIC_DIR, FAISS_INDEX_PATH, 
    METADATA_PATH, VECTOR_STORE_DIR, get_image_metadata_path
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 下载必要的 NLTK 数据
nltk.download('punkt')
nltk.download('punkt_tab')

def get_language_name(lang_code):
    for name, code in LANGUAGE_CODES.items():
        if code == lang_code:
            return name
    return "English"  # 默认返回英语

def load_attribute_json(json_file: str) -> dict:
    """
    加载属性 JSON 文件，获取每张图片的属性（如 URL 和 context）。

    Args:
        json_file (str): 属性 JSON 文件的路径。

    Returns:
        dict: key 为图片文件名，value 为属性字典。
    """
    if not os.path.exists(json_file):
        logger.warning(f"属性 JSON 文件不存在: {json_file}")
        return {}
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"已加载属性 JSON 文件: {json_file}")
        return {os.path.basename(item['filepath']): item for item in data if 'filepath' in item}
    except Exception as e:
        logger.error(f"读取属性 JSON 文件时出错: {e}")
        return {}

def process_image_with_embedding(filepath: str, url: Optional[str], context: str, theme: str, vector_store: VectorStore):
    """
    处理单个图片：生成 embedding 并存储到向量数据库
    
    Args:
        filepath: 图片文件路径
        url: 图片的 URL
        context: 图片上下文信息
        theme: 主题名称
        vector_store: 向量存储实例
    """
    try:
        # 如果没有 URL，跳过该图片
        if not url:
            logger.warning(f"跳过没有 URL 的图片: {filepath}")
            return
        
        # 检查 URL 是否已存在
        if vector_store.url_exists(url):
            logger.info(f"跳过已存在的图片 URL: {url}")
            return
            
        # 生成描述和嵌入
        description = describe_images_with_context([(url, context)])[0][1]
        embedding = generate_embedding(description)
        
        # 检查维度是否匹配
        if embedding.shape[0] != vector_store.dimension:  # 使用 vector_store 的维度而不是常量
            logger.error(f"意外的嵌入维度: {embedding.shape[0]}，期望: {vector_store.dimension}")
            return
        
        # 准备元数据
        metadata = {
            'filepath': filepath,
            'url': url,
            'context': context,
            'theme': theme,
            'description': description
        }
        
        # 添加到向量存储
        vector_store.add_embeddings([embedding], [metadata])
        logger.debug(f"已将图片添加到向量存储: {filepath}")
        
    except Exception as e:
        logger.error(f"处理图片时出错: {e}", exc_info=True)

def generate_embeddings(input_dir: str, theme: str, attribute_json_path: str):
    """生成图片的嵌入并保存到向量数据库中"""
    vector_store = VectorStore(FAISS_INDEX_PATH, METADATA_PATH)
    logger.debug(f"向量存储维度: {vector_store.dimension}")
    logger.debug(f"正在处理目录中的图片: {input_dir}, 主题: {theme}")
    
    image_files = [f for f in os.listdir(input_dir) 
                  if os.path.isfile(os.path.join(input_dir, f)) and 
                  f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.avif'))]
    
    attributes = load_attribute_json(attribute_json_path)
    logger.debug(f"从 {attribute_json_path} 加载了 {len(attributes)} 个图片属性")
    
    # 统计计数
    total_count = len(image_files)
    no_url_count = 0
    existing_count = 0
    updated_filepath_count = 0
    description_failed_count = 0
    embedding_failed_count = 0
    actual_added_count = 0
    success_count = 0
    
    # 1. 首先筛选所有需要处理的图片
    pending_images = []
    for image_file in image_files:
        full_path = os.path.abspath(os.path.join(input_dir, image_file))
        img_attributes = attributes.get(image_file, {})
        url = img_attributes.get('url', '') or None
        
        if not url:
            no_url_count += 1
            logger.debug(f"跳过图片(无URL): {full_path}")
            continue
            
        # 检查 URL 是否已存在，如果存在则更新 filepath
        if vector_store.url_exists(url):
            existing_count += 1
            try:
                metadata = vector_store.get_metadata_by_url(url)
                if metadata and metadata.get('filepath') != full_path:
                    new_metadata = metadata.copy()
                    new_metadata['filepath'] = full_path
                    if vector_store.update_metadata_by_url(url, new_metadata):
                        updated_filepath_count += 1
                        # 确保更新后立即保存
                        vector_store.save()
                        logger.debug(f"更新并保存了filepath: {full_path}")
                    else:
                        logger.warning(f"更新filepath失败: {full_path}")
            except Exception as e:
                logger.error(f"更新filepath失败: {full_path}: {str(e)}")
            continue
        
        context = img_attributes.get('context', '') or ''
        pending_images.append((full_path, url, context))
        logger.debug(f"添加待处理图片: {full_path} [URL: {url}]")
    
    logger.info(f"""
        初始筛选结果:
        - 总图片数: {total_count}
        - 无URL图片: {no_url_count}
        - 已存在URL: {existing_count}
        - 更新filepath: {updated_filepath_count}
        - 待处理图片: {len(pending_images)}
    """)
    
    batch_size = 32
    # 2. 批量处理未处理的图片
    for i in range(0, len(pending_images), batch_size):
        batch = pending_images[i:i + batch_size]
        descriptions = describe_images_with_context([
            (url, context) for _, url, context in batch
        ])
        
        # 3. 处理成功的描述
        embeddings = []
        metadata_list = []
        
        for idx, ((full_path, url, context), (_, description)) in enumerate(zip(pending_images, descriptions)):
            try:
                embedding = generate_embedding(description)
                
                if embedding.shape[0] == vector_store.dimension:
                    embeddings.append(embedding)
                    metadata_list.append({
                        'filepath': full_path,
                        'url': url,
                        'context': context,
                        'theme': theme,
                        'description': description
                    })
                    success_count += 1
                else:
                    embedding_failed_count += 1
                    logger.warning(f"维度不匹配: {full_path}, 期望 {vector_store.dimension}, 实际 {embedding.shape[0]}")
                    
            except Exception as e:
                embedding_failed_count += 1
                logger.error(f"生成 embedding 失败: {full_path}: {str(e)}")
        
        # 4. 批量添加到向量存储
        if embeddings:
            try:
                added_count = vector_store.add_embeddings(embeddings, metadata_list)
                actual_added_count += added_count  # 使用实际添加的数量
                logger.info(f"批量添加了 {added_count} 个新的 embeddings")
            except Exception as e:
                logger.error(f"添加向量存储失败: {str(e)}")
                embedding_failed_count += len(embeddings)
        
        # 5. 定期输出进度
        processed = i + len(batch)
        logger.info(f"""
            处理进度: {processed}/{total_count} ({processed/total_count*100:.1f}%)
            - 无URL: {no_url_count}
            - 已存在: {existing_count}
            - 描述失败: {description_failed_count}
            - Embedding失败: {embedding_failed_count}
            - 成功生成: {success_count}
            - 实际添加: {actual_added_count}
            """)
    
    # 6. 保存向量存储
    vector_store.save()
    logger.info(f"完成处理，向量存储中总计 {vector_store.get_total_vectors()} 个向量")


def main():
    """
    主函数，解析命令行参数并执行相应的操作。
    """
    parser = argparse.ArgumentParser(description="生成图片嵌入")
    parser.add_argument("--theme", help="指定的主题名称")
    parser.add_argument("--embedding", help="图片文件目录")

    args = parser.parse_args()

    if args.embedding:
        if not args.theme:
            logger.error("使用 --embedding 时必须指定 --theme")
            return
        theme = args.theme
        input_dir = args.embedding
        attribute_json_path = get_image_metadata_path(input_dir, theme)
        generate_embeddings(input_dir, theme, attribute_json_path)
    else:
        print("请同时指定 --embedding 和 --theme 参数")

if __name__ == "__main__":
    main()