import os
import logging
import requests
from abc import ABC, abstractmethod
from typing import Dict, List, Optional

class BaseDownloader(ABC):
    """下载器的基类接口"""
    
    @abstractmethod
    def authenticate(self) -> bool:
        """
        认证方法
        
        :return: 认证成功返回 True，失败返回 False
        """
        pass
    
    @abstractmethod
    def add_download(self, url: str, destination: str) -> Optional[str]:
        """
        添加下载任务
        
        :param url: 要下载的文件URL
        :param destination: 下载目标路径
        :return: 成功返回下载ID或文件路径，失败返回 None
        """
        pass
    
    @abstractmethod
    def monitor_downloads(self, urls: List[str], timeout: int = 600) -> Dict[str, Optional[str]]:
        """
        监控下载进度
        
        :param urls: 要监控的URL列表
        :param timeout: 超时时间（秒）
        :return: URL到文件路径的映射字典
        """
        pass

class DirectDownloader(BaseDownloader):
    """使用 requests 直接下载文件的实现"""
    
    def __init__(self, destination: str = "downloads"):
        self.logger = logging.getLogger("DirectDownloader")
        self.destination = destination

    def authenticate(self) -> bool:
        """
        直接下载不需要认证
        
        :return: 始终返回 True
        """
        return True

    def add_download(self, url: str, destination: str) -> Optional[str]:
        """
        直接开始下载文件
        
        :param url: 要下载的文件URL
        :param destination: 下载目标路径
        :return: 成功返回文件路径，失败返回 None
        """
        try:
            if not os.path.exists(destination):
                os.makedirs(destination)

            # 从 URL 中提取文件名
            filename = url.split('/')[-1].split('?')[0]
            filepath = os.path.join(destination, filename)
            
            # 下载文件
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # 写入文件
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:  # 过滤掉 keep-alive 新块
                        f.write(chunk)
            
            self.logger.info(f"文件已下载到: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"下载失败 {url}: {e}")
            return None

    def monitor_downloads(self, urls: List[str], timeout: int = 600) -> Dict[str, Optional[str]]:
        """
        由于是同步下载，直接返回下载结果
        
        :param urls: 要下载的URL列表
        :param timeout: 超时时间（未使用）
        :return: URL到文件路径的映射字典
        """
        results = {}
        for url in urls:
            self.logger.info(f"开始下载: {url}")
            filepath = self.add_download(url, self.destination)
            results[url] = filepath
            if filepath:
                self.logger.info(f"下载完成: {url} -> {filepath}")
            else:
                self.logger.error(f"下载失败: {url}")
        
        return results 