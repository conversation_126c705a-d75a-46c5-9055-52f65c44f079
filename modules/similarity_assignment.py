#!/usr/bin/env python3
# modules/similarity_assignment.py - Core logic for BGE similarity, reranking, filtering, and ILP assignment

import os
import numpy as np
import json
import logging
import copy
import math
import re
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict
from PIL import Image
import torch

# Enhanced dependencies (ensure these are installed in the environment)
try:
    from sentence_transformers import SentenceTransformer, CrossEncoder
    # Import CLIP wrapper with a different name to avoid conflict
    from sentence_transformers import SentenceTransformer as ClipSentenceTransformer
except ImportError:
    SentenceTransformer, CrossEncoder, ClipSentenceTransformer = None, None, None
    logging.error("sentence-transformers library not found. Install it to use BGE and CLIP models.")

try:
    from rank_bm25 import BM25Okapi
except ImportError:
    BM25Okapi = None
    logging.warning("rank_bm25 library not found. BM25 scoring will be unavailable.")

try:
    import nltk
    from nltk.tokenize import word_tokenize, sent_tokenize
except ImportError:
    nltk = None
    logging.warning("nltk library not found. Text tokenization might be less accurate.")

try:
    import faiss
except ImportError:
    faiss = None
    logging.error("faiss library not found. Install faiss-cpu or faiss-gpu for dense retrieval.")

try:
    from ortools.linear_solver import pywraplp
except ImportError:
    pywraplp = None
    logging.error("ortools library not found. ILP optimization will be unavailable.")

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
logger.setLevel(logging.INFO)


# --- Constants --- (Moved from main script)
TOP_K_RETRIEVAL = 20
TOP_N_POST_FILTER = 5
CLIP_POST_FILTER_THRESHOLD = 0.22
BGE_EMBEDDING_DIM = 768
CLIP_EMBEDDING_DIM = 512
DEFAULT_BM25_WEIGHT = 0.1
DEFAULT_NULL_IMAGE_REWARD_ROUND1 = 0.01
DEFAULT_NULL_IMAGE_REWARD_FALLBACK = 0.01
NULL_REWARD = 0.2 # Used in ILP fallback calculation maybe?
REPEAT_PENALTY = 1 # Used in ILP
MIN_DISTANCE_BETWEEN_REUSE = 3 # Used in ILP
MAX_TOTAL_USES = 3 # Used in ILP fallback calculation
# MAX_DIVERSITY_PAIRS_PER_SEGMENT = 5000 # Used in ILP


# --- Module Globals / State ---
_clip_model_for_filter = None
_bge_bi_encoder = None
_bge_reranker = None
BM25_INDEX_CACHE = {}

# --- Helper Functions ---

def preprocess(text):
    """Preprocess text for BM25."""
    if not text or nltk is None:
        return text.lower().split() if text else []
    try:
        return word_tokenize(text.lower())
    except Exception as e:
        logger.warning(f"NLTK word_tokenize failed ({e}), using simple split.")
        return text.lower().split()

def create_safe_zero_vector(dim: int) -> np.ndarray:
    """Creates a near-zero vector of specified dimension."""
    if not dim or dim <= 0: dim = BGE_EMBEDDING_DIM
    vec = np.zeros(dim, dtype=np.float32)
    vec[0] = 1e-9
    return vec

# --- Model Initializers ---

def get_clip_model_for_filter(device: str):
    """Lazy initialization of CLIP model for visual post-filtering."""
    if ClipSentenceTransformer is None: return None # Check if import failed
    global _clip_model_for_filter
    if _clip_model_for_filter is None:
        logger.debug(f"Loading CLIP model (clip-ViT-B-32) for filter on {device}...")
        try:
            _clip_model_for_filter = ClipSentenceTransformer("clip-ViT-B-32")
            _clip_model_for_filter.to(device)
            assert all(p.device.type == device for p in _clip_model_for_filter.parameters()), "CLIP parameters not on target device!"
            assert all(b.device.type == device for b in _clip_model_for_filter.buffers()), "CLIP buffers not on target device!"
            logger.debug("CLIP model for filter loaded.")
        except Exception as e:
            logger.error(f"Failed to load CLIP filter model: {e}")
            _clip_model_for_filter = None # Ensure it remains None on failure
    return _clip_model_for_filter

def get_bge_bi_encoder(device: str):
    """Lazy initialization of BGE bi-encoder model."""
    if SentenceTransformer is None: return None
    global _bge_bi_encoder
    if _bge_bi_encoder is None:
        logger.debug(f"Loading BGE bi-encoder (BAAI/bge-base-en-v1.5) on {device}...")
        try:
            _bge_bi_encoder = SentenceTransformer("BAAI/bge-base-en-v1.5", device=device)
            assert all(p.device.type == device for p in _bge_bi_encoder.parameters()), "BGE parameters not on target device!"
            assert all(b.device.type == device for b in _bge_bi_encoder.buffers()), "BGE buffers not on target device!"
            logger.debug("BGE bi-encoder loaded.")
        except Exception as e:
            logger.error(f"Failed to load BGE bi-encoder: {e}")
            _bge_bi_encoder = None
    return _bge_bi_encoder

def get_bge_reranker(device: str):
    """Lazy initialization of BGE cross-encoder/reranker model."""
    if CrossEncoder is None: return None
    global _bge_reranker
    if _bge_reranker is None:
        logger.debug(f"Loading BGE reranker (BAAI/bge-reranker-base) on CPU...")
        try:
            _bge_reranker = CrossEncoder("BAAI/bge-reranker-base", device='cpu', max_length=512)
            assert all(p.device.type == 'cpu' for p in _bge_reranker.model.parameters()), "Reranker parameters not on CPU!"
            assert all(b.device.type == 'cpu' for b in _bge_reranker.model.buffers()), "Reranker buffers not on CPU!"
            logger.debug("BGE reranker (base) loaded on CPU.")
        except Exception as e:
            logger.error(f"Failed to load BGE reranker: {e}")
            _bge_reranker = None
    return _bge_reranker

# --- Embedding Functions ---

def encode_image_clip_for_filter(image_path: str, device: str) -> Optional[np.ndarray]:
    """Encode image using CLIP for post-filter step."""
    logger.debug(f"Encoding image with CLIP filter: {os.path.basename(image_path)}")
    try:
        clip_model = get_clip_model_for_filter(device)
        if clip_model is None: return None
        img = Image.open(image_path).convert("RGB")
        embedding = clip_model.encode([img], batch_size=1, convert_to_numpy=True,
                                     normalize_embeddings=True, show_progress_bar=False)[0]
        return embedding if np.any(embedding) else None
    except FileNotFoundError:
         logger.error(f"Image not found for CLIP filter: {image_path}")
         return None
    except Exception as e:
        logger.error(f"Error encoding image {os.path.basename(image_path)} with CLIP filter: {e}")
        return None

def encode_text_clip_for_filter(text: str, device: str) -> Optional[np.ndarray]:
    """Encode text using CLIP for post-filter step."""
    if not text: return None
    logger.debug(f"Encoding text with CLIP filter: '{text[:50]}...'")
    try:
        clip_model = get_clip_model_for_filter(device)
        if clip_model is None or nltk is None: return None
        if nltk is None:
             logger.warning("NLTK not found. Using regex splitting for sentences in CLIP encoding.")
             # 使用 regex 分句作为回退
             sentences = [s.strip() for s in re.split(r'[.!?]+', text) if s.strip()]
             if not sentences: sentences = [text] # 如果正则分割失败或结果为空，则使用整个文本
        else:
             try:
                 sentences = sent_tokenize(text)
                 sentences = [s.strip() for s in sentences if s.strip()]
                 if not sentences: sentences = [text] # 如果分句结果为空，也使用原文
             except Exception as e:
                 logger.warning(f"NLTK sent_tokenize failed ({e}). Using regex splitting for sentences in CLIP encoding.")
                 # 使用 regex 分句作为回退
                 sentences = [s.strip() for s in re.split(r'[.!?]+', text) if s.strip()]
                 if not sentences: sentences = [text] # 如果正则分割失败或结果为空，则使用整个文本
        if not sentences:
             logger.warning(f"Could not extract sentences from text for CLIP encoding: '{text[:50]}...'. Returning None.")
             return None
        embeddings = clip_model.encode(sentences, normalize_embeddings=True, show_progress_bar=False)
        avg_embedding = np.mean(embeddings, axis=0)
        norm = np.linalg.norm(avg_embedding)
        return avg_embedding / norm if norm > 0 else None
    except Exception as e:
        logger.error(f"Error encoding text '{text[:50]}...' with CLIP filter: {e}")
        return None

def encode_text_bge(texts: List[str], device: str, batch_size: int = 32) -> np.ndarray:
    """Encode texts using the BGE bi-encoder."""
    logger.debug(f"Encoding {len(texts)} texts with BGE...")
    if not texts: return np.array([], dtype=np.float32).reshape(0, BGE_EMBEDDING_DIM)
    try:
        bge_model = get_bge_bi_encoder(device)
        if bge_model is None: raise RuntimeError("BGE bi-encoder model not loaded.")
        embeddings = bge_model.encode(texts, batch_size=batch_size,
                                     normalize_embeddings=True, show_progress_bar=False)
        return embeddings
    except Exception as e:
        logger.error(f"Error encoding texts with BGE: {e}")
        return np.array([create_safe_zero_vector(BGE_EMBEDDING_DIM) for _ in texts])

# --- Indexing and Similarity Calculation ---

def build_bge_index_and_data(
    image_metadata: List[Dict],
    valid_image_paths: List[str],
    theme: str,
    attribute_json_path: str,
    device: str
) -> Tuple[Optional[faiss.Index], Optional[BM25Okapi], Dict[int, int], List[Dict], Optional[np.ndarray]]:
    """
    Builds FAISS index, BM25 index, filters metadata, and returns embeddings.
    """
    if faiss is None: # Check if FAISS is available
        logger.error("FAISS library not installed, cannot build index.")
        return None, None, {}, [], None

    logger.debug("Building BGE/FAISS index and BM25 index...")
    valid_image_paths_set = set(valid_image_paths)
    filtered_metadata = []
    faiss_id_to_meta_idx = {}
    image_summaries = []
    added_filepaths = set()

    # Filter metadata for valid paths with summaries
    for item in image_metadata:
        abs_filepath = item.get('filepath') # Should be absolute from enrichment
        if abs_filepath and abs_filepath in valid_image_paths_set:
            summary = item.get('image_summary')
            if not summary: continue
            if abs_filepath not in added_filepaths:
                current_meta_idx = len(filtered_metadata)
                filtered_metadata.append(item)
                image_summaries.append(summary)
                faiss_id_to_meta_idx[current_meta_idx] = current_meta_idx
                added_filepaths.add(abs_filepath)

    if not filtered_metadata:
        logger.warning("No valid images with summaries found.")
        return None, None, {}, [], None

    logger.debug(f"Found {len(filtered_metadata)} images with summaries for indexing.")

    # Encode summaries
    bge_embeddings = encode_text_bge(image_summaries, device)

    # 零向量检查
    zero_vec_count = sum(1 for v in bge_embeddings if not np.any(v))
    if zero_vec_count > 0:
        logger.error(f"{zero_vec_count} zero vectors detected in BGE embeddings! This should not happen.")
        # Decide on behavior: error out or try to continue?
        # For now, log error and filter them out below.
        # Consider adding `assert zero_vec_count == 0, f"{zero_vec_count} zero vectors detected!"` for stricter check

    # Filter out failed embeddings (including potential zero vectors)
    valid_indices = [i for i, emb in enumerate(bge_embeddings) if np.any(emb)]
    if len(valid_indices) < len(filtered_metadata):
        logger.warning(f"Removed {len(filtered_metadata) - len(valid_indices)} images due to failed encoding.")
        filtered_metadata = [filtered_metadata[i] for i in valid_indices]
        bge_embeddings = bge_embeddings[valid_indices]
        image_summaries = [image_summaries[i] for i in valid_indices]
        faiss_id_to_meta_idx = {i: i for i in range(len(filtered_metadata))}

    if bge_embeddings.shape[0] == 0:
        logger.error("No valid BGE embeddings generated.")
        return None, None, {}, [], None

    # Build FAISS index
    logger.debug(f"Building FAISS index with {bge_embeddings.shape[0]} embeddings...")
    try:
        index_flat_ip = faiss.IndexFlatIP(BGE_EMBEDDING_DIM)
        index_flat_ip.add(bge_embeddings.astype(np.float32))
        logger.debug(f"FAISS index built. Total entries: {index_flat_ip.ntotal}")
    except Exception as faiss_e:
        logger.error(f"Failed to build FAISS index: {faiss_e}", exc_info=True)
        return None, None, {}, [], None

    # Build BM25 index (optional)
    bm25 = None
    if BM25Okapi:
        global BM25_INDEX_CACHE
        cache_needs_update = True; current_mtime = None
        try: current_mtime = os.path.getmtime(attribute_json_path)
        except FileNotFoundError: pass

        if theme in BM25_INDEX_CACHE and current_mtime is not None:
            cached_mtime, cached_data = BM25_INDEX_CACHE[theme]
            if cached_mtime == current_mtime and cached_data.get('corpus_size') == len(image_summaries):
                try:
                    bm25 = BM25Okapi(cached_data['corpus']); cache_needs_update = False
                    logger.debug(f"Using cached BM25 index for theme '{theme}'")
                except Exception: logger.warning("Failed to load BM25 from cache. Recomputing.")

        if cache_needs_update:
            logger.debug(f"Computing BM25 index for theme '{theme}'...")
            try:
                corpus_tokens = [preprocess(summary) for summary in image_summaries]
                bm25 = BM25Okapi(corpus_tokens)
                if current_mtime is not None:
                    BM25_INDEX_CACHE[theme] = (current_mtime, {'corpus': corpus_tokens, 'corpus_size': len(image_summaries)})
                    logger.debug(f"BM25 index computed and cached for theme '{theme}'")
            except Exception as bm25_build_err: logger.error(f"Failed to build BM25 index: {bm25_build_err}"); bm25 = None
    else:
         logger.warning("rank_bm25 not installed. BM25 scoring disabled.")

    return index_flat_ip, bm25, faiss_id_to_meta_idx, filtered_metadata, bge_embeddings

def calculate_bge_similarity_and_rerank(
    paragraphs: List[Dict],
    device: str,
    faiss_index: faiss.Index,
    bm25_index: Optional[BM25Okapi],
    faiss_id_to_meta_idx: Dict[int, int],
    filtered_metadata: List[Dict],
    rerank_k: int = TOP_K_RETRIEVAL,
    bm25_weight: float = DEFAULT_BM25_WEIGHT
) -> Dict[int, List[Tuple[int, float]]]:
    """
    Performs BGE retrieval, BM25 scoring, BGE reranking, and score fusion.
    """
    logger.debug(f"Calculating BGE similarity, reranking Top-{rerank_k}...")
    paragraph_texts = [p.get('paragraph_text', '') for p in paragraphs]
    bge_reranker = get_bge_reranker(device)
    if bge_reranker is None:
         logger.error("BGE Reranker not available. Cannot perform reranking.")
         # Decide fallback: return empty, or just use FAISS scores?
         # For now, return empty as reranking is core to the strategy.
         return {i: [] for i in range(len(paragraphs))}

    paragraph_embeddings = encode_text_bge(paragraph_texts, device)
    results = {}

    for para_idx, (para_text, para_embedding) in enumerate(zip(paragraph_texts, paragraph_embeddings)):
        if not np.any(para_embedding): continue
        try:
            query_embedding = np.array([para_embedding], dtype=np.float32)
            scores_dense_all, faiss_ids_all = faiss_index.search(query_embedding, rerank_k)
            
            # 修正过滤顺序
            scores_dense_row = scores_dense_all[0]
            faiss_ids_row = faiss_ids_all[0]
            valid_mask = faiss_ids_row != -1
            faiss_ids = faiss_ids_row[valid_mask]
            scores_dense = scores_dense_row[valid_mask]
            
            if len(faiss_ids) == 0: results[para_idx] = []; continue
        except Exception as faiss_err: logger.error(f"FAISS search failed for para {para_idx}: {faiss_err}"); results[para_idx] = []; continue

        # 使用过滤后的 faiss_ids 查找元数据索引
        candidate_meta_indices = [faiss_id_to_meta_idx[fid] for fid in faiss_ids]

        scores_sparse_norm = np.zeros(len(faiss_ids))
        if bm25_index:
            para_tokens = preprocess(para_text)
            try:
                candidate_bm25_scores = bm25_index.get_scores(para_tokens)[faiss_ids]
                max_bm25 = np.max(candidate_bm25_scores) if candidate_bm25_scores.size > 0 else 0
                if max_bm25 > 1e-6: scores_sparse_norm = candidate_bm25_scores / max_bm25
            except Exception as bm25_err: logger.warning(f"BM25 scoring failed for para {para_idx}: {bm25_err}")

        reranker_pairs, valid_indices_map = [], {}
        for i, meta_idx in enumerate(candidate_meta_indices):
            if 0 <= meta_idx < len(filtered_metadata):
                 summary = filtered_metadata[meta_idx].get('image_summary', '')
                 if summary: reranker_pairs.append([para_text, summary]); valid_indices_map[len(reranker_pairs)-1] = i

        scores_rerank_aligned = np.zeros(len(faiss_ids))
        if reranker_pairs:
            try:
                scores_rerank_raw = bge_reranker.predict(reranker_pairs, show_progress_bar=False)
                for score_idx, align_idx in valid_indices_map.items(): scores_rerank_aligned[align_idx] = scores_rerank_raw[score_idx]
            except Exception as rerank_err: logger.error(f"BGE Reranker failed for para {para_idx}: {rerank_err}")

        final_scores = (1.0 - bm25_weight) * scores_rerank_aligned + bm25_weight * scores_sparse_norm

        para_results_unsorted = [(candidate_meta_indices[i], score) for i, score in enumerate(final_scores)]
        results[para_idx] = sorted(para_results_unsorted, key=lambda item: item[1], reverse=True)

    logger.debug("BGE similarity calculation and reranking complete.")
    return results

# --- CLIP Post-Filter ---

def apply_clip_post_filter(
    reranked_results: Dict[int, List[Tuple[int, float]]],
    paragraphs: List[Dict],
    filtered_metadata: List[Dict],
    device: str,
    top_n: int = TOP_N_POST_FILTER,
    clip_threshold: float = CLIP_POST_FILTER_THRESHOLD
) -> Dict[int, List[Tuple[int, float, Optional[float]]]]:
    """
    Applies CLIP visual similarity check to top N candidates.
    Returns results with (meta_idx, bge_score, clip_score).
    clip_score is None if not calculated or below threshold.
    """
    logger.debug(f"Applying CLIP visual post-filter to Top-{top_n} candidates (Threshold: {clip_threshold})...")
    filtered_results = {}
    paragraphs_clip_encoded = {}

    for para_idx, candidates in reranked_results.items():
        if not candidates: filtered_results[para_idx] = []; continue
        para_text = paragraphs[para_idx].get('paragraph_text', '')
        if not para_text: filtered_results[para_idx] = candidates; continue

        para_clip_embedding = paragraphs_clip_encoded.get(para_idx)
        if para_clip_embedding is None:
            para_clip_embedding = encode_text_clip_for_filter(para_text, device)
            if para_clip_embedding is None: filtered_results[para_idx] = candidates; continue
            paragraphs_clip_encoded[para_idx] = para_clip_embedding

        kept_candidates = []
        num_to_check = min(top_n, len(candidates))
        logger.debug(f"Para {para_idx}: Checking top {num_to_check}/{len(candidates)} candidates with CLIP.")

        for i, (meta_idx, bge_score) in enumerate(candidates):
            if not (0 <= meta_idx < len(filtered_metadata)): continue # Skip invalid index
            
            clip_sim = None # Default to None
            passes_filter = True # Assume passes unless filter applied and fails

            if i < num_to_check:
                image_path = filtered_metadata[meta_idx].get('filepath')
                if not image_path or not os.path.exists(image_path):
                    logger.warning(f"Para {para_idx}, Cand {i}: Image path missing or invalid: {image_path}")
                    # Keep candidate but cannot calculate CLIP score
                else:
                    image_clip_embedding = encode_image_clip_for_filter(image_path, device)
                    if image_clip_embedding is None:
                         logger.warning(f"Para {para_idx}, Cand {i}: Failed to get CLIP embedding for image {os.path.basename(image_path)}")
                         # Keep candidate but cannot calculate CLIP score
                    else:
                        try:
                            clip_sim = np.dot(para_clip_embedding, image_clip_embedding)
                            logger.debug(f"Para {para_idx}, Cand {i} (meta {meta_idx}): BGE={bge_score:.4f}, CLIP={clip_sim:.4f}")
                            if clip_sim < clip_threshold:
                                passes_filter = False
                                logger.debug(f"CLIP filter removing cand {i} for para {para_idx} (score {clip_sim:.4f} < {clip_threshold}) - MetaIdx: {meta_idx}")
                        except Exception as dot_err:
                             logger.error(f"CLIP dot product error for Para {para_idx}, Cand {i}: {dot_err}")
                             # Keep candidate if dot product fails, but clip_sim remains None
            
            # Add candidate if it passes the filter (or if filter wasn't applied to it)
            if passes_filter:
                # Store bge_score and clip_sim (which might be None)
                kept_candidates.append((meta_idx, bge_score, clip_sim))
            # else: Candidate was filtered out by CLIP

        # Ensure results are still sorted by BGE score primarily if needed downstream,
        # though ILP just needs the scores. CLIP score is for potential future use.
        # kept_candidates.sort(key=lambda x: x[1], reverse=True) # Re-sort if order matters after filtering
        filtered_results[para_idx] = kept_candidates

    logger.debug("CLIP visual post-filter complete.")
    return filtered_results

# --- ILP Optimization ---

def enhanced_ilp_optimization(
    paragraphs_to_assign: List[int],
    # candidate_scores format: {para_idx: [(meta_idx, bge_score, clip_score), ...]}
    candidate_scores: Dict[int, List[Tuple[int, float, Optional[float]]]],
    filtered_metadata: List[Dict],
    image_bge_embeddings: Optional[np.ndarray],
    max_usage_per_image: int = 1,
    min_distance_between_reuse: int = 2,
    prioritize_unused: bool = True,
    null_image_reward: float = DEFAULT_NULL_IMAGE_REWARD_ROUND1
) -> List[Tuple[int, Optional[str]]]:
    """ILP optimization adapted for Top-K candidates."""
    if pywraplp is None: # Check if OR-Tools is available
        logger.error("OR-Tools not installed. Skipping ILP.")
        return [(para_idx, None) for para_idx in paragraphs_to_assign]

    logger.debug(f"Starting ILP optimization for {len(paragraphs_to_assign)} paragraphs.")

    # Map metadata index to a unique image ID
    all_meta_indices = sorted(list(set(
        # Unpack all three elements, but only use idx
        idx for candidates in candidate_scores.values()
        for idx, bge_score, clip_score in candidates if 0 <= idx < len(filtered_metadata)
    )))
    meta_idx_to_img_id = {meta_idx: i for i, meta_idx in enumerate(all_meta_indices)}
    num_images = len(all_meta_indices)
    null_img_id = num_images

    if num_images == 0 and paragraphs_to_assign:
        logger.warning("No valid candidates for ILP. Assigning null.")
        return [(para_idx, None) for para_idx in paragraphs_to_assign]
    if not paragraphs_to_assign: return []

    solver = pywraplp.Solver.CreateSolver('SCIP')
    if not solver: logger.error("Could not create SCIP solver."); return [(p, None) for p in paragraphs_to_assign]

    x = {}
    # 预先为所有段落创建空分配变量
    for para_idx in paragraphs_to_assign:
        x[para_idx, null_img_id] = solver.IntVar(0, 1, f'x_{para_idx}_null')

    # 为有候选的段落创建实际图像分配变量
    for para_idx in paragraphs_to_assign:
        if para_idx in candidate_scores:
            # Use bge_score (index 1) for ILP objective
            for meta_idx, bge_score, _ in candidate_scores[para_idx]: # Ignore clip_score here
                if meta_idx in meta_idx_to_img_id:
                    img_id = meta_idx_to_img_id[meta_idx]
                    if (para_idx, img_id) not in x: # 确保不重复创建
                        x[para_idx, img_id] = solver.IntVar(0, 1, f'x_{para_idx}_{img_id}')

    # Constraints: Each paragraph gets one assignment
    for para_idx in paragraphs_to_assign:
        possible = [x[para_idx, null_img_id]]
        if para_idx in candidate_scores:
             # Use bge_score (index 1) for ILP objective
             possible.extend(x[para_idx, meta_idx_to_img_id[mid]] for mid, _, _ in candidate_scores[para_idx] if mid in meta_idx_to_img_id and (para_idx, meta_idx_to_img_id[mid]) in x)
        if len(possible) > 0 : solver.Add(solver.Sum(possible) == 1)
        else: logger.warning(f"Para {para_idx} has no assignment variables. Forcing null."); solver.Add(x[para_idx, null_img_id] == 1)

    # Constraints: Image usage
    is_used = {img_id: solver.BoolVar(f'used_{img_id}') for img_id in range(num_images)}
    overuse = {img_id: solver.BoolVar(f'over_{img_id}') for img_id in range(num_images)} if max_usage_per_image < len(paragraphs_to_assign) else {}
    for img_id in range(num_images):
        assigns = [x[p, img_id] for p in paragraphs_to_assign if (p, img_id) in x]
        if not assigns: continue
        total = solver.Sum(assigns)
        solver.Add(total <= len(paragraphs_to_assign) * is_used[img_id])
        solver.Add(is_used[img_id] <= total)
        if max_usage_per_image == 1: solver.Add(total <= 1)
        elif img_id in overuse: solver.Add(total <= max_usage_per_image + len(paragraphs_to_assign) * overuse[img_id])

    # Constraints: Reuse distance
    if max_usage_per_image > 1 and min_distance_between_reuse > 0:
         sorted_paras = sorted(paragraphs_to_assign)
         for img_id in range(num_images):
             for i in range(len(sorted_paras) - min_distance_between_reuse):
                 win_vars = [x[p, img_id] for p in sorted_paras[i:i+min_distance_between_reuse+1] if (p, img_id) in x]
                 if len(win_vars) > 1: solver.Add(solver.Sum(win_vars) <= 1)

    # Objective
    objective_terms = []
    for para_idx in paragraphs_to_assign:
        if para_idx in candidate_scores:
             # Use bge_score (index 1) for ILP objective
             for meta_idx, bge_score, _ in candidate_scores[para_idx]: # Ignore clip_score here
                 if meta_idx in meta_idx_to_img_id:
                      img_id = meta_idx_to_img_id[meta_idx]
                      if (para_idx, img_id) in x: objective_terms.append(bge_score * x[para_idx, img_id])
        if (para_idx, null_img_id) in x: objective_terms.append(null_image_reward * x[para_idx, null_img_id])
    if overuse: objective_terms.extend([-REPEAT_PENALTY * 3.0 * overuse[img_id] for img_id in overuse])
    if prioritize_unused and num_images > 0:
         valid_is_used = [is_used[i] for i in range(num_images) if i in is_used]
         if valid_is_used: objective_terms.append(0.1 * solver.Sum(valid_is_used))

    # Objective: Diversity Penalty (修正嵌入顺序)
    if image_bge_embeddings is not None and len(all_meta_indices) > 1 and image_bge_embeddings.shape[0] >= len(all_meta_indices):
        try:
            # 根据 all_meta_indices 的顺序选择并重新排序嵌入
            # all_meta_indices 包含的是 filtered_metadata 中的索引
            embeddings_for_ilp = image_bge_embeddings[all_meta_indices]
            if embeddings_for_ilp.shape[0] != num_images: # Sanity check
                raise ValueError(f"Shape mismatch: embeddings_for_ilp ({embeddings_for_ilp.shape[0]}) vs num_images ({num_images})")
                
            norms = np.linalg.norm(embeddings_for_ilp, axis=1, keepdims=True)
            normalized_embeddings = embeddings_for_ilp / np.where(norms == 0, 1e-6, norms)
            dot_products = normalized_embeddings @ normalized_embeddings.T
            
            # i, j 现在对应于 img_id (0 到 num_images-1)，这与 embeddings_for_ilp 的行索引一致
            for i in range(num_images):
                for j in range(i + 1, num_images):
                    # 使用 is_used[i] 和 is_used[j]，因为 i, j 是 img_id
                    if i in is_used and j in is_used and dot_products[i, j] > 0.7:
                        y_ij = solver.BoolVar(f'y_{i}_{j}')
                        solver.Add(y_ij <= is_used[i]); solver.Add(y_ij <= is_used[j]); solver.Add(y_ij >= is_used[i] + is_used[j] - 1)
                        # 使用 dot_products[i, j]
                        objective_terms.append(-(0.05 * dot_products[i, j]) * y_ij)
        except Exception as div_err: logger.error(f"Diversity penalty setup error: {div_err}")

    if not objective_terms: logger.warning("ILP objective empty."); return [(p, None) for p in paragraphs_to_assign]
    solver.Maximize(solver.Sum(objective_terms))
    status = solver.Solve()

    assignments = []
    if status == pywraplp.Solver.OPTIMAL or status == pywraplp.Solver.FEASIBLE:
        logger.debug(f"ILP solved. Status: {status}. Objective: {solver.Objective().Value():.4f}")
        img_id_to_meta_idx = {v: k for k, v in meta_idx_to_img_id.items()}
        for para_idx in paragraphs_to_assign:
            assign_path = None
            if x[para_idx, null_img_id].solution_value() > 0.5: assign_path = None
            else:
                 for img_id in range(num_images):
                     if (para_idx, img_id) in x and x[para_idx, img_id].solution_value() > 0.5:
                         meta_idx = img_id_to_meta_idx[img_id]
                         if 0 <= meta_idx < len(filtered_metadata):
                             assign_path = filtered_metadata[meta_idx].get('filepath')
                         break
            assignments.append((para_idx, assign_path))
    else:
        logger.error(f"ILP solver failed. Status: {status}")
        assignments = [(p, None) for p in paragraphs_to_assign]

    return assignments
