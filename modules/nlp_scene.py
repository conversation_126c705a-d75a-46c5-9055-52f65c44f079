# modules/nlp_scene.py
from transformers import pipeline
import torch
import logging
import os # Added for log rotation path
from logging.handlers import RotatingFileHandler # Added for log rotation
from sentence_transformers import SentenceTransformer, util, CrossEncoder # Added CrossEncoder
import spacy # Added for NER
import subprocess # Added for conda run
import json # Added for conda run IO
from typing import Dict, List, Tuple, Optional, Any

from config import LOG_DIR

# --- Scene Type Classification Logic ---
CONCEPT_VERBS = {
    "believe", "think", "suppose", "hope", "imagine",
    "know", "remember", "forget", "realize", "mean",
    "claim", "suggest", "report", "feel", "perceive",
    "seem", "appear", "want", "need", "require",
    "involve", "risk", "evaluate"
}

# Added COPULAS set to filter out non-action verbs
COPULAS = {"be", "is", "are", "am", "was", "were", "'s", "'s"}

def _is_concrete_verb(v: str) -> bool:
    return bool(v) and v.lower() not in CONCEPT_VERBS and v.lower() not in COPULAS

def classify_scene_type(scene_graph: dict, gore_flag: bool) -> str:
    """
    根据场景图内容和 gore 标记确定场景类型。
    concrete 条件：
      1) gore_flag 为真
      2) 或者 具备『具体动词』(非 CONCEPT_VERBS 且非 COPULAS)
      3) 或者 具备『地点实体』(非空字符串)
      4) 特殊情况: 如果动词和地点都为空，且 gore_flag 为假，则强制为 abstract
    其余归为 abstract
    """
    if gore_flag:
        return "concrete"

    verbs = [v for v in scene_graph.get("verbs", []) if _is_concrete_verb(v)]
    # Handle potential non-string items in places before checking, filter empty/whitespace strings and "unspecified_location"
    places = [
        p for p in scene_graph.get("places", [])
        if isinstance(p, str) and p.strip() and p != "unspecified_location"
    ]

    # Refined condition: Force abstract if BOTH verbs and places are empty (and gore is false)
    if not verbs and not places:
        # logger.debug("Classify Scene: No concrete verbs or specific places found, forcing abstract.") # Optional logging
        return "abstract"

    # Main condition: concrete if concrete verbs OR specific places exist
    if verbs or places:
        # logger.debug(f"Classify Scene: Concrete verbs ({bool(verbs)}) or specific places ({bool(places)}) found, classifying as concrete.") # Optional logging
        return "concrete"

    # logger.debug("Classify Scene: Defaulting to abstract.") # Optional logging
    return "abstract"
# ---------------------------------------

class DefaultSettings:
    # Add default values for settings used in this module
    log_dir = LOG_DIR
    grounding_log_filename = "grounding_failures.log"
    grounding_log_max_bytes = 5_000_000
    grounding_log_backup_count = 3
    GROUNDING_MIN_SCORE = 0.70 # Default grounding score
settings = DefaultSettings()

from modules.langchain_interface import call_llm_json_response
from modules.prompts_image import image_prompts
from modules.gpt_parameters import LLM_PROVIDER_DEEPSEEK, MODEL_KEY_ARK_DEEPSEEK_R1

logger = logging.getLogger(__name__)

# --- Grounding Failure Log Setup ---
log_dir = getattr(settings, 'log_dir', '.')
grounding_log_file = os.path.join(log_dir, getattr(settings, 'grounding_log_filename', 'grounding_failures.log'))
os.makedirs(log_dir, exist_ok=True)
max_bytes = getattr(settings, 'grounding_log_max_bytes', 5_000_000)
backup_count = getattr(settings, 'grounding_log_backup_count', 3)

# Create a specific logger for grounding failures to avoid interfering with main logger
grounding_logger = logging.getLogger('GroundingFailures')
grounding_logger.setLevel(logging.WARNING) # Or desired level
grounding_logger.propagate = False # Prevent double logging to root logger if configured

# Add the rotating file handler
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
rhandler = RotatingFileHandler(grounding_log_file, maxBytes=max_bytes, backupCount=backup_count)
rhandler.setFormatter(formatter)
grounding_logger.addHandler(rhandler)
# ------------------------------------

# --- Model Loading ---
# Load models once when the module is imported to avoid repeated loading.
# SRL_PIPE = None # REMOVED SRL_PIPE
# SRL_MODEL_NAME = "liaad/srl-en_xlmr-large" # REMOVED SRL_MODEL_NAME
EMBEDDING_MODEL = None
EMBEDDING_DEVICE = 'cpu'
SPACY_NLP = None # Added global for spaCy model
CROSS_RERANKER = None # Added global for CrossEncoder


def load_models():
    """Loads the required NLP models (excluding SRL)."""
    global EMBEDDING_MODEL, EMBEDDING_DEVICE, SPACY_NLP, CROSS_RERANKER # REMOVED SRL_PIPE

    # REMOVED SRL_PIPE loading logic
    # if SRL_PIPE is None:
    #     ... (all SRL loading code removed) ...

    # Load Sentence Transformer
    if EMBEDDING_MODEL is None:
        try:
            # Use paraphrase-MiniLM-L6-v2 and specify cache folder
            model_name = 'paraphrase-MiniLM-L6-v2'
            cache_dir = os.path.expanduser('~/.cache/sbert') # Default cache loc
            os.makedirs(cache_dir, exist_ok=True)
            logger.info(f"正在加载 SentenceTransformer 模型 ({model_name}) from cache: {cache_dir}...")
            if torch.cuda.is_available():
                EMBEDDING_DEVICE = 'cuda'
            # Check MPS availability more robustly
            elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
                 EMBEDDING_DEVICE = 'mps'
            else:
                 EMBEDDING_DEVICE = 'cpu'
            # SentenceTransformer handles cache_folder arg gracefully if version is sufficient
            EMBEDDING_MODEL = SentenceTransformer(model_name, device=EMBEDDING_DEVICE, cache_folder=cache_dir)
            logger.info(f"SentenceTransformer 模型加载完成，使用设备: {EMBEDDING_DEVICE}")
        except Exception as e:
            logger.error(f"加载 SentenceTransformer 模型失败: {e}", exc_info=True)
            EMBEDDING_MODEL = None

    # Load spaCy Model
    if SPACY_NLP is None:
        try:
            # Try loading a small English model
            spacy_model_name = "en_core_web_sm"
            logger.info(f"Loading spaCy model ({spacy_model_name})...")
            # Disable unnecessary pipes for speed if only using NER
            SPACY_NLP = spacy.load(spacy_model_name)
            logger.info("spaCy model loaded successfully.")
        except OSError:
            logger.error(f"Could not find spaCy model '{spacy_model_name}'. Please download it (python -m spacy download {spacy_model_name})")
            SPACY_NLP = None # Ensure it's None if loading failed
        except Exception as e:
            logger.error(f"加载 spaCy 模型失败: {e}", exc_info=True)
            SPACY_NLP = None

    # Load CrossEncoder Model
    if CROSS_RERANKER is None:
        try:
            model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"
            logger.info(f"Loading CrossEncoder model ({model_name})...")
            # CrossEncoder automatically uses GPU/MPS if available
            CROSS_RERANKER = CrossEncoder(model_name)
            logger.info("CrossEncoder model loaded successfully.")
        except Exception as e:
            logger.error(f"加载 CrossEncoder 模型失败: {e}", exc_info=True)
            CROSS_RERANKER = None

# --- Helper Functions ---

def run_srl_predictor(sentence: str) -> dict:
    """
    调用隔离的 srl_env 环境中 modules/run_srl_predictor.py 来做 SRL，
    返回 AllenNLP Predictor 的原始 JSON 输出。
    """
    script_path = os.path.normpath(
        os.path.join(os.path.dirname(__file__), "..", "modules", "run_srl_predictor.py")
    )
    # Ensure the environment name and script path are correct for your setup
    # Use the absolute path to the Python interpreter in the specific conda env
    cmd = ["/Users/<USER>/miniconda3/envs/srl_env/bin/python", script_path]
    logger.debug(f"Running SRL predictor command: {' '.join(cmd)}")
    try:
        proc = subprocess.run(
            cmd,
            input=json.dumps({"sentence": sentence}), # Pass sentence as JSON input
            text=True,          # Ensure input/output are strings
            capture_output=True, # Capture stdout/stderr
            check=False          # Don't raise exception on non-zero exit, handle manually
        )

        # Log stderr regardless of return code for debugging
        # if proc.stderr:
        #      logger.debug(f"SRL predictor stderr: {proc.stderr.strip()}")

        if proc.returncode != 0:
            logger.error(f"SRL predictor 调用失败 (retcode={proc.returncode}): {proc.stderr.strip()}")
            return {}

        # Log raw stdout for debugging
        #logger.debug(f"SRL predictor raw stdout: {proc.stdout.strip()}")

        # Handle potential empty output before JSON parsing
        if not proc.stdout.strip():
             logger.warning("SRL predictor returned empty output.")
             return {}

        # Parse the JSON output from stdout
        return json.loads(proc.stdout)

    except FileNotFoundError:
        logger.error(f"命令 'conda' 未找到。请确保 Conda 已安装并在 PATH 中。")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"解析 SRL predictor 输出失败: {e} - Raw output: '{proc.stdout[:200]}...'")
        return {}
    except Exception as e:
        logger.error(f"运行 SRL predictor 时发生意外错误: {e}", exc_info=True)
        return {}

def decode_bio_to_spans(tokens):
    """
    Decodes BIO-tagged tokens into entity spans.
    Handles potential issues like missing keys or non-string tags/words.
    """
    spans, current = [], None
    for tok in tokens:
        # Safely get tag and word, defaulting to avoid errors
        tag = tok.get("entity", "O") # Default to 'O' if missing
        word = tok.get("word", "")   # Default to empty string if missing

        # Ensure tag is a string
        if not isinstance(tag, str):
             tag = "O" # Treat non-string tags as 'O'
        # Ensure word is a string (handles subword tokens represented differently)
        if not isinstance(word, str):
             word = str(word)

        if tag.startswith("B-"): # Begin a new span
            if current:
                spans.append(current) # Save the previous span
            current = {"type": tag[2:], "text": [word]}
        elif tag.startswith("I-") and current and tag[2:] == current["type"]: # Inside a span
            current["text"].append(word)
        else: # Outside a span (O tag) or continuation of a different type
            if current:
                spans.append(current) # Save the previous span
            current = None # Reset current span

    # Add the last span if it exists
    if current:
        spans.append(current)

    # Combine text lists into strings, handling subword markers (like ##)
    for span in spans:
        span["text"] = " ".join(span["text"]).replace(" ##", "")

    logger.debug(f"Decoded BIO spans: {spans}")
    return spans

# --- Main Functions ---

def extract_scene_graph(text: str) -> dict:
    """
    Run AllenNLP SRL via conda run, decode BIO tags into verb + ARG0/ARG1/TMP spans, plus NER places.
    """
    # REMOVED Check for SRL_PIPE
    # if SRL_PIPE is None:
    #    logger.error("SRL pipeline not loaded.")
    #    return {"verbs": [], "places": [], "objects": [], "arg0": [], "arg1": [], "tmp": [], "timeframe": ""}


    # --- Start Fix: Add input validation ---
    if not isinstance(text, str):
        logger.warning(f"Input text is not a string: {type(text)}. Skipping SRL and NER.")
        return {"verbs": [], "places": [], "objects": [], "arg0": [], "arg1": [], "tmp": [], "timeframe": ""}

    if not text.strip(): # Check if text is empty or whitespace only
        logger.debug("SRL parsing skipped: Input text is empty or whitespace.")
        # Return default structure for empty input
        return {"verbs": [], "places": [], "objects": [], "arg0": [], "arg1": [], "tmp": [], "timeframe": ""}
    # --- End Fix ---

    # Initialize result structure
    graph = {"verbs": [], "places": [], "objects": [], "arg0": [], "arg1": [], "tmp": [], "timeframe": ""}

    try:
        # 1. SRL using conda run AllenNLP predictor
        # 调用 conda run 版本的 AllenNLP Predictor
        srl_output = run_srl_predictor(text)
        if not srl_output: # Check if predictor failed or returned empty
             logger.warning(f"SRL predictor returned no output for text: '{text[:50]}...'")
             # Proceed without SRL data, NER will still run
             raw_entities = []
        else:
            raw_entities = []
            words = srl_output.get("words", [])
            verbs_data = srl_output.get("verbs", []) # AllenNLP predictor format

            if verbs_data: # Check if any verbs were found
                # Process the first verb's tags as per the example logic
                # You might need to adapt this if multiple verbs or different structures are expected
                tags = verbs_data[0].get("tags", [])
                if len(words) == len(tags):
                    for word, tag in zip(words, tags):
                        raw_entities.append({"word": word, "entity": tag})
                else:
                    logger.warning(f"SRL output word/tag count mismatch: {len(words)} words, {len(tags)} tags. Skipping BIO decoding for this verb.")
            else:
                 logger.debug(f"SRL predictor found no verbs in text: '{text[:50]}...'")

        # 2. Decode BIO tags into spans (remains the same)
        srl_spans = decode_bio_to_spans(raw_entities)

        # 3. Populate graph components from decoded SRL spans
        verbs = [span["text"] for span in srl_spans if span["type"] in ("V", "PRED")] # Accept V or PRED
        arg0 = [span["text"] for span in srl_spans if span["type"] == "ARG0"]
        arg1 = [span["text"] for span in srl_spans if span["type"] == "ARG1"]
        tmp = [span["text"] for span in srl_spans if span["type"] == "ARGM-TMP"]

        graph["verbs"] = verbs
        graph["arg0"] = arg0
        graph["arg1"] = arg1
        graph["tmp"] = tmp
        logger.debug(f"Extracted SRL components - Verbs: {verbs}, Arg0: {arg0}, Arg1: {arg1}, Tmp: {tmp}")

        # 4. Named Entity Recognition (NER) for Places and Objects using spaCy
        places = []
        objects = [] # Initialize objects list
        if SPACY_NLP is not None:
            try:
                doc = SPACY_NLP(text)
                # Extract Places (GPE, LOC, FAC, ORG, NORP)
                places = [ent.text for ent in doc.ents if ent.label_ in {"GPE", "LOC", "FAC", "ORG", "NORP"}]
                # Extract Potential Objects (NOUN, PROPN, potentially filtered further)
                objects = [token.text for token in doc if token.pos_ in ["NOUN", "PROPN"] and token.text.lower() not in ["i", "you", "he", "she", "it", "we", "they"]] # Simple object extraction
                logger.debug(f"spaCy NER extracted places: {places}, objects: {objects}")
            except Exception as ner_err:
                logger.error(f"spaCy NER processing failed: {ner_err}", exc_info=True)
        else:
            logger.warning("spaCy model not loaded, cannot extract places/objects using NER.")
            # Fallback to old heuristic (optional, could also just leave places empty)
            # places = [w for w in " ".join(tmp).split() if w.istitle()]

        # Structure consistent with hybrid graph expectations
        return {
            "verbs": verbs,
            "places": places,
            "objects": objects, # Added objects
            "arg0": arg0,
            "arg1": arg1,
            "tmp": tmp,
            "timeframe": " ".join(tmp) # Use tmp as timeframe for consistency
        }
    except Exception as e:
        logger.error(f"Error during SRL processing: {e}", exc_info=True)
        # Allow proceeding to NER even if SRL fails

    # 4. Named Entity Recognition (NER) for Places and Objects using spaCy
    # ... existing code ...
    return graph

def hybrid_scene_graph(text: str) -> dict:
    """
    Generates a scene graph by combining local SRL/NER results with an LLM fallback.

    1) Runs local SRL/NER (extract_scene_graph).
    2) If verbs or places are missing, calls LLM (extract_scene_graph_llm) as a fallback.
    3) Merges results, prioritizing local extraction but complementing with LLM data.

    Returns:
        dict: A scene graph with keys: "verbs", "places", "objects", "timeframe".
    """
    srl_graph = extract_scene_graph(text)
    llm_graph = {}

    # Check if local extraction is insufficient (e.g., missing BOTH verbs and places)
    if not srl_graph.get("verbs") and not srl_graph.get("places"): # Modified: Only fallback if BOTH are missing
        logger.warning("Local SRL/NER insufficient (missing both verbs and places). Falling back to LLM.")
        # raise Exception("Local SRL/NER insufficient (missing verbs or places). ") # Line 329 - Commented out to allow LLM fallback
        try:
            llm_graph = call_llm_json_response(
                api_function="extract_scene_graph_llm",
                prompt_data={"text": text},
                expected_fields=["verbs", "places"], # Core fields needed
                using_cache=True # Don't cache fallback? Or cache aggressively? Decide based on usage.
            )
            # Ensure fallback response has expected structure
            llm_graph.setdefault("verbs", [])
            llm_graph.setdefault("places", [])
            llm_graph.setdefault("objects", [])
            llm_graph.setdefault("timeframe", "")
            logger.info("LLM fallback successful.")
        except Exception as llm_err:
            logger.error(f"LLM scene graph fallback failed: {llm_err}", exc_info=True)
            # Keep llm_graph empty if error occurs

    # Merge results: Union of lists, prioritize SRL for timeframe/objects if available
    # Use sets for unique elements in lists
    combined_verbs = list(set(srl_graph.get("verbs", []) + llm_graph.get("verbs", [])))
    # Filter verbs using _is_concrete_verb to remove copulas and concept verbs
    filtered_verbs = [v for v in combined_verbs if _is_concrete_verb(v)]
    
    merged_graph = {
        "verbs": filtered_verbs,
        "places": list(set(srl_graph.get("places", []) + llm_graph.get("places", []))),
        "objects": list(set(srl_graph.get("objects", []) + llm_graph.get("objects", []))),
        # Prioritize local timeframe if available, else use LLM's
        "timeframe": srl_graph.get("timeframe") or llm_graph.get("timeframe", "")
    }

    #logger.debug(f"Hybrid Scene Graph: {merged_graph}")
    return merged_graph

# --- REMOVED classify_scene_type definitions below this point --- #

# --- Old classify_scene_type function (commented out or removed) ---
# def classify_scene_type(scene_graph: dict, gore_flag: bool) -> str:
#     """
#     根据场景图内容和 gore 标记确定场景类型。
#     """
#     if gore_flag:
#         return "concrete"
#     # If there are any meaningful verbs or places identified, classify as concrete
#     if scene_graph.get("verbs") or scene_graph.get("places"):
#         return "concrete"
#     return "abstract"
# --- End old function ---

def embedding_cosine(text1: str, text2: str) -> float:
    """
    (DEPRECATED - Use cross_encoder_score for grounding)
    """
    if EMBEDDING_MODEL is None:
        logger.error("错误：Embedding 模型未成功加载，无法计算余弦相似度。返回 0.0")
        return 0.0
    if not text1 or not text2:
        logger.debug("无法计算余弦相似度，因为一个或两个文本为空。")
        return 0.0 # Avoid errors with empty strings

    try:
        # Encode texts
        embeddings = EMBEDDING_MODEL.encode([text1, text2], convert_to_tensor=True, device=EMBEDDING_DEVICE)
        # Calculate cosine similarity
        cosine_score = util.pytorch_cos_sim(embeddings[0], embeddings[1])[0][0].item()
        return cosine_score
    except Exception as e:
        logger.error(f"计算嵌入余弦相似度时出错: {e}", exc_info=True)
        return 0.0

def cross_encoder_score(prompt_text: str, original_text: str) -> float:
    """
    Calculates the relevance score between a generated prompt/text and the original text
    using a CrossEncoder model. Higher scores indicate higher relevance.
    """
    if CROSS_RERANKER is None:
        logger.error("错误：CrossEncoder 模型未成功加载，无法计算相关性分数。返回 0.0")
        return 0.0
    if not prompt_text or not original_text:
        logger.debug("无法计算 CrossEncoder 分数，因为一个或两个文本为空。")
        return 0.0

    try:
        # predict returns a NumPy array or Tensor, convert to float
        score = float(CROSS_RERANKER.predict([(prompt_text, original_text)], convert_to_numpy=True)[0])
        # logger.debug(f"CrossEncoder Score: {score:.4f} for Prompt='{prompt_text[:50]}...' vs Original='{original_text[:50]}...'")
        return score
    except Exception as e:
        logger.error(f"计算 CrossEncoder 分数时出错: {e}", exc_info=True)
        return 0.0

def log_grounding_failure(segment_id, rank, score: float, original_text: str, generated_text: str, mismatch_report: dict = None):
    """记录 Grounding Check 失败事件 using rotating log file, including score and mismatch info."""
    log_message = (
        f"GROUNDING_CHECK_FAILURE: "
        f"SegmentID={segment_id}, Rank={rank}, Score={score:.4f}, "
        f"OriginalText='{original_text[:100].replace(chr(10), ' ')}...', "
        f"GeneratedText='{generated_text[:100].replace(chr(10), ' ')}...'"
    )
    if mismatch_report:
        log_message += f", Mismatch={mismatch_report}"

    # Use the dedicated grounding logger with rotating file handler
    grounding_logger.warning(log_message)

# --- Initialization ---
# Load models when the module is first imported
# Note: Consider moving load_models() call to application startup for better control
load_models()

# --- Public API Export ---
__all__ = ["hybrid_scene_graph", "cross_encoder_score", "embedding_cosine", "log_grounding_failure", "classify_scene_type", "load_models", "extract_scene_graph", "decode_bio_to_spans", "run_srl_predictor"] # Added run_srl_predictor to exports

# ... existing code ... 