import os
import faiss
import numpy as np
import json
from typing import List, Dict, Optional, Tuple
import logging
from config import logger, EXPECTED_EMBEDDING_DIMENSION

class VectorStore:
    def __init__(self, index_path: str, metadata_path: str):
        """初始化向量存储"""
        self.index_path = index_path
        self.metadata_path = metadata_path
        self.dimension = EXPECTED_EMBEDDING_DIMENSION
        self.index = None
        self.metadata = {}
        self._url_index = {}  # 初始化为空字典
        self._initialize()
    
    def _initialize(self):
        """初始化 Faiss 索引和元数据"""
        # 确保目录存在
        os.makedirs(os.path.dirname(self.index_path), exist_ok=True)
        
        # 加载元数据
        if os.path.exists(self.metadata_path):
            try:
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                logger.info(f"已加载元数据: {self.metadata_path}")
            except Exception as e:
                logger.error(f"加载元数据时出错: {str(e)}")
                self.metadata = {}
                logger.info("创建了新的元数据字典")
        else:
            self.metadata = {}
            logger.info("创建了新的元数据字典")
        
        # 构建 URL 索引
        self._url_index = self._build_url_index()
        
        # 初始化或加载 Faiss 索引
        if os.path.exists(self.index_path):
            try:
                self.index = faiss.read_index(self.index_path)
                if self.index.d != self.dimension:
                    logger.warning(f"现有索引维度 ({self.index.d}) 与期望维度 ({self.dimension}) 不匹配")
                    self.index = faiss.IndexFlatIP(self.dimension)
                    logger.info("创建新的 Faiss 索引")
                else:
                    logger.info(f"已加载现有的 Faiss 索引: {self.index_path}")
            except Exception as e:
                logger.error(f"加载现有索引时出错: {str(e)}")
                self.index = faiss.IndexFlatIP(self.dimension)
                logger.info("创建新的 Faiss 索引")
        else:
            self.index = faiss.IndexFlatIP(self.dimension)
            logger.info("创建了新的 Faiss 索引")
        
        # 修改文件路径索引的构建
        self._filepath_index = {}
        for idx_str, metadata in self.metadata.items():
            if 'filepath' in metadata:
                # 同时存储完整路径和相对路径
                full_path = os.path.normpath(metadata['filepath'])
                relative_path = os.path.normpath(os.path.relpath(full_path, '/home/<USER>/ai-video'))
                self._filepath_index[full_path] = int(idx_str)
                self._filepath_index[relative_path] = int(idx_str)
                
        logger.info(f"已构建文件路径索引，包含 {len(self._filepath_index)} 个路径")
    
    def _build_url_index(self) -> Dict[str, int]:
        """构建 URL 到索引的映射"""
        url_index = {}
        for idx_str, metadata in self.metadata.items():
            if 'url' in metadata:
                # 将字符串索引转换为整数
                url_index[metadata['url']] = int(idx_str)
        logger.info(f"已构建URL索引，包含 {len(url_index)} 个URL")
        return url_index
    
    def url_exists(self, url: str) -> bool:
        """检查 URL 是否已存在于数据库中"""
        exists = url in self._url_index
        # if exists:
        #     logger.debug(f"URL已存在: {url}")
        return exists
    
    def add_embeddings(self, embeddings: List[np.ndarray], metadata_list: List[Dict]) -> int:
        """添加 embeddings 到索引，返回实际添加的数量"""
        if not embeddings or not metadata_list:
            return 0
        
        # 转换为 numpy 数组并归一化
        embeddings_array = np.array(embeddings).astype('float32')
        faiss.normalize_L2(embeddings_array)
        
        # 添加到索引
        self.index.add(embeddings_array)
        
        # 更新元数据和 URL 索引
        start_idx = len(self.metadata)
        added_count = 0
        
        for i, meta in enumerate(metadata_list):
            idx = str(start_idx + i)
            if 'url' in meta and not self.url_exists(meta['url']):
                self.metadata[idx] = meta
                self._url_index[meta['url']] = start_idx + i
                added_count += 1
        
        # 保存更新
        self.save()
        logger.info(f"添加了 {added_count} 个新的 embeddings")
        return added_count
    
    def search(self, query_embedding: np.ndarray, k: int = 5) -> List[Tuple[int, float, Dict]]:
        """搜索最相似的向量"""
        query_embedding = query_embedding.astype('float32').reshape(1, -1)
        faiss.normalize_L2(query_embedding)
        
        scores, indices = self.index.search(query_embedding, k)
        
        results = []
        for idx, score in zip(indices[0], scores[0]):
            if idx != -1:
                # 使用字符串键访问 metadata
                metadata = self.metadata.get(str(idx), {})
                results.append((idx, float(score), metadata))
        
        return results
    
    def save(self):
        """保存索引和元数据到磁盘"""
        try:
            # 确保索引目录存在
            index_dir = os.path.dirname(self.index_path)
            os.makedirs(index_dir, exist_ok=True)
            logger.debug(f"确保索引目录存在: {index_dir}")
            
            # 保存 Faiss 索引
            faiss.write_index(self.index, self.index_path)
            logger.info(f"成功保存索引到: {self.index_path}")
            
            # 确保元数据目录存在
            metadata_dir = os.path.dirname(self.metadata_path)
            os.makedirs(metadata_dir, exist_ok=True)
            logger.debug(f"确保元数据目录存在: {metadata_dir}")
            
            # 保存元数据
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            logger.info(f"成功保存元数据到: {self.metadata_path}")
            
        except Exception as e:
            logger.error(f"保存失败: {str(e)}")
            raise
    
    def get_total_vectors(self) -> int:
        """获取索引中的总向量数"""
        return self.index.ntotal 
    
    def get_all_metadata(self) -> List[Dict]:
        """获取所有元数据的列表
        
        Returns:
            List[Dict]: 按索引顺序排列的元数据列表
        """
        # 将字符串键转换为整数并排序
        sorted_keys = sorted([int(k) for k in self.metadata.keys()])
        # 返回排序后的元数据列表
        return [self.metadata[str(k)] for k in sorted_keys]
        
    def get_embedding(self, idx: int) -> np.ndarray:
        """获取指定索引的embedding向量
        
        Args:
            idx: 向量索引
            
        Returns:
            np.ndarray: embedding向量
        """
        if idx >= self.index.ntotal:
            raise ValueError(f"索引 {idx} 超出范围")
            
        # 构造一个只包含目标索引的查询
        k = 1
        D, I = self.index.search(np.zeros((1, self.dimension), dtype='float32'), k)
        return self.index.reconstruct(idx)
        
    def get_metadata(self, idx: int) -> Dict:
        """获取指定索引的元数据
        
        Args:
            idx: 元数据索引
            
        Returns:
            Dict: 元数据字典
        """
        return self.metadata.get(str(idx), {}) 
    
    def get_metadata_by_url(self, url: str) -> Dict:
        """通过 URL 获取 metadata"""
        # 使用 _url_index 来获取索引
        if url in self._url_index:
            idx = str(self._url_index[url])
            return self.metadata.get(idx, {})
        return {}
    
    def update_metadata_by_url(self, url: str, new_metadata: Dict):
        """通过 URL 更新 metadata"""
        if url in self._url_index:
            idx = str(self._url_index[url])
            old_metadata = self.metadata.get(idx, {})
            
            # 如果存在旧的文件路径，从索引中移除
            if 'filepath' in old_metadata:
                old_path = os.path.normpath(old_metadata['filepath'])
                if old_path in self._filepath_index:
                    del self._filepath_index[old_path]
            
            # 更新元数据
            self.metadata[idx] = new_metadata
            
            # 添加新的文件路径到索引
            if 'filepath' in new_metadata:
                new_path = os.path.normpath(new_metadata['filepath'])
                self._filepath_index[new_path] = int(idx)
                
                # 同时添加相对路径
                relative_path = os.path.normpath(os.path.relpath(new_path, '/home/<USER>/ai-video'))
                self._filepath_index[relative_path] = int(idx)
            
            return True
        return False 
    
    def get_embedding_by_path(self, filepath: str) -> Optional[np.ndarray]:
        """通过文件路径获取 embedding"""
        # 标准化路径格式
        normalized_filepath = os.path.normpath(filepath)
        
        # 使用已构建的文件路径索引
        if normalized_filepath in self._filepath_index:
            idx = self._filepath_index[normalized_filepath]
            return self.get_embedding(idx)
        
        # 如果在索引中找不到，尝试遍历查找（兼容旧数据）
        for idx_str, metadata in self.metadata.items():
            stored_path = metadata.get('filepath', '')
            if os.path.normpath(stored_path) == normalized_filepath:
                return self.get_embedding(int(idx_str))
        
        logger.warning(f"找不到文件路径对应的embedding: {filepath}")
        # 添加更详细的调试信息
        logger.debug(f"当前存储的文件路径: {[m.get('filepath') for m in self.metadata.values()]}")
        return None
        # 如果找不到匹配的文件路径，返回None
        logger.warning(f"找不到文件路径对应的embedding: {filepath}")
        return None 