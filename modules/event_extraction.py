# modules/event_extraction.py
"""
事件提取模块

主要功能：
1. 从剧本文本中提取事件
2. 使用LLM进行智能事件识别
3. 支持多种剧本格式
"""

import re
from typing import List, Dict, Any, Optional
from config import logger
from modules.langchain_interface import call_llm_json_response


def extract_events(script_text: str) -> List[str]:
    """
    从剧本文本中提取事件列表

    Args:
        script_text: 完整的剧本文本

    Returns:
        List[str]: 事件列表，格式为"<actor><verb><object>"
    """
    try:
        if not script_text or not script_text.strip():
            logger.warning("剧本文本为空")
            return []

        # 首先尝试使用LLM提取
        llm_events = _extract_events_with_llm(script_text)
        if llm_events:
            logger.info(f"LLM成功提取 {len(llm_events)} 个事件")
            return llm_events

        # LLM失败时，直接返回空列表（按照方案要求，不使用正则兜底）
        logger.warning("LLM事件提取失败，返回空列表")
        return []

    except Exception as e:
        logger.error(f"提取事件时出错: {str(e)}")
        return []


def _extract_events_with_llm(script_text: str) -> List[str]:
    """
    使用LLM提取事件

    Args:
        script_text: 剧本文本

    Returns:
        List[str]: 事件列表
    """
    try:
        # 限制输入长度以避免token超限
        max_length = 8000
        if len(script_text) > max_length:
            script_text = script_text[:max_length] + "..."
            logger.info(f"剧本文本过长，截取前{max_length}字符")

        # 构建LLM请求
        request_data = {
            "script": script_text
        }

        # 调用LLM
        response = call_llm_json_response(
            api_function="extract_events",
            prompt_data=request_data,
            using_cache=False  # 不使用缓存以确保实时性
        )

        if not response:
            logger.warning("LLM返回空响应")
            return []

        # 提取事件列表
        events = response.get("events", [])
        if not isinstance(events, list):
            logger.warning("LLM返回的events不是列表格式")
            return []

        # 验证和清理事件
        valid_events = []
        for event in events:
            if isinstance(event, str) and event.strip():
                # 简单验证事件格式
                cleaned_event = event.strip()
                if len(cleaned_event) > 5:  # 至少5个字符
                    valid_events.append(cleaned_event)

        logger.info(f"LLM提取到 {len(valid_events)} 个有效事件")
        return valid_events

    except Exception as e:
        logger.error(f"LLM事件提取时出错: {str(e)}")
        return []


def validate_event_format(event: str) -> bool:
    """
    验证事件格式是否符合要求

    Args:
        event: 事件字符串

    Returns:
        bool: 是否有效
    """
    try:
        if not isinstance(event, str) or not event.strip():
            return False

        # 基本长度检查
        if len(event.strip()) < 5:
            return False

        # 检查是否包含基本的动作元素
        # 这里可以根据需要添加更复杂的验证逻辑
        return True

    except Exception:
        return False


def normalize_event(event: str) -> str:
    """
    标准化事件描述

    Args:
        event: 原始事件描述

    Returns:
        str: 标准化后的事件描述
    """
    try:
        if not event:
            return ""

        # 去除多余空格
        normalized = re.sub(r'\s+', ' ', event.strip())

        # 确保以句号结尾（如果不是标点符号结尾）
        if normalized and not normalized[-1] in '.!?。！？':
            normalized += '。'

        return normalized

    except Exception as e:
        logger.error(f"标准化事件时出错: {str(e)}")
        return event


def extract_character_actions(script_text: str) -> Dict[str, List[str]]:
    """
    提取角色动作映射

    Args:
        script_text: 剧本文本

    Returns:
        Dict[str, List[str]]: 角色名到动作列表的映射
    """
    try:
        character_actions = {}

        # 简单的角色对话提取（可以根据需要扩展）
        dialogue_pattern = r'([^：\n]+)：([^\n]+)'
        matches = re.findall(dialogue_pattern, script_text)

        for character, dialogue in matches:
            character = character.strip()
            if character not in character_actions:
                character_actions[character] = []

            # 从对话中提取动作（简化版本）
            action = f"{character}说话"
            character_actions[character].append(action)

        return character_actions

    except Exception as e:
        logger.error(f"提取角色动作时出错: {str(e)}")
        return {}


def merge_similar_events(events: List[str], similarity_threshold: float = 0.8) -> List[str]:
    """
    合并相似的事件

    Args:
        events: 事件列表
        similarity_threshold: 相似度阈值

    Returns:
        List[str]: 去重后的事件列表
    """
    try:
        if not events:
            return []

        # 简化版本：基于文本长度和关键词进行去重
        unique_events = []

        for event in events:
            is_duplicate = False
            for existing in unique_events:
                # 简单的相似度检查
                if _simple_similarity(event, existing) > similarity_threshold:
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_events.append(event)

        logger.info(f"事件去重：{len(events)} -> {len(unique_events)}")
        return unique_events

    except Exception as e:
        logger.error(f"合并相似事件时出错: {str(e)}")
        return events


def _simple_similarity(text1: str, text2: str) -> float:
    """
    简单的文本相似度计算

    Args:
        text1, text2: 待比较的文本

    Returns:
        float: 相似度分数 (0-1)
    """
    try:
        if not text1 or not text2:
            return 0.0

        # 转换为小写并分词
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        # 计算Jaccard相似度
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0

    except Exception:
        return 0.0


def extract_scene_events(script_text: str) -> List[Dict[str, Any]]:
    """
    按场景提取事件

    Args:
        script_text: 剧本文本

    Returns:
        List[Dict]: 场景事件列表
    """
    try:
        scene_events = []

        # 简单的场景分割
        scene_pattern = r'(场景\s*\d+|Scene\s*\d+|第\d+场)'
        scenes = re.split(scene_pattern, script_text, flags=re.IGNORECASE)

        for i, scene_content in enumerate(scenes):
            if scene_content.strip():
                events = extract_events(scene_content)
                if events:
                    scene_events.append({
                        "scene_number": i + 1,
                        "events": events,
                        "content_length": len(scene_content)
                    })

        return scene_events

    except Exception as e:
        logger.error(f"按场景提取事件时出错: {str(e)}")
        return []
