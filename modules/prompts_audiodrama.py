#modules/prompts_audiodrama.py

audiodrama_system_messages = {
    "generate_audiodrama_structure": (
        "You are a master audio drama architect. Your task is to create a detailed structure that "
        "organizes the story content into a series of interconnected scenes, each driven by its own "
        "conflict. Focus on emotional rhythm, tension patterns, and ensuring each scene has its own "
        "mini-climax while contributing to the larger narrative flow."
    ),
    
    "generate_script_segment": (
        "You are a professional audio drama scriptwriter. Your task is to create engaging script segments "
        "that emphasize emotional waves and continuous tension. Each scene should have its own conflict "
        "and resolution while maintaining smooth transitions and emotional connections with adjacent scenes."
    ),
     
    "refine_audiodrama_script": (
        "You are an expert audio drama script editor. Your task is to enhance the emotional impact and "
        "conflict development of each scene while ensuring smooth transitions between them. Focus on "
        "maintaining consistent tension and emotional engagement throughout the entire drama."
    ),
    
    "merge_script_segments": (
        "You are an expert script editor specializing in audio drama. Your task is to merge multiple "
        "script segments into a cohesive whole, ensuring smooth transitions between scenes and maintaining "
        "the emotional flow throughout the entire drama."
    ),

    "analyze_chapter_conflict": (
        "You are an expert story analyst specializing in audio drama adaptation. Your task is to analyze "
        "chapter content with a focus on identifying core conflicts, character dynamics, and emotional "
        "elements. Pay special attention to the emotional intensity variations and how each conflict "
        "can be effectively presented in audio format."
    ),

    "generate_continuous_conflict_structure": (
        "You are a master audio drama architect. Your task is to create a detailed structure that "
        "organizes the story content into a series of interconnected scenes, each driven by its own "
        "conflict. Focus on emotional rhythm, tension patterns, and ensuring each scene has its own "
        "mini-climax while contributing to the larger narrative flow."
    )
}

audiodrama_prompts = {
    "generate_audiodrama_structure": """
    [ROLE AND OBJECTIVE]
    Create a detailed audio drama structure based on continuous conflict and emotional waves.
    
    [INPUT]
    Story Analysis: {story_analysis}
    Style: {style}
    Language: {language}
    
    [STRUCTURE REQUIREMENTS]
    1. Conflict Design:
       - Each scene must have its own mini-conflict
       - Alternate between emotional and external conflicts
       - Ensure conflicts vary in intensity (low/medium/high)
       - Create natural transitions between conflicts
    
    2. Emotional Rhythm:
       - Design emotional wave patterns across scenes
       - Maintain tension through varying intensities
       - Create emotional connections between scenes
       - Balance high and low emotional moments
    
    3. Scene Integration:
       - Connect scenes through emotional or narrative bridges
       - Ensure each scene advances both its conflict and overall story
       - Create smooth transitions between emotional states
       - Maintain consistent pacing while varying intensity
    
    [RESPONSE FORMAT]
    {{
        "audiodrama_structure": {{
            "estimated_duration": "string",
            "emotional_flow": "string",
            "tension_pattern": "string",
            "scenes": [
                {{
                    "scene_number": "integer",
                    "conflict_type": "emotional/external",
                    "emotional_intensity": "low/medium/high",
                    "estimated_duration": "string",
                    "mini_conflict": {{
                        "setup": "string",
                        "development": "string",
                        "resolution": "string"
                    }},
                    "emotional_elements": {{
                        "starting_mood": "string",
                        "peak_emotion": "string",
                        "ending_mood": "string",
                        "transition_notes": "string"
                    }},
                    "technical_requirements": {{
                        "sound_design": ["string"],
                        "atmosphere_notes": "string",
                        "pacing_notes": "string"
                    }}
                }}
            ],
            "transition_map": [
                {{
                    "from_scene": "integer",
                    "to_scene": "integer",
                    "emotional_bridge": "string",
                    "tension_shift": "string"
                }}
            ]
        }}
    }}
    """,
    
    "generate_script_segment": """
    [ROLE AND OBJECTIVE]
    Create a script segment focusing on conflict development and emotional resonance.
    
    [INPUT]
    Audio Drama Structure: {audiodrama_structure}
    Story Analysis: {story_analysis}
    Scene Number: {scene_number}
    Style: {style}
    Language: {language}
    
    [SCRIPT REQUIREMENTS]
    1. Conflict Development:
       - Clear setup of the scene's mini-conflict
       - Progressive tension building
       - Satisfying conflict resolution
       - Connection to adjacent scenes
    
    2. Emotional Progression:
       - Dynamic emotional shifts
       - Clear emotional peaks and valleys
       - Character emotional responses
       - Mood transitions
    
    3. Technical Elements:
       - Sound design supporting emotional states
       - Pacing variations based on tension
       - Atmospheric elements
       - Transition techniques
    
    [RESPONSE FORMAT]
    {{
        "script_segment": {{
            "scene_number": "integer",
            "conflict_progression": {{
                "setup_phase": {{
                    "duration": "string",
                    "content": "string",
                    "emotional_state": "string"
                }},
                "development_phase": {{
                    "duration": "string",
                    "content": "string",
                    "tension_points": ["string"]
                }},
                "resolution_phase": {{
                    "duration": "string",
                    "content": "string",
                    "emotional_impact": "string"
                }}
            }},
            "shots": [
                {{
                    "shot_number": "integer",
                    "type": "establishing/action/dialogue/emotional",
                    "duration": "string",
                    "content": {{
                        "description": "string",
                        "dialogue": ["string"],
                        "sound_design": ["string"]
                    }},
                    "emotional_notes": {{
                        "intensity": "low/medium/high",
                        "mood": "string",
                        "transition": "string"
                    }}
                }}
            ]
        }}
    }}
    """,
    
    "generate_scene_descriptions": """
    [ROLE AND OBJECTIVE]
    Create detailed scene descriptions that emphasize emotional atmosphere and conflict development.
    
    [INPUT]
    Script: {script}
    Scene Number: {scene_number}
    Style: {style}
    Language: {language}
    
    [DESCRIPTION REQUIREMENTS]
    1. Emotional Atmosphere:
       - Mood progression
       - Tension indicators
       - Character emotional states
       - Environmental impact on emotions
    
    2. Conflict Elements:
       - Conflict setup cues
       - Tension building elements
       - Resolution indicators
       - Transition markers
    
    [RESPONSE FORMAT]
    {{
        "scene_descriptions": [
            {{
                "timestamp": "string",
                "duration": "string",
                "atmosphere": {{
                    "mood": "string",
                    "tension_level": "string",
                    "emotional_cues": ["string"]
                }},
                "conflict_elements": {{
                    "stage": "setup/development/resolution",
                    "intensity": "low/medium/high",
                    "key_moments": ["string"]
                }},
                "technical_notes": {{
                    "sound_design": ["string"],
                    "transition_cues": ["string"]
                }}
            }}
        ]
    }}
    """,

    "analyze_chapter_conflict": """
    [ROLE AND OBJECTIVE]
    Analyze the chapter content to identify core conflicts, character dynamics, and emotional elements suitable for audio drama adaptation.

    [INPUT]
    Chapter Text: {chapter_text}
    Style: {style}
    Language: {language}

    [ANALYSIS REQUIREMENTS]
    1. Core Conflicts:
       - Identify main conflict type (internal/external)
       - Extract conflict progression (setup/development/resolution)
       - Note emotional intensity and impact
       - Identify potential audio drama moments

    2. Character Dynamics:
       - Track character emotional states
       - Note character relationships and interactions
       - Identify character goals and obstacles
       - Mark significant character development points

    3. Theme Elements:
       - Extract key themes and motifs
       - Note emotional color and atmosphere
       - Identify symbolic elements
       - Mark potential audio enhancement points

    [RESPONSE FORMAT]
    {
        "chapter_analysis": {
            "core_conflict": {
                "type": "internal/external",
                "setup": "string",
                "development": "string",
                "resolution": "string",
                "emotional_intensity": "low/medium/high"
            },
            "character_dynamics": [
                {
                    "character": "string",
                    "emotional_state": "string",
                    "goals": ["string"],
                    "obstacles": ["string"],
                    "development": "string"
                }
            ],
            "theme_elements": {
                "main_themes": ["string"],
                "emotional_tone": "string",
                "atmosphere": "string",
                "audio_enhancement_points": ["string"]
            }
        }
    }
    """,

    "generate_continuous_conflict_structure": """
    [ROLE AND OBJECTIVE]
    Create a detailed audio drama structure based on continuous small conflicts and emotional waves.

    [INPUT]
    Chapter Analyses: {chapter_analyses}
    Total Duration: {total_duration}
    Number of Scenes: {num_scenes}
    Style: {style}
    Language: {language}

    [STRUCTURE REQUIREMENTS]
    1. Scene Design:
       - Each scene must have a distinct mini-conflict
       - Alternate between emotional and external conflicts
       - Ensure smooth transitions between scenes
       - Maintain tension through varying intensities

    2. Timing Distribution:
       - Allocate appropriate duration for each scene
       - Account for conflict intensity in timing
       - Balance high and low intensity moments
       - Ensure proper pacing throughout

    3. Emotional Flow:
       - Create emotional wave patterns
       - Build tension progressively
       - Plan emotional peaks and valleys
       - Maintain audience engagement

    [RESPONSE FORMAT]
    {
        "continuous_conflict_structure": {
            "total_duration": "integer (minutes)",
            "num_scenes": "integer",
            "scenes": [
                {
                    "scene_number": "integer",
                    "conflict_type": "emotional/external",
                    "duration": "float (minutes)",
                    "emotional_intensity": "low/medium/high",
                    "emotional_wave": {
                        "intensity": "float (0-1)",
                        "duration": "float (minutes)",
                        "peak_position": "float (0-1)",
                        "transition_type": "gradual/sudden/plateau"
                    },
                    "mini_conflict": {
                        "setup": "string",
                        "development": "string",
                        "resolution": "string"
                    },
                    "shots": [
                        {
                            "shot_number": "integer",
                            "shot_type": "establishing/action/dialogue/emotional",
                            "duration": "float (minutes)",
                            "focus": "string",
                            "mood": "string"
                        }
                    ],
                    "transition": {
                        "to_next_scene": "string",
                        "emotional_bridge": "string"
                    }
                }
            ]
        }
    }
    """,

    "optimize_scene_durations": """
    [ROLE AND OBJECTIVE]
    Optimize scene durations based on conflict intensity and emotional impact.

    [INPUT]
    Structure: {structure}
    Total Duration: {total_duration}
    Style: {style}
    Language: {language}

    [OPTIMIZATION REQUIREMENTS]
    1. Duration Adjustment:
       - Extend high-intensity conflict scenes
       - Compress low-intensity scenes
       - Maintain overall time limit
       - Ensure proper pacing

    2. Transition Timing:
       - Account for scene transitions
       - Adjust for emotional bridges
       - Consider sound design needs
       - Maintain flow continuity

    [RESPONSE FORMAT]
    {
        "optimized_structure": {
            "total_duration": "integer (minutes)",
            "scenes": [
                {
                    "scene_number": "integer",
                    "original_duration": "float (minutes)",
                    "optimized_duration": "float (minutes)",
                    "adjustment_reason": "string",
                    "transition_timing": {
                        "fade_in": "float (seconds)",
                        "fade_out": "float (seconds)",
                        "overlap": "float (seconds)"
                    }
                }
            ],
            "timing_notes": "string"
        }
    }
    """,

    "merge_script_segments": """
    [ROLE AND OBJECTIVE]
    Merge multiple script segments into a cohesive audio drama script.

    [INPUT]
    Script Segments: {script_segments}
    Story Outline: {story_outline}
    Style: {style}
    Language: {language}

    [MERGE REQUIREMENTS]
    1. Narrative Flow:
       - Ensure smooth transitions between segments
       - Maintain story continuity
       - Preserve character consistency
       - Balance emotional progression

    2. Technical Integration:
       - Harmonize sound design elements
       - Adjust timing for transitions
       - Maintain consistent style
       - Optimize pacing

    [RESPONSE FORMAT]
    {
        "merged_script": {
            "scenes": [
                {
                    "scene_number": "integer",
                    "duration": "float (minutes)",
                    "content": {
                        "dialogues": ["string"],
                        "narration": "string",
                        "sound_effects": ["string"]
                    },
                    "transition": {
                        "type": "string",
                        "duration": "float",
                        "description": "string"
                    }
                }
            ],
            "total_duration": "float (minutes)",
            "scene_count": "integer"
        }
    }
    """,

    "review_and_refine_script": """
    [ROLE AND OBJECTIVE]
    Review and refine the audio drama script for quality and coherence.

    [INPUT]
    Script: {script}
    Context: {context}
    Style: {style}
    Language: {language}
    Is Complete: {is_complete}

    [REVIEW ASPECTS]
    1. Content Quality:
       - Narrative coherence
       - Character consistency
       - Emotional impact
       - Dialogue authenticity

    2. Technical Elements:
       - Sound design integration
       - Timing and pacing
       - Transition effectiveness
       - Production feasibility

    [RESPONSE FORMAT]
    {
        "refined_script": {
            "scenes": [
                {
                    "scene_number": "integer",
                    "content": "object",
                    "improvements": ["string"]
                }
            ]
        },
        "improvement_suggestions": [
            {
                "scene_number": "integer",
                "type": "dialogue/narration/transition",
                "content": "object"
            }
        ]
    }
    """
} 