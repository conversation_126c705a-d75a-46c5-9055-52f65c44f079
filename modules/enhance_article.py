# module/enhance_article.py
import logging
from typing import List, Dict, Any, Tuple, Optional
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import nltk
from nltk.tokenize import sent_tokenize
import concurrent.futures

from modules.gpt4_interface import (
    call_gpt_json_response,
    generate_embedding
)
from modules.utils import (
    normalize_language,
    calculate_token_count,
    split_into_chunks,
    clean_text
)
from modules.embedding_management import get_embedding_manager
from modules.prompts_enhancement import (
    enhancement_prompts,
    article_enhancement_prompts
)
from modules.fact_checker import (
    fact_check_script_pipeline,
    extract_entities_and_facts,
    fact_check_and_correct
)

logger = logging.getLogger(__name__)

# Constants
MAX_TOKENS = 1800  # 调整分块长度以优化文本处理
MIN_QUALITY_SCORE = 0.7
MIN_SIMILARITY_SCORE = 0.6
MAX_RETRIES = 3
MIN_CONTENT_RATIO = 0.8

def analyze_semantic_level(text: str) -> Dict[str, Any]:
    """分析文本的语义复杂度"""
    try:
        return call_gpt_json_response(
            "semantic_level_analysis",
            {"text": text[:2000]},
            using_cache=True
        )
    except Exception as e:
        logger.error(f"语义层级分析失败: {str(e)}")
        return None

def predict_focus_points(text: str) -> Dict[str, Any]:
    """预测认知焦点"""
    try:
        return call_gpt_json_response(
            "focus_prediction",
            {"text": text},
            using_cache=True
        )
    except Exception as e:
        logger.error(f"焦点预测失败: {str(e)}")
        return None

def evaluate_reference_quality(reference: str) -> float:
    """评估参考资料的质量"""
    try:
        response = call_gpt_json_response(
            "reference_quality",
            {"text": reference[:1000]},
            using_cache=True
        )
        
        if not response:
            return 0.0
            
        scores = {
            "completeness": response.get("completeness", 0),
            "timeliness": response.get("timeliness", 0),
            "professionalism": response.get("professionalism", 0),
            "objectivity": response.get("objectivity", 0)
        }
        
        weights = {
            "completeness": 0.3,
            "timeliness": 0.2,
            "professionalism": 0.3,
            "objectivity": 0.2
        }
        
        return sum(scores[key] * weights[key] for key in scores)
        
    except Exception as e:
        logger.error(f"参考资料质量评估失败: {str(e)}")
        return 0.0

def filter_references(
    chunk: str,
    references: List[str],
    threshold: float = MIN_QUALITY_SCORE
) -> List[Dict[str, Any]]:
    """筛选高质量的相关参考资料"""
    try:
        filtered_refs = []
        embedding_manager = get_embedding_manager()
        chunk_embedding = embedding_manager.get_cached_embedding(chunk)
        
        for ref in references:
            ref_embedding = embedding_manager.get_cached_embedding(ref)
            similarity = cosine_similarity(
                [chunk_embedding],
                [ref_embedding]
            )[0][0]
            
            quality_score = evaluate_reference_quality(ref)
            final_score = similarity * 0.5 + quality_score * 0.5
            
            if final_score >= threshold:
                filtered_refs.append({
                    "text": ref,
                    "similarity": similarity,
                    "quality": quality_score,
                    "score": final_score
                })
        
        return sorted(filtered_refs, key=lambda x: x["score"], reverse=True)
        
    except Exception as e:
        logger.error(f"参考资料筛选失败: {str(e)}")
        return []

def analyze_enhancement_points(
    chunk: str,
    language: str
) -> List[Dict[str, Any]]:
    """分析文本中需要增强的位置"""
    try:
        response = call_gpt_json_response(
            "analyze_enhancement",
            {
                "text": chunk,
                "language": language
            },
            using_cache=True
        )
        
        return response.get("points", [])
        
    except Exception as e:
        logger.error(f"增强点分析失败: {str(e)}")
        return []

def generate_enhancement_content(
    point: Dict[str, Any],
    references: List[Dict[str, Any]],
    language: str
) -> Optional[str]:
    """为特定增强点生成内容"""
    try:
        relevant_refs = [
            ref["text"] for ref in references
            if ref["similarity"] > MIN_SIMILARITY_SCORE
        ][:2]
        
        if not relevant_refs:
            return None
            
        response = call_gpt_json_response(
            "generate_enhancement",
            {
                "point_type": point["type"],
                "context": point["context"],
                "requirement": point["requirement"],
                "references": relevant_refs,
                "language": language
            },
            using_cache=True
        )
        
        return response.get("content") if response else None
        
    except Exception as e:
        logger.error(f"内容生成失败: {str(e)}")
        return None

def validate_enhancement(
    original: str,
    enhanced: str,
    references: List[str]
) -> Tuple[bool, List[str]]:
    """验证增强内容的质量"""
    try:
        response = call_gpt_json_response(
            "validate_enhancement",
            {
                "original": original,
                "enhanced": enhanced,
                "references": references
            },
            using_cache=True
        )
        
        if not response:
            return False, ["验证失败：无效的响应"]
            
        is_valid = response.get("is_valid", False)
        issues = response.get("issues", [])
        
        scores = response.get("scores", {})
        if any(score < 0.7 for score in scores.values()):
            is_valid = False
            issues.append(f"质量分数不达标: {scores}")
        
        # 新增引用验证：检查返回的 sources 是否为有效链接
        if "sources" in response:
            if any(not str(ref).startswith("http") for ref in response["sources"]):
                is_valid = False
                issues.append("存在未经验证的引用来源")
        
        return is_valid, issues
        
    except Exception as e:
        logger.error(f"质量验证失败: {str(e)}")
        return False, [str(e)]

def enhance_chunk(
    chunk: str,
    references: List[str],
    language: str
) -> Dict[str, Any]:
    """增强单个文本块"""
    try:
        # 1. 分析语义层级
        semantic_analysis = analyze_semantic_level(chunk)
        current_level = semantic_analysis.get("current_level", 3) if semantic_analysis else 3
        
        # 2. 筛选高质量参考资料
        filtered_refs = filter_references(chunk, references)
        
        if not filtered_refs:
            return {"content": chunk, "enhancements": []}
        
        # 3. 分析认知焦点和增强点
        focus_points = predict_focus_points(chunk)
        if not focus_points:
            points = analyze_enhancement_points(chunk, language)
            focus_points = {"focus_points": points} if points else {"focus_points": []}
        
        # 4. 生成增强内容
        enhancements = []
        enhanced_content = chunk
        
        # 对所有增强点按插入位置降序排序，避免位置偏移问题
        points = focus_points.get("focus_points", [])
        sorted_points = sorted(points, key=lambda x: x.get("position", 0), reverse=True)
        for point in sorted_points:
            enhancement = generate_enhancement_content(
                point,
                filtered_refs,
                language
            )
            
            if enhancement:
                position = point.get("position", 0)
                enhanced_content = (
                    enhanced_content[:position] +
                    f"\n{enhancement}\n" +
                    enhanced_content[position:]
                )
                enhancements.append({
                    "type": point.get("type", "unknown"),
                    "content": enhancement
                })
        
        return {
            "content": enhanced_content,
            "enhancements": enhancements,
            "semantic_level": current_level
        }
        
    except Exception as e:
        logger.error(f"文本块增强失败: {str(e)}")
        return {"content": chunk, "enhancements": []}

def enhance_article(
    content: str,
    references: List[str],
    language: str = "zh"
) -> Dict[str, Any]:
    """文章增强主流程"""
    try:
        # 1. 标准化语言代码
        language = normalize_language(language)
        
        # 2. 清理文本
        content = clean_text(content)
        
        # 3. 文本分块
        chunks = split_into_chunks(content, max_tokens=MAX_TOKENS)
        
        if not chunks:
            logger.warning("文本分块失败")
            return {"content": content, "error": "文本分块失败"}
        
        enhanced_chunks = []
        all_enhancements = []
        semantic_levels = []
        
        # 使用线程池并行处理文本块
        def process_chunk(index, chunk, references, language):
            logger.info(f"处理第 {index}/{len(chunks)} 个文本块")
            result = enhance_chunk(chunk, references, language)
            is_valid, issues = validate_enhancement(chunk, result["content"], references)
            if not is_valid:
                logger.warning(f"块 {index} 增强未通过质量验证: {issues}")
                return (index, chunk, [], 3)
            return (index, result["content"], result["enhancements"], result.get("semantic_level", 3))
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(process_chunk, idx, chunk, references, language) for idx, chunk in enumerate(chunks, start=1)]
            results = []
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        
        results.sort(key=lambda x: x[0])
        for idx, enhanced_chunk_text, enhancements, sem_level in results:
            enhanced_chunks.append(enhanced_chunk_text)
            all_enhancements.extend(enhancements)
            semantic_levels.append(sem_level)
        
        # 5. 合并结果
        enhanced_content = "\n\n".join(enhanced_chunks)
        
        # 6. 最终质量检查
        final_valid, final_issues = validate_enhancement(
            content,
            enhanced_content,
            references
        )
        
        if not final_valid:
            logger.error(f"最终内容未通过质量验证: {final_issues}")
            return {
                "content": content,
                "error": final_issues,
                "enhancements": all_enhancements
            }
        
        # 7. 计算平均语义层级
        avg_semantic_level = (
            sum(semantic_levels) / len(semantic_levels)
            if semantic_levels else 3
        )
        
        return {
            "content": enhanced_content,
            "success": True,
            "enhancements": all_enhancements,
            "semantic_level": avg_semantic_level
        }
        
    except Exception as e:
        logger.error(f"文章增强失败: {str(e)}")
        return {"content": content, "error": str(e)}

def enhance_article_with_fact_check(
    content: str,
    references: List[str],
    script_path: str,
    language: str = "zh",
    fact_check_threshold: float = 0.5
) -> Dict[str, Any]:
    """结合事实检查和内容增强的主流程"""
    try:
        # 1. 首先进行事实检查
        logger.info("开始事实检查流程")
        fact_checked_content, theme = fact_check_script_pipeline(
            script_content=content,
            script_path=script_path,
            language=language,
            threshold=fact_check_threshold
        )
        
        if fact_checked_content == content:
            logger.info("事实检查未发现需要修正的内容")
        else:
            logger.info("事实检查完成，内容已更新")
            
        # 2. 基于事实检查后的内容进行增强
        logger.info("开始内容增强流程")
        enhanced_result = enhance_article(
            content=fact_checked_content,
            references=references,
            language=language
        )
        
        # 3. 合并结果
        if enhanced_result.get("error"):
            logger.warning(f"内容增强过程出现问题: {enhanced_result['error']}")
            return {
                "content": fact_checked_content,
                "fact_checked": fact_checked_content != content,
                "enhanced": False,
                "error": enhanced_result["error"]
            }
            
        return {
            "content": enhanced_result["content"],
            "fact_checked": fact_checked_content != content,
            "enhanced": True,
            "enhancements": enhanced_result.get("enhancements", []),
            "semantic_level": enhanced_result.get("semantic_level", 3)
        }
        
    except Exception as e:
        logger.error(f"内容处理失败: {str(e)}")
        return {
            "content": content,
            "error": str(e),
            "fact_checked": False,
            "enhanced": False
        }

if __name__ == "__main__":
    # 示例用法
    test_content = "人工智能正在改变我们的生活。2022年全球AI市场规模达到800亿美元。"
    test_references = [
        "2023年全球AI市场规模达到1500亿美元，预计2025年将突破3000亿美元。",
        "根据最新研究，AI在医疗诊断领域的准确率已达到95%以上。"
    ]
    
    # 测试完整流程
    result = enhance_article_with_fact_check(
        content=test_content,
        references=test_references,
        script_path="test_script.txt",
        language="zh"
    )
    
    print("处理结果:")
    print(f"内容是否经过事实检查: {result['fact_checked']}")
    print(f"内容是否被增强: {result['enhanced']}")
    print(f"语义层级: {result.get('semantic_level', 3)}")
    print("\n最终内容:")
    print(result["content"])
    
    if result.get("enhancements"):
        print("\n增强内容列表:")
        for enhancement in result["enhancements"]:
            print(f"- 类型: {enhancement['type']}")
            print(f"  内容: {enhancement['content']}")
            print() 