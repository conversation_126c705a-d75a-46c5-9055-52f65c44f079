import os
import sys
import logging
import requests
from typing import List, Dict, Optional
from dotenv import load_dotenv
import time
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import VIDEO_DIR, logger

# 从 .env 文件加载环境变量
load_dotenv()

def get_config() -> Dict[str, str]:
    """
    从环境变量获取配置。

    :return: 包含配置参数的字典。
    """
    return {
        "PEXELS_API_KEY": os.getenv("PEXELS_API_KEY"),
        "MAX_VIDEOS": int(os.getenv("MAX_VIDEOS", "5")),  # 要下载的最大视频数
        "MIN_WIDTH": int(os.getenv("MIN_WIDTH", "1280")),  # 最小视频宽度
        "MIN_DURATION": int(os.getenv("MIN_DURATION", "10"))  # 最小视频时长（秒）
    }

def setup_logging(log_level: str):
    """
    配置日志设置。

    :param log_level: 日志级别字符串。
    """
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def search_videos(keyword: str, api_key: str, max_videos: int, min_width: int, min_duration: int) -> List[Dict]:
    """
    使用 Pexels API 搜索视频。

    :param keyword: 搜索关键词。
    :param api_key: Pexels API 密钥。
    :param max_videos: 要获取的最大视频数。
    :param min_width: 最小视频宽度。
    :param min_duration: 最小视频时长（秒）。
    :return: 视频信息列表。
    """
    logger = logging.getLogger("search_videos")
    headers = {
        'Authorization': api_key
    }
    url = f'https://api.pexels.com/videos/search?query={keyword}&per_page={max_videos}'
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        videos = []
        for video in data.get('videos', []):
            # 选择符合条件的最高质量视频文件
            video_files = [f for f in video['video_files'] 
                         if f['width'] >= min_width and video['duration'] >= min_duration]
            
            if video_files:
                best_quality = max(video_files, key=lambda x: x['width'])
                video_info = {
                    "id": video['id'],
                    "duration": video['duration'],
                    "width": best_quality['width'],
                    "height": best_quality['height'],
                    "video_url": best_quality['link'],
                    "preview_image_url": video['image'],
                    "user": video['user'].get('name', ''),
                    "url": video['url'],  # Pexels 页面 URL（用于归属）
                    "file_path": None,  # 下载后填充
                    "preview_image_path": None,  # 下载后填充
                    "surround_text": f"Video by {video['user'].get('name', '')} on Pexels"
                }
                videos.append(video_info)
                logger.debug(f"找到符合条件的视频：ID {video['id']}")
        
        logger.info(f"找到 {len(videos)} 个符合条件的视频。")
        return videos
    
    except requests.RequestException as e:
        logger.error(f"搜索视频时发生错误：{e}")
        return []

def download_file(url: str, filename: str, chunk_size: int = 8192) -> bool:
    """
    从 URL 下载文件。

    :param url: 文件的 URL。
    :param filename: 保存文件的路径。
    :param chunk_size: 每次下载的块大小。
    :return: 如果下载成功则为 True，否则为 False。
    """
    logger = logging.getLogger("download_file")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
        
        logger.info(f"已下载文件：{filename}")
        return True
    
    except requests.RequestException as e:
        logger.error(f"下载文件失败：{e}")
        return False
    except IOError as e:
        logger.error(f"保存文件失败：{e}")
        return False

def process_keyword(keyword: str) -> List[Dict]:
    """
    处理关键词搜索并返回视频元数据。

    :param keyword: 搜索关键词
    :return: 视频元数据列表
    """
    config = get_config()
    
    # 搜索视频
    videos = search_videos(
        keyword=keyword,
        api_key=config["PEXELS_API_KEY"],
        max_videos=config["MAX_VIDEOS"],
        min_width=config["MIN_WIDTH"],
        min_duration=config["MIN_DURATION"]
    )
    
    if not videos:
        logger.warning(f"未找到与关键词 '{keyword}' 相关的视频")
        return []

    # 转换为统一的视频条目格式
    video_entries = []
    for video in videos:
        entry = {
            "video_url": video["video_url"],
            "surround_text": video["surround_text"],
            "preview_image_url": video["preview_image_url"],
            "preview_image_path": None,  # 下载时填充
            "file_path": None,  # 下载时填充
            "codec_info": None  # 下载后填充
        }
        video_entries.append(entry)
        logger.debug(f"添加视频条目：{video['id']}")

    return video_entries

def save_metadata_to_json(metadata: List[Dict], json_output: str):
    """
    将元数据列表保存到 JSON 文件。

    :param metadata: 元数据字典列表。
    :param json_output: JSON 输出文件的路径。
    """
    logger = logging.getLogger("save_metadata_to_json")
    try:
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=4)
        logger.info(f"元数据已保存到 JSON 文件：{json_output}")
    except Exception as e:
        logger.error(f"保存元数据到 JSON 文件失败：{e}")

def main():
    """
    主函数。
    """
    config = get_config()
    setup_logging(config["LOG_LEVEL"])
    logger = logging.getLogger("Main")

    try:
        # 获取搜索关键词
        keyword = input("请输入要搜索的视频关键词：").strip()
        if not keyword:
            logger.error("未提供关键词。退出。")
            return

        # 处理关键词搜索和下载
        videos = process_keyword(keyword)
        
        if videos:
            # 保存元数据
            save_metadata_to_json(videos, config["JSON_OUTPUT"])
            logger.info("处理完成。")
        else:
            logger.warning("未能成功下载任何视频。")

    except KeyboardInterrupt:
        logger.info("用户中断进程。")

if __name__ == "__main__":
    main() 