import os
import json
import requests
import time
import logging
import random
import copy
from typing import Dict, Any, List, Optional
# 日志记录器
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
# 全局配置
QUEUE_TIMEOUT = 1800  # 队列超时时间（秒）
CHECK_INTERVAL = 10   # 状态检查间隔（秒）

# 工作流节点配置 (从 image_generation.py 移动过来)
WORKFLOW_CONFIG = {
    # 定义不同工作流的配置
    "workflows": {
        "storytelling-api": {  # 新增工作流配置
            # 提示词相关节点
            "PROMPT_NODES": {
                "action": {  # 主要动作/场景描述
                    "node_id": "21",  # 对应工作流中的 "Prompt Action" 节点
                    "clip_id": "8"    # 对应的 CLIP 模型节点
                },
                "style": {  # 风格提示词
                    "node_id": "19",  # 对应工作流中的 "CLIP Text Encode (Prompt)" 节点
                    "clip_id": "8"    # 对应的 CLIP 模型节点
                }
            },
            # 图片输出节点
            "OUTPUT_NODE": {
                "node_id": "58"  # 对应工作流中的 "Save Image" 节点
            },
            # 预览节点
            "PREVIEW_NODE": {
                "node_id": "60"  # 对应工作流中的 "Preview Image" 节点
            },
            # 随机噪声节点
            "NOISE_NODES": ["24", "45"]  # 对应工作流中的 RandomNoise 节点
        },
        "storytelling-without-upscale": {  # 新增不带上采样的工作流配置
            # 提示词相关节点
            "PROMPT_NODES": {
                "action": {  # 主要动作/场景描述
                    "node_id": "21",  # 对应工作流中的 "Prompt Action" 节点
                    "clip_id": "8"    # 对应的 CLIP 模型节点
                },
                "style": {  # 风格提示词
                    "node_id": "19",  # 对应工作流中的 "CLIP Text Encode (Prompt)" 节点
                    "clip_id": "8"    # 对应的 CLIP 模型节点
                }
            },
            # 图片输出节点 - 使用预览节点作为输出
            "OUTPUT_NODE": {
                "node_id": "60"  # 对应工作流中的 "Preview Image" 节点
            },
            # 预览节点
            "PREVIEW_NODE": {
                "node_id": "60"  # 对应工作流中的 "Preview Image" 节点
            },
            # 随机噪声节点
            "NOISE_NODES": ["24"]  # 对应工作流中的 RandomNoise 节点
        },
        "default": { # 添加一个基础的后备配置
             "PROMPT_NODES": {
                 # 可能需要根据通用情况调整
                 "action": {"node_id": "6", "clip_id": "3"}, # Example IDs
                 "negative": {"node_id": "7", "clip_id": "3"} # Example IDs
             },
             "OUTPUT_NODE": {"node_id": "9"}, # Example ID: Save Image
             "PREVIEW_NODE": {"node_id": "10"}, # Example ID: Preview Image
             "NOISE_NODES": ["4"] # Example ID: KSampler
        }
    }
}

def load_workflow(workflow_path: str) -> dict:
    """加载 ComfyUI 工作流文件 (JSON 格式)"""
    try:
        with open(workflow_path, 'r', encoding='utf-8') as file:
            workflow_data = json.load(file)
            # 检查是否是 API 格式（使用节点 ID 作为键）
            if all(isinstance(k, (str, int)) and isinstance(v, dict) for k, v in workflow_data.items()):
                return workflow_data
            # 检查是否是标准工作流格式（包含 nodes 数组）
            elif "nodes" in workflow_data:
                # 转换为 API 格式
                api_format = {}
                for node in workflow_data["nodes"]:
                    node_data = {
                        "inputs": node.get("inputs", {}),
                        "class_type": node["type"],
                    }
                    if "widgets_values" in node:
                        node_data["widgets_values"] = node["widgets_values"]
                    api_format[str(node["id"])] = node_data
                return api_format
            else:
                raise ValueError(f"工作流文件 '{workflow_path}' 格式无效")
    except json.JSONDecodeError as e:
        raise ValueError(f"工作流文件 '{workflow_path}' 不是有效的 JSON 格式: {e}")
    except FileNotFoundError:
        raise FileNotFoundError(f"找不到工作流文件：'{workflow_path}'")
    except Exception as e:
        raise RuntimeError(f"加载工作流 '{workflow_path}' 时发生未知错误: {e}")


def get_workflow_type(workflow_path: str) -> str:
    """
    根据工作流文件路径获取工作流类型 (从 WORKFLOW_CONFIG 中匹配)

    Args:
        workflow_path: 工作流文件路径

    Returns:
        str: 工作流类型, 未匹配到返回 "default"
    """
    basename = os.path.basename(workflow_path)

    # 尝试匹配工作流配置
    for workflow_type in WORKFLOW_CONFIG["workflows"].keys():
        # 确保 workflow_type 不是 'default' 时才进行匹配
        if workflow_type != 'default' and workflow_type in basename:
            logger.info(f"匹配到工作流类型: {workflow_type}")
            return workflow_type

    logger.info(f"未根据文件名 '{basename}' 匹配到特定工作流类型，使用 'default'")
    return "default"

def get_workflow_config_by_path(workflow_path: str) -> Optional[dict]:
    """
    根据工作流文件路径获取相应的工作流配置字典。

    Args:
        workflow_path: 工作流文件路径。

    Returns:
        Optional[dict]: 对应的工作流配置字典，如果找不到则返回 None。
    """
    workflow_type = get_workflow_type(workflow_path)
    config = WORKFLOW_CONFIG["workflows"].get(workflow_type)
    if not config:
        logger.error(f"无法为工作流类型 '{workflow_type}' (来自路径 '{workflow_path}') 找到配置。")
        # 尝试回退到 default，如果 default 也失败则返回 None
        config = WORKFLOW_CONFIG["workflows"].get("default")
        if not config:
             logger.error("连 'default' 后备工作流配置也缺失！")
             return None
        else:
             logger.warning("回退到使用 'default' 工作流配置。")
    return config

def set_workflow_parameters(workflow_data: dict, prompt_data: dict, workflow_config: dict) -> dict:
    """
    根据提示词和工作流配置设置工作流参数 (节点输入)。

    Args:
        workflow_data: 工作流数据 (API 格式)。
        prompt_data: 单个提示词数据 (例如 {'flux_prompt': '...', 'negative_prompt': '...'})。
        workflow_config: 与此工作流类型匹配的配置字典。

    Returns:
        dict: 更新后的工作流数据。
    """
    if not workflow_config:
        logger.error("设置工作流参数错误：未提供 workflow_config。")
        return workflow_data # 返回原始数据避免崩溃

    try:
        nodes = workflow_data # 假设已经是 API 格式

        # 获取提示词节点配置
        prompt_nodes = workflow_config.get("PROMPT_NODES", {})

        # 提取 flux_prompt 和 negative_prompt
        flux_prompt = prompt_data.get("flux_prompt", "")
        negative_prompt = prompt_data.get("negative_prompt", "")

        # 设置提示词
        for node_type, node_info in prompt_nodes.items():
            node_id = node_info.get("node_id")
            clip_id = node_info.get("clip_id") # CLIP ID 可能在顶层或嵌套

            if node_id and node_id in nodes:
                node = nodes[node_id]
                if "inputs" not in node:
                    logger.warning(f"节点 {node_id} 缺少 'inputs' 字段，跳过参数设置。")
                    continue

                inputs = node["inputs"]

                # 主提示词节点（正面）
                if node_type == "action":
                    text_field = next((f for f in ["text", "prompt", "positive"] if f in inputs), None)
                    if text_field:
                        target_prompt = flux_prompt or (json.dumps(prompt_data, ensure_ascii=False) if isinstance(prompt_data, dict) else str(prompt_data))
                        inputs[text_field] = target_prompt
                        logger.info(f"设置节点 {node_id} (action) 的 '{text_field}': {target_prompt}...")
                        # 更新 CLIP 引用
                        clip_field = next((f for f in ["clip", "clip_g", "clip_l"] if f in inputs), None)
                        if clip_field and clip_id:
                            inputs[clip_field] = [str(clip_id), 0] # 确保 clip_id 是字符串
                            logger.debug(f"更新节点 {node_id} 的 '{clip_field}' 引用到 CLIP {clip_id}")
                    else:
                        logger.warning(f"节点 {node_id} (action) 未找到可用的文本输入字段 (text/prompt/positive)。")

                # 负面提示词节点
                elif node_type == "negative":
                    text_field = next((f for f in ["text", "prompt", "negative"] if f in inputs), None)
                    if text_field:
                        if negative_prompt:
                             inputs[text_field] = negative_prompt
                             logger.debug(f"设置节点 {node_id} (negative) 的 '{text_field}': {negative_prompt[:100]}...")
                             # 更新 CLIP 引用
                             clip_field = next((f for f in ["clip", "clip_g", "clip_l"] if f in inputs), None)
                             if clip_field and clip_id:
                                 inputs[clip_field] = [str(clip_id), 0] # 确保 clip_id 是字符串
                                 logger.debug(f"更新节点 {node_id} (negative) 的 '{clip_field}' 引用到 CLIP {clip_id}")
                        else:
                             logger.debug(f"节点 {node_id} (negative) 无 negative_prompt 提供，保持不变。")

                    else:
                        logger.warning(f"节点 {node_id} (negative) 未找到可用的文本输入字段 (text/prompt/negative)。")

                # 样式提示词节点 (可选，通常合并到主提示词)
                elif node_type == "style":
                    style_text = ""
                    if isinstance(prompt_data, dict):
                        style_text = prompt_data.get("style") or prompt_data.get("visual_style", "")

                    if style_text:
                         text_field = next((f for f in ["text", "prompt", "positive"] if f in inputs), None)
                         if text_field:
                              inputs[text_field] = style_text
                              logger.debug(f"设置节点 {node_id} (style) 的 '{text_field}': {style_text[:100]}...")
                              # 更新 CLIP 引用 (如果需要且配置了)
                              clip_field = next((f for f in ["clip", "clip_g", "clip_l"] if f in inputs), None)
                              if clip_field and clip_id:
                                   inputs[clip_field] = [str(clip_id), 0]
                                   logger.debug(f"更新节点 {node_id} (style) 的 '{clip_field}' 引用到 CLIP {clip_id}")
                         else:
                              logger.warning(f"节点 {node_id} (style) 未找到可用的文本输入字段。")


        # 设置图像尺寸 (如果配置中存在)
        if "IMAGE_SIZE" in workflow_config:
            # 这个逻辑比较复杂，取决于 IMAGE_SIZE 的具体结构
            # 假设 IMAGE_SIZE 结构类似: {"width_node": "ID", "height_node": "ID", "width_field": "value", "height_field": "value", "width": 1024, "height": 576}
            size_config = workflow_config["IMAGE_SIZE"]
            width_node_id = size_config.get("width_node")
            height_node_id = size_config.get("height_node")
            width_field = size_config.get("width_field", "value") # 假设默认字段是 'value'
            height_field = size_config.get("height_field", "value")
            width = size_config.get("width")
            height = size_config.get("height")

            if width_node_id and width is not None and width_node_id in nodes and "inputs" in nodes[width_node_id] and width_field in nodes[width_node_id]["inputs"]:
                 nodes[width_node_id]["inputs"][width_field] = width
                 logger.debug(f"设置节点 {width_node_id} 的 '{width_field}' 为宽度: {width}")

            if height_node_id and height is not None and height_node_id in nodes and "inputs" in nodes[height_node_id] and height_field in nodes[height_node_id]["inputs"]:
                 nodes[height_node_id]["inputs"][height_field] = height
                 logger.debug(f"设置节点 {height_node_id} 的 '{height_field}' 为高度: {height}")
                 
        # 设置随机种子 (如果配置了噪声节点)
        noise_nodes = workflow_config.get("NOISE_NODES", [])
        if noise_nodes:
            # 使用 copy.deepcopy 确保每个任务的 workflow 是独立的
            current_workflow_data = copy.deepcopy(workflow_data)
            nodes = current_workflow_data # 使用副本
            seed = random.randint(0, 2**32 - 1) # 生成随机种子
            for node_id in noise_nodes:
                if node_id in nodes:
                    node = nodes[node_id]
                    # 检查常见的种子字段
                    seed_field = next((f for f in ["seed", "noise_seed", "seed_"] if f in node.get("inputs", {})), None)
                    if seed_field:
                        node["inputs"][seed_field] = seed
                        logger.debug(f"为噪声节点 {node_id} 设置随机种子 '{seed_field}': {seed}")
                    else:
                        # 有些节点可能在 widgets_values 里设置种子
                        if "widgets_values" in node and isinstance(node["widgets_values"], list):
                             # 查找名为 'seed' 或 'noise_seed' 的 widget
                             seed_widget_index = -1
                             for i, widget in enumerate(node.get("widgets_info", [])): # 需要 widgets_info (API格式可能没有)
                                  if isinstance(widget, dict) and widget.get("name") in ["seed", "noise_seed"]:
                                       seed_widget_index = i
                                       break
                             if seed_widget_index != -1 and seed_widget_index < len(node["widgets_values"]):
                                  node["widgets_values"][seed_widget_index] = seed
                                  logger.debug(f"为噪声节点 {node_id} 设置 widget 随机种子 (索引 {seed_widget_index}): {seed}")
                             else:
                                  logger.warning(f"无法为节点 {node_id} 找到明确的种子字段 (inputs/widgets_values)。")
                        elif "inputs" in node: # 回退检查 inputs 是否有 seed
                             if "seed" in node["inputs"]: # 有些采样器直接在inputs里
                                  node["inputs"]["seed"] = seed
                                  logger.debug(f"为噪声节点 {node_id} 设置 inputs 随机种子 'seed': {seed}")
                             else:
                                  logger.warning(f"无法为节点 {node_id} 找到明确的种子字段 (inputs/widgets_values)。")


        # 返回修改后的副本
        return current_workflow_data
    except Exception as e:
        logger.error(f"设置工作流参数时出错: {str(e)}", exc_info=True)
        return workflow_data # 返回原始数据

def queue_prompt(server_address: str, prompt_workflow_data: dict) -> Optional[str]:
    """
    向 ComfyUI 提交生成任务。

    Args:
        server_address: ComfyUI 服务器地址 (例如 "127.0.0.1:8188")。
        prompt_workflow_data: 准备好的工作流数据 (API 格式)。

    Returns:
        Optional[str]: 成功则返回任务的 prompt_id，失败则返回 None。
    """
    url = f"http://{server_address}/prompt"
    headers = {'Content-Type': 'application/json'}
    payload = json.dumps({"prompt": prompt_workflow_data}).encode('utf-8')

    try:
        response = requests.post(url, data=payload, headers=headers, timeout=30) # 设置超时
        response.raise_for_status() # Raises HTTPError for bad responses (4xx or 5xx)
        result = response.json()
        prompt_id = result.get('prompt_id')
        if prompt_id:
             logger.debug(f"任务成功提交，获得 Prompt ID: {prompt_id}")
             return prompt_id
        else:
             logger.error(f"任务提交响应中未找到 'prompt_id'。响应: {result}")
             return None
    except requests.exceptions.Timeout:
        logger.error(f"提交生成任务超时 (连接 {url})")
        return None
    except requests.exceptions.ConnectionError as e:
        logger.error(f"提交生成任务连接失败: {e}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"提交生成任务失败 (RequestException): {e}")
        logger.error(f"响应状态码: {e.response.status_code if e.response else 'N/A'}")
        logger.error(f"响应内容: {e.response.text[:500] if e.response else 'N/A'}...")
        return None
    except json.JSONDecodeError as e:
         logger.error(f"解析服务器响应失败: {e}. 响应内容: {response.text[:500]}...")
         return None
    except Exception as e:
        logger.error(f"提交生成任务时发生未知错误: {e}", exc_info=True)
        return None


def find_image_in_outputs(outputs_data: dict, workflow_config: Optional[dict] = None) -> Optional[dict]:
    """
    在 ComfyUI 任务的输出数据中查找图片信息。
    优先使用 workflow_config 中定义的 OUTPUT_NODE 和 PREVIEW_NODE。

    Args:
        outputs_data: ComfyUI /history/<prompt_id> 返回的 outputs 字典。
        workflow_config: 当前工作流的配置字典。

    Returns:
        Optional[dict]: 包含图片信息 {'filename': ..., 'subfolder': ..., 'type': ...} 的字典，
                        如果未找到则返回 None。
    """
    if not isinstance(outputs_data, dict):
        logger.warning(f"无效的 outputs_data 格式 (非字典): {type(outputs_data)}")
        return None

    node_ids_with_images = [] # 用于调试

    def _extract_image_info(node_data: dict, node_id: str) -> Optional[dict]:
        """辅助函数：从节点数据中提取第一个图片信息"""
        if isinstance(node_data, dict) and 'images' in node_data and isinstance(node_data['images'], list) and node_data['images']:
            img_data = node_data['images'][0]
            node_ids_with_images.append(node_id) # 记录找到图片的节点
            if isinstance(img_data, dict) and 'filename' in img_data:
                logger.debug(f"在节点 {node_id} 找到图片 (字典格式): {img_data['filename']}")
                return {
                    'filename': img_data['filename'],
                    'subfolder': img_data.get('subfolder', ''),
                    'type': img_data.get('type', 'output') # 假设默认类型
                }
            elif isinstance(img_data, str): # 兼容旧格式或预览格式
                logger.debug(f"在节点 {node_id} 找到图片 (字符串格式): {img_data}")
                # 尝试从字符串解析信息 (如果格式是 "type/subfolder/filename.png")
                parts = img_data.split('/')
                if len(parts) >= 3:
                     return {'filename': parts[-1], 'subfolder': '/'.join(parts[1:-1]), 'type': parts[0]}
                elif len(parts) == 1: # 只有文件名
                     return {'filename': parts[0], 'subfolder': '', 'type': 'output'} # 假设类型
                else: # 无法解析，直接返回文件名
                     return {'filename': img_data, 'subfolder': '', 'type': 'output'} # 假设类型
            else:
                logger.warning(f"节点 {node_id} 的 'images' 列表包含未知格式的数据: {type(img_data)}")
        return None

    # 1. 优先使用配置中的 OUTPUT_NODE
    if workflow_config and "OUTPUT_NODE" in workflow_config:
        output_node_id = str(workflow_config["OUTPUT_NODE"].get("node_id")) # 确保是字符串
        if output_node_id and output_node_id in outputs_data:
            logger.debug(f"检查配置的 OUTPUT_NODE: {output_node_id}")
            image_info = _extract_image_info(outputs_data[output_node_id], output_node_id)
            if image_info:
                return image_info
        else:
             logger.debug(f"配置的 OUTPUT_NODE '{output_node_id}' 不在输出数据中或未配置。")

    # 2. 其次检查配置中的 PREVIEW_NODE
    if workflow_config and "PREVIEW_NODE" in workflow_config:
        preview_node_id = str(workflow_config["PREVIEW_NODE"].get("node_id")) # 确保是字符串
        if preview_node_id and preview_node_id in outputs_data:
            logger.debug(f"检查配置的 PREVIEW_NODE: {preview_node_id}")
            image_info = _extract_image_info(outputs_data[preview_node_id], preview_node_id)
            if image_info:
                return image_info
        else:
             logger.debug(f"配置的 PREVIEW_NODE '{preview_node_id}' 不在输出数据中或未配置。")

    # 3. 通用回退：遍历所有节点查找 'images'
    logger.debug("在配置节点未找到图片，开始遍历所有输出节点...")
    for node_id, node_data in outputs_data.items():
        image_info = _extract_image_info(node_data, node_id)
        if image_info:
            logger.debug(f"通过通用遍历在节点 {node_id} 找到图片。")
            return image_info

    logger.warning(f"在所有输出节点中均未找到有效的图片信息。包含图片的节点有: {node_ids_with_images}")
    logger.debug(f"所有输出节点键: {list(outputs_data.keys())}")
    return None


def check_queue_status(server_address: str, prompt_id: str, workflow_config: Optional[dict] = None, timeout: int = QUEUE_TIMEOUT, max_retries_on_error: int = 3) -> Optional[dict]:
    """
    轮询 ComfyUI 任务状态，直到完成、失败或超时。

    Args:
        server_address: ComfyUI 服务器地址。
        prompt_id: 要查询的任务 ID。
        workflow_config: 工作流配置，用于 find_image_in_outputs。
        timeout: 单个任务的总等待超时时间（秒）。
        max_retries_on_error: 任务内部报告错误时的最大重试次数。

    Returns:
        Optional[dict]: 如果任务成功且找到图片，返回图片信息字典。
                        如果任务失败、超时或未找到图片，返回 None。
    """
    history_url = f"http://{server_address}/history/{prompt_id}"
    queue_url = f"http://{server_address}/queue" # 用于检查队列状态

    start_time = time.time()
    error_retries = 0
    last_status_str = "unknown"
    last_exec_info = None

    while time.time() - start_time < timeout:
        current_status_str = "checking"
        exec_info = None
        try:
            # 1. 先尝试直接获取历史记录
            logger.debug(f"查询任务历史: {prompt_id} (URL: {history_url})")
            response = requests.get(history_url, timeout=15)

            if response.status_code == 200:
                try:
                    history_data = response.json()
                    if isinstance(history_data, dict) and prompt_id in history_data:
                        task_entry = history_data[prompt_id]

                        # 检查状态 (可能在 status 或 outputs 中)
                        status_info = task_entry.get('status')
                        outputs_data = task_entry.get('outputs')

                        if isinstance(status_info, dict):
                            current_status_str = status_info.get('status_str', 'unknown')
                            exec_info = status_info.get('exec_info') # 获取执行信息
                            if current_status_str == 'success':
                                logger.info(f"任务 {prompt_id} 历史记录显示成功。")
                                if isinstance(outputs_data, dict):
                                    image_info = find_image_in_outputs(outputs_data, workflow_config)
                                    if image_info:
                                        logger.info(f"任务 {prompt_id} 成功并在输出中找到图片。")
                                        return image_info
                                    else:
                                        logger.warning(f"任务 {prompt_id} 成功，但在输出中未找到图片。Outputs keys: {list(outputs_data.keys()) if outputs_data else 'N/A'}")
                                        # 即使没图片也算完成，只是没有结果
                                        return None
                                else:
                                    logger.warning(f"任务 {prompt_id} 成功，但 'outputs' 字段缺失或格式错误。")
                                    return None # 任务完成但无结果
                            elif current_status_str == 'error':
                                error_msg = status_info.get('error', {}).get('message', '未知错误')
                                exception_msg = status_info.get('exception_message', '')
                                logger.error(f"任务 {prompt_id} 历史记录显示错误: {error_msg} {exception_msg}")
                                error_retries += 1
                                if error_retries >= max_retries_on_error:
                                     logger.error(f"任务 {prompt_id} 错误重试次数达到上限 ({max_retries_on_error})。")
                                     return None
                                else:
                                     logger.info(f"任务 {prompt_id} 遇到错误，将在 {CHECK_INTERVAL} 秒后重试 (第 {error_retries}/{max_retries_on_error} 次)。")
                                     # 等待后继续循环
                            elif current_status_str in ['pending', 'running', 'processing']:
                                logger.debug(f"任务 {prompt_id} 仍在进行中 (来自历史): {current_status_str}")
                                # 继续轮询
                            else:
                                logger.warning(f"任务 {prompt_id} 历史记录返回未知状态: {current_status_str}")
                                # 继续轮询
                        else:
                             # 没有 status 字段，但可能有 outputs，检查一下
                             if isinstance(outputs_data, dict):
                                  logger.debug(f"任务 {prompt_id} 历史记录无 'status' 字段，尝试从 'outputs' 查找图片。")
                                  image_info = find_image_in_outputs(outputs_data, workflow_config)
                                  if image_info:
                                       logger.info(f"任务 {prompt_id} 从无状态历史记录的 'outputs' 中找到图片。")
                                       return image_info
                                  else:
                                       logger.debug(f"任务 {prompt_id} 历史记录无 'status' 且 'outputs' 中无图片，可能仍在运行或已失败。")
                             else:
                                  logger.debug(f"任务 {prompt_id} 历史记录既无 'status' 也无有效 'outputs'。")

                    else:
                        # history API 返回了 200，但数据中没有这个 prompt_id，说明任务可能还在队列里或刚开始
                        logger.debug(f"任务 {prompt_id} 不在历史记录中，检查队列...")
                        current_status_str = "queued_or_running"

                except json.JSONDecodeError as e:
                    logger.warning(f"解析任务 {prompt_id} 历史响应失败: {e}. 响应: {response.text[:200]}...")
                    current_status_str = "parse_error" # 可能暂时性问题，继续轮询
                except Exception as e:
                    logger.error(f"处理任务 {prompt_id} 历史响应时发生意外错误: {e}", exc_info=True)
                    # 可能是严重问题，但也可能暂时性，谨慎起见继续轮询一会
                    current_status_str = "history_exception"

            elif response.status_code == 404:
                 # 历史记录 404，任务可能还在队列或从未执行
                 logger.debug(f"任务 {prompt_id} 历史记录返回 404，检查队列...")
                 current_status_str = "queued_or_running"
            else:
                # 其他 HTTP 错误
                logger.warning(f"查询任务 {prompt_id} 历史失败，状态码: {response.status_code}. 响应: {response.text[:200]}...")
                current_status_str = f"http_error_{response.status_code}"
                # 可能是服务器问题，短暂等待后继续

            # 记录状态变化和执行信息
            if current_status_str != last_status_str or (exec_info and exec_info != last_exec_info):
                 logger.info(f"任务 {prompt_id} 状态更新: {current_status_str}" + (f" | Exec Info: {exec_info}" if exec_info else ""))
                 last_status_str = current_status_str
                 last_exec_info = exec_info


        except requests.exceptions.Timeout:
            logger.warning(f"查询任务 {prompt_id} 状态超时。")
            current_status_str = "timeout"
        except requests.exceptions.RequestException as e:
            logger.error(f"查询任务 {prompt_id} 状态时发生连接错误: {e}")
            current_status_str = "connection_error"
            # 连接错误可能需要更长的等待或不同的重试策略

        # 间隔等待
        time.sleep(CHECK_INTERVAL)

    # 超时处理
    logger.error(f"任务 {prompt_id} 查询超时 (超过 {timeout} 秒)。最后状态: {last_status_str}")
    return None


def download_image(server_address: str, image_info: dict, output_path: str, max_retries: int = 3) -> Optional[str]:
    """
    从 ComfyUI 服务器下载生成的图片。

    Args:
        server_address: ComfyUI 服务器地址。
        image_info: 图片信息字典 (包含 'filename', 'subfolder', 'type')。
        output_path: 图片要保存的完整本地路径。
        max_retries: 下载失败时的最大重试次数。

    Returns:
        Optional[str]: 成功则返回保存的图片路径 (output_path)，失败则返回 None。
    """
    filename = image_info.get('filename')
    subfolder = image_info.get('subfolder', '')
    img_type = image_info.get('type', 'output') # 默认 'output'

    if not filename:
        logger.error("下载失败：图片信息中缺少 'filename'")
        return None

    url = f"http://{server_address}/view"
    output_dir = os.path.dirname(output_path)
    os.makedirs(output_dir, exist_ok=True) # 确保目标目录存在

    params = {
        "filename": filename,
        "type": img_type,
        "subfolder": subfolder
    }

    temp_file_path = output_path + ".tmp" # 使用临时文件

    for attempt in range(max_retries):
        logger.debug(f"尝试下载图片 (第 {attempt + 1}/{max_retries} 次): {filename} (Type: {img_type}, Subfolder: '{subfolder}') 到 {output_path}")
        try:
            response = requests.get(url, params=params, stream=True, timeout=120) # 增加下载超时
            response.raise_for_status()

            # 下载到临时文件
            file_size = 0
            with open(temp_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192 * 4): # 增大块
                    if chunk:
                        f.write(chunk)
                        file_size += len(chunk)

            # 检查文件大小
            if file_size > 0:
                # 下载成功，移动临时文件到最终路径
                try:
                    # 如果目标文件已存在，先删除（例如重新生成时）
                    if os.path.exists(output_path):
                         logger.warning(f"目标文件已存在，将覆盖: {output_path}")
                         os.remove(output_path)
                    os.rename(temp_file_path, output_path)
                    logger.info(f"图片成功保存到: {output_path} (大小: {file_size} bytes)")
                    return output_path
                except OSError as e:
                     logger.error(f"移动临时文件 {temp_file_path} 到 {output_path} 失败: {e}")
                     # 移动失败也算失败，清理临时文件
                     if os.path.exists(temp_file_path): os.remove(temp_file_path)
                     return None # 返回失败
            else:
                # 文件大小为0，可能是下载问题或服务器问题
                logger.warning(f"下载的文件 {filename} 大小为 0 (临时路径: {temp_file_path})。")
                if os.path.exists(temp_file_path): os.remove(temp_file_path) # 清理空文件
                # 继续重试

        except requests.exceptions.Timeout:
             logger.warning(f"下载图片 {filename} 超时 (第 {attempt + 1} 次尝试)。")
        except requests.exceptions.HTTPError as e:
             logger.warning(f"下载图片 {filename} 时发生 HTTP 错误 (状态码: {e.response.status_code}) (第 {attempt + 1} 次尝试)。响应: {e.response.text[:200]}...")
             # 404 等错误可能不需要重试太多次
             if e.response.status_code == 404 and attempt >= 1: # 404 重试一次即可
                  logger.error(f"图片 {filename} 在服务器上找不到 (404)，停止重试。")
                  break # 跳出重试循环
        except requests.exceptions.RequestException as e:
            logger.warning(f"下载图片 {filename} 时发生连接或请求错误: {e} (第 {attempt + 1} 次尝试)。")
        except Exception as e:
             logger.warning(f"下载图片 {filename} 时发生未知错误: {e} (第 {attempt + 1} 次尝试)。", exc_info=True)

        # 如果是最后一次尝试失败
        if attempt == max_retries - 1:
            logger.error(f"下载图片 {filename} 失败，已达到最大重试次数 {max_retries}。")
            # 确保清理临时文件
            if os.path.exists(temp_file_path):
                try: os.remove(temp_file_path)
                except OSError: pass
            return None # 返回失败

        # 等待后重试
        wait_time = 2 ** attempt # 指数退避
        logger.info(f"将在 {wait_time} 秒后重试下载...")
        time.sleep(wait_time)

    # 如果循环因 break 或其他原因结束且未成功返回
    return None

# 可选：添加一个获取常量的函数
def get_comfyui_constants() -> dict:
    return {
        "QUEUE_TIMEOUT": QUEUE_TIMEOUT,
        "CHECK_INTERVAL": CHECK_INTERVAL
    } 