import pysubs2
import subprocess
import os
import sys
import argparse
import platform
from config import logger, VIDEO_ENCODE_PARAMS, AUDIO_ENCODE_PARAMS, FFMPEG_BASE_PARAMS
import traceback
import subprocess
import logging
import shutil
import tempfile
import chardet

# 定义默认字幕配置
DEFAULT_SUBTITLE_CONFIG = {
    'max_chars_per_line': 60,
    'font_name': 'SFGeorgian',
    'font_size': 14,
    'primary_color': '&H00FFFFFF',  # 完全不透明的白色
    'back_color': '&H80000000',  # 50% 不透明度的黑色
    'outline_color': '&H000000&',  # 黑色
    'bold': False,
    'italic': False,
    'outline': 1,
    'shadow': 0,
    'margin_v': 10,
    'margin_l': 10,
    'margin_r': 10,
    'alignment': 2,  # 底部居中
}

# 定义语言特定的字幕配置
SUBTITLE_CONFIG = {
    'English': {
        'code': 'en-US',
    },
    'Spanish': {
        'code': 'es-ES',
        'font_size':  16,
    },
    'Portuguese': {
        'code': 'pt-PT',
        'font_size': 16,
    },
    'Japanese': {
        'code': 'ja-JP',
        'max_chars_per_line': 20,
        'font_name': 'Noto Sans CJK JP',
        'font_size': 16,
    },
    'Chinese': {
        'code': 'zh-CN',
        'max_chars_per_line': 20,
        'font_name': 'Noto Sans CJK SC',
        'font_size': 8,
    },
}

EMBED_DEBUG = False
SYNC_SUBTITLE = False  # 新增变量

def run_subprocess(command):
    """运行子进程命令，增强错误处理。"""
    try:
        # 对于 FFmpeg 命令，我们希望看到实时进度
        if command[0] == 'ffmpeg':
            # 直接将 stderr 输出到控制台
            result = subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=None)
            return result.stdout
        else:
            # 其他命令保持原样
            result = subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
            return result.stdout
    except subprocess.CalledProcessError as e:
        # 记录错误但不终止程序，而是抛出异常让调用者处理
        logger.error(f"运行命令时出错: {' '.join(command)}\n{e.stderr if hasattr(e, 'stderr') else ''}")
        raise  # 重新抛出异常，让调用者决定如何处理

def synchronize_subtitles(video_file, sub_file, output_file):
    """使用 ffsubsync 同步字幕。"""
    logger.info(f"正在为 {video_file} 同步字幕 {sub_file}...")
    command = [
        'ffsubsync', 
        video_file, 
        '-i', sub_file, 
        '-o', output_file,
        '--gss',                          # 使用黄金分割搜索来优化帧率比
        '--max-subtitle-seconds', '8',    # 设置字幕最大显示时间为8秒
        '--vad', 'auditok',                # 使用 webrtc 作为语音活动检测方法
        '--max-offset-seconds', '20',      # 设置最大允许的字幕偏移时间为5秒 
        '--strict',                       # 严格模式，确保字幕时间戳准确
    ]
    result = run_subprocess(command)
    logger.info(f"ffsubsync 输出: {result}")
    logger.info(f"字幕同步完成: {output_file}")

def check_cuda_support():
    """检查是否支持 CUDA 并且 GPU 可用。"""
    try:
        if not shutil.which('nvidia-smi'):
            logger.info("未找到 nvidia-smi。不支持 CUDA。")
            return False

        gpu_check = subprocess.run(['nvidia-smi'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        logger.info(f"nvidia-smi 输出: {gpu_check.stdout}")
        
        result = subprocess.run(['ffmpeg', '-hwaccels'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        logger.info(f"FFmpeg hwaccels 输出: {result.stdout}")
        
        if 'cuda' in result.stdout:
            logger.info("在 hwaccels 列表中找到 CUDA，尝试使用...")
            test_cmd = ['ffmpeg', '-hwaccel', 'cuda', '-i', '/dev/null', '-f', 'null', '-']
            subprocess.run(test_cmd, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            logger.info("CUDA 测试成功")
            return True
        else:
            logger.info("在 hwaccels 列表中未找到 CUDA")
        return False
    except subprocess.CalledProcessError as e:
        logger.error(f"检查 CUDA 支持时出错 (CalledProcessError): {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"检查 CUDA 支持时发生意外错误: {str(e)}")
        logger.error(f"追踪信息: {traceback.format_exc()}")
        return False

def check_videotoolbox_support():
    """检查macOS上是否支持VideoToolbox硬件加速。"""
    try:
        # 首先检查是否为macOS系统
        if platform.system() != 'Darwin':
            logger.info("非macOS系统，不支持VideoToolbox。")
            return False
            
        # 检查FFmpeg是否支持videotoolbox
        result = subprocess.run(['ffmpeg', '-hwaccels'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        logger.info(f"FFmpeg hwaccels 输出: {result.stdout}")
        
        if 'videotoolbox' in result.stdout:
            logger.info("检测到VideoToolbox硬件加速支持")
            # 简单测试确认VideoToolbox可用
            test_cmd = ['ffmpeg', '-hwaccel', 'videotoolbox', '-i', '/dev/null', '-f', 'null', '-']
            try:
                subprocess.run(test_cmd, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                logger.info("VideoToolbox测试成功")
                return True
            except subprocess.CalledProcessError:
                logger.warning("VideoToolbox测试失败，但检测到支持，将尝试使用")
                return True
        else:
            logger.info("在hwaccels列表中未找到VideoToolbox")
            return False
    except Exception as e:
        logger.error(f"检查VideoToolbox支持时出错: {str(e)}")
        logger.error(f"追踪信息: {traceback.format_exc()}")
        return False

def get_subtitle_config(language):
    config = DEFAULT_SUBTITLE_CONFIG.copy()
    config.update(SUBTITLE_CONFIG.get(language, {}))
    return config

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as file:
        raw = file.read()
    return chardet.detect(raw)['encoding']

def split_subtitle_sentence(text: str, max_chars: int) -> str:
    """智能分割字幕句子，追求更均衡的分行效果"""
    if len(text) <= max_chars:
        return text

    words = text.split()
    total_length = len(text)
    target_length = total_length // 2  # 理想的分割点
    
    # 尝试找到最接近中点的分割位置
    best_split_index = 0
    best_balance = float('inf')
    current_length = 0
    
    for i, word in enumerate(words):
        prev_length = current_length
        current_length += len(word) + (1 if current_length > 0 else 0)
        
        # 计算当前位置的不平衡度
        first_line_len = current_length
        second_line_len = total_length - current_length
        balance = abs(first_line_len - second_line_len)
        
        # 如果第一行没有超过最大长度，并且这个分割点比之前的更平衡
        if first_line_len <= max_chars and balance < best_balance:
            best_split_index = i
            best_balance = balance
    
    # 如果没有找到合适的分割点（第一个词就超过max_chars）
    if best_split_index == 0 and len(words[0]) > max_chars:
        # 强制在max_chars位置分割
        return text[:max_chars] + '\\N' + text[max_chars:]
    
    # 构建分行后的文本
    first_line = ' '.join(words[:best_split_index + 1])
    second_line = ' '.join(words[best_split_index + 1:])
    
    return first_line + '\\N' + second_line

def srt_to_ass(srt_file, language):
    config = get_subtitle_config(language)
    max_chars = config['max_chars_per_line']  # 从配置中获取值
    
    # 尝试检测文件编码
    detected_encoding = detect_encoding(srt_file)
    logger.info(f"检测到的字幕文件编码: {detected_encoding}")
    
    try:
        subs = pysubs2.load(srt_file, encoding=detected_encoding)
    except Exception as e:
        logger.warning(f"使用检测到的编码 {detected_encoding} 读取失败，尝试其他编码")
        encodings = ['utf-8', 'utf-16', 'ascii', 'latin-1', 'utf-8-sig']
        for encoding in encodings:
            try:
                subs = pysubs2.load(srt_file, encoding=encoding)
                logger.info(f"成功使用 {encoding} 编码读取字幕文件")
                break
            except Exception:
                continue
        else:
            raise ValueError(f"无法读取字幕文件 {srt_file}，请检查文件编码")

    # 添加 CJK 字幕分行处理
    if language in ['Japanese', 'Chinese']:
        split_points = {
            'Japanese': ['。', '、', '！', '？', '」', '』', '）', '】', '，', ' '],
            'Chinese': ['。', '，', '！', '？', '；', '：', '）', '》', '」', '』', '、', ' ']
        }
        
        for line in subs:
            text = line.text
            # 如果文本长度超过每行最大字符数，进行智能分行处理
            if len(text) > max_chars:
                line.text = split_subtitle_sentence(text, max_chars)
                logger.debug(f"{language}字幕分行处理: '{text}' -> '{line.text}'")
    else:
        # 英文等其他语言的分行处理
        # 使用空格作为分割点
        for line in subs:
            text = line.text
            if len(text) > max_chars:
                words = text.split()
                current_line = []
                current_length = 0
                result = []
                
                for word in words:
                    word_len = len(word)
                    if current_length + word_len + 1 <= max_chars:
                        current_line.append(word)
                        current_length += word_len + 1
                    else:
                        result.append(' '.join(current_line))
                        current_line = [word]
                        current_length = word_len + 1
                
                if current_line:
                    result.append(' '.join(current_line))
                
                line.text = '\\N'.join(result)
                logger.debug(f"字幕分行处理: '{text}' -> '{line.text}'")

    style = pysubs2.SSAStyle(
        fontname=config['font_name'],
        fontsize=config['font_size'],
        primarycolor='&H00FFFFFF',  # 完全不透明的白色
        backcolor='&H80000000',  # 半透明黑色（在 borderstyle=1 时不影响显示）
        outlinecolor='&H80000000',  # 半透明黑色用于轮廓
        bold=config['bold'],
        italic=config['italic'],
        outline=3,  # 调整轮廓宽度，可以根据需要修改
        shadow=0,  # 关闭阴影
        marginv=config['margin_v'],
        marginl=config['margin_l'],
        marginr=config['margin_r'],
        alignment=config['alignment'],
        borderstyle=1,  # 使用轮廓和阴影模式
        secondarycolor='&H00FFFFFF'  # 完全不透明的白色（用于卡拉OK效果，这里不会用到）
    )
    subs.styles["Default"] = style
    logger.info(f"ASS样式设置: {style}")
    
    # 不需要修改每个字幕行的文本，因为样式已经在全局设置中定义
    for line in subs:
        line.style = "Default"

    return subs

def embed_subtitles(video_file, subtitles_file, output_file, language):
    with tempfile.NamedTemporaryFile(suffix='.ass', delete=False) as temp_ass:
        ass_file = temp_ass.name
        srt_to_ass(subtitles_file, language).save(ass_file)

    try:
        # 默认使用标准编码参数（软件编码方案）
        default_command = ['ffmpeg', '-y', '-i', video_file] + FFMPEG_BASE_PARAMS + [
            '-vf', f"ass={ass_file}"
        ] + VIDEO_ENCODE_PARAMS + [
            '-c:a', 'copy', 
            '-max_muxing_queue_size', '1024',
            output_file
        ]

        # 调试模式处理
        if EMBED_DEBUG:
            command = ['ffmpeg', '-y', '-i', video_file] + FFMPEG_BASE_PARAMS + [
                '-vf', f"ass={ass_file}",
                '-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '28',
                '-c:a', 'copy', 
                '-max_muxing_queue_size', '1024',
                output_file
            ]
            logger.info("使用快速调试模式嵌入字幕")
        else:
            # 正常模式 - 尝试硬件加速
            command = default_command
            logger.info("使用标准参数嵌入字幕")

            # 检查并应用硬件加速
            try_hardware_accel = False
            
            # 首先检查macOS的VideoToolbox
            if check_videotoolbox_support() and not EMBED_DEBUG:
                # macOS VideoToolbox硬件加速
                hw_command = [
                    'ffmpeg', '-y', 
                    '-hwaccel', 'videotoolbox',
                    '-i', video_file
                ] + FFMPEG_BASE_PARAMS + [
                    '-vf', f"ass={ass_file}",
                    '-c:v', 'h264_videotoolbox', '-q:v', '23',
                    '-r', '25', '-movflags', '+faststart', '-pix_fmt', 'yuv420p',
                    '-c:a', 'copy', 
                    '-max_muxing_queue_size', '1024',
                    output_file
                ]
                logger.info("使用macOS VideoToolbox硬件加速")
                try_hardware_accel = True
                command = hw_command
                
            # 如果不是macOS或VideoToolbox不可用，则尝试CUDA
            elif check_cuda_support() and not EMBED_DEBUG:
                # NVIDIA CUDA硬件加速
                hw_command = [
                    'ffmpeg', '-y', '-hwaccel', 'cuda', '-i', video_file
                ] + FFMPEG_BASE_PARAMS + [
                    '-vf', f"ass={ass_file}",
                    '-c:v', 'h264_nvenc', '-preset', 'p4', '-cq', '23',
                    '-c:a', 'copy', 
                    '-max_muxing_queue_size', '1024',
                    output_file
                ]
                logger.info("使用NVIDIA CUDA硬件加速")
                try_hardware_accel = True
                command = hw_command
                
            if not try_hardware_accel and not EMBED_DEBUG:
                logger.info("未检测到硬件加速支持，使用CPU编码")

        logger.debug(f"使用的 FFmpeg 命令: {' '.join(command)}")
        
        try:
            run_subprocess(command)
            logger.info(f"带字幕的视频已保存到 {output_file}")
        except Exception as e:
            # 如果硬件加速失败，尝试回退到软件编码
            if command != default_command and not EMBED_DEBUG:
                logger.warning(f"硬件加速编码失败: {str(e)}")
                logger.info("回退到软件编码...")
                logger.debug(f"使用的 FFmpeg 命令: {' '.join(default_command)}")
                run_subprocess(default_command)
                logger.info(f"带字幕的视频已保存到 {output_file}")
            else:
                # 如果软件编码也失败，或者是调试模式的错误，直接抛出异常
                raise

    finally:
        shutil.copy(ass_file, f"{output_file}.ass")
        logger.info(f"生成的ASS文件已保存到 {output_file}.ass")
        os.unlink(ass_file)
        logger.info(f"临时 ASS 文件已删除: {ass_file}")

def generate_output_filenames(video_file, custom_output=None):
    """生成输出文件名
    
    Args:
        video_file: 输入视频文件路径
        custom_output: 自定义输出文件路径，如果提供则使用此路径
    """
    if custom_output:
        # 确保自定义输出也遵循命名规则
        custom_base = os.path.splitext(custom_output)[0]
        if not custom_base.endswith('_sub'):
            custom_base += '_sub'
        return f"{custom_base}.mp4"
        
    base_name = os.path.splitext(os.path.basename(video_file))[0]
    output_dir = os.path.dirname(video_file)
    
    # 处理各种后缀情况
    if base_name.lower().endswith('voice'):
        base_name = base_name[:-5].rstrip('_')
    elif base_name.lower().endswith('video'):
        base_name = base_name[:-5].rstrip('_')
        
    # 如果已经有 _sub 后缀，先移除它
    if base_name.lower().endswith('_sub'):
        base_name = base_name[:-4]
    
    # 统一添加 _sub 后缀
    embedded_output = os.path.join(output_dir, f"{base_name}_sub.mp4")
    return embedded_output

def check_and_adjust_subtitles(srt_file):
    subs = pysubs2.load(srt_file, encoding="utf-8")
    if subs:
        first_sub = subs[0]
        logger.info(f"同步后第一句字幕: 开始={first_sub.start}ms, 结束={first_sub.end}ms, 文本='{first_sub.text}'")
        if first_sub.start < 100:  # 如果第一句字幕开始时间小于 100 毫秒
            logger.warning(f"第一句字幕开始时间过早: {first_sub.start}ms. 调整到 100ms.")
            first_sub.start = 100
    subs.save(srt_file)

def check_subtitle_content(original_srt, synced_srt):
    original_subs = pysubs2.load(original_srt, encoding="utf-8")
    synced_subs = pysubs2.load(synced_srt, encoding="utf-8")

    if original_subs[0].text.strip() != synced_subs[0].text.strip():
        logger.warning("同步后的第一句字幕内容与原始字幕不匹配。使用原始字幕。")
        logger.debug(f"原始字幕第一句: '{original_subs[0].text.strip()}'")
        logger.debug(f"同步后字幕第一句: '{synced_subs[0].text.strip()}'")
        return original_srt
    return synced_srt

def process_video(video_file, srt_sub, language, output_file=None):
    with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt:
        synchronized_subtitles = temp_srt.name
        try:
            subtitles_to_use = srt_sub

            if SYNC_SUBTITLE:
                synchronize_subtitles(video_file, srt_sub, synchronized_subtitles)
                logger.info(f"字幕同步完成: {synchronized_subtitles}")

                # 检查同步后的字幕内容
                subtitles_to_use = check_subtitle_content(srt_sub, synchronized_subtitles)

                # 检查并调整字幕时间戳
                check_and_adjust_subtitles(subtitles_to_use)
            else:
                logger.info("跳过字幕同步和检查步骤")

            embedded_output = generate_output_filenames(video_file, output_file)
            embed_subtitles(video_file, subtitles_to_use, embedded_output, language)
            logger.info(f"字幕已成功嵌入到: {embedded_output}")

        except Exception as e:
            logger.error(f"处理过程中发生错误: {e}")
            logger.error(f"追踪信息: {traceback.format_exc()}")
            sys.exit(1)
        finally:
            # 清理临时文件
            os.unlink(synchronized_subtitles)
            logger.info("临时同步字幕文件已清理")

def main():
    parser = argparse.ArgumentParser(description="同步并将字幕嵌入到视频中")
    parser.add_argument('--video', type=str, required=True, help='视频文件路径')
    parser.add_argument('--sub', type=str, required=True, help='未同步的 SRT 字幕文件路径')
    parser.add_argument('--lang', type=str, default='Chinese', choices=SUBTITLE_CONFIG.keys(), help='字幕语言')
    parser.add_argument('--output', type=str, help='输出文件路径（可选）')

    args = parser.parse_args()

    process_video(args.video, args.sub, args.lang, args.output)

if __name__ == "__main__":
    main()
