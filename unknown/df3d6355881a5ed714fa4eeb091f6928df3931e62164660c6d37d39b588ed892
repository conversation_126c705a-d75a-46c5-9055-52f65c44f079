import os
import time
import requests
import json
import argparse
import base64
from PIL import Image
from config import MINIMAX_API_KEY, logger
from modules.gpt4_interface import call_gpt_json_response

# API 常量定义
API_BASE_URL = "https://api.minimaxi.chat/v1"
VIDEO_GENERATION_ENDPOINT = f"{API_BASE_URL}/video_generation"
QUERY_VIDEO_ENDPOINT = f"{API_BASE_URL}/query/video_generation"
FILE_RETRIEVE_ENDPOINT = f"{API_BASE_URL}/files/retrieve"
PROMPT_BY_GPT = False  # 新增变量，默认为 False

def generate_video_prompt(image_prompt: dict) -> str:
    """Generate video prompt from image prompt"""
    try:
        # Prepare input data
        prompt_data = {
            "image_prompt": json.dumps(image_prompt, ensure_ascii=False)
        }
        
        # Call GPT-4 to generate video prompt
        result = call_gpt_json_response("generate_video_prompt", prompt_data)
        
        if not result:
            logger.error("Failed to generate video prompt")
            return None
            
        video_prompt = result.get("video_prompt")
        
        if not video_prompt:
            logger.error("Could not extract video_prompt from response")
            return None
            
        logger.debug(f"Generated video prompt: {video_prompt}")
        return video_prompt
        
    except Exception as e:
        logger.error(f"Error generating video prompt: {str(e)}")
        return None

def load_json_data(json_file: str, scene_number: str) -> tuple[str, str, dict]:
    """Load image path, text, and prompt from JSON file"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        
    # 将scene_number转换为整数并减1以匹配索引
    scene_idx = int(scene_number) - 1
    if scene_idx < 0 or scene_idx >= len(data['scenes']):
        raise ValueError(f"Scene number not found: {scene_number}")
        
    scene_data = data['scenes'][scene_idx]
    
    # 获取图片路径
    image_path = scene_data['image']
    
    # 获取场景时间
    scene_start_time = scene_data['scene_start_time']
    scene_end_time = scene_data['scene_end_time']
    
    # 组合提示文本
    prompt_parts = []
    
    # 添加旁白内容(如果存在)
    if scene_data.get('narration') and scene_data['narration'].get('content'):
        prompt_parts.append(scene_data['narration']['content'])
    
    # 添加对话内容(如果存在)
    if scene_data.get('dialogues'):
        for dialogue in scene_data['dialogues']:
            if dialogue.get('content'):
                prompt_parts.append(f"{dialogue['character']}: {dialogue['content']}")
    
    # 合并所有提示文本
    text = ' '.join(prompt_parts)
    
    # 获取场景提示
    image_prompt = scene_data['prompt']
    
    logger.debug(f"Scene {scene_number} timing: {scene_start_time} - {scene_end_time}")
    logger.debug(f"Combined prompt text: {text}")
    
    return image_path, text, image_prompt

def validate_and_convert_image(image_path: str) -> str:
    """验证并转换图片格式"""
    try:
        with Image.open(image_path) as img:
            # 检查图片尺寸
            width, height = img.size
            aspect_ratio = width / height
            
            logger.debug(f"图片尺寸: {width}x{height}, 宽高比: {aspect_ratio}")
            logger.debug(f"原始图片格式: {img.format}")
            
            # 检查宽高比（2:5 到 5:2 之间）
            if not (0.4 <= aspect_ratio <= 2.5):
                raise ValueError(f"图片宽高比 {aspect_ratio} 超出允许范围 (0.4-2.5)")
            
            # 检查最短边是否大于300像素
            if min(width, height) < 300:
                raise ValueError(f"图片最短边 {min(width, height)} 像素小于要求的300像素")
            
            # 始终转换为 JPEG 格式
            converted_path = f"{os.path.splitext(image_path)[0]}_converted.jpg"
            img = img.convert('RGB')
            img.save(converted_path, 'JPEG', quality=95)
            logger.debug(f"已转换图片为JPEG格式: {converted_path}")
            return converted_path
            
    except Exception as e:
        logger.error(f"图片验证失败: {str(e)}")
        raise

def encode_image_to_base64(image_path: str) -> str:
    """将图片编码为base64格式，并添加正确的前缀"""
    try:
        with open(image_path, 'rb') as img_file:
            image_data = img_file.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            # 添加数据URI前缀
            return f"data:image/jpeg;base64,{base64_data}"
    except Exception as e:
        logger.error(f"图片base64编码失败: {str(e)}")
        raise

def invoke_video_generation(prompt: str, image_path: str) -> str:
    """Submit video generation task"""
    logger.info("-----------------Submitting Video Generation Task-----------------")
    
    # Validate and convert image
    image_path = validate_and_convert_image(image_path)
    
    # Check file size
    file_size = os.path.getsize(image_path) / (1024 * 1024)  # Convert to MB
    if file_size > 20:
        raise ValueError(f"Image file size ({file_size:.2f}MB) exceeds 20MB limit")
    
    # 修改这部分逻辑
    if PROMPT_BY_GPT:
        video_prompt = generate_video_prompt(prompt)
        if not video_prompt:
            logger.warning("Using original text as video prompt")
            video_prompt = prompt
    else:
        video_prompt = prompt
    
    # Convert image to base64
    image_base64 = encode_image_to_base64(image_path)
    logger.debug(f"Base64 encoding length: {len(image_base64)}")
    
    headers = {
        'authorization': f'Bearer {MINIMAX_API_KEY}',
        'content-type': 'application/json',
    }
    
    payload = {
        "model": "video-01",
        "prompt": video_prompt,
        "first_frame_image": image_base64,
        "prompt_optimizer": True
    }
    
    try:
        logger.debug(f"Sending request to: {VIDEO_GENERATION_ENDPOINT}")
        logger.debug(f"Headers: {headers}")
        logger.debug(f"Prompt: {video_prompt}")
        logger.debug(f"Image path: {image_path}")
        logger.debug(f"Image size: {file_size:.2f}MB")
        
        response = requests.post(VIDEO_GENERATION_ENDPOINT, headers=headers, json=payload)
        logger.debug(f"API response status code: {response.status_code}")
        logger.debug(f"API response content: {response.text}")
        
        response.raise_for_status()
        
        task_id = response.json()['task_id']
        logger.info(f"Video generation task submitted, Task ID: {task_id}")
        return task_id
    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed: {str(e)}")
        raise
    except KeyError as e:
        logger.error(f"Could not get task_id from response: {str(e)}")
        logger.error(f"Full response: {response.text}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error occurred: {str(e)}")
        raise

def query_video_generation(task_id: str) -> tuple[str, str]:
    """查询视频生成任务状态"""
    url = f"{QUERY_VIDEO_ENDPOINT}?task_id={task_id}"
    
    headers = {
        'authorization': f'Bearer {MINIMAX_API_KEY}',
        'content-type': 'application/json',
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        status = response.json()['status']
        logger.debug(f"任务状态: {status}")
        
        if status == 'Success':
            return response.json()['file_id'], "Finished"
        elif status in ['Queueing', 'Processing']:
            logger.info(f"...{status}...")
            return "", status
        else:
            logger.error(f"任务失败，状态: {status}")
            return "", "Failed"
    except Exception as e:
        logger.error(f"查询任务状态时发生错误: {str(e)}")
        raise

def fetch_video_result(file_id: str, output_path: str) -> str:
    """获取并下载生成的视频，返回下载URL"""
    logger.info("---------------视频生成成功，正在下载---------------")
    url = f"{FILE_RETRIEVE_ENDPOINT}?file_id={file_id}"
    
    headers = {
        'authorization': f'Bearer {MINIMAX_API_KEY}',
        'content-type': 'application/json',
    }
    
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    
    download_url = response.json()['file']['download_url']
    logger.info(f"视频下载链接：{download_url}")
    
    video_response = requests.get(download_url)
    video_response.raise_for_status()
    
    with open(output_path, 'wb') as f:
        f.write(video_response.content)
    logger.info(f"视频已下载到：{os.path.abspath(output_path)}")
    
    return download_url

def get_default_output_path(json_file: str, scene_number: str) -> str:
    """根据JSON文件路径生成默认的输出视频路径"""
    json_dir = os.path.dirname(json_file)
    video_dir = os.path.join(json_dir, 'video')
    return os.path.join(video_dir, f"scene_{scene_number}.mp4")

def main():
    parser = argparse.ArgumentParser(description='Generate video from JSON file image and text')
    parser.add_argument('--json', required=True, help='JSON file path')
    parser.add_argument('--scene_number', required=True, help='Scene number (e.g., 1)')
    parser.add_argument('--output', help='Output video file path')
    
    args = parser.parse_args()
    
    try:
        # Check API key
        if not MINIMAX_API_KEY:
            logger.error("MINIMAX_API_KEY not set")
            raise ValueError("MINIMAX_API_KEY not set")
            
        logger.info(f"Processing JSON file: {args.json}")
        logger.info(f"Scene number: {args.scene_number}")
        
        # Use default path if output not specified
        output_path = args.output or get_default_output_path(args.json, args.scene_number)
        logger.info(f"Output path: {output_path}")
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Load data
        image_path, text, image_prompt = load_json_data(args.json, args.scene_number)
        logger.info(f"Loaded image path: {image_path}")
        logger.info(f"Loaded text content: {text}")
        logger.info(f"Loaded image prompt: {json.dumps(image_prompt, ensure_ascii=False)}")
        
        # Check if image file exists
        if not os.path.exists(image_path):
            logger.error(f"Image file does not exist: {image_path}")
            raise FileNotFoundError(f"Image file does not exist: {image_path}")
        
        # Submit task - 根据 PROMPT_BY_GPT 决定使用哪个提示
        prompt_to_use = image_prompt if PROMPT_BY_GPT else text
        task_id = invoke_video_generation(prompt_to_use, image_path)
        
        # Poll task status
        while True:
            time.sleep(30)
            file_id, status = query_video_generation(task_id)
            
            if file_id:
                download_url = fetch_video_result(file_id, output_path)
                logger.info("---------------Processing Successful---------------")
                logger.info(f"Video URL: {download_url}")
                logger.info(f"Video file: {output_path}")
                break
            elif status in ["Failed", "Unknown"]:
                logger.error("---------------Processing Failed---------------")
                break
                
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise

if __name__ == '__main__':
    main() 