import os
import sys
import logging
import requests
from typing import List, Dict, Optional
from urllib.parse import urljoin
from dotenv import load_dotenv
import time
import json
import cv2
import base64
import openai
import ffmpeg

# 从 .env 文件加载环境变量
load_dotenv()

def get_config() -> Dict[str, str]:
    """
    从环境变量获取配置。

    :return: 包含配置参数的字典。
    """
    return {
        "JSON_OUTPUT": os.getenv("JSON_OUTPUT", "download_metadata.json"),
        "JSON_DESCRIPTION_OUTPUT": os.getenv("JSON_DESCRIPTION_OUTPUT", "download_metadata_with_descriptions.json"),
        "PREVIEW_IMAGES_DIR": os.getenv("PREVIEW_IMAGES_DIR", "preview_images"),
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
        "FRAME_STEP": int(os.getenv("FRAME_STEP", "50")),  # 要跳过的帧数
        "FRAMES_PER_VIDEO": int(os.getenv("FRAMES_PER_VIDEO", "10")),  # 要选择的帧数
        "DESCRIPTION_MAX_TOKENS": int(os.getenv("DESCRIPTION_MAX_TOKENS", "200")),
        "DESCRIPTION_TIMEOUT": int(os.getenv("DESCRIPTION_TIMEOUT", "60")),  # 以秒为单位
        "LOG_LEVEL": os.getenv("LOG_LEVEL", "INFO")
    }

def setup_logging(log_level: str):
    """
    配置日志设置。

    :param log_level: 日志级别字符串。
    """
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def extract_frames(video_path: str, frame_step: int, frames_per_video: int) -> List[str]:
    """
    在指定间隔提取视频帧并以 base64 编码。

    :param video_path: 视频文件的路径。
    :param frame_step: 每次提取之间要跳过的帧数。
    :param frames_per_video: 要提取的总帧数。
    :return: base64 编码的帧图像列表。
    """
    logger = logging.getLogger("extract_frames")
    logger.info(f"正在从视频提取帧：{video_path}")
    frames = []
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件：{video_path}")
            return frames

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        logger.debug(f"视频总帧数：{total_frames}")
        extracted = 0
        frame_id = 0

        while extracted < frames_per_video and frame_id < total_frames:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_id)
            ret, frame = cap.read()
            if not ret:
                logger.warning(f"无法读取位置 {frame_id} 的帧")
                break

            _, buffer = cv2.imencode(".jpg", frame)
            encoded_frame = base64.b64encode(buffer).decode("utf-8")
            frames.append(encoded_frame)
            logger.debug(f"已提取帧 {frame_id}")
            extracted += 1
            frame_id += frame_step

        cap.release()
        logger.info(f"已从视频提取 {len(frames)} 帧。")
    except Exception as e:
        logger.error(f"从视频 {video_path} 提取帧时出错：{e}")

    return frames

def generate_description(frames: List[str], openai_api_key: str, max_tokens: int) -> Optional[str]:
    """
    使用 OpenAI 的 GPT-4o 模型生成视频描述。

    :param frames: base64 编码的帧图像列表。
    :param openai_api_key: OpenAI API 密钥。
    :param max_tokens: 响应的最大令牌数。
    :return: 生成的描述或 None。
    """
    logger = logging.getLogger("generate_description")
    if not frames:
        logger.warning("未提供帧用于生成描述。")
        return None

    try:
        openai.api_key = openai_api_key
        prompt_content = [
            "这些是视频中的帧。生成一个引人入胜的描述，我可以将其与视频一起上传。"
        ]

        for frame in frames:
            prompt_content.append({
                "image": frame,
                "resize": 768
            })

        prompt = [
            {
                "role": "user",
                "content": prompt_content
            }
        ]

        logger.info("正在向 OpenAI API 发送请求以生成描述。")
        response = openai.ChatCompletion.create(
            model="gpt-4-vision-preview",
            messages=prompt,
            max_tokens=max_tokens,
        )

        description = response.choices[0].message.content.strip()
        logger.info("已成功生成描述。")
        return description

    except openai.error.OpenAIError as e:
        logger.error(f"OpenAI API 错误：{e}")
    except Exception as e:
        logger.error(f"生成描述期间发生意外错误：{e}")

    return None

def save_metadata_to_json(metadata: List[Dict], json_output: str):
    """
    将元数据列表保存到 JSON 文件。

    :param metadata: 元数据字典列表。
    :param json_output: JSON 输出文件的路径。
    """
    logger = logging.getLogger("save_metadata_to_json")
    try:
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=4)
        logger.info(f"带描述的元数据已保存到 JSON 文件：{json_output}")
    except Exception as e:
        logger.error(f"保存元数据到 JSON 文件失败：{e}")

def process_videos(config: Dict[str, str]) -> bool:
    """
    处理每个视频以生成描述并更新元数据。

    :param config: 配置字典。
    :return: 如果所有描述都成功生成则为 True，否则为 False。
    """
    logger = logging.getLogger("process_videos")

    json_input = config["JSON_OUTPUT"]
    json_output = config["JSON_DESCRIPTION_OUTPUT"]
    preview_dir = config["PREVIEW_IMAGES_DIR"]
    frame_step = config["FRAME_STEP"]
    frames_per_video = config["FRAMES_PER_VIDEO"]
    openai_api_key = config["OPENAI_API_KEY"]
    max_tokens = config["DESCRIPTION_MAX_TOKENS"]
    timeout = config["DESCRIPTION_TIMEOUT"]

    if not openai_api_key:
        logger.error("环境变量中未设置 OpenAI API 密钥。")
        return False

    if not os.path.exists(json_input):
        logger.error(f"元数据 JSON 文件不存在：{json_input}")
        return False

    try:
        with open(json_input, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
    except Exception as e:
        logger.error(f"读取元数据 JSON 文件失败：{e}")
        return False

    for index, entry in enumerate(metadata, start=1):
        video_path = entry.get("file_path")
        if not video_path or not os.path.exists(video_path):
            logger.warning(f"视频文件不存在：{video_path}")
            continue

        logger.info(f"正在处理视频 {index}：{video_path}")

        # 提取帧
        frames = extract_frames(video_path, frame_step, frames_per_video)

        # 生成描述
        description = generate_description(frames, openai_api_key, max_tokens)
        if description:
            entry["description"] = description
        else:
            logger.warning(f"视频描述生成失败：{video_path}")
            entry["description"] = ""

    # 保存更新后的元数据
    save_metadata_to_json(metadata, json_output)

    logger.info("视频描述生成过程已完成。")
    return True

def main():
    """
    通过命令行执行脚本的主函数。
    """
    config = get_config()
    setup_logging(config["LOG_LEVEL"])
    logger = logging.getLogger("Main")

    try:
        # 步骤 1：处理视频
        success = process_videos(config)
        if success:
            logger.info("所有视频描述已成功生成并更新元数据。")
        else:
            logger.warning("处理完成但存在一些问题。请查看日志了解详情。")
    except KeyboardInterrupt:
        logger.info("用户中断进程。")

if __name__ == "__main__":
    main() 