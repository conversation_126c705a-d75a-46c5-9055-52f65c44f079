#!/bin/bash

# 检查是否提供了目录参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

# 设置变量并正确处理路径
WORK_DIR="${1%/}"  # 移除末尾的斜杠
THEME_NAME=$(basename "$WORK_DIR" | sed 's/\\//g')  # 移除反斜杠
echo "Debug: Working directory: $WORK_DIR"
echo "Debug: Theme name: $THEME_NAME"

# 设置远程服务器信息
SERVER_USER="azureuser"
SERVER_HOST="**************"
REMOTE_BASE_DIR="/home/<USER>/source-video-clips"
REMOTE_CLIPS_DIR="$REMOTE_BASE_DIR/outputs/${THEME_NAME}/clips"
REMOTE_METADATA="$REMOTE_BASE_DIR/outputs/${THEME_NAME}/clips/${THEME_NAME}_metadata.json"

# 检查工作目录和clips目录是否存在
if [ ! -d "$WORK_DIR" ]; then
    echo "Error: Directory '$WORK_DIR' does not exist."
    exit 1
fi

LOCAL_CLIPS_DIR="$WORK_DIR/clips"
if [ ! -d "$LOCAL_CLIPS_DIR" ]; then
    echo "Error: Clips directory '$LOCAL_CLIPS_DIR' does not exist."
    exit 1
fi

# 同步本地clips到远程服务器（包括删除操作）
echo "Syncing clips to remote server..."
rsync -avz --delete --progress -e "ssh -i ~/.ssh/videoadmin.pem" \
    "$LOCAL_CLIPS_DIR/" \
    "$SERVER_USER@$SERVER_HOST:$REMOTE_CLIPS_DIR/"

# 更新远程服务器上的metadata.json
echo "Updating metadata.json..."
ssh -i ~/.ssh/videoadmin.pem "$SERVER_USER@$SERVER_HOST" "python3 -c '
import json
import os
import pathlib

try:
    metadata_file = \"$REMOTE_METADATA\"
    clips_dir = \"$REMOTE_CLIPS_DIR\"

    print(f\"Reading metadata from: {metadata_file}\")
    
    with open(metadata_file, \"r\", encoding=\"utf-8\") as f:
        metadata = json.load(f)
        print(f\"Successfully loaded metadata file\")

    existing_clips = {f.name for f in pathlib.Path(clips_dir).glob(\"*.mp4\")}
    print(f\"Found {len(existing_clips)} clips in directory\")

    updated_segments = []
    for segment in metadata.get(\"segments\", []):
        clip_path = segment.get(\"clip_path\", \"\")
        if not clip_path:
            continue
        clip_filename = os.path.basename(clip_path)
        if clip_filename in existing_clips:
            updated_segments.append(segment)

    for i, segment in enumerate(updated_segments, 1):
        segment[\"segment_number\"] = i

    metadata[\"segments\"] = updated_segments

    with open(metadata_file, \"w\", encoding=\"utf-8\") as f:
        json.dump(metadata, f, indent=4, ensure_ascii=False)

    print(f\"Metadata updated successfully. {len(updated_segments)} segments remain.\")

except Exception as e:
    print(f\"Error occurred: {str(e)}\")
    print(f\"Error type: {type(e).__name__}\")
    raise
'"

if [ $? -eq 0 ]; then
    echo "Sync completed! Metadata has been updated."
else
    echo "Error occurred while updating metadata!"
    exit 1
fi 