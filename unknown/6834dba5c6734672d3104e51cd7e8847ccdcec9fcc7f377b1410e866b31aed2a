# Core Libraries
torch
whisper @ git+https://github.com/openai/whisper.git
pyscenedetect[opencv]
nltk
sentence-transformers
faiss-cpu
ffmpeg-python
# Or faiss-gpu if CUDA is available and preferred

# Models & Specific Functionality (Potentially large, manage installation carefully)
# For InternVideo2-CLIP, installation might involve cloning a repo and installing locally.
# Placeholder for InternVideo2-CLIP, actual installation may vary:
# internvideo2_clip @ git+https://example.com/internvideo2_clip.git
# For whisper-timestamped or stable-ts:
# whisper-timestamped @ git+https://github.com/linto-ai/whisper-timestamped.git
# stable-ts @ git+https://github.com/jianfch/stable-ts.git


# Parameter Tuning & ML Utilities
scikit-learn

# Web UI & Visualization (Optional, for development/debugging)
matplotlib
streamlit
gradio

# Database
sqlite3

# General Utilities
numpy
pandas
tqdm
python-dotenv 