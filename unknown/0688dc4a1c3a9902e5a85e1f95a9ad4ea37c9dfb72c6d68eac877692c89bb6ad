import json
import subprocess
import argparse
import os
from moviepy.editor import Video<PERSON>ileClip, concatenate_videoclips
from config import logger

def parse_arguments():
    parser = argparse.ArgumentParser(description="替换视频中的指定片段。")
    parser.add_argument("--json", required=True, help="包含时间信息的JSON文件路径")
    parser.add_argument("--video", required=True, help="原始视频文件路径")
    parser.add_argument("--scene_number", required=True, help="要替换的场景编号，例如'1'")
    parser.add_argument("--clip", required=True, help="用于替换的视频片段路径")
    parser.add_argument("--output", help="输出视频文件路径")
    args = parser.parse_args()
    
    # 如果未指定输出路径，则使用默认值
    if args.output is None:
        video_dir = os.path.dirname(args.video)
        video_name = os.path.splitext(os.path.basename(args.video))[0]
        args.output = os.path.join(video_dir, f"{video_name}_video.mp4")
    
    return args

def get_clip_timing(json_file, scene_number):
    """从JSON文件中获取指定场景的时间信息"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 将scene_number转换为整数并减1以匹配索引
        scene_idx = int(scene_number) - 1
        if scene_idx < 0 or scene_idx >= len(data['scenes']):
            raise KeyError(f"在JSON文件中找不到场景 {scene_number}")
            
        scene_data = data['scenes'][scene_idx]
        return {
            'start_time': scene_data['scene_start_time'],
            'end_time': scene_data['scene_end_time']
        }
        
    except FileNotFoundError:
        logger.error(f"找不到JSON文件：{json_file}")
        raise
    except json.JSONDecodeError:
        logger.error(f"JSON文件格式错误：{json_file}")
        raise
    except KeyError as e:
        logger.error(str(e))
        raise

def validate_files(video_path, clip_path):
    """验证视频文件是否存在"""
    if not os.path.isfile(video_path):
        raise FileNotFoundError(f"找不到原始视频文件：{video_path}")
    if not os.path.isfile(clip_path):
        raise FileNotFoundError(f"找不到替换用的视频片段：{clip_path}")

def replace_video_clip(args):
    try:
        # 1. 验证文件
        validate_files(args.video, args.clip)
        
        # 2. 检查原始视频是否有音频并提取
        has_audio = len(subprocess.check_output([
            'ffprobe', '-i', args.video,
            '-show_streams', '-select_streams', 'a', 
            '-loglevel', 'error'
        ])) > 0

        temp_audio = None
        if has_audio:
            # 提取音频到临时文件
            temp_audio = args.output + '.temp_audio.aac'
            subprocess.run([
                'ffmpeg', '-y',
                '-i', args.video,
                '-vn',  # 不处理视频
                '-acodec', 'copy',  # 直接复制音频编码
                '-loglevel', 'error',
                temp_audio
            ], check=True)

        # 3. 获取视频时长和分辨率
        original_video = VideoFileClip(args.video)
        clip_video = VideoFileClip(args.clip)
        original_duration = original_video.duration
        clip_duration = clip_video.duration
        original_width = original_video.w
        original_height = original_video.h
        original_video.close()
        clip_video.close()
        
        # 4. 获取时间信息
        timing = get_clip_timing(args.json, args.scene_number)
        start_time = timing['start_time']
        end_time = timing['end_time']
        target_duration = end_time - start_time
        
        # 记录时长信息
        logger.info(f"原始视频时长: {original_duration:.2f}s")
        logger.info(f"替换片段时长: {clip_duration:.2f}s")
        logger.info(f"目标时长: {target_duration:.2f}s")

        # 5. 处理视频替换（不处理音频）
        filter_complex = []
        
        if abs(clip_duration - target_duration) > 0.1:
            if clip_duration > target_duration:
                filter_complex.append(
                    f'[1:v]scale={original_width}:{original_height}:force_original_aspect_ratio=decrease,'
                    f'pad={original_width}:{original_height}:-1:-1:color=black,'
                    f'trim=0:{target_duration},setpts=PTS-STARTPTS[v2]'
                )
            else:
                filter_complex.extend([
                    f'[1:v]scale={original_width}:{original_height}:force_original_aspect_ratio=decrease,'
                    f'pad={original_width}:{original_height}:-1:-1:color=black,'
                    f'trim=0:{clip_duration},setpts=PTS-STARTPTS[v2_main]',
                    
                    f'[1:v]scale={original_width}:{original_height}:force_original_aspect_ratio=decrease,'
                    f'pad={original_width}:{original_height}:-1:-1:color=black,'
                    f'trim={clip_duration-0.1}:{clip_duration},'
                    f'setpts=PTS-STARTPTS,'
                    f'loop=loop={int((target_duration-clip_duration)*25)}:size=1:start=0[v2_loop]',
                    
                    f'[v2_main][v2_loop]concat=n=2:v=1:a=0[v2]'
                ])
        else:
            filter_complex.append(
                f'[1:v]scale={original_width}:{original_height}:force_original_aspect_ratio=decrease,'
                f'pad={original_width}:{original_height}:-1:-1:color=black[v2]'
            )

        if end_time < original_duration:
            filter_complex.append(
                f'[0:v]trim=start={end_time},setpts=PTS-STARTPTS[v3]'
            )
            filter_complex.append(
                '[v2][v3]concat=n=2:v=1:a=0[outv]'
            )
        else:
            filter_complex.append(
                '[v2]null[outv]'
            )

        # 6. 生成无音频的替换后视频
        temp_video = args.output + '.temp_video.mp4'
        filter_complex_str = ';'.join(filter_complex)
        
        subprocess.run([
            'ffmpeg', '-y',
            '-i', args.video,
            '-i', args.clip,
            '-hide_banner',
            '-loglevel', 'error',
            '-stats',
            '-stats_period', '10',
            '-filter_complex', filter_complex_str,
            '-map', '[outv]',
            '-c:v', 'libx264',
            '-preset', 'fast',
            '-threads', '0',
            '-movflags', '+faststart',
            '-crf', '23',
            temp_video
        ], check=True)

        # 7. 如果有音频，合并音频和视频
        if has_audio:
            subprocess.run([
                'ffmpeg', '-y',
                '-i', temp_video,
                '-i', temp_audio,
                '-c:v', 'copy',  # 直接复制视频流
                '-c:a', 'copy',  # 直接复制音频流
                '-loglevel', 'error',
                args.output
            ], check=True)
            
            # 删除临时文件
            os.remove(temp_audio)
            os.remove(temp_video)
        else:
            # 如果没有音频，直接重命名临时视频文件
            os.rename(temp_video, args.output)
            
        logger.info(f"视频替换完成：{args.output}")

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg 命令执行失败：{str(e)}")
        raise
    finally:
        # 清理临时文件
        if 'temp_audio' in locals() and temp_audio and os.path.exists(temp_audio):
            os.remove(temp_audio)
        if 'temp_video' in locals() and temp_video and os.path.exists(temp_video):
            os.remove(temp_video)

if __name__ == "__main__":
    args = parse_arguments()
    replace_video_clip(args) 