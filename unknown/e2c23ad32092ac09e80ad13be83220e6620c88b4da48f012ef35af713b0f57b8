import os
import sys
import logging
import requests
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
from urllib.parse import urljoin
from dotenv import load_dotenv
import time
import argparse
import json
import ffmpeg
from config import VIDEO_DIR, logger
from modules.pexels_video_downloader import process_keyword
from modules.direct_downloader import BaseDownloader, DirectDownloader
from modules.jdownloader import JDownloader

# 从 .env 文件加载环境变量
load_dotenv()

# 全局变量，用于控制是否使用 Selenium
USE_SELENIUM = os.getenv("USE_SELENIUM", "False").lower() in ['true', '1', 't']

# 仅在需要使用 Selenium 时导入相关模块
if USE_SELENIUM:
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        SELENIUM_AVAILABLE = True
    except ImportError:
        SELENIUM_AVAILABLE = False
        logger.warning("Selenium 相关模块导入失败，将使用基本的请求方式。")
else:
    SELENIUM_AVAILABLE = False

def get_config(theme: Optional[str] = None) -> Dict[str, str]:
    """
    从环境变量获取配置。

    :param theme: 可选的主题名称，用于设置主题特定的路径
    :return: 包含配置参数的字典。
    """
    config = {
        "JD_API_URL": os.getenv("JD_API_URL", "https://api.jdownloader.org"),
        "JD_EMAIL": os.getenv("JD_EMAIL"),
        "JD_PASSWORD": os.getenv("JD_PASSWORD"),
        "DOWNLOAD_DESTINATION": os.getenv("DOWNLOAD_DESTINATION", "/downloads"),
        "LOG_LEVEL": os.getenv("LOG_LEVEL", "INFO"),
        "USE_SELENIUM": USE_SELENIUM,
        "CHROMEDRIVER_PATH": os.getenv("CHROMEDRIVER_PATH", ""),
        "JSON_OUTPUT": os.getenv("JSON_OUTPUT", "download_metadata.json"),
        "PREVIEW_IMAGES_DIR": os.getenv("PREVIEW_IMAGES_DIR", "preview_images"),
        "DOWNLOADER_TYPE": os.getenv("DOWNLOADER_TYPE", "direct"),
    }

    # 如果提供了主题，设置主题特定的路径
    if theme:
        base_dir = VIDEO_DIR.format(theme=theme)
        preview_dir = os.path.join(base_dir, "preview_images")
        
        # 创建必要的目录
        os.makedirs(base_dir, exist_ok=True)
        os.makedirs(preview_dir, exist_ok=True)
        
        # 更新配置中的路径
        config["DOWNLOAD_DESTINATION"] = base_dir
        config["PREVIEW_IMAGES_DIR"] = preview_dir
        config["JSON_OUTPUT"] = os.path.join(base_dir, "download_metadata.json")

    return config

def init_webdriver(use_selenium: bool, chromedriver_path: str) -> Optional[object]:
    """
    如果需要，初始化 Selenium WebDriver。

    :param use_selenium: 是否使用 Selenium 的布尔值。
    :param chromedriver_path: ChromeDriver 可执行文件的路径。
    :return: Selenium WebDriver 实例或 None。
    """
    if not use_selenium or not SELENIUM_AVAILABLE:
        return None

    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        if chromedriver_path:
            service = Service(executable_path=chromedriver_path)
        else:
            service = Service()
        driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.debug("已初始化 Selenium WebDriver。")
        return driver
    except Exception as e:
        logger.error(f"初始化 WebDriver 失败：{e}")
        return None

def is_video_url(url: str) -> bool:
    """判断 URL 是否指向视频。"""
    # 视频文件扩展名
    video_extensions = ('.mp4', '.webm', '.ogg', '.m3u8', '.mov', '.avi', '.wmv', '.flv')
    # 视频平台域名
    video_platforms = ('youtube.com', 'vimeo.com', 'dailymotion.com', 'youku.com')
    
    url_lower = url.lower()
    return (
        url_lower.endswith(video_extensions) or
        any(platform in url_lower for platform in video_platforms) or
        'video' in url_lower or
        'player' in url_lower
    )

def scrape_video_urls(url: str, use_selenium: bool, chromedriver_path: str, config: Dict[str, str]) -> List[Dict[str, Optional[str]]]:
    """
    从给定的 URL 抓取视频链接。
    """
    video_entries = []
    driver = None
    
    try:
        # 获取页面内容
        if use_selenium and SELENIUM_AVAILABLE:
            driver = init_webdriver(use_selenium, chromedriver_path)
            if not driver:
                logger.error("无法初始化 Selenium WebDriver")
                return []
            driver.get(url)
            html_content = driver.page_source
        else:
            response = requests.get(url, timeout=15)
            response.raise_for_status()
            html_content = response.text

        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找所有视频标签
        videos = soup.find_all('video')
        
        # 处理视频标签
        for video in videos:
            video_data = {
                "video_url": None,
                "surround_text": "",
                "preview_image_url": None,
                "preview_image_path": None,
                "file_path": None,
                "codec_info": None
            }
            
            # 获取视频 URL
            if video.get('src'):
                video_data["video_url"] = urljoin(url, video['src'])
            else:
                source = video.find('source')
                if source and source.get('src'):
                    video_data["video_url"] = urljoin(url, source['src'])
            
            if video_data["video_url"] and is_video_url(video_data["video_url"]):
                # 获取周围文本
                parent = video.find_parent()
                if parent:
                    video_data["surround_text"] = parent.get_text(strip=True)
                    
                # 获取预览图片
                if video.get('poster'):
                    video_data["preview_image_url"] = urljoin(url, video['poster'])
                    
                video_entries.append(video_data)
                logger.debug(f"找到视频：{video_data['video_url']}")
        
        # 处理 iframe 标签
        iframes = soup.find_all('iframe')
        for iframe in iframes:
            if iframe.get('src'):
                iframe_src = iframe['src']
                if is_video_url(iframe_src):
                    video_data = {
                        "video_url": urljoin(url, iframe_src),
                        "surround_text": iframe.find_parent().get_text(strip=True) if iframe.find_parent() else "",
                        "preview_image_url": None,
                        "preview_image_path": None,
                        "file_path": None,
                        "codec_info": None
                    }
                    video_entries.append(video_data)
                    logger.debug(f"找到 iframe 视频：{iframe_src}")

        logger.info(f"找到 {len(video_entries)} 个视频条目。")
        return video_entries

    except requests.RequestException as e:
        logger.error(f"HTTP 请求失败：{e}")
    except Exception as e:
        logger.error(f"抓取视频时出错：{e}")
    finally:
        if driver:
            driver.quit()
            logger.debug("已关闭 Selenium WebDriver。")
    
    return video_entries

def download_preview_image(preview_image_url: str, save_dir: str, video_index: int) -> Optional[str]:
    """
    下载预览图片并本地保存。

    :param preview_image_url: 预览图片的 URL。
    :param save_dir: 保存预览图片的目录。
    :param video_index: 用于命名图片的视频索引。
    :return: 下载的图片的本地文件路径或 None。
    """
    logger = logging.getLogger("download_preview_image")
    try:
        response = requests.get(preview_image_url, stream=True, timeout=15)
        response.raise_for_status()
        image_extension = os.path.splitext(preview_image_url)[1].split('?')[0]  # 移除查询参数
        if not image_extension:
            image_extension = ".jpg"  # 默认扩展名
        image_filename = f"preview_{video_index}{image_extension}"
        image_path = os.path.join(save_dir, image_filename)
        with open(image_path, 'wb') as f:
            for chunk in response.iter_content(1024):
                f.write(chunk)
        logger.info(f"已下载预览图片：{image_path}")
        return image_path
    except requests.RequestException as e:
        logger.error(f"从 {preview_image_url} 下载预览图片失败：{e}")
    except Exception as e:
        logger.error(f"下载预览图片时发生意外错误：{e}")
    return None

def authenticate_jdownloader(api_url: str, email: str, password: str) -> Optional[str]:
    """
    通过 JDownloader API 进行身份验证以获取会话令牌。

    :param api_url: JDownloader API 的基础 URL。
    :param email: 用户的 JDownloader 邮箱。
    :param password: 用户的 JDownloader 密码。
    :return: 如果身份验证成功则返回会话令牌，否则返回 None。
    """
    logger = logging.getLogger("authenticate_jdownloader")
    logger.info("正在通过 JDownloader API 进行身份验证。")
    payload = {
        "email": email,
        "password": password
    }
    try:
        response = requests.post(f"{api_url}/myjd/auth", json=payload, timeout=15)
        response.raise_for_status()
        data = response.json()
        session_token = data.get("sessiontoken")
        if session_token:
            logger.info("已成功通过 JDownloader 身份验证。")
            return session_token
        else:
            logger.error("身份验证失败：未收到会话令牌。")
    except requests.RequestException as e:
        logger.error(f"身份验证请求失败：{e}")
    except ValueError:
        logger.error("身份验证期间收到无效的 JSON 响应。")
    return None

def add_download_link(api_url: str, session_token: str, video_url: str, destination_folder: str) -> Optional[str]:
    """
    将视频 URL 添加到 JDownloader 进行下载。

    :param api_url: JDownloader API 的基础 URL。
    :param session_token: 已验证的会话令牌。
    :param video_url: 要下载的视频 URL。
    :param destination_folder: 下载目标文件夹。
    :return: 如果成功则返回下载 ID，否则返回 None。
    """
    logger = logging.getLogger("add_download_link")
    headers = {
        "Authorization": f"Bearer {session_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "urls": [video_url],
        "packageName": "Downloaded Videos",
        "destinationFolder": destination_folder
    }
    try:
        response = requests.post(f"{api_url}/myjd/addLinks", json=payload, headers=headers, timeout=15)
        response.raise_for_status()
        data = response.json()
        download_id = data.get("downloadIds")
        if download_id:
            logger.info(f"已成功将视频添加到 JDownloader：{video_url}")
            return download_id
        else:
            logger.warning(f"已将视频添加到 JDownloader 但未返回下载 ID：{video_url}")
            return None
    except requests.RequestException as e:
        logger.error(f"将视频添加到 JDownloader 失败：{e}")
    except ValueError:
        logger.error("添加下载链接时收到无效的 JSON 响应。")
    return None

def get_jdownloader_downloads(api_url: str, session_token: str) -> Optional[List[Dict]]:
    """
    从 JDownloader 获取当前下载列表。

    :param api_url: JDownloader API 的基础 URL。
    :param session_token: 已验证的会话令牌。
    :return: 下载字典列表或 None。
    """
    logger = logging.getLogger("get_jdownloader_downloads")
    headers = {
        "Authorization": f"Bearer {session_token}"
    }
    try:
        response = requests.get(f"{api_url}/myjd/downloads", headers=headers, timeout=15)
        response.raise_for_status()
        downloads = response.json().get("downloads")
        if downloads is not None:
            logger.debug(f"从 JDownloader 获取了 {len(downloads)} 个下载。")
            return downloads
        else:
            logger.warning("在 JDownloader 响应中未找到下载。")
            return None
    except requests.RequestException as e:
        logger.error(f"从 JDownloader 获取下载失败：{e}")
    except ValueError:
        logger.error("获取下载时收到无效的 JSON 响应。")
    return None

def monitor_downloads(api_url: str, session_token: str, video_urls: List[str], timeout: int = 600) -> Dict[str, Optional[str]]:
    """
    监控下载直到完成并获取文件路径。

    :param api_url: JDownloader API 的基础 URL。
    :param session_token: 已验证的会话令牌。
    :param video_urls: 正在下载的视频 URL 列表。
    :param timeout: 等待下载的最长时间（秒）。
    :return: 将视频 URL 映射到其下载文件路径的字典。
    """
    logger = logging.getLogger("monitor_downloads")
    start_time = time.time()
    video_to_file = {url: None for url in video_urls}
    logger.info("开始监控下载...")

    while time.time() - start_time < timeout:
        downloads = get_jdownloader_downloads(api_url, session_token)
        if downloads is None:
            logger.warning("未获取到下载。重试中...")
        else:
            for download in downloads:
                link = download.get("link")
                status = download.get("status")
                file_path = download.get("filePath")
                if link in video_to_file and status == "FINISHED":
                    video_to_file[link] = file_path
                    logger.info(f"下载完成：{link} -> {file_path}")

        # 检查是否所有下载都已完成
        if all(path is not None for path in video_to_file.values()):
            logger.info("所有下载已完成。")
            return video_to_file

        logger.debug("等待下载完成...")
        time.sleep(10)  # 轮询前等待

    logger.warning("下载监控超时。")
    return video_to_file

def extract_codec_info(video_path: str) -> Optional[Dict[str, str]]:
    """
    使用 FFmpeg 从视频文件中提取编解码器相关信息。

    :param video_path: 视频文件的路径。
    :return: 包含编解码器信息的字典或 None。
    """
    logger = logging.getLogger("extract_codec_info")
    try:
        probe = ffmpeg.probe(video_path)
        video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        audio_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'audio'), None)

        codec_info = {}
        if video_stream:
            codec_info['video_codec'] = video_stream.get('codec_name')
            codec_info['width'] = video_stream.get('width')
            codec_info['height'] = video_stream.get('height')
            codec_info['frame_rate'] = video_stream.get('r_frame_rate')
            codec_info['bit_rate'] = video_stream.get('bit_rate')
            codec_info['duration'] = video_stream.get('duration')
        if audio_stream:
            codec_info['audio_codec'] = audio_stream.get('codec_name')
            codec_info['audio_sample_rate'] = audio_stream.get('sample_rate')
            codec_info['audio_channels'] = audio_stream.get('channels')
            codec_info['audio_bit_rate'] = audio_stream.get('bit_rate')

        logger.debug(f"已提取 {video_path} 的编解码器信息：{codec_info}")
        return codec_info
    except ffmpeg.Error as e:
        logger.error(f"提取编解码器信息时发生 FFmpeg 错误：{e}")
    except Exception as e:
        logger.error(f"提取编解码器信息时发生意外错误：{e}")
    return None

def save_metadata_to_json(metadata: List[Dict], json_output: str):
    """
    将元数据列表保存到 JSON 文件。

    :param metadata: 元数据字典列表。
    :param json_output: JSON 输出文件的路径。
    """
    logger = logging.getLogger("save_metadata_to_json")
    try:
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=4)
        logger.info(f"元数据已保存到 JSON 文件：{json_output}")
    except Exception as e:
        logger.error(f"保存元数据到 JSON 文件失败：{e}")

def get_downloader(config: Dict) -> BaseDownloader:
    """根据配置选择下载器"""
    downloader_type = config.get("DOWNLOADER_TYPE", "direct")
    destination = config.get("DOWNLOAD_DESTINATION", "downloads")
    
    if downloader_type == "jdownloader":
        return JDownloader(
            api_url=config["JD_API_URL"],
            email=config["JD_EMAIL"],
            password=config["JD_PASSWORD"],
            destination=destination
        )
    else:
        return DirectDownloader(destination=destination)

def process_web_url(url: str, config: Dict[str, str]) -> bool:
    """
    处理网页 URL 的接口函数

    :param url: 要处理的网页 URL
    :param config: 配置字典
    :return: 处理是否成功的布尔值
    """
    # 创建下载器实例
    downloader = get_downloader(config)
    
    # 使用配置中的下载目录
    download_dir = config["DOWNLOAD_DESTINATION"]
    
    # 抓取视频链接
    video_entries = scrape_video_urls(
        url=url,
        use_selenium=config["USE_SELENIUM"],
        chromedriver_path=config["CHROMEDRIVER_PATH"],
        config=config
    )
    
    if not video_entries:
        logger.info("未找到视频。")
        return False
    
    # 认证
    if not downloader.authenticate():
        logger.error("下载器认证失败。")
        return False
    
    # 处理每个视频
    video_urls = []
    for entry in video_entries:
        video_url = entry["video_url"]
        if downloader.add_download(video_url, download_dir):
            video_urls.append(video_url)
    
    # 监控下载
    video_to_file = downloader.monitor_downloads(video_urls)
    
    # 更新元数据
    for entry in video_entries:
        video_url = entry["video_url"]
        entry["file_path"] = video_to_file.get(video_url)
        if entry["file_path"]:
            entry["codec_info"] = extract_codec_info(entry["file_path"])
    
    # 保存元数据
    save_metadata_to_json(video_entries, config["JSON_OUTPUT"])
    
    return all(entry["file_path"] is not None for entry in video_entries)

def process_video_entries(video_entries: List[Dict], theme: str, config: Dict[str, str]) -> bool:
    """处理视频条目列表"""
    # 创建下载器实例
    downloader = get_downloader(config)
    
    # 下载预览图片
    for index, entry in enumerate(video_entries, start=1):
        if entry.get("preview_image_url"):
            preview_path = download_preview_image(
                entry["preview_image_url"],
                config["PREVIEW_IMAGES_DIR"],
                index
            )
            entry["preview_image_path"] = preview_path
    
    # 下载视频
    video_urls = []
    for entry in video_entries:
        video_url = entry["video_url"]
        if downloader.add_download(video_url, config["DOWNLOAD_DESTINATION"]):
            video_urls.append(video_url)
    
    # 监控下载
    video_to_file = downloader.monitor_downloads(video_urls)
    
    # 更新元数据
    for entry in video_entries:
        video_url = entry["video_url"]
        entry["file_path"] = video_to_file.get(video_url)
        if entry["file_path"]:
            entry["codec_info"] = extract_codec_info(entry["file_path"])
    
    # 保存元数据到主题目录
    save_metadata_to_json(video_entries, config["JSON_OUTPUT"])
    
    return all(entry["file_path"] is not None for entry in video_entries)

def create_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器。

    :return: 配置好的参数解析器。
    """
    parser = argparse.ArgumentParser(
        description='视频下载工具 - 支持网页URL下载和关键词搜索下载',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 添加子命令解析器
    subparsers = parser.add_subparsers(dest='command', help='下载模式')
    
    # URL 下载模式
    url_parser = subparsers.add_parser('url', help='通过网页 URL 下载视频')
    url_parser.add_argument('--url', required=True, help='要抓取视频的网页 URL')
    url_parser.add_argument('--theme', required=True, help='主题名称（用于组织下载的视频）')
    
    # 关键词搜索模式
    keyword_parser = subparsers.add_parser('keyword', help='通过关键词搜索并下载视频')
    keyword_parser.add_argument('--keyword', required=True, help='要搜索的视频关键词')
    
    return parser

def main():
    """
    通过命令行执行脚本的主函数。
    """
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return

    try:
        if args.command == 'url':
            # 获取配置并传递给 process_web_url
            config = get_config(theme=args.theme)
            success = process_web_url(args.url, config)  # 修改这里，移除 theme 参数
            if success:
                logger.info("所有视频已成功添加到 JDownloader 并保存元数据。")
            else:
                logger.warning("处理完成但存在一些问题。请查看日志了解详情。")

        elif args.command == 'keyword':
            try:
                config = get_config(theme=args.keyword)
                video_entries = process_keyword(args.keyword)
                if video_entries:
                    success = process_video_entries(video_entries, args.keyword, config)
                    if success:
                        logger.info("所有视频已成功下载并保存元数据。")
                    else:
                        logger.warning("处理完成但存在一些问题。请查看日志了解详情。")
                else:
                    logger.warning("未找到任何符合条件的视频。")
                    return 1

            except ImportError as e:
                logger.error(f"无法导入 pexels_video_downloader 模块：{e}")
                logger.error("请确保文件存在且依赖已安装。")
                return 1

    except KeyboardInterrupt:
        logger.info("用户中断进程。")
    except Exception as e:
        logger.error(f"执行过程中发生错误：{e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main()) 