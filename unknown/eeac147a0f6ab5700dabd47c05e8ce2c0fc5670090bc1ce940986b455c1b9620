#!/bin/bash

# 设置区域设置和字符编码
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# 新增参数：指定目录
REMOTE_SYNC_DIR=""
UTILS_DIR="/home/<USER>/theme-talker/utils"
REMOTE_LOG_DIR="/home/<USER>/theme-talker/logs/"
PROCESSED_TOPICS="/home/<USER>/theme-talker/topics/processed_topics.json"

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --remote_sync_dir)
            REMOTE_SYNC_DIR="$2"
            shift 2
            ;;
        *)
            echo "错误: 无效的参数 '$1'"
            exit 1
            ;;
    esac
done

# 检查是否提供了远程同步目录
if [ -z "$REMOTE_SYNC_DIR" ]; then
    echo "错误：未提供远程同步目录"
    exit 1
fi

# 加载 shell 配置
source ~/.bashrc

# 切换到指定目录
cd ~/theme-talker

# 激活 conda 环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate base

# 检查并创建 logs 目录
if [ ! -d "$REMOTE_LOG_DIR" ]; then
    echo "创建 logs 目录: $REMOTE_LOG_DIR"
    mkdir -p "$REMOTE_LOG_DIR"
fi

# 检查并创建 topics 目录
TOPICS_DIR=$(dirname "$PROCESSED_TOPICS")
if [ ! -d "$TOPICS_DIR" ]; then
    echo "创建 topics 目录: $TOPICS_DIR"
    mkdir -p "$TOPICS_DIR"
fi

# 加载已处理的 topics 列表
if [ -f "$PROCESSED_TOPICS" ]; then
    processed_topics=$(cat "$PROCESSED_TOPICS")
else
    processed_topics="[]"
    echo "$processed_topics" > "$PROCESSED_TOPICS"
fi

# 定义函数：记录已处理的 topic
record_processed_topic() {
    local topic_name="$1"
    local audio_file="$2"
    local generated_time="$3"
    local translated="$4"
    local fetch_images="$5"

    new_entry=$(jq -n \
        --arg topic_name "$topic_name" \
        --arg audio_file "$audio_file" \
        --arg generated_time "$generated_time" \
        --argjson translated "$translated" \
        --argjson fetch_images "$fetch_images" \
        '{topic_name: $topic_name, audio_file: $audio_file, generated_time: $generated_time, translated: $translated, fetch_images: $fetch_images}')

    processed_topics=$(echo "$processed_topics" | jq ". + [$new_entry]")

    # 更新 JSON 文件
    echo "$processed_topics" > "$PROCESSED_TOPICS"
}

# 修改处理函数，合并原来的两个检查函数
is_fully_processed() {
    local topic_name="$1"
    echo "$processed_topics" | jq -e ".[] | select(.topic_name == \"$topic_name\" and .translated == true and .fetch_images == true)" > /dev/null 2>&1
}

# 主处理逻辑
echo "开始处理音频文件..."

OLD_IFS="$IFS"
IFS=
while IFS= read -r -d '' wav_file; do
    filename=$(basename "$wav_file")
    prefix="${filename%.*}"
    txt_file="${REMOTE_SYNC_DIR}/${prefix}.txt"
    en_file="${REMOTE_SYNC_DIR}/${prefix}_en.txt"
    topic_name="$prefix"

    if is_fully_processed "$topic_name"; then
        processed_entry=$(echo "$processed_topics" | jq -r ".[] | select(.topic_name == \"$topic_name\")")
        processed_date=$(echo "$processed_entry" | jq -r '.generated_time')
        echo "文件 $wav_file 已于 $processed_date 完全处理，跳过"
        continue
    fi

    # 1. 音频转文本
    echo "处理音频文件: $wav_file"
    if ! bash "$UTILS_DIR/source_script.sh" --source "$wav_file" --json </dev/null; then
        echo "音频转文本失败，标记文件并继续"
        echo "$wav_file" >> "${REMOTE_SYNC_DIR}/audio_to_text_failed.log"
        continue
    fi

    # 2. 获取图片
    if [ -f "$txt_file" ]; then
        echo "调用 fetch_process_images.py 处理文件: $txt_file"
        if python fetch_process_images.py --script "$txt_file"; then
            current_date=$(date '+%Y-%m-%dT%H:%M:%S')
            record_processed_topic "$topic_name" "$wav_file" "$current_date" "true" "true"
            echo "成功处理 $txt_file 并获取图像"
        else
            echo "处理 $txt_file 失败，标记文件并继续"
            echo "$txt_file" >> "${REMOTE_SYNC_DIR}/processing_failed.log"
            current_date=$(date '+%Y-%m-%dT%H:%M:%S')
            record_processed_topic "$topic_name" "$wav_file" "$current_date" "true" "false"
        fi
    fi
done < <(find "$REMOTE_SYNC_DIR" -type f -name "*.wav" -print0)
IFS="$OLD_IFS"

echo "处理完成"
