 #!/bin/bash

# 检查是否提供了目录参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

# 设置变量并正确处理路径
WORK_DIR="${1%/}"  # 移除末尾的斜杠
THEME_NAME=$(basename "$WORK_DIR" | sed 's/\\//g')  # 移除反斜杠
echo "Debug: Working directory: $WORK_DIR"
echo "Debug: Theme name: $THEME_NAME"

# 设置远程服务器信息
SERVER_USER="gpuuser"
SERVER_HOST="*************"
REMOTE_CLIPS_DIR="/home/<USER>/source-video-clips/outputs/${THEME_NAME}/clips"

# 检查工作目录是否存在
if [ ! -d "$WORK_DIR" ]; then
    echo "Error: Directory '$WORK_DIR' does not exist."
    exit 1
fi

# 在本地创建 clips 目录
LOCAL_CLIPS_DIR="$WORK_DIR/clips"
mkdir -p "$LOCAL_CLIPS_DIR"

# 使用 rsync 下载远程 clips 目录中的所有文件
echo "Downloading clips from remote server..."
rsync -avz --progress -e "ssh -i ~/.ssh/gpu.pem" \
    "$SERVER_USER@$SERVER_HOST:$REMOTE_CLIPS_DIR/" \
    "$LOCAL_CLIPS_DIR/"

echo "Clips download completed!"