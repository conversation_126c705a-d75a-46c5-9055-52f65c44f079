import os
import arg<PERSON>se
import logging
import json
import shutil
import datetime
from typing import List, Dict, Any, Optional, Union

from config import (
    logger,
    SCRIPT_DIR,
    TOPIC_DIR,
    LANGUAGE_CODES,
    TEXT_ANALYTICS_KEY,
    TEXT_ANALYTICS_ENDPOINT
)
from modules.langchain_interface import (
    call_llm_json_response,
    call_llm,
    OPENAI_o3_mini_MODEL,
    DEEPSEEK_REASONER_MODEL,
    DEEPSEEK_CHAT_MODEL,
    LLM_PROVIDER_LOCAL_DEEPSEEK,
    LLM_PROVIDER_GOOGLE,
    MODEL_KEY_LOCAL_DEEPSEEK_R1
)
from modules.gpt_parameters import (
    LLM_PROVIDER_AZURE,
    LLM_PROVIDER_OPENAI,
    LLM_PROVIDER_ANTHROPIC,
    LLM_PROVIDER_DEEPSEEK,
    MODEL_KEY_GPT41,
    <PERSON>OD<PERSON>_KEY_O3_MINI,
    MOD<PERSON>_KEY_DEEPSEEK_CHAT,
    <PERSON><PERSON><PERSON>_KEY_DEEPSEEK_REASONER,
    <PERSON><PERSON><PERSON>_KEY_ARK_DEEPSEEK_R1,
    load_model_settings,
    DEFAULT_LLM_TYPE,
    DEFAULT_MODEL_KEY,
    MODEL_KEY_GEMINI_25_PRO
)
from modules.utils import (
    write_temp_file,
    calculate_token_count,
    split_into_chunks,
    smart_paragraph_split,
    normalize_language
)
from modules.fact_checker import fact_check_json_segments_pipeline, AZURE_CORRECTION_THRESHOLD,fact_check_script_pipeline

# 设置日志记录
# logging.basicConfig(level=logging.INFO) # Already configured by config.py
# logger = logging.getLogger(__name__) # Already configured by config.py

# 使用 LANGUAGE_CODES 的值作为支持的语言列表
SUPPORTED_LANGUAGES = list(LANGUAGE_CODES.values())

# 修改常量定义
TRANSLATE_TOKEN_LIMIT = 1000  # 每个块的token上限
REFINE_TOKEN_LIMIT = 1500  # 每个块的token上限
REFINE_THRESHOLD = 0.1  # 精炼内容减少的阈值
MAX_RETRIES = 3  # 最大重试次数
CHECK_FACT = True  # 是否执行事实检查步骤 - this might be superseded by new JSON pipeline logic

# 为每个API函数定义LLM类型和模型键
TRANSLATION_LLM_TYPE = LLM_PROVIDER_GOOGLE
TRANSLATION_MODEL_KEY = MODEL_KEY_GEMINI_25_PRO

SCRIPT_QUALITY_REVIEW_LLM_TYPE = LLM_PROVIDER_GOOGLE
SCRIPT_QUALITY_REVIEW_MODEL_KEY = MODEL_KEY_GEMINI_25_PRO

SCRIPT_REFINEMENT_LLM_TYPE = LLM_PROVIDER_GOOGLE
SCRIPT_REFINEMENT_MODEL_KEY = MODEL_KEY_GEMINI_25_PRO

TRANSLATION_FALLBACK_LLM_TYPE = LLM_PROVIDER_DEEPSEEK
TRANSLATION_FALLBACK_MODEL_KEY = MODEL_KEY_ARK_DEEPSEEK_R1

REWRITE_LLM_TYPE = LLM_PROVIDER_GOOGLE
REWRITE_MODEL_KEY = MODEL_KEY_GEMINI_25_PRO

# one-pass流程是否进行实体校正和review/refine
ONE_PASS_ENTITY_CORRECTION = True

def get_azure_language_code(language_name: str) -> Optional[str]:
    """
    将项目内部的语言名称 (如 "Chinese", "English") 转换为 Azure Text Analytics 支持的语言代码。
    参考: https://learn.microsoft.com/azure/ai-services/language-service/entity-linking/language-support
    """
    normalized_lang_name = normalize_language(language_name) # 先通过我们已有的函数标准化
    
    # 从 LANGUAGE_CODES 获取基础代码 (如 "zh", "en")
    base_code = None
    for name, code in LANGUAGE_CODES.items():
        if name.lower() == normalized_lang_name.lower():
            base_code = code
            break
    
    if not base_code: 
        if normalized_lang_name.lower() in ['en', 'es', 'ja', 'ko', 'fr', 'de', 'pt', 'it', 'zh-hans', 'zh-hant']:
             base_code = normalized_lang_name.lower()
        else:
            logger.warning(f"无法从项目内部语言名称 '{language_name}' (标准化为 '{normalized_lang_name}') 映射到基础语言代码。")
            return None

    # 映射到 Azure 特定代码
    if base_code == "zh":
        return "zh-Hans"
    elif base_code in ["en", "es", "ja", "ko", "fr", "de", "pt", "it"]: 
        return base_code
    
    logger.warning(f"语言 '{language_name}' (基础代码 '{base_code}') 当前未明确映射到 Azure 支持的实体链接语言代码。")
    return None

def get_chunk_size(original_language: str, target_language: str) -> int:
    """
    根据源语言和目标语言返回适当的 token 限制
    中文和日文等语言的 token 数通常比英文要多，需要适当调整
    """
    base_limit = TRANSLATE_TOKEN_LIMIT
    
    if target_language.lower() in ['chinese', 'japanese', 'korean']:
        return int(base_limit * 0.7)
    return base_limit

def restore_paragraph_structure(content: str) -> str:
    paragraphs = content.split('\n\n')
    return '\n\n'.join(p.strip() for p in paragraphs if p.strip())

def translate_segments_in_chunk(segments_chunk: List[Dict[str, Any]], original_language: str, target_language: str, 
                                llm_type: str, model_key: str, fallback_llm_type: str, fallback_model_key: str) -> List[Dict[str, Any]]:
    """
    Translates a chunk of segments.
    Returns a list of dictionaries, e.g., [{"id": segment_id, "text": "translated_text"}, ...]
    The 'id' in the returned dict should match the 'segment_id' from the input.
    """
    input_json_for_prompt = {
        "segments": []
    }
    for segment_data in segments_chunk:
        # Ensure segment_id is a string if the prompt or downstream processing expects it.
        # The prompt example for segment-translation shows "id": "integer" or "string".
        # Let's assume segment_id can be int or str and pass it as is.
        input_json_for_prompt["segments"].append({
            "id": segment_data["segment_id"], # Ensure this key matches what the prompt expects for ID
            "text": segment_data["paragraph_text"]
        })

    prompt_data = {
        "original_language": original_language,
        "target_language": target_language,
        "current_segments_json": json.dumps(input_json_for_prompt) 
    }

    try:
        response = call_llm_json_response(
            api_function="segment-translation",
            prompt_data=prompt_data,
            max_retries=MAX_RETRIES,
            using_cache=True, # Consider if caching is beneficial here
            llm_type=llm_type,
            model_key=model_key,
            expected_fields=["segments"]
        )
        translated_segments_payload = response.get("segments")
        if not translated_segments_payload or not isinstance(translated_segments_payload, list):
            raise ValueError("Segment translation did not return a list of segments in payload.")
        
        # Basic validation: check if number of translated segments matches input
        if len(translated_segments_payload) != len(segments_chunk):
            logger.warning(f"Number of translated segments ({len(translated_segments_payload)}) does not match input ({len(segments_chunk)}). Matching by ID.")
            # Fallback to matching by ID if lengths differ, or ensure strictness
            # For now, we'll rely on the LLM returning them in order or with correct IDs.
            
        # The prompt for 'segment-translation' specifies "id": "integer" or "string".
        # Let's ensure the output format for this function is consistent.
        # The plan indicates [{"id": segment_id, "text": "translated_text"}, ...]
        # The LLM response is {"segments": [{"id": id, "text": text}, ...]}
        # So, translated_segments_payload is already in the desired list format.

        return translated_segments_payload # This list contains dicts like {"id":..., "text":...}

    except Exception as e:
        logger.warning(f"Segment translation failed with primary model: {str(e)}. Trying fallback.")
        try:
            response = call_llm_json_response(
                api_function="segment-translation",
                prompt_data=prompt_data,
                max_retries=MAX_RETRIES,
                llm_type=fallback_llm_type,
                model_key=fallback_model_key,
                using_cache=True,
                expected_fields=["segments"]
            )
            translated_segments_payload = response.get("segments")
            if not translated_segments_payload or not isinstance(translated_segments_payload, list):
                raise ValueError("Segment translation with fallback did not return a list of segments.")
            if len(translated_segments_payload) != len(segments_chunk):
                 logger.warning(f"Fallback: Number of translated segments ({len(translated_segments_payload)}) does not match input ({len(segments_chunk)}).")
            return translated_segments_payload
        except Exception as fallback_e:
            logger.error(f"Segment translation failed with fallback model: {str(fallback_e)}")
            # For failed chunks, we might want to return original text for those segments
            # or handle this more gracefully. For now, raise.
            failed_segments_data = []
            for seg_data in segments_chunk:
                failed_segments_data.append({
                    "id": seg_data["segment_id"],
                    "text": seg_data.get("paragraph_text", "") # Fallback to original text
                })
            logger.warning(f"Returning original text for the failed segment chunk due to: {fallback_e}")
            return failed_segments_data


def is_translation_complete(source_text: str, translated_text: str, 
                           original_language: str, target_language: str) -> bool:
    """
    Check if translation is complete and valid.
    """
    DEFAULT_THRESHOLD = 0.5
    CHINESE_TO_ENGLISH_THRESHOLD = 0.3
    ENGLISH_TO_OTHERS_THRESHOLD = 0.4

    source_tokens = calculate_token_count(source_text)
    translated_tokens = calculate_token_count(translated_text)
    
    if source_tokens == 0 and translated_tokens == 0: # Both empty is fine
        return True
    if source_tokens > 0 and translated_tokens == 0: # Source had content, translation is empty
        logger.warning(f"Translation resulted in empty text from non-empty source (tokens: {source_tokens}).")
        return False # Potentially incomplete

    threshold = DEFAULT_THRESHOLD
    if original_language.lower() == 'chinese' and target_language.lower() == 'english':
        threshold = CHINESE_TO_ENGLISH_THRESHOLD
    elif original_language.lower() == 'english' and target_language.lower() in [
        'japanese', 'spanish', 'portuguese', 'french', 'german', 'italian', 'russian', 'korean', 'arabic', 'chinese'
    ]:
        threshold = ENGLISH_TO_OTHERS_THRESHOLD

    # Avoid division by zero if source_tokens is 0 but translated_tokens is not (though unlikely)
    if source_tokens == 0:
        return True # Or handle as an anomaly if translated_tokens > 0

    ratio = translated_tokens / source_tokens
    logger.info(f"翻译结果token数({translated_tokens}) vs 原文token数({source_tokens}), 比例为{ratio:.2f}, 阈值: {threshold:.2f}")
    
    if translated_tokens < source_tokens * threshold:
        logger.warning(f"翻译结果token数({translated_tokens})显著低于原文token数({source_tokens})的{threshold*100}%")
        return False
    
    return True

def translate_chunk(content: str, full_original_content: str, original_language: str, target_language: str, 
                   chunk_index: int, total_chunks: int, previous_translated_sections: str = "") -> str:
    """
    Translates a text chunk (for plain text file processing).
    """
    prompt_data = {
        "original_language": original_language,
        "target_language": target_language,
        "full_original_content": full_original_content,
        "current_section": content,
        "section_position": f"{chunk_index}/{total_chunks}", # Ensure string as per prompt example
        "total_sections": total_chunks, # Ensure consistent type if prompt uses it
        "previous_translated_sections": previous_translated_sections if total_chunks > 1 and chunk_index > 1 and previous_translated_sections else ""
    }
 
    try:
        response = call_llm_json_response(
            api_function="comprehensive-translation",
            prompt_data=prompt_data,
            max_retries=MAX_RETRIES,
            using_cache=True,
            llm_type=TRANSLATION_LLM_TYPE,
            model_key=TRANSLATION_MODEL_KEY,
            expected_fields=["translation"]
        )
        translated_text = response.get("translation")
        if not translated_text and content: # If original content was not empty, translation shouldn't be empty
            raise ValueError("翻译结果为空但原文不为空")
        if not translated_text: # Allow empty translation if original was empty
            translated_text = ""

        logger.debug(f"Translate Before: {content} \n Translate After: {translated_text}")

        if not is_translation_complete(content, translated_text, original_language, target_language):
            raise ValueError("翻译结果疑似不完整")
        
        return translated_text
        
    except Exception as e:
        logger.warning(f"常规翻译失败或检测到疑似遗漏：{str(e)}，尝试更换翻译模型-DeepSeek")
        prompt_data["previous_translated_sections"] = previous_translated_sections # Ensure it's passed to fallback
        try:
            response = call_llm_json_response(
                api_function="strict-translation", # Using strict as fallback for comprehensive
                prompt_data=prompt_data,
                max_retries=MAX_RETRIES,
                llm_type=TRANSLATION_FALLBACK_LLM_TYPE,
                model_key=TRANSLATION_FALLBACK_MODEL_KEY,  
                using_cache=False,
                expected_fields=["translation"]
            )
            
            translated_text = response.get("translation")
            if not translated_text and content:
                 raise ValueError("严格翻译模式返回空结果但原文不为空")
            if not translated_text:
                translated_text = ""

            logger.debug(f"Strict Translate Before: {content} \n Strict Translate After: {translated_text}")

            if not is_translation_complete(content, translated_text, original_language, target_language):
                logger.warning("即使在严格模式下，翻译结果仍可能不完整，但将继续使用")
            
            return translated_text
            
        except Exception as strict_e:
            logger.error(f"严格翻译模式失败：{str(strict_e)}")
            raise ValueError(f"翻译失败：两种翻译模式均失败 - {str(strict_e)}")
        
            
def review_and_refine(translated_content: str, original_content: str, target_language: str, original_language: str) -> str:
    """
    Review and refine for plain text content.
    """
    logger.info("开始专有名词验证和修正流程...")
    
    try:
        review_prompt_data = {
            "target_language": target_language,
            "translated_content": translated_content
            # Add original_language if script_quality_review prompt needs it
        }

        logger.info("正在验证专有名词...")
        review_response = call_llm_json_response(
            api_function="script_quality_review",
            prompt_data=review_prompt_data,
            llm_type=SCRIPT_QUALITY_REVIEW_LLM_TYPE,
            model_key=SCRIPT_QUALITY_REVIEW_MODEL_KEY,
            expected_fields=["review_report"],
            using_cache=True
        )

        if not review_response or not review_response.get("review_report"):
            logger.error("专有名词验证失败或未返回报告，使用原始翻译内容")
            return translated_content

        review_report = review_response.get("review_report", {})
        oa = review_report.get("overall_assessment", {})
        pn_errors_count = oa.get("proper_noun_errors_count", 0)
        fd_errors_count = oa.get("factual_data_errors_count", 0)
        pt_errors_count = oa.get("pronoun_typo_errors_count", 0)

        logger.debug(f"专有名词验证报告: {json.dumps(review_report, ensure_ascii=False, indent=2)}")
        logger.info(f"检测到专有名词错误: {pn_errors_count}, 事实数据错误: {fd_errors_count}, 代词或拼写错误: {pt_errors_count}")

        if pn_errors_count == 0 and fd_errors_count == 0 and pt_errors_count == 0:
            logger.info("未发现需要修正的错误，跳过修正步骤")
            return translated_content
        else:
            logger.info("检测到错误，开始专有名词和拼写修正流程")
            for attempt in range(MAX_RETRIES):
                refine_prompt_data = {
                    "target_language": target_language,
                    "translated_content": translated_content,
                    "review_report": json.dumps(review_report)
                }

                logger.info(f"正在修正专有名词错误... (尝试 {attempt+1}/{MAX_RETRIES})")
                refined_response = call_llm_json_response(
                    api_function="script_refinement",
                    prompt_data=refine_prompt_data,
                    expected_fields=["refined_script"],
                    llm_type=SCRIPT_REFINEMENT_LLM_TYPE,
                    model_key=SCRIPT_REFINEMENT_MODEL_KEY,
                    using_cache=True
                )

                if not refined_response or "refined_script" not in refined_response:
                    logger.error(f"专有名词修正失败 (尝试 {attempt+1}/{MAX_RETRIES})，未返回 refined_script")
                    if attempt == MAX_RETRIES - 1:
                        logger.error("已达到最大重试次数，使用原始翻译内容")
                        return translated_content
                    continue

                refined_content = refined_response.get("refined_script", translated_content)

                original_token_count = calculate_token_count(translated_content)
                refined_token_count = calculate_token_count(refined_content)
                
                if original_token_count == 0 and refined_token_count > 0: # Avoid division by zero if original was empty
                    token_reduction = -1.0 # Indicates growth from empty
                elif original_token_count == 0 and refined_token_count == 0:
                     token_reduction = 0.0
                else:
                    token_reduction = (original_token_count - refined_token_count) / original_token_count


                logger.info(f"修正后的内容 tokens: {refined_token_count} (变化: {token_reduction:.1%})")

                if token_reduction > REFINE_THRESHOLD:
                    if attempt < MAX_RETRIES - 1:
                        logger.warning(f"修正后内容减少过多 ({token_reduction:.1%})，尝试重新修正")
                        continue
                    else:
                        logger.warning(f"修正后内容减少过多 ({token_reduction:.1%})，已达到最大重试次数，使用原始翻译内容")
                        return translated_content

                logger.info(f"专有名词修正成功 (尝试 {attempt+1}/{MAX_RETRIES})")
                return refined_content

            return translated_content
    except Exception as e:
        logger.error(f"专有名词验证和修正过程出错: {str(e)}")
        return translated_content


def review_and_refine_json_segments(
    segments_to_refine: List[Dict[str, Any]],
    full_original_document_context: str,
    full_el_corrected_document_context: str,
    target_language: str,
    original_language: str
) -> str: # Keep this return type for now, but it will be a list with one item or handled differently
    """
    Performs review and refinement on the entire document content formed from JSON segments.
    The input segments_to_refine should have 'segment_id', 'original_text', 'translated_text', 'el_corrected_text'.
    This function will now call LLM once for review and once for refinement on the WHOLE document text.
    It will return a list containing a single dictionary with the full refined text, or just the string.
    Let's make it return the single refined string for clarity in this new approach.
    """
    logger.info(f"开始对整个文档（由 {len(segments_to_refine)} 个JSON段落组成）进行一次性审查和精炼...")

    # 1. Prepare the full EL-corrected document text for LLM
    # Use el_corrected_text if available, fallback to translated_text, then original_text, then empty string.
    segment_texts_for_concat = []
    for seg in segments_to_refine:
        text_to_use = seg.get('el_corrected_text')
        if text_to_use is None:
            text_to_use = seg.get('translated_text')
        if text_to_use is None:
            text_to_use = seg.get('original_text', '') # Should not happen if pipeline is correct
        segment_texts_for_concat.append(text_to_use)
    
    full_el_corrected_doc_for_llm = "\n\n".join(segment_texts_for_concat)

    if not full_el_corrected_doc_for_llm.strip():
        logger.warning("从JSON段落构建的EL校正后文档为空。跳过审查和精炼。")
        # In this new model, if the whole doc is empty, we return an empty string for the refined doc.
        return ""

    # The 'full_original_document_context' is already a string passed to this function.
    # It was prepared in Phase 0.

    # --- Logic adapted from the original review_and_refine for plain text --- 
    refined_text_final = full_el_corrected_doc_for_llm # Default to this if steps fail or no errors

    try:
        # Step 1: Review the entire document
        review_prompt_data = {
            "target_language": target_language,
            "translated_content": full_el_corrected_doc_for_llm
            # original_language is not in script_quality_review prompt, but available if needed
        }

        logger.info("对整个文档（来自JSON段落）进行审查...")
        review_response = call_llm_json_response(
            api_function="script_quality_review", # Use the prompt for full document review
            prompt_data=review_prompt_data,
            llm_type=SCRIPT_QUALITY_REVIEW_LLM_TYPE,
            model_key=SCRIPT_QUALITY_REVIEW_MODEL_KEY,
            expected_fields=["review_report"],
            using_cache=False, 
            max_retries=MAX_RETRIES 
        )

        if not review_response or not review_response.get("review_report"):
            logger.warning("整个文档（来自JSON段落）的审查失败或未返回报告。将使用其EL校正后的完整文本。")
            return full_el_corrected_doc_for_llm

        review_report = review_response.get("review_report", {})
        oa = review_report.get("overall_assessment", {})
        pn_errors_count = oa.get("proper_noun_errors_count", 0)
        fd_errors_count = oa.get("factual_data_errors_count", 0)
        pt_errors_count = oa.get("pronoun_typo_errors_count", 0)
        
        logger.debug(f"整个文档（来自JSON段落）的审查报告: {json.dumps(review_report, ensure_ascii=False, indent=2)}")
        logger.info(f"整个文档（来自JSON段落）检测到错误: PN={pn_errors_count}, FD={fd_errors_count}, PT={pt_errors_count}")

        if pn_errors_count == 0 and fd_errors_count == 0 and pt_errors_count == 0:
            logger.info("整个文档（来自JSON段落）未发现需修正错误。使用其EL校正后的文本。")
            refined_text_final = full_el_corrected_doc_for_llm
        else:
            logger.info("整个文档（来自JSON段落）检测到错误，开始精炼流程...")
            # Step 2: Refine the entire document
            for attempt in range(MAX_RETRIES):
                refine_prompt_data = {
                    "target_language": target_language,
                    "translated_content": full_el_corrected_doc_for_llm, # Content to refine
                    "review_report": json.dumps(review_report) # The review report for the whole doc
                    # original_content (full_original_document_context) is not part of script_refinement prompt
                }
                logger.info(f"正在精炼整个文档（来自JSON段落）... (尝试 {attempt+1}/{MAX_RETRIES})")
                
                refined_llm_response = call_llm_json_response(
                    api_function="script_refinement", # Use the prompt for full document refinement
                    prompt_data=refine_prompt_data,
                    expected_fields=["refined_script"],
                    llm_type=SCRIPT_REFINEMENT_LLM_TYPE,
                    model_key=SCRIPT_REFINEMENT_MODEL_KEY,
                    using_cache=False,
                    max_retries=1 # Manage retries for this specific call here
                )

                if not refined_llm_response or "refined_script" not in refined_llm_response:
                    logger.error(f"整个文档（来自JSON段落）精炼失败 (尝试 {attempt+1}/{MAX_RETRIES})，未返回 refined_script。")
                    if attempt == MAX_RETRIES - 1:
                        logger.error(f"整个文档（来自JSON段落）已达到最大精炼重试次数，将使用EL校正文本。")
                        refined_text_final = full_el_corrected_doc_for_llm # Fallback
                    continue # Try next attempt

                temp_refined_text_doc = refined_llm_response.get("refined_script", full_el_corrected_doc_for_llm)
                
                original_doc_token_count = calculate_token_count(full_el_corrected_doc_for_llm)
                refined_doc_token_count = calculate_token_count(temp_refined_text_doc)
                
                token_reduction_doc = 0.0
                if original_doc_token_count > 0:
                    token_reduction_doc = (original_doc_token_count - refined_doc_token_count) / original_doc_token_count
                elif refined_doc_token_count > 0: 
                    token_reduction_doc = -1.0 

                logger.info(f"整个文档（来自JSON段落）精炼后 tokens: {refined_doc_token_count} (EL校正后: {original_doc_token_count}, 变化: {token_reduction_doc:.1%})")

                if token_reduction_doc > REFINE_THRESHOLD:
                    if attempt < MAX_RETRIES - 1:
                        logger.warning(f"整个文档（来自JSON段落）精炼后内容减少过多 ({token_reduction_doc:.1%})，尝试重新精炼。")
                        continue
                    else:
                        logger.warning(f"整个文档（来自JSON段落）精炼后内容减少过多 ({token_reduction_doc:.1%})，已达到最大重试次数，使用EL校正文本。")
                        refined_text_final = full_el_corrected_doc_for_llm # Fallback
                        break 
                
                refined_text_final = temp_refined_text_doc
                logger.info(f"整个文档（来自JSON段落）精炼成功 (尝试 {attempt+1}/{MAX_RETRIES})。")
                break # Success, exit retry loop
        
        return refined_text_final # Return the single string for the whole document

    except Exception as e:
        logger.error(f"处理整个文档（来自JSON段落）的审查与精炼过程时出错: {str(e)}")
        return full_el_corrected_doc_for_llm # Fallback to EL corrected full text


def process_entitylink_review_refine(
    input_text: str,
    original_content: str,
    target_language: str,
    original_language: str,
    file_dir: str = None,
    filename_without_ext: str = None,
    write_temp: bool = True
) -> str:
    """
    统一的实体校正 + review/refine流程，供process_translation和one_pass_rewrite_and_refine共用。
    input_text: 需要处理的文本（如翻译后或one-pass refine后的文本）
    original_content: 原文内容
    target_language/original_language: 语言参数
    file_dir/filename_without_ext: 如需写入临时文件则提供
    write_temp: 是否写入临时文件
    返回最终精炼后的文本
    """
    factchecked_content = input_text
    if CHECK_FACT:
        logger.info("开始进行实体名称校正 (Azure Entity Linking)...")
        try:
            azure_lang_code = get_azure_language_code(target_language)
            if azure_lang_code:
                corrected_text = fact_check_script_pipeline(
                    translated_text=input_text,
                    language=azure_lang_code
                )
                if corrected_text and isinstance(corrected_text, str):
                    original_len = len(input_text)
                    corrected_len = len(corrected_text)
                    if corrected_len < original_len * AZURE_CORRECTION_THRESHOLD and original_len > 0:
                        logger.warning(f"Azure实体链接校正后的文本长度 ({corrected_len}) 显著小于原始翻译文本长度 ({original_len})，使用原始翻译。")
                    else:
                        factchecked_content = corrected_text
                        logger.info("Azure 实体链接校正应用成功。")
                else:
                    logger.warning("Azure 实体链接校正未返回有效文本，使用原始翻译。")
            else:
                logger.warning(f"无法获取适用于 Azure 的语言代码 for '{target_language}'，跳过实体链接校正。")
        except ImportError:
            logger.warning("`fact_check_script_pipeline` 未导入，跳过 Azure 实体链接。")
        except Exception as e:
            logger.warning(f"Azure 实体链接校正过程中出现错误: {str(e)}，使用原始翻译。")
        if write_temp and file_dir and filename_without_ext:
            write_temp_file(factchecked_content, f"{filename_without_ext}_entity_corrected_content", file_dir)
    else:
        logger.info("跳过实体名称校正步骤 (Azure Entity Linking)")

    logger.info("开始统一审查和精炼过程...")
    refined_content = review_and_refine(
        factchecked_content,
        original_content,
        target_language,
        original_language
    )
    logger.info(f"精炼后文件的 tokens 数量: {calculate_token_count(refined_content)}")
    if write_temp and file_dir and filename_without_ext:
        write_temp_file(refined_content, f"{filename_without_ext}_refined_content", file_dir)
    return refined_content


def process_translation(file_path: str, content: str, original_language: str, target_language: str, theme: str = None):
    # This function handles plain text files.
    # The main refactoring is for process_json_translation.
    # This function will remain largely the same unless specific changes for text files are also required by the plan.
    # For now, ensure it still works and uses CHECK_FACT if needed for its own pipeline.
    
    file_dir, original_filename = os.path.split(file_path)
    filename_without_ext, ext = os.path.splitext(original_filename)

    logger.info(f"开始翻译文件：{file_path}")
    logger.info(f"原文文件的 tokens 数量: {calculate_token_count(content)}")
    chunk_size = get_chunk_size(original_language, target_language) # This is for text files
    logger.info(f"设置的块大小：{chunk_size}")

    # Step 1: Chunkify content
    content_chunks = split_into_chunks(content, max_tokens=chunk_size, language=original_language)
    total_chunks = len(content_chunks)
    logger.info(f"块的数量：{total_chunks}")

    logger.info(f"正在将内容从 {original_language} 翻译为 {target_language}...")
    
    full_translated_content = ""
    if total_chunks == 1:
        logger.info("检测到单块文档，使用简化的翻译流程...")
        chunk = content_chunks[0] if content_chunks else ""
        full_translated_content = translate_chunk(
            chunk, content, original_language, target_language, 1, 1
        )
        write_temp_file(full_translated_content, f"{filename_without_ext}_translate_chunk", file_dir)
    else:
        translated_chunks = []
        all_previous_translated = ""
        for i, chunk in enumerate(content_chunks):
            logger.info(f"正在翻译第 {i + 1}/{total_chunks} 块...")
            translated_chunk = translate_chunk(
                chunk, content, original_language, target_language, 
                i + 1, total_chunks, all_previous_translated
            )
            translated_chunks.append(translated_chunk)
            all_previous_translated += translated_chunk + "\n\n"
        full_translated_content = "\n\n".join(translated_chunks)
        write_temp_file(full_translated_content, f"{filename_without_ext}_translate_chunk", file_dir)
    
    logger.debug(f"翻译后文件的 tokens 数量: {calculate_token_count(full_translated_content)}")

    # 统一调用实体校正+review/refine流程
    refined_content = process_entitylink_review_refine(
        input_text=full_translated_content,
        original_content=content,
        target_language=target_language,
        original_language=original_language,
        file_dir=file_dir,
        filename_without_ext=filename_without_ext,
        write_temp=True
    )
    logger.info(f"精炼后文件的 tokens 数量: {calculate_token_count(refined_content)}")
    write_temp_file(refined_content, f"{filename_without_ext}_refined_content", file_dir)

    language_code = LANGUAGE_CODES.get(target_language, target_language) # Default to target_language if not in map
    if isinstance(language_code, list): language_code = language_code[0] # take first if list
    
    # Normalize filename if it ends with _cn or other language codes before adding new one
    for lc_marker in ['_cn', '_en', '_ja', '_ko', '_es', '_fr', '_de', '_pt', '_it', '_ru', '_ar']:
        if filename_without_ext.endswith(lc_marker):
            filename_without_ext = filename_without_ext[:-len(lc_marker)]
            break
            
    translated_filename = f"{filename_without_ext}_{language_code}{ext}"
    translated_filepath = os.path.join(file_dir, translated_filename)
    
    with open(translated_filepath, 'w', encoding='utf-8') as f:
        f.write(refined_content)
    logger.info(f"翻译已保存到 {translated_filepath}")

    if theme and language_code == 'en':
        copy_script_files(translated_filepath, theme)


def copy_script_files(source_file: str, theme: str):
    """
    复制主脚本文件到目标目录。
    """
    base_dir = os.path.dirname(source_file)
    # Ensure SCRIPT_DIR is properly formatted if it contains placeholders like {theme}
    if "{theme}" in SCRIPT_DIR:
        target_dir = SCRIPT_DIR.format(theme=theme)
    else: # Fallback if SCRIPT_DIR is a direct path but theme is still given
        target_dir = os.path.join(SCRIPT_DIR, theme) 
        
    os.makedirs(target_dir, exist_ok=True)

    # Determine the target filename. The plan implies the main English file is named {theme}.txt
    target_filename = f"{theme}.txt" # Assuming the output is always .txt for this function
    main_target = os.path.join(target_dir, target_filename)

    if os.path.abspath(source_file) != os.path.abspath(main_target):
        shutil.copy2(source_file, main_target)
        logger.info(f"已将文件 {source_file} 复制到 {main_target}")
    else:
        logger.info(f"源文件和目标文件相同，跳过复制: {source_file}")


def process_json_translation(file_path: str, json_data: List[Dict[str, Any]], original_language: str, target_language: str, theme: str = None):
    file_dir, original_filename = os.path.split(file_path)
    filename_without_ext, ext = os.path.splitext(original_filename)
    language_code = LANGUAGE_CODES.get(target_language.lower(), target_language.lower())
    if isinstance(language_code, list): language_code = language_code[0]

    logger.info(f"开始翻译 JSON 文件中的段落 (新流程)：{file_path}")

    # --- Phase 0: Initial Setup and Data Preparation ---
    logger.info("阶段 0: 初始设置与数据准备")
    original_texts_map = {str(seg['segment_id']): seg.get('paragraph_text', '') for seg in json_data}
    
    # Ensure segment_id is string for consistent keying
    # Also, ensure paragraph_text exists, default to empty string if not
    for seg in json_data:
        seg['segment_id'] = str(seg['segment_id'])
        if 'paragraph_text' not in seg:
            seg['paragraph_text'] = ''

    full_original_document_context = "\n\n".join(
        [f"[SEGMENT ID={seg['segment_id']}]\n{seg.get('paragraph_text', '')}" for seg in json_data]
    )
    # Write temp file for inspection
    write_temp_file(full_original_document_context, f"{filename_without_ext}_p0_full_original_context", file_dir)

    # --- Phase 1: Batch Translate JSON Segments ---
    logger.info("阶段 1: 批量翻译 JSON Segments")
    segment_chunks_for_translation = []
    current_chunk = []
    current_chunk_tokens = 0
    # Use a token limit suitable for the JSON segment translation prompt (TRANSLATE_TOKEN_LIMIT can be used as a guide)
    # The segment-translation prompt takes a JSON of segments. The limit is on the sum of text in that JSON.
    for segment in json_data:
        segment_text = segment.get("paragraph_text", "")
        segment_tokens = calculate_token_count(segment_text) # Tokens of one segment's text

        # The prompt takes a JSON string of segments. The limit should apply to the total content.
        # For simplicity, let's assume TRANSLATE_TOKEN_LIMIT applies to the sum of tokens of paragraph_text in a chunk.
        if current_chunk_tokens + segment_tokens > TRANSLATE_TOKEN_LIMIT and current_chunk:
            segment_chunks_for_translation.append(current_chunk)
            current_chunk = [segment]
            current_chunk_tokens = segment_tokens
        else:
            current_chunk.append(segment)
            current_chunk_tokens += segment_tokens
    if current_chunk:
        segment_chunks_for_translation.append(current_chunk)

    logger.info(f"JSON 段落被分为 {len(segment_chunks_for_translation)} 个翻译块.")
    
    translated_json_segments_phase1_output = [] # List of Dicts: {segment_id, original_text, translated_text}
    raw_translated_segments_map = {} # Helper map {segment_id_str: translated_text_str}

    for i, chunk_of_segments_to_translate in enumerate(segment_chunks_for_translation):
        logger.info(f"正在翻译 JSON 块 {i + 1}/{len(segment_chunks_for_translation)}...")
        
        # Filter out segments that are entirely empty (no paragraph_text) if desired,
        # or let translate_segments_in_chunk handle them (it should pass them through).
        # The current translate_segments_in_chunk will attempt to translate them, LLM might return empty.
        
        segments_with_text_in_chunk = [s for s in chunk_of_segments_to_translate if s.get("paragraph_text","").strip()]
        empty_segments_in_chunk = [s for s in chunk_of_segments_to_translate if not s.get("paragraph_text","").strip()]

        if not segments_with_text_in_chunk:
            logger.info(f"JSON 块 {i+1} 中没有需要翻译的文本，直接保留原文（空）。")
            for seg_data in empty_segments_in_chunk:
                 raw_translated_segments_map[str(seg_data["segment_id"])] = "" # Empty translation for empty original
            continue # move to the next chunk

        try:
            # translate_segments_in_chunk expects list of dicts with 'segment_id' and 'paragraph_text'
            # It returns list of dicts with 'id' and 'text'
            translated_segment_payloads_from_llm = translate_segments_in_chunk(
                segments_with_text_in_chunk, # Only send segments with text
                original_language, 
                target_language,
                TRANSLATION_LLM_TYPE, 
                TRANSLATION_MODEL_KEY,
                TRANSLATION_FALLBACK_LLM_TYPE,
                TRANSLATION_FALLBACK_MODEL_KEY
            )
            for translated_seg_info in translated_segment_payloads_from_llm:
                # Ensure 'id' from LLM response is consistently handled (string)
                raw_translated_segments_map[str(translated_seg_info["id"])] = translated_seg_info.get("text", "")
            
            # For segments that were empty in this chunk, ensure they are also mapped
            for seg_data in empty_segments_in_chunk:
                raw_translated_segments_map[str(seg_data["segment_id"])] = ""


        except Exception as e:
            logger.error(f"处理 JSON 块 {i+1} 翻译失败: {e}. 该块中的段落将保留原文。")
            for seg_data in chunk_of_segments_to_translate: # all segments in this failed chunk
                 raw_translated_segments_map[str(seg_data["segment_id"])] = seg_data.get("paragraph_text", "")


    # Consolidate results for Phase 1 output structure
    for seg_data in json_data: # Iterate original json_data to maintain order and completeness
        s_id = str(seg_data['segment_id'])
        translated_json_segments_phase1_output.append({
            "segment_id": s_id,
            "original_text": original_texts_map.get(s_id, ''),
            "translated_text": raw_translated_segments_map.get(s_id, '') # Use original if translation failed for a chunk
        })
    # Write temp file for inspection
    write_temp_file(json.dumps(translated_json_segments_phase1_output, indent=2, ensure_ascii=False), f"{filename_without_ext}_p1_translated_segments", file_dir)


    # --- Phase 2: Batch Azure Entity Linking ---
    logger.info("阶段 2: 批量 Azure 实体链接")
    azure_target_lang_code = get_azure_language_code(target_language)
    el_corrected_json_segments = []

    if azure_target_lang_code and CHECK_FACT: # CHECK_FACT can globally enable/disable this phase
        el_corrected_json_segments = fact_check_json_segments_pipeline(
            translated_json_segments_phase1_output, # Input is list of {segment_id, original_text, translated_text}
            azure_target_lang_code
        )
    else:
        logger.warning(f"跳过 Azure 实体链接阶段。CHECK_FACT: {CHECK_FACT}, Azure Lang Code: {azure_target_lang_code}")
        el_corrected_json_segments = translated_json_segments_phase1_output # Pass through
        # Ensure 'el_corrected_text' field is present, even if same as 'translated_text'
        for seg in el_corrected_json_segments:
            seg['el_corrected_text'] = seg.get('translated_text', '')
    # Write temp file for inspection
    write_temp_file(json.dumps(el_corrected_json_segments, indent=2, ensure_ascii=False), f"{filename_without_ext}_p2_el_corrected_segments", file_dir)


    # --- Phase 3: Batch Review and Refine ---
    logger.info("阶段 3: 批量审查和精炼")
    
    # Prepare full_el_corrected_document_context before calling review_and_refine_json_segments
    full_el_corrected_document_context = "\n\n".join(
        [f"[SEGMENT ID={seg['segment_id']}]\n{seg.get('el_corrected_text', seg.get('translated_text', ''))}" 
         for seg in el_corrected_json_segments]
    )
    write_temp_file(full_el_corrected_document_context, f"{filename_without_ext}_p3_full_el_corrected_context", file_dir)

    # Call for single-pass review and refine of the whole document content from JSON segments
    refined_full_document_text_from_json = review_and_refine_json_segments(
        segments_to_refine=el_corrected_json_segments, 
        full_original_document_context=full_original_document_context, 
        full_el_corrected_document_context=full_el_corrected_document_context, # Ensure this is passed
        target_language=target_language,
        original_language=original_language
    )
    write_temp_file(refined_full_document_text_from_json, f"{filename_without_ext}_p3_refined_FULL_DOCUMENT_from_json", file_dir)


    # --- Phase 4: Final JSON Assembly ---
    logger.info("阶段 4: 最终 JSON 组装")
    final_output_json_data = []
    
    # Since refined_full_document_text_from_json is a single string for the whole document,
    # we cannot directly map parts of it to individual segments for the paragraph_text_{language_code} field.
    # We will use the el_corrected_text for per-segment fields and store the full refined text separately if needed.
    
    el_corrected_text_map = {str(seg['segment_id']): seg.get('el_corrected_text', seg.get('translated_text', '')) for seg in el_corrected_json_segments}

    for original_segment_struct in json_data: # Iterate over the original input structure
        s_id = str(original_segment_struct['segment_id'])
        final_segment_data = original_segment_struct.copy()
        
        # Populate the target language field for each segment with its EL-corrected text.
        final_segment_data[f"paragraph_text_{language_code}"] = el_corrected_text_map.get(s_id, '')
        
        # The refined_full_document_text_from_json contains the refinement for the entire document.
        # This could be added as a top-level key in the JSON file if desired, or used for other purposes.
        # For example, you might write it to a separate .txt file or include it once in the JSON structure.

        final_output_json_data.append(final_segment_data)

    # Option 1: Create a final JSON object that includes segments and the full refined text
    final_json_to_save = {
        "segments": final_output_json_data,
        f"refined_full_text_{language_code}": refined_full_document_text_from_json
    }

    # Output final JSON file
    for lc_marker in ['_cn', '_en', '_ja', '_ko', '_es', '_fr', '_de', '_pt', '_it', '_ru', '_ar']:
        if filename_without_ext.endswith(lc_marker):
            filename_without_ext = filename_without_ext[:-len(lc_marker)]
            break
    translated_filename = f"{filename_without_ext}_{language_code}{ext}" 
    translated_filepath = os.path.join(file_dir, translated_filename)

    with open(translated_filepath, 'w', encoding='utf-8') as f:
        json.dump(final_json_to_save, f, ensure_ascii=False, indent=2) # Save the object with the full refined text
    
    logger.info(f"增强的 JSON 已保存到 {translated_filepath} (包含完整的精炼文本和基于EL校正的段落文本)")

    # Optional: copy if theme and target language is English (for JSON)
    if theme and language_code == 'en':
        # The copy_script_files is designed for .txt. For JSON, decide on naming.
        # For instance, copy as {theme}_{language_code}.json or just {theme}.json if it's the primary English output.
        target_json_dir = SCRIPT_DIR.format(theme=theme) if "{theme}" in SCRIPT_DIR else os.path.join(SCRIPT_DIR, theme)
        os.makedirs(target_json_dir, exist_ok=True)
        target_json_filename = f"{theme}.json" # Or include lang_code if multiple JSON outputs per theme
        target_json_path = os.path.join(target_json_dir, target_json_filename)
        
        if os.path.abspath(translated_filepath) != os.path.abspath(target_json_path):
            shutil.copy2(translated_filepath, target_json_path)
            logger.info(f"已将 JSON 文件 {translated_filepath} 复制到 {target_json_path}")
        else:
            logger.info(f"JSON 源文件和目标文件相同，跳过复制: {translated_filepath}")


def one_pass_rewrite_and_refine(file_path: str, content: str, original_language: str, target_language: str, theme: str = None):
    """
    新的one-pass流程：不分块，直接调用one_pass_rewrite和refine_rewrite_script两个prompt进行处理。
    若ONE_PASS_ENTITY_CORRECTION为True，则在refine后依次进行Azure Entity Linking、script_quality_review、script_refinement。
    """
    file_dir, original_filename = os.path.split(file_path)
    filename_without_ext, ext = os.path.splitext(original_filename)
    language_code = LANGUAGE_CODES.get(target_language, target_language)
    if isinstance(language_code, list):
        language_code = language_code[0]

    logger.info(f"开始 one-pass 改写流程：{file_path}")

    # Step 1: 调用 one_pass_rewrite prompt
    prompt_data = {
        "original_language": original_language,
        "target_language": target_language,
        "full_original_content": content
    }
    try:
        response = call_llm_json_response(
            api_function="one_pass_rewrite",
            prompt_data=prompt_data,
            max_retries=MAX_RETRIES,
            using_cache=True,
            llm_type=REWRITE_LLM_TYPE,
            model_key=REWRITE_MODEL_KEY,
            expected_fields=["translation"]
        )
        rewritten_text = response.get("translation", "")
        if not rewritten_text and content:
            raise ValueError("one_pass_rewrite 返回空结果但原文不为空")
    except Exception as e:
        logger.error(f"one_pass_rewrite 步骤失败: {str(e)}")
        return

    write_temp_file(rewritten_text, f"{filename_without_ext}_one_pass_rewrite", file_dir)

    # Step 2: 调用 refine_rewrite_script prompt
    refine_prompt_data = {
        "full_rewrite_script": rewritten_text
    }
    try:
        refine_response = call_llm_json_response(
            api_function="refine_rewrite_script",
            prompt_data=refine_prompt_data,
            max_retries=MAX_RETRIES,
            using_cache=True,
            llm_type=REWRITE_LLM_TYPE,
            model_key=REWRITE_MODEL_KEY,
            expected_fields=["refined_script"]
        )
        refined_text = refine_response.get("refined_script", rewritten_text)
    except Exception as e:
        logger.error(f"refine_rewrite_script 步骤失败: {str(e)}，使用 one_pass_rewrite 结果")
        refined_text = rewritten_text

    write_temp_file(refined_text, f"{filename_without_ext}_one_pass_refined", file_dir)

    # Step 3: Azure Entity Linking + script_quality_review + script_refinement（可选，调用统一函数）
    if ONE_PASS_ENTITY_CORRECTION:
        final_text = process_entitylink_review_refine(
            input_text=refined_text,
            original_content=content,
            target_language=target_language,
            original_language=original_language,
            file_dir=file_dir,
            filename_without_ext=filename_without_ext,
            write_temp=True
        )
        write_temp_file(final_text, f"{filename_without_ext}_one_pass_final_entity_corrected", file_dir)
    else:
        logger.info("one-pass流程未启用实体校正和review/refine，直接输出refined结果。")
        final_text = refined_text

    # 输出最终文件
    for lc_marker in ['_cn', '_en', '_ja', '_ko', '_es', '_fr', '_de', '_pt', '_it', '_ru', '_ar']:
        if filename_without_ext.endswith(lc_marker):
            filename_without_ext = filename_without_ext[:-len(lc_marker)]
            break
    translated_filename = f"{filename_without_ext}_{language_code}{ext}"
    translated_filepath = os.path.join(file_dir, translated_filename)
    with open(translated_filepath, 'w', encoding='utf-8') as f:
        f.write(final_text)
    logger.info(f"one-pass 翻译已保存到 {translated_filepath}")

    if theme and language_code == 'en':
        copy_script_files(translated_filepath, theme)


def main():
    parser = argparse.ArgumentParser(description="将文本文件或JSON文件中的段落翻译成指定语言。")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--text", help="要翻译的文本文件路径")
    group.add_argument("--json", help="要翻译的JSON文件路径 (包含段落)")
    parser.add_argument("--lang", default=SUPPORTED_LANGUAGES[0] if SUPPORTED_LANGUAGES else "English", 
                        help=f"翻译的目标语言 (默认为: {SUPPORTED_LANGUAGES[0] if SUPPORTED_LANGUAGES else 'English'})")
    parser.add_argument("--orig_lang", default="English", help="原文语言 (默认为: English)")
    parser.add_argument("--theme", help="主题名称，用于复制文件到目标目录")
    parser.add_argument("--skip_fact_check", action="store_true", help="跳过事实检查/实体链接步骤")
    parser.add_argument("--one-pass", action="store_true", help="不分块，直接整体改写和精炼，仅支持 --text 输入")

    args = parser.parse_args()
    
    # Update global CHECK_FACT based on argparse
    global CHECK_FACT
    if args.skip_fact_check:
        CHECK_FACT = False
        logger.info("Fact checking (Azure Entity Linking) is SKIPPED due to --skip_fact_check flag.")
    else:
        logger.info(f"Fact checking (Azure Entity Linking) is ENABLED. Current global CHECK_FACT: {CHECK_FACT}")

    file_path_to_process = None
    is_json_input = False

    if args.text:
        file_path_to_process = args.text
        is_json_input = False
    elif args.json:
        file_path_to_process = args.json
        is_json_input = True

    try:
        if is_json_input:
            if args.one_pass:
                logger.error("--one-pass 仅支持 --text 输入，不支持 --json 输入。")
                return
            with open(file_path_to_process, 'r', encoding='utf-8') as f:
                try:
                    json_content_loaded = json.load(f)
                    if not isinstance(json_content_loaded, list):
                        logger.error(f"JSON 文件 {file_path_to_process} 的顶层结构不是一个列表。")
                        return
                    if json_content_loaded:
                        first_element = json_content_loaded[0]
                        if not isinstance(first_element, dict) or \
                           "segment_id" not in first_element or \
                           "paragraph_text" not in first_element: # paragraph_text is the original text field
                            logger.error(f"JSON 文件 {file_path_to_process} 中的元素缺少必需的 'segment_id' 或 'paragraph_text' 字段。")
                            return 
                except json.JSONDecodeError as e:
                    logger.error(f"解析 JSON 文件 {file_path_to_process} 时出错: {e}")
                    return
            process_json_translation(file_path_to_process, json_content_loaded, args.orig_lang, args.lang, args.theme)
        else: 
            with open(file_path_to_process, 'r', encoding='utf-8') as f:
                content = f.read()
            if args.one_pass:
                one_pass_rewrite_and_refine(file_path_to_process, content, args.orig_lang, args.lang, args.theme)
            else:
                process_translation(file_path_to_process, content, args.orig_lang, args.lang, args.theme)

    except FileNotFoundError:
        logger.error(f"文件 {file_path_to_process} 未找到。")
    except Exception as e:
        logger.error(f"处理文件 {file_path_to_process} 时发生意外错误: {e}", exc_info=True) # Add exc_info for traceback

if __name__ == "__main__":
    main()