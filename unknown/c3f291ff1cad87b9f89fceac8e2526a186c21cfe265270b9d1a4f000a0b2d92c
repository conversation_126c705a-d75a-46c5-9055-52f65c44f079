{"1": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader"}, "2": {"inputs": {"lora_name": "Art_<PERSON><PERSON><PERSON><PERSON>_吉卜力动画风格_V1.safetensors", "strength_model": 1, "model": ["1", 0]}, "class_type": "LoraLoaderModelOnly"}, "3": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 720, "height": 1080, "model": ["2", 0]}, "class_type": "ModelSamplingFlux"}, "6": {"inputs": {"seed": 614508338992081, "steps": 24, "cfg": 1, "sampler_name": "euler", "scheduler": "ddim_uniform", "denoise": 1, "model": ["3", 0], "positive": ["15", 0], "negative": ["16", 0], "latent_image": ["12", 0]}, "class_type": "K<PERSON><PERSON><PERSON>"}, "12": {"inputs": {"width": 720, "height": 1080, "batch_size": 1}, "class_type": "EmptyLatentImage"}, "13": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "14": {"inputs": {"text": "A medieval castle's main hall with ancient stone walls and flickering torches casting shadows. In the center, a young man, Prince <PERSON>, stands with a resolute expression, dressed in royal medieval attire. He is surrounded by shocked onlookers, including noblemen and guards. In front of him, a woman accused of witchcraft kneels, her hands bound, looking up at <PERSON> with a mixture of fear and hope. The atmosphere is tense and dramatic, with the cold, damp air of the castle contrasting with the warm glow of the torches. The scene is depicted in a realistic, detailed artistic style, with a focus on the intricate textures of the stone walls, the rich fabrics of the clothing, and the subtle play of light and shadow.", "clip": ["13", 0]}, "class_type": "CLIPTextEncode"}, "15": {"inputs": {"guidance": 3.5, "conditioning": ["21", 0]}, "class_type": "FluxGuidance"}, "16": {"inputs": {"conditioning": ["14", 0]}, "class_type": "ConditioningZeroOut"}, "18": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "19": {"inputs": {"samples": ["6", 0], "vae": ["18", 0]}, "class_type": "VAEDecode"}, "20": {"inputs": {"filename_prefix": "ComfyUI", "images": ["19", 0]}, "class_type": "SaveImage"}, "21": {"inputs": {"conditioning_to": ["22", 0], "conditioning_from": ["14", 0]}, "class_type": "ConditioningConcat"}, "22": {"inputs": {"text": "<PERSON><PERSON><PERSON><PERSON>, ", "clip": ["13", 0]}, "class_type": "CLIPTextEncode"}}