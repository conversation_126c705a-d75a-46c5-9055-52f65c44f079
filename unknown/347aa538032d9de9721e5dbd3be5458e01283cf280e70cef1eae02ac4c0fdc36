import subprocess
import json
from datetime import timed<PERSON>ta
import re # Added for sanitize_filename
import os # Added for sanitize_filename (splitext, basename)

def sanitize_filename(filename):
    """
    Sanitizes a filename by removing or replacing characters that are generally
    not allowed or problematic in file systems.
    """
    # Remove characters that are definitely problematic
    # Also removing characters that might be problematic in URLs or certain shells
    filename = re.sub(r'[<>:"/\\|?*\x00-\x1f\'\`#&!@$%^&*()+={}[\];~]', '_', filename)
    # Replace multiple spaces or underscores with a single underscore
    filename = re.sub(r'[\s._-]+', '_', filename)
    # Remove leading/trailing underscores that might have resulted
    filename = filename.strip('_')
    
    # Limit length (optional, but good practice for cross-platform compatibility)
    # Many systems have a limit around 255 bytes (not necessarily characters)
    # We will aim for a character limit, assuming UTF-8 where chars can be >1 byte.
    MAX_FILENAME_CHARS = 200 # A conservative limit
    if len(filename) > MAX_FILENAME_CHARS:
        # Try to preserve extension if one exists
        base, ext = os.path.splitext(filename)
        # Calculate how much of the base name we can keep
        # -1 for the dot if an extension exists
        allowed_base_len = MAX_FILENAME_CHARS - (len(ext) + (1 if ext else 0))
        
        if allowed_base_len <= 0 and ext: # Extension itself is too long
             filename = ext[:MAX_FILENAME_CHARS] # Truncate extension
        elif allowed_base_len < 0 and not ext: # Filename (no ext) is just too long.
            filename = base[:MAX_FILENAME_CHARS]
        elif ext: # Normal case with extension
            filename = base[:allowed_base_len] + ext
        else: # No extension
            filename = base[:MAX_FILENAME_CHARS]

    if not filename: # if filename becomes empty after sanitization
        filename = "sanitized_empty_name" # Provide a default name
    return filename

def frame_timecode_to_seconds(frame_timecode_obj, frame_rate=None):
    """Converts a PySceneDetect FrameTimecode object to seconds."""
    if hasattr(frame_timecode_obj, 'get_seconds'):
        return frame_timecode_obj.get_seconds()
    elif frame_rate and hasattr(frame_timecode_obj, 'get_frames'): # Fallback if only frames are available
        return frame_timecode_obj.get_frames() / frame_rate
    raise ValueError("Unsupported FrameTimecode object or missing frame_rate")

def run_ffmpeg_command(cmd_list):
    """Runs an FFmpeg command using subprocess and logs output."""
    try:
        print(f"Running FFmpeg command: {' '.join(cmd_list)}")
        process = subprocess.Popen(cmd_list, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f"FFmpeg Error: {stderr.decode('utf-8')}")
            return False, stderr.decode('utf-8')
        # print(f"FFmpeg Output: {stdout.decode('utf-8')}")
        return True, stdout.decode('utf-8')
    except FileNotFoundError:
        print("FFmpeg command not found. Please ensure FFmpeg is installed and in your PATH.")
        return False, "FFmpeg not found"
    except Exception as e:
        print(f"An error occurred while running FFmpeg: {e}")
        return False, str(e)

def format_timestamp_srt(seconds):
    """Converts seconds to SRT timestamp format (HH:MM:SS,ms)."""
    delta = timedelta(seconds=seconds)
    hours = delta.seconds // 3600
    minutes = (delta.seconds % 3600) // 60
    secs = delta.seconds % 60
    milliseconds = delta.microseconds // 1000
    return f"{hours:02}:{minutes:02}:{secs:02},{milliseconds:03}"

def get_video_duration(video_path):
    """Gets the duration of a video file using ffprobe."""
    cmd = [
        "ffprobe",
        "-v", "error",
        "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1",
        video_path
    ]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True)
        return float(result.stdout.strip())
    except subprocess.CalledProcessError as e:
        print(f"Error getting video duration for {video_path}: {e.stderr}")
        return None
    except FileNotFoundError:
        print("ffprobe command not found. Please ensure FFmpeg (which includes ffprobe) is installed and in your PATH.")
        return None

# Example: If you need to serialize FrameTimecode objects for metadata
class FrameTimecodeEncoder(json.JSONEncoder):
    def default(self, obj):
        from scenedetect import FrameTimecode # Local import to avoid circularity if utils is imported by scenedetect related modules early
        if isinstance(obj, FrameTimecode):
            return {'timecode': obj.get_timecode(), 'frames': obj.get_frames(), 'fps': obj.framerate}
        return json.JSONEncoder.default(self, obj)

def deserialize_frametimecode(data):
    from scenedetect import FrameTimecode # Local import
    return FrameTimecode(timecode=data['timecode'], framerate=data['fps']) 