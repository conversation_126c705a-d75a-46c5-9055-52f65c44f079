import os
from pathlib import Path
from dotenv import load_dotenv
import logging

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
# Load environment variables from .env file
load_dotenv()

# Project root directory
ROOT_DIR = Path('/Users/<USER>/ai-video/data')

# API Keys
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
YOUTUBE_API_KEY = os.getenv('GOOGLE_API_KEY')
DUCKDUCKGO_API_KEY = os.getenv('DUCKDUCKGO_API_KEY')

# DuckDuckGo API Configuration
DUCKDUCKGO_API_URL = "https://api.duckduckgo.com/"
DUCKDUCKGO_PARAMS = {
    'format': 'json',
    'pretty': 1,
    'no_html': 1,
    'skip_disambig': 1
}

# YouTube Search Configuration
MAX_RESULTS = 5  # Number of YouTube URLs to fetch

# Directory Configuration
DOWNLOAD_DIR = ROOT_DIR / 'downloads'
OUTPUT_DIR = ROOT_DIR / 'video_clips'
LOG_DIR = ROOT_DIR / 'logs'
COOKIE_FILE = ROOT_DIR / 'settings' / 'cookies.txt'

# yt-dlp Configuration
DOWNLOAD_OPTIONS = {
    'format': 'bestvideo+bestaudio/best',
    'outtmpl': str(DOWNLOAD_DIR / '%(title)s.%(ext)s'),
    'ignoreerrors': True,
    'quiet': True,
    'no_warnings': True,
}

# Video全局参数
VIDEO_RESOLUTION = (1920, 1080)  # Width x Height
VIDEO_BITRATE = '6000K'  # 提升至6Mbps
VIDEO_FPS = 25
VIDEO_PRESET = 'medium'
VIDEO_CRF = 23  # 降低CRF值以提高质量（范围0-51，越小质量越好）
VIDEO_CODEC = 'libx264'
VIDEO_AUDIO_CODEC = 'aac'
VIDEO_AUDIO_BITRATE = '192k'  # 新增：音频比特率

XFADE_TRANSITION_TIME = '1.000'
VOICE_DELAY_TIME = 1000 #milliseconds
# 新增：FFmpeg 通用参数组
FFMPEG_BASE_PARAMS = [
    '-hide_banner',
    '-loglevel', 'error',
    '-stats',
    #'-stats_period', '10'
]

# 新增：视频编码通用参数组
VIDEO_ENCODE_PARAMS = [
    '-c:v', VIDEO_CODEC,
    '-preset', VIDEO_PRESET,
    '-crf', str(VIDEO_CRF),
    '-r', str(VIDEO_FPS),
    '-movflags', '+faststart',  # 支持快速播放
    '-pix_fmt', 'yuv420p'      # 确保兼容性
]

# 新增：音频编码通用参数组
AUDIO_ENCODE_PARAMS = [
    '-c:a', VIDEO_AUDIO_CODEC,
    '-b:a', VIDEO_AUDIO_BITRATE
]

# ffmpeg Configuration
AUDIO_FORMAT = 'wav'

# Whisper Configuration
WHISPER_MODEL = 'base'  # Options: tiny, base, small, medium, large

# Logging Configuration
LOG_FILE = LOG_DIR / 'app.log'

# Create necessary directories
for directory in [DOWNLOAD_DIR, OUTPUT_DIR, LOG_DIR]:
    directory.mkdir(parents=True, exist_ok=True) 