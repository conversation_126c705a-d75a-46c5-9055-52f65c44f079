import argparse
import os
import json
from modules.tavily_search import search_tavily, process_results
from modules.describe_images import generate_keyword
from config import IMAGE_DIR, LANGUAGE_CODES, get_image_metadata_path,TEMP_DIR, FILTERED_IMAGE_DIR
from modules.filter_irrelevant_images import filter_images_by_clip
from modules.utils import get_image_paths, normalize_words
from modules.text_preprocessing import generate_keywords
from video_downloader import process_web_url, get_config
import nltk
from langdetect import detect, LangDetectException
from typing import Optional, List, Dict
import logging
import shutil
import datetime
import numpy as np
import atexit
import sys

INITIAL_CLIP_THRESHOLD = 0.27
MIN_CLIP_THRESHOLD = 0.20
MIN_REQUIRED_IMAGES = 200
THRESHOLD_STEP = 0.01


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 下载必要的 NLTK 数据
nltk.download('punkt')
nltk.download('punkt_tab')

def get_language_name(lang_code):
    for name, code in LANGUAGE_CODES.items():
        if code == lang_code:
            return name
    return "English"  # 默认返回英语


def build_clip_prompts(theme: str, keyword: Optional[str] = None) -> List[str]:
    """生成多个基于CLIP最佳实践的提示词"""
    base_prompt = f"{theme} {keyword}" if keyword and keyword != theme else theme
    templates = [
        "a high quality photo of {}",
        "a detailed photograph of {}",
        "a professional image of {}",
        "a clear, high resolution image of {}"
    ]
    return [template.format(base_prompt) for template in templates]

def move_filtered_images(irrelevant_images: List[str], filtered_dir: str) -> List[str]:
    """
    将不相关的图片移动到过滤目录
    
    Args:
        irrelevant_images: 不相关图片的路径列表
        filtered_dir: 过滤图片存放目录
    
    Returns:
        List[str]: 成功移动的图片的新路径列表
    """
    os.makedirs(filtered_dir, exist_ok=True)
    moved_images = []
    
    for image_path in irrelevant_images:
        try:
            dest_path = os.path.join(filtered_dir, os.path.basename(image_path))
            shutil.move(image_path, dest_path)
            moved_images.append(dest_path)
        except Exception as e:
            logger.error(f"移动图像 '{image_path}' 时出错: {e}")
    
    logger.info(f"成功移动 {len(moved_images)}/{len(irrelevant_images)} 张不相关图片到 {filtered_dir}")
    return moved_images

def filter_images_with_dynamic_threshold(theme: str, image_dir: str, filtered_images: list, filter_prompts=None) -> tuple[list, float]:
    """
    使用动态阈值过滤图片，确保保留足够数量的相关图片
    
    Args:
        theme: 主题名称（用于目录创建）
        image_dir: 图像目录
        filtered_images: 预过滤的图像列表
        filter_prompts: 用于CLIP模型的提示词，默认为None时使用theme
    """
    filtered_dir = FILTERED_IMAGE_DIR.format(theme=theme)
    os.makedirs(filtered_dir, exist_ok=True)
    
    # 获取所有图片路径
    image_paths = get_image_paths(image_dir)
    if not image_paths:
        logger.warning("没有找到需要过滤的图片")
        return filtered_images, INITIAL_CLIP_THRESHOLD
    
    best_remaining_images = []
    best_threshold = INITIAL_CLIP_THRESHOLD
    current_threshold = INITIAL_CLIP_THRESHOLD
    final_irrelevant_images = []
    
    try:
        # 1. 尝试不同的阈值进行过滤，但不移动文件
        while current_threshold >= MIN_CLIP_THRESHOLD:
            logger.info(f"使用阈值 {current_threshold:.2f} 进行CLIP过滤...")
            
            # 修复：如果filter_prompts是列表，使用第一个元素或连接成单个字符串
            clip_input = filter_prompts[0] if isinstance(filter_prompts, list) and filter_prompts else theme
            
            relevant_images, irrelevant_images = filter_images_by_clip(
                theme=clip_input,
                image_paths=image_paths,
                threshold=current_threshold
            )
            
            # 二次过滤，确保图片在 filtered_images 列表中
            remaining_images = [
                img for img in filtered_images 
                if os.path.basename(img['filepath']) in [os.path.basename(path) for path in relevant_images]
            ]
            
            remaining_count = len(remaining_images)
            logger.info(f"阈值 {current_threshold:.2f} 过滤后保留了 {remaining_count} 张相关图片")
            
            # 更新最佳结果
            if remaining_count > len(best_remaining_images):
                best_remaining_images = remaining_images
                best_threshold = current_threshold
                final_irrelevant_images = irrelevant_images
            
            # 如果达到目标数量或已达最低阈值，结束尝试
            if remaining_count >= MIN_REQUIRED_IMAGES or current_threshold <= MIN_CLIP_THRESHOLD:
                break
            
            current_threshold -= THRESHOLD_STEP
        
        # 2. 使用最佳阈值的结果，一次性移动不相关的图片
        if final_irrelevant_images:
            move_filtered_images(final_irrelevant_images, filtered_dir)
        
        logger.info(f"最终使用的阈值为: {best_threshold:.2f}，过滤后剩余图片数: {len(best_remaining_images)}")
        return best_remaining_images, best_threshold
        
    except Exception as e:
        logger.error(f"过滤图片时出错: {e}")
        return filtered_images, INITIAL_CLIP_THRESHOLD

def load_attribute_json(json_file: str) -> dict:
    """加载属性 JSON 文件"""
    if not os.path.exists(json_file):
        logger.warning(f"属性 JSON 文件不存在: {json_file}")
        return {}
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"已加载属性 JSON 文件: {json_file}")
        return {os.path.basename(item['filepath']): item for item in data if 'filepath' in item}
    except Exception as e:
        logger.error(f"读取属性 JSON 文件时出错: {e}")
        return {}

def download_images(theme: str, output_dir: str, keyword: str = None, download_video: bool = False):
    """下载和处理图片，返回搜索结果以供后续视频下载使用
    
    Args:
        theme: 主题名称（用于文件夹命名和结果保存）
        output_dir: 输出目录
        keyword: 用于tavily搜索的关键词，如果为None则使用theme
        download_video: 是否下载视频
    """
    metadata_path = get_image_metadata_path(output_dir, theme)
    existing_images = []
    
    # 1. 读取并验证现有数据
    if os.path.exists(metadata_path):
        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                existing_images = json.load(f)
            
            # 验证现有图片的完整性
            valid_existing_images = []
            for img in existing_images:
                img_path = os.path.join(output_dir, img['filepath'])
                if os.path.exists(img_path):
                    valid_existing_images.append(img)
                else:
                    logger.warning(f"图片文件不存在，将从结果中移除: {img_path}")
            
            existing_images = valid_existing_images
            logger.info(f"读取到有效的现有结果: {len(existing_images)} 张图片")
        except Exception as e:
            logger.warning(f"读取现有数据时出错: {e}")
            existing_images = []
    
    # 2. 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 3. 获取新的搜索结果
    results, images = search_tavily(keyword)
    if not results:
        logger.error(f"没有找到搜索关键词 '{keyword or theme}' 的结果")
        if existing_images:
            logger.info("将只使用现有的图片进行处理")
            filtered_images = existing_images
        else:
            return None
    else:
        # 4. 处理新的搜索结果
        new_filtered_images = process_results(theme, results, images)
        
        # 5. 合并新旧结果
        existing_urls = {img['url'] for img in existing_images}
        existing_filepaths = {img['filepath'] for img in existing_images}
        
        merged_images = existing_images + [
            img for img in new_filtered_images 
            if img['url'] not in existing_urls and 
               img['filepath'] not in existing_filepaths
        ]
        
        logger.info(f"合并后的图片总数: {len(merged_images)} (现有: {len(existing_images)}, 新增: {len(new_filtered_images)})")
        filtered_images = merged_images
    
    # 6. 过滤所有图片
    try:
        # 使用多个基于CLIP最佳实践的提示词
        filter_prompts = build_clip_prompts(theme, keyword)
        logger.info(f"开始使用动态阈值CLIP模型过滤所有图片，初始图片数是{len(filtered_images)}，使用过滤提示词: {filter_prompts}")
        
        # 正确传递：theme用于目录命名，filter_prompts用于CLIP过滤
        filtered_images, final_threshold = filter_images_with_dynamic_threshold(
            theme=theme,  # 保持原主题名用于目录结构
            image_dir=output_dir,
            filtered_images=filtered_images,
            filter_prompts=filter_prompts  # 额外参数传递优化的提示词
        )
        logger.info(f"最终使用的阈值为: {final_threshold:.2f}，过滤后剩余图片数: {len(filtered_images)}")
    except Exception as e:
        logger.error(f"过滤不相关图片时出错: {e}")
        if existing_images:
            logger.info("使用现有的过滤结果")
            filtered_images = existing_images
        else:
            logger.warning("没有可用的备份数据")
            raise
    
    # 7. 保存结果（带备份）
    if os.path.exists(metadata_path):
        backup_path = f"{metadata_path}.bak"
        try:
            shutil.copy2(metadata_path, backup_path)
            logger.info(f"已创建元数据文件备份: {backup_path}")
        except Exception as e:
            logger.warning(f"创建备份文件失败: {e}")
    
    try:
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(filtered_images, f, ensure_ascii=False, indent=4)
        logger.info(f"已成功处理主题 '{theme}' 并保存合并结果到 {metadata_path}")
    except Exception as e:
        logger.error(f"保存主题 '{theme}' 的结果时失败: {e}", exc_info=True)
        # 如果保存失败且存在备份，尝试恢复备份
        if os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, metadata_path)
                logger.info("已恢复到备份版本")
            except Exception as restore_error:
                logger.error(f"恢复备份失败: {restore_error}")
    
    return results

def process_videos(theme: str, search_results: List[Dict]):
    """处理搜索结果中的视频"""

    
    logger.info("开始处理视频下载...")
    success_count = 0
    total_urls = len(search_results)
    
    # 获取配置
    config = get_config(theme)  # 需要导入 get_config
    
    for idx, result in enumerate(search_results, 1):
        url = result.get('url')
        if not url:
            continue
            
        logger.info(f"正在处理第 {idx}/{total_urls} 个URL: {url}")
        try:
            if process_web_url(url, config):  # 传入配置参数
                success_count += 1
                logger.info(f"成功从 {url} 下载视频")
            else:
                logger.warning(f"从 {url} 下载视频失败")
        except Exception as e:
            logger.error(f"处理 URL {url} 时出错: {e}")
    
    logger.info(f"视频处理完成。成功: {success_count}/{total_urls}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="下载图片并生成嵌入")
    parser.add_argument("--script", required=True, help="包含文本的文件路径")
    parser.add_argument("--theme", help="主题名称（用于文件夹命名）")
    parser.add_argument("--gen_theme", action="store_true", help="自动生成主题名称和搜索关键词")
    parser.add_argument("--keyword", nargs='?', const=True, help="指定搜索关键词，不提供值则自动生成")
    parser.add_argument("--download_video", action="store_true", help="是否下载相关视频")
    parser.add_argument("--log_level", type=str, default="INFO", 
                      choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                      help="设置日志级别")
    parser.add_argument("--lang", type=str, help="指定语言")

    args = parser.parse_args()
    logging.basicConfig(level=args.log_level)
    logger.setLevel(args.log_level)

    # 读取脚本文件
    with open(args.script, 'r', encoding='utf-8') as file:
        transcript_text = file.read()
    
    # 使用指定的语言或默认为英语
    detected_language = args.lang if args.lang else "English"
    logger.info(f"使用语言: {detected_language}")

    # 确定主题名称和搜索关键词
    if args.gen_theme:
        try:
            # 一次性生成主题关键词和多个额外关键词
            keyword_results = generate_keywords(args.script, detected_language)
            
            # 从结果中提取关键词
            topic_keyword = keyword_results["topic"]
            additional_keywords = keyword_results["keywords"]
            
            if not topic_keyword or not topic_keyword.strip():
                logger.error("生成的主题关键词为空")
                sys.exit(1)
                
            if not additional_keywords or len(additional_keywords) < 2:
                logger.error("生成的额外关键词不足")
                sys.exit(1)
            
            # 设置主题和主关键词
            theme = normalize_words(topic_keyword)
            keyword_main = topic_keyword
            
            # 获取前两个额外关键词
            additional_keyword1 = additional_keywords[0]
            additional_keyword2 = additional_keywords[1]
            
        except Exception as e:
            logger.error(f"生成关键词时出错: {e}")
            sys.exit(1)

        logger.info(f"生成主题名称: {theme}, 主搜索关键词: {keyword_main}")
        logger.info(f"额外搜索关键词1: {additional_keyword1}, 额外搜索关键词2: {additional_keyword2}")

        output_dir = IMAGE_DIR.format(theme=theme)
        os.makedirs(output_dir, exist_ok=True)

        # 执行三次搜索
        results_main, images_main = search_tavily(keyword_main)
        results_top, images_top = search_tavily(f"{additional_keyword1} + {keyword_main}")
        results_bottom, images_bottom = search_tavily(f"{additional_keyword2} + {keyword_main}")

        # 处理搜索结果（若存在搜索结果则调用 process_results，否则为空列表）
        new_images_main = process_results(theme, results_main, images_main) if results_main else []
        new_images_top = process_results(theme, results_top, images_top) if results_top else []
        new_images_bottom = process_results(theme, results_bottom, images_bottom) if results_bottom else []

        # 读取并验证现有的搜索结果
        metadata_path = get_image_metadata_path(output_dir, theme)
        existing_images = []
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    existing_images = json.load(f)
                valid_existing_images = []
                for img in existing_images:
                    img_path = os.path.join(output_dir, img['filepath'])
                    if os.path.exists(img_path):
                        valid_existing_images.append(img)
                existing_images = valid_existing_images
                logger.info(f"读取到有效的现有结果: {len(existing_images)} 张图片")
            except Exception as e:
                logger.warning(f"读取现有数据时出错: {e}")
                existing_images = []

        # 合并三个搜索结果与现有结果（根据 url 和 filepath 去重）
        combined_new_images = new_images_main + new_images_top + new_images_bottom
        existing_urls = {img['url'] for img in existing_images}
        existing_filepaths = {img['filepath'] for img in existing_images}
        merged_images = existing_images + [
            img for img in combined_new_images 
            if img['url'] not in existing_urls and img['filepath'] not in existing_filepaths
        ]
        logger.info(f"合并后的图片总数: {len(merged_images)} (现有: {len(existing_images)}, 新增: {len(combined_new_images)})")

        # 构造包含所有关键词的过滤提示词
        combined_keywords = f"{keyword_main} {additional_keyword1} {additional_keyword2}"
        filter_prompts = build_clip_prompts(theme, combined_keywords)
        logger.info(f"开始使用动态阈值CLIP模型过滤所有图片，初始图片数是{len(merged_images)}，使用过滤提示词: {filter_prompts}")

        try:
            filtered_images, final_threshold = filter_images_with_dynamic_threshold(
                theme=theme,
                image_dir=output_dir,
                filtered_images=merged_images,
                filter_prompts=filter_prompts
            )
            logger.info(f"最终使用的阈值为: {final_threshold:.2f}，过滤后剩余图片数: {len(filtered_images)}")
        except Exception as e:
            logger.error(f"过滤不相关图片时出错: {e}")
            filtered_images = merged_images

        # 保存过滤结果（备份原有元数据文件）
        if os.path.exists(metadata_path):
            backup_path = f"{metadata_path}.bak"
            try:
                shutil.copy2(metadata_path, backup_path)
                logger.info(f"已创建元数据文件备份: {backup_path}")
            except Exception as e:
                logger.warning(f"创建备份文件失败: {e}")
        try:
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(filtered_images, f, ensure_ascii=False, indent=4)
            logger.info(f"已成功处理主题 '{theme}' 并保存合并结果到 {metadata_path}")
        except Exception as e:
            logger.error(f"保存主题 '{theme}' 的结果时失败: {e}", exc_info=True)
            if os.path.exists(backup_path):
                try:
                    shutil.copy2(backup_path, metadata_path)
                    logger.info("已恢复到备份版本")
                except Exception as restore_error:
                    logger.error(f"恢复备份失败: {restore_error}")

        # 处理视频：将三个搜索结果合并后处理视频
        if args.download_video and (results_main or results_top or results_bottom):
            combined_results = []
            for res in (results_main or [], results_top or [], results_bottom or []):
                combined_results.extend(res)
            process_videos(theme, combined_results)
    elif args.keyword:
        # 如果只生成关键词，使用指定的主题名称
        theme = args.theme
        # 如果提供了具体的关键词则使用，否则生成
        keyword = args.keyword if isinstance(args.keyword, str) else generate_keyword(args.script, detected_language)
        logger.info(f"使用指定主题名称: {theme}, {'使用指定' if isinstance(args.keyword, str) else '自动生成'}搜索关键词: {keyword}")
        output_dir = IMAGE_DIR.format(theme=theme)
        search_results = download_images(theme=theme, output_dir=output_dir, keyword=keyword)
        if args.download_video and search_results:
            process_videos(theme, search_results)
    else:
        # 使用指定的主题名称作为关键词
        theme = keyword = args.theme
        logger.info(f"使用指定主题名称作为搜索关键词: {theme}")
        output_dir = IMAGE_DIR.format(theme=theme)
        search_results = download_images(theme=theme, output_dir=output_dir, keyword=keyword)
        if args.download_video and search_results:
            process_videos(theme, search_results)

if __name__ == "__main__":
    main()