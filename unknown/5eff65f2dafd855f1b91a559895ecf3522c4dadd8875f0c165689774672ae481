import os
import json
import argparse
import whisper
import torch
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
import tempfile
import re
from config import logger, TEMP_FILE_DIR
from modules.text_optimizer import smart_paragraph_split
from modules.text_preprocessing import split_into_sentences

# 确保临时文件目录存在
if not os.path.exists(TEMP_FILE_DIR):
    os.makedirs(TEMP_FILE_DIR)

tempfile.tempdir = TEMP_FILE_DIR

SEGMENTS_PER_PARAGRAPH = 10  # 每个段落包含的segment数量
DEFAULT_WHISPER_MODEL = "medium"  # 默认模型

# 检查并设置设备
device = torch.device("mps") if torch.backends.mps.is_available() else torch.device("cpu")
logger.info(f"使用设备: {device}")

# 若MPS可用但有兼容性问题，设置环境变量允许回落到CPU
if device.type == "mps" and os.environ.get("PYTORCH_ENABLE_MPS_FALLBACK") != "1":
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    logger.info("已启用MPS回落支持")

# 全局模型缓存
_whisper_model_cache = {}

@contextmanager
def temporary_wav_file():
    """创建一个临时 WAV 文件，并在使用后自动删除"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    try:
        yield temp_file.name
    finally:
        os.unlink(temp_file.name)

def get_whisper_model(model_name: str):
    """获取Whisper模型实例，使用缓存避免重复加载"""
    if model_name not in _whisper_model_cache:
        logger.info(f"加载Whisper模型到 {device}: {model_name}")
        # 先加载模型
        model = whisper.load_model(model_name)
        
        # 处理稀疏张量问题
        if "alignment_heads" in dict(model.named_buffers()):
            sparse_heads = model.get_buffer("alignment_heads")
            dense_heads = sparse_heads.to_dense()
            model.register_buffer("alignment_heads", dense_heads, persistent=False)
        
        # 再移动到目标设备
        model = model.to(device)
        _whisper_model_cache[model_name] = model
    return _whisper_model_cache[model_name]

def whisper_transcribe(audio_file: str, model_name: str = DEFAULT_WHISPER_MODEL) -> List[Dict[str, Any]]:
    """
    使用 Whisper 模型转录音频文件，自动检测语言
    """
    try:
        logger.info(f"开始 Whisper 转录: {audio_file}, 使用模型: {model_name}")
        # 使用缓存的模型
        model = get_whisper_model(model_name)
        result = model.transcribe(audio_file, language=None)
        segments = result["segments"]
        logger.info(f"Whisper 转录完成，共 {len(segments)} 个段。")
        return segments
    except Exception as e:
        logger.error(f"Whisper 转录过程中出错: {e}")
        logger.exception("详细错误信息")
        return []

def add_punctuation(text: str) -> str:
    """
    使用规则和模式识别添加基本标点符号
    """
    # 1. 添加句号 - 在明显的句子结束位置
    text = re.sub(r'([^。！？\.\!\?])\s+([A-Z我你他她它这那])', r'\1。 \2', text)
    
    # 2. 添加问号 - 在疑问词后
    text = re.sub(r'(为什么|怎么|什么|谁|何时|哪里|是否|吗)\s+([A-Z我你他她它])', r'\1？ \2', text)
    
    # 3. 添加逗号 - 在连接词前
    text = re.sub(r'([^，,。！？\.\!\?])\s+(但是|然而|不过|因此|所以|因为|如果|虽然)', r'\1，\2', text)
    
    # 4. 处理数字和日期后的标点
    text = re.sub(r'(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年|\d{4}-\d{2}-\d{2})\s+', r'\1，', text)
    
    # 5. 处理引用和对话
    text = re.sub(r'"([^"]+)"\s+', r'"\1"，', text)
    
    # 6. 确保句子结尾有标点
    if not re.search(r'[。！？\.\!\?]$', text):
        text = text + "。"
    
    # 改进：更多重复标点的处理
    text = re.sub(r'([，。！？\.!?])\1+', r'\1', text)  # 去除重复标点
    
    # 改进：处理长句
    if len(text) > 100 and '。' not in text and '！' not in text and '？' not in text:
        # 在自然停顿处添加句号
        pause_patterns = [
            r'([^，。！？\.\!\?])(然后|接着|随后|之后|其次|因此)',
            r'([^，。！？\.\!\?])(但是|不过|然而|相反)',
        ]
        for pattern in pause_patterns:
            text = re.sub(pattern, r'\1。\2', text)
    
    return text

def fallback_sentence_split(text: str) -> List[str]:
    """
    当常规分句方法失败时的备用分句策略
    """
    # 1. 尝试按照明显的分隔符分割
    sentences = []
    
    # 按照常见的句子分隔符分割
    segments = re.split(r'([。！？\.\!\?]+)', text)
    
    # 重组句子（保留标点）
    for i in range(0, len(segments)-1, 2):
        if segments[i]:
            sentence = segments[i] + (segments[i+1] if i+1 < len(segments) else '')
            sentences.append(sentence.strip())
    
    # 处理最后一个片段
    if len(segments) % 2 == 1 and segments[-1].strip():
        sentences.append(segments[-1].strip())
    
    # 如果仍然没有足够的句子，尝试按照逗号、分号等次要分隔符分割
    if len(sentences) < 3:
        sentences = []
        segments = re.split(r'([,，;；])', text)
        
        for i in range(0, len(segments)-1, 2):
            if segments[i]:
                sentence = segments[i] + (segments[i+1] if i+1 < len(segments) else '')
                sentences.append(sentence.strip())
        
        if len(segments) % 2 == 1 and segments[-1].strip():
            sentences.append(segments[-1].strip())
    
    # 如果仍然没有足够的句子，尝试按照固定长度分割
    if len(sentences) < 3:
        sentences = []
        chunk_size = 100  # 大约每100个字符分割一次
        
        for i in range(0, len(text), chunk_size):
            chunk = text[i:i+chunk_size].strip()
            if chunk:
                sentences.append(chunk)
    
    return sentences

def is_natural_break(text: str) -> bool:
    """
    检测是否是自然的段落结束点
    """
    # 1. 基于结尾标点判断
    if re.search(r'[。！？\.\!\?]$', text):
        return True
    
    # 2. 基于关键词判断
    transition_words = [
        '总之', '因此', '所以', '然而', '但是', '不过', '另外', '此外',
        '接下来', '首先', '其次', '最后', '总结', '综上所述'
    ]
    
    for word in transition_words:
        if word in text:
            return True
    
    # 3. 基于句子长度判断
    if len(text) > 100:  # 较长的句子可能是段落结束
        return True
    
    return False

def organize_paragraphs(sentences: List[str]) -> List[str]:
    """
    将句子组织成段落
    """
    if not sentences:
        return []
    
    # 使用smart_paragraph_split函数进行智能分段
    try:
        # 先将句子合并成一个文本
        text = " ".join(sentences)
        
        paragraphs = smart_paragraph_split(
            text,
            min_sentences=1,
            max_sentences=5,
            language='Chinese'
        )
        
        # 如果分段结果合理，直接返回
        if len(paragraphs) >= 2:
            return paragraphs
    except Exception as e:
        logger.warning(f"智能分段失败: {e}")
    
    # 备用方案：基于句子数量的简单分段
    paragraphs = []
    current_paragraph = []
    
    for sentence in sentences:
        current_paragraph.append(sentence)
        
        # 每3-5个句子形成一个段落
        if len(current_paragraph) >= 3 and (len(current_paragraph) >= 5 or is_natural_break(sentence)):
            paragraphs.append(" ".join(current_paragraph))
            current_paragraph = []
    
    # 处理剩余的句子
    if current_paragraph:
        paragraphs.append(" ".join(current_paragraph))
    
    return paragraphs

def simple_paragraph_split(text: str) -> List[str]:
    """
    简单的段落分割方法，作为最后的保底方案
    """
    # 按照固定长度分割文本
    paragraphs = []
    max_length = 200  # 每段大约200个字符
    
    # 尝试按照换行符分割
    if '\n' in text:
        raw_paragraphs = text.split('\n')
        for p in raw_paragraphs:
            if p.strip():
                paragraphs.append(p.strip())
    else:
        # 按照固定长度分割
        for i in range(0, len(text), max_length):
            # 寻找附近的空格作为分割点
            end = min(i + max_length, len(text))
            
            # 如果不是文本末尾，尝试找到更好的分割点
            if end < len(text):
                # 向后查找最近的句号、问号或感叹号
                for j in range(end, max(i, end - 50), -1):
                    if j < len(text) and text[j] in '。.!?！？':
                        end = j + 1
                        break
            
            paragraph = text[i:end].strip()
            if paragraph:
                paragraphs.append(paragraph)
    
    return paragraphs

def optimize_transcription(text: str) -> List[str]:
    """
    优化转录文本的格式和结构
    1. 添加缺失的标点符号
    2. 智能分段
    3. 清理多余空格
    """
    try:
        # 第一步：基础清理
        text = re.sub(r'\s+', ' ', text)  # 合并多余空格
        text = text.replace(" ,", ",").replace(" .", ".")  # 修复常见标点错误
        
        # 第二步：添加标点符号
        punctuated_text = add_punctuation(text)
        
        # 第三步：使用现有的分句和分段函数
        sentences = split_into_sentences(punctuated_text)
        
        # 如果分句结果太少（可能是标点不足），尝试使用备用分句策略
        if len(sentences) < 3 and len(text) > 500:
            logger.warning(f"分句结果过少（{len(sentences)}个句子），尝试使用备用分句策略")
            sentences = fallback_sentence_split(punctuated_text)
        
        # 第四步：将句子组织成段落
        paragraphs = organize_paragraphs(sentences)
        
        return paragraphs
        
    except Exception as e:
        logger.error(f"文本优化失败: {e}")
        # 保底方案：使用简单的长度分割
        return simple_paragraph_split(text)

def transcribe_audio_in_chunks(audio_file: str, model_name: str = DEFAULT_WHISPER_MODEL) -> Optional[str]:
    """处理完整音频文件，使用 Whisper 模型进行转录"""
    try:
        # 调用 Whisper 转录函数
        segments = whisper_transcribe(audio_file, model_name)
        
        if not segments:
            logger.error("转录失败，没有返回任何文本")
            return None

        # 将segments合并为原始文本
        raw_text = " ".join([s['text'].strip() for s in segments])
        
        # 优化文本：添加标点和分段
        optimized_text = optimize_transcription(raw_text)

        return "\n\n".join(optimized_text)

    except Exception as e:
        logger.error(f"音频文件处理过程中出错: {e}")
        logger.exception("详细错误信息")
        return None

def save_transcript_to_file(transcript_text: str, audio_file: str) -> str:
    """将转录文本保存到文件"""
    # 使用音频文件的名称，但将扩展名改为 .txt
    base_name = os.path.splitext(os.path.basename(audio_file))[0]
    output_file = os.path.join(os.path.dirname(audio_file), f"{base_name}.txt")
    
    # 写入转录文本到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(transcript_text)
    
    logger.info(f"转录文本已保存到: {output_file}")
    return output_file

def main():
    """主函数，用于命令行使用"""
    parser = argparse.ArgumentParser(description="使用 Whisper 模型将音频文件转为文本")
    parser.add_argument("audio_file", type=str, help="音频文件路径 (mp3 或 wav)")
    parser.add_argument("--model", type=str, choices=["tiny", "base", "small", "medium", "large"], 
                        default=DEFAULT_WHISPER_MODEL, help="选择Whisper模型大小")
    parser.add_argument("--device", type=str, choices=["cpu", "mps"], 
                        default="auto", help="选择推理设备 (auto=自动检测)")
    args = parser.parse_args()

    # 设置设备
    global device
    if args.device == "cpu":
        device = torch.device("cpu")
    elif args.device == "mps" and torch.backends.mps.is_available():
        device = torch.device("mps")
    # auto 模式下保持之前的自动检测结果
        
    logger.info(f"使用设备: {device}")

    audio_file = args.audio_file

    if not os.path.isfile(audio_file):
        logger.error(f"未找到音频文件: {audio_file}")
        return

    try:
        # 调用转录函数
        transcript_text = transcribe_audio_in_chunks(audio_file, args.model)
        
        if transcript_text:
            # 保存文本转录
            output_file = save_transcript_to_file(transcript_text, audio_file)
            print(f"转录已保存至 {output_file}")
        else:
            logger.error("转录失败，未返回任何文本")
        
    except Exception as e:
        logger.error(f"发生错误: {e}")

if __name__ == "__main__":
    main()