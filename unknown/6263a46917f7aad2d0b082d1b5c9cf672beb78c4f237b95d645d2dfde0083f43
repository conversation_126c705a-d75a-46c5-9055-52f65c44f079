#generate_video_desc.py
import os
import argparse
import json
import logging
from pathlib import Path
import base64
import subprocess
from typing import Optional, Dict, Any
from PIL import Image
import io
import re

from modules.langchain_interface import call_llm_json_response
from config import logger, get_param

# Constants
FRAME_TIMESTAMP = '00:00:05'  # Extract frame at 5 seconds
MAX_IMAGE_DIMENSION = 768
OUTPUT_IMAGE_FORMAT = 'jpg'

def extract_frame(video_path: str, output_path: str, timestamp: str = FRAME_TIMESTAMP) -> bool:
    """Extract a frame from the video at specified timestamp"""
    try:
        command = [
            'ffmpeg',
            '-i', video_path,
            '-ss', timestamp,
            '-vframes', '1',
            '-q:v', '2',
            output_path,
            '-y'
        ]
        result = subprocess.run(command, check=True, capture_output=True)
        logger.debug(f"FFmpeg command: {' '.join(command)}")
        logger.debug(f"FFmpeg output: {result.stdout.decode()}")
        
        # 添加文件验证
        if not os.path.exists(output_path):
            logger.error(f"Frame extraction failed: output file {output_path} does not exist")
            return False
            
        file_size = os.path.getsize(output_path)
        logger.debug(f"Extracted frame size: {file_size} bytes")
        if file_size == 0:
            logger.error(f"Extracted frame is empty: {output_path}")
            return False
            
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to extract frame from {video_path}: {e.stderr.decode()}")
        return False

def resize_image(image_path: str, max_dimension: int = MAX_IMAGE_DIMENSION) -> bool:
    """Resize image if it exceeds maximum dimensions"""
    try:
        with Image.open(image_path) as img:
            # 检查图片格式
            logger.debug(f"Original image format: {img.format}")
            logger.debug(f"Original image size: {img.size}")
            
            width, height = img.size
            if width > max_dimension or height > max_dimension:
                ratio = min(max_dimension/width, max_dimension/height)
                new_size = (int(width * ratio), int(height * ratio))
                img = img.resize(new_size, Image.LANCZOS)
                
                # 确保图片是RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 使用明确的JPEG格式保存
                img.save(image_path, format='JPEG', quality=95)
                logger.info(f"Resized image to {new_size}")
                logger.debug(f"Saved image format: JPEG")
            return True
    except Exception as e:
        logger.error(f"Failed to resize image {image_path}: {str(e)}")
        logger.error(f"Error details: {type(e).__name__}")
        return False

def encode_image(image_path: str, max_size_kb: int = 100) -> Optional[str]:
    """
    Encode image to base64 string, ensuring the output is within size limit while maintaining quality
    
    Args:
        image_path: Path to the image file
        max_size_kb: Maximum size in KB for the base64 encoded string
    
    Returns:
        Optional[str]: Base64 encoded string of the image, or None if failed
    """
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            original_size = os.path.getsize(image_path)
            logger.debug(f"Original image size: {original_size/1024:.2f}KB")
            
            # Initial parameters
            quality = 95
            min_quality = 60  # Increased minimum quality for better recognition
            max_dimension = 1024  # Maximum dimension constraint
            output = io.BytesIO()
            
            # Resize if dimensions are too large
            if max(img.size) > max_dimension:
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.LANCZOS)
                logger.debug(f"Resized image from {img.size} to {new_size}")
            
            while True:
                # Clear the buffer
                output.seek(0)
                output.truncate(0)
                
                # Save with current quality
                img.save(output, 
                        format='JPEG', 
                        quality=quality,
                        optimize=True,  # Enable additional optimization
                        progressive=True)  # Use progressive JPEG
                
                # Calculate base64 size
                current_size = len(output.getvalue())
                base64_size = (current_size * 4 + 2) / 3  # More accurate base64 size calculation
                base64_kb = base64_size / 1024
                
                logger.debug(f"Current quality: {quality}, Size: {base64_kb:.2f}KB")
                
                if base64_kb <= max_size_kb:
                    break
                
                # Calculate new quality based on target size ratio
                size_ratio = max_size_kb / base64_kb
                # More gradual quality reduction
                new_quality = int(quality * (size_ratio ** 0.7))
                
                # Ensure quality doesn't drop too much at once
                quality = max(min_quality, new_quality)
                
                # Break if we can't reduce quality further
                if quality == min_quality:
                    logger.warning(
                        f"Reached minimum quality ({min_quality}). "
                        f"Final size: {base64_kb:.2f}KB"
                    )
                    break
            
            encoded = base64.b64encode(output.getvalue()).decode('utf-8')
            logger.info(
                f"Final image stats - Quality: {quality}, "
                f"Size: {base64_kb:.2f}KB, "
                f"Dimensions: {img.size}"
            )
            return encoded
            
    except Exception as e:
        logger.error(f"Failed to encode image {image_path}: {str(e)}")
        logger.debug("Error details:", exc_info=True)
        return None

def generate_description(base64_image: str, video_title: str, filename: str) -> Optional[str]:
    """Generate description using GPT-4 vision model"""
    try:
        # Format base64 image with proper data URI prefix
        image_data = f"data:image/jpeg;base64,{base64_image}"
        
        # Prepare the prompt data according to the prompt template format
        prompt_data = {
            "image": image_data,  # This matches the {image} placeholder in the prompt
            "video_title": video_title,  # Additional context if needed
            "filename": filename  # Add the filename context
        }
        
        # Call GPT-4 through our interface
        response = call_llm_json_response(
            api_function="describe_video_frame",
            prompt_data=prompt_data,
            max_retries=3,
            using_cache=True
        )
        
        #logger.debug(f"API Response: {response}")
        
        if response and 'description' in response:
            return response['description']
        return None
    except Exception as e:
        logger.error(f"Failed to generate description: {str(e)}")
        return None

def update_json(json_path: str, clip_path: str, description: str) -> bool:
    """Update JSON file with video frame description"""
    try:
        # Load existing data
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Find the segment with matching clip_path and update it
        for segment in data["segments"]:
            if segment["clip_path"] == clip_path:
                segment["description"] = description
                break
        
        # Write updated data back to file
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"Failed to update JSON file {json_path}: {str(e)}")
        return False

def extract_filename(clip_path: str) -> str:
    """Extract clean filename from clip path without segment number"""
    # Get base filename
    base_name = os.path.basename(clip_path)
    # Remove segment number pattern (e.g., '_segment_0021.mp4')
    clean_name = re.sub(r'_segment_\d+\.mp4$', '', base_name)
    return clean_name

def process_video(video_path: str, output_dir: str, json_data: Dict[str, Any], segment: Dict[str, Any]) -> None:
    """Process a single video file"""
    video_name = Path(video_path).stem
    frames_dir = Path(output_dir) / "frames"
    frames_dir.mkdir(exist_ok=True)
    
    frame_path = frames_dir / f"{video_name}_frame.jpg"
    logger.debug(f"Extracting frame to: {frame_path}")
    
    # 检查文件是否已存在
    if frame_path.exists():
        logger.debug(f"Frame already exists: {frame_path}")
        # 检查文件大小
        if frame_path.stat().st_size == 0:
            logger.warning("Existing frame file is empty, will extract again")
        else:
            logger.debug(f"Existing frame file size: {frame_path.stat().st_size} bytes")
    
    # Extract frame
    if not extract_frame(video_path, str(frame_path)):
        logger.error("Frame extraction failed")
        return
        
    # 验证提取的帧
    if not frame_path.exists():
        logger.error("Frame file not created")
        return
    if frame_path.stat().st_size == 0:
        logger.error("Extracted frame is empty")
        return
    
    # Resize if necessary
    if not resize_image(str(frame_path)):
        return
    
    # Encode image
    base64_image = encode_image(str(frame_path))
    if not base64_image:
        return
    
    # Log the first 100 characters of base64 image for debugging
    logger.debug(f"Base64 image preview (first 100 chars): {base64_image[:100]}...")
    
    # Extract filename from clip_path
    filename = extract_filename(segment["clip_path"])
    
    # Generate description
    description = generate_description(
        base64_image=base64_image,
        video_title=segment["video_title"],
        filename=filename
    )
    if not description:
        return
    
    logger.debug(f"Generated description: {description}")
    
    # Update JSON with the description
    if update_json(json_data["json_path"], segment["clip_path"], description):
        logger.info(f"Successfully processed video segment: {video_name}")
    
    # Note: We're not removing the frame file anymore, keeping it for reference
    logger.debug(f"Frame saved at: {frame_path}")

def main():
    parser = argparse.ArgumentParser(description="Generate descriptions for videos in a directory")
    parser.add_argument("--video_dir", required=True, help="Directory containing video files")
    parser.add_argument("--json", required=True, help="Path to JSON file containing video information")
    args = parser.parse_args()
    
    video_dir = Path(args.video_dir)
    json_path = Path(args.json)
    
    if not video_dir.is_dir():
        logger.error(f"Directory not found: {video_dir}")
        return
    
    if not json_path.is_file():
        logger.error(f"JSON file not found: {json_path}")
        return
    
    # Load JSON data
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
    except Exception as e:
        logger.error(f"Failed to load JSON file: {str(e)}")
        return
    
    # Create output directory structure
    output_dir = video_dir
    frames_dir = output_dir / "frames"
    frames_dir.mkdir(exist_ok=True)
    
    # Add json_path to json_data for easy access
    json_data["json_path"] = str(json_path)
    
    # Process all segments
    logger.info(f"Found {len(json_data['segments'])} segments to process")
    for segment in json_data["segments"]:
        clip_path = segment["clip_path"]
        if Path(clip_path).is_file():
            logger.info(f"Processing video segment: {Path(clip_path).name}")
            process_video(clip_path, str(output_dir), json_data, segment)
        else:
            logger.warning(f"Clip file not found: {clip_path}")
    
    logger.info("Video description generation completed")
    logger.info(f"Frames are saved in: {frames_dir}")

if __name__ == "__main__":
    main() 