#!/bin/bash

# 检查参数数量
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <old_dir_name> <new_dir_name>"
    exit 1
fi

OLD_DIR="$1"
NEW_DIR="$2"

# 检查源目录是否存在
if [ ! -d "$OLD_DIR" ]; then
    echo "Error: Directory $OLD_DIR does not exist"
    exit 1
fi

# 构建旧的和新的 json 文件路径
OLD_JSON="$OLD_DIR/images/${OLD_DIR##*/}_images_metadata.json"
NEW_JSON="$OLD_DIR/images/${NEW_DIR##*/}_images_metadata.json"

# 检查 json 文件是否存在
if [ ! -f "$OLD_JSON" ]; then
    echo "Error: JSON file $OLD_JSON does not exist"
    exit 1
fi

# 创建临时文件
TMP_FILE=$(mktemp)

# 更新 JSON 文件中的 filepath
sed "s|$OLD_DIR|$NEW_DIR|g" "$OLD_JSON" > "$TMP_FILE"

# 重命名 JSON 文件
mv "$TMP_FILE" "$OLD_JSON"
mv "$OLD_JSON" "$NEW_JSON"

# 重命名目录
mv "$OLD_DIR" "$NEW_DIR"

echo "Directory and files have been renamed successfully!"
echo "Old directory: $OLD_DIR"
echo "New directory: $NEW_DIR"
echo "Old JSON file: $OLD_JSON"
echo "New JSON file: $NEW_JSON"