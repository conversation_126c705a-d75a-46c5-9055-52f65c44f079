import os
import json
import argparse
import azure.cognitiveservices.speech as speechsdk
from pydub import AudioSegment
import logging
from modules.utils import clean_filename
from config import (
    AZURE_SUBSCRIPTION_KEY,
    AZURE_REGION,
    TEMP_FILE_DIR
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 音频参数常量
SAMPLE_RATE = 44100
BITS_PER_SAMPLE = 16
CHANNELS = 2
BITRATE = 192000
PAUSE_DURATION = 500  # ms
SENTENCE_END_PAUSE = 500  # ms

# JSON结构字段常量
# 剧集级别字段
EPISODE_NUMBER = "n"
EPISODE_TITLE = "t"
EPISODE_CHARACTERS = "c"
EPISODE_SCENES = "s"

# 场景级别字段
SCENE_NUMBER = "n"
SCENE_SEQUENCE = "sn"
SCENE_ENVIRONMENT = "environment"
SCENE_NARRATION = "narration"
SCENE_DIALOGUE = "dialogue"

# 角色字段
CHARACTER_NAME = "name"
CHARACTER_ALIASES = "aliases"

# 旁白字段
NARRATION_TEXT = "nr"

# 对话字段
DIALOGUE_CHARACTER = "c"
DIALOGUE_MOOD = "m"
DIALOGUE_TEXT = "t"

# 输出JSON额外字段
OUTPUT_SCENE_START_TIME = "scene_start_time"
OUTPUT_SCENE_END_TIME = "scene_end_time"
OUTPUT_START_TIME = "start_time"
OUTPUT_END_TIME = "end_time"
OUTPUT_CONTENT = "content"

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件失败 {file_path}: {e}")
        raise

def get_character_voice_config(character_name, voice_config):
    """获取角色的语音配置"""
    character_config = voice_config.get('characters', {}).get(character_name, {})
    
    # 处理多层嵌套的voice配置
    if character_config and 'voice' in character_config:
        voice_config = character_config['voice']
        # 如果还有一层voice嵌套
        if isinstance(voice_config, dict) and 'voice' in voice_config:
            voice_config = voice_config['voice']
        return voice_config
    
    # 如果找不到角色配置，返回旁白配置
    return voice_config.get('narration', {})

def synthesize_speech(text, voice_config, style=None):
    """合成单条语音"""
    try:
        # 获取正确的voice名称
        voice_name = voice_config['voice'] if isinstance(voice_config, dict) else voice_config
        
        # 准备SSML
        ssml = (
            f"<speak version='1.0' xml:lang='zh-CN'>"
            f"    <voice name='{voice_name}'"
            f"        xmlns:mstts='http://www.w3.org/2001/mstts'>"
        )
        
        if style:
            ssml += f"<mstts:express-as style='{style}'>"
        
        ssml += f"{text}"
        
        if style:
            ssml += "</mstts:express-as>"
        
        ssml += f"    </voice>"
        ssml += f"</speak>"

        # 设置临时输出文件
        temp_file = os.path.join(TEMP_FILE_DIR, f"temp_{hash(text)}.wav")
        
        # 配置Azure TTS
        speech_config = speechsdk.SpeechConfig(
            subscription=AZURE_SUBSCRIPTION_KEY, 
            region=AZURE_REGION
        )
        audio_config = speechsdk.audio.AudioOutputConfig(filename=temp_file)
        synthesizer = speechsdk.SpeechSynthesizer(
            speech_config=speech_config, 
            audio_config=audio_config
        )

        # 合成语音
        result = synthesizer.speak_ssml(ssml)
        
        if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
            audio_segment = AudioSegment.from_wav(temp_file)
            os.remove(temp_file)
            return audio_segment
        else:
            logger.error(f"语音合成失败: {result.reason}")
            return None

    except Exception as e:
        logger.error(f"语音合成过程中出错: {e}")
        return None

def process_scene(scene, voice_config, final_audio, current_time):
    """处理单个场景"""
    # 记录场景开始时间
    scene[OUTPUT_SCENE_START_TIME] = current_time / 1000.0
    
    # 处理旁白
    if SCENE_NARRATION in scene and scene[SCENE_NARRATION]:
        narration_obj = scene[SCENE_NARRATION]
        if isinstance(narration_obj, dict) and NARRATION_TEXT in narration_obj:
            narration_text = narration_obj[NARRATION_TEXT]
            if narration_text.strip():  # 确保文本内容非空
                # 添加content字段用于输出
                narration_obj[OUTPUT_CONTENT] = narration_text
                
                narration_config = voice_config['narration']
                audio = synthesize_speech(
                    narration_text,
                    narration_config,
                    narration_config['default_style']
                )
                
                if audio:
                    narration_obj[OUTPUT_START_TIME] = current_time / 1000.0
                    final_audio += audio
                    current_time += len(audio)
                    narration_obj[OUTPUT_END_TIME] = current_time / 1000.0
                    final_audio += AudioSegment.silent(duration=PAUSE_DURATION)
                    current_time += PAUSE_DURATION
            else:
                logger.debug("场景中的旁白为空，已跳过处理")
        else:
            logger.warning(f"旁白结构不符合预期: {narration_obj}")

    # 处理对话
    if SCENE_DIALOGUE in scene:
        for dialogue in scene[SCENE_DIALOGUE]:
            character = dialogue[DIALOGUE_CHARACTER]
            char_voice_config = get_character_voice_config(character, voice_config)
            
            if not char_voice_config:
                logger.warning(f"未找到角色 {character} 的语音配置")
                continue

            style = None
            if isinstance(char_voice_config, dict):
                styles = char_voice_config.get('styles', {})
                style = styles.get(
                    dialogue[DIALOGUE_MOOD],
                    char_voice_config.get('default_style')
                )
            
            voice_name = char_voice_config['voice'] if isinstance(char_voice_config, dict) else char_voice_config
            
            dialogue_text = dialogue[DIALOGUE_TEXT]
            audio = synthesize_speech(
                dialogue_text,
                voice_name,
                style
            )
            
            if audio:
                dialogue[OUTPUT_START_TIME] = current_time / 1000.0
                final_audio += audio
                current_time += len(audio)
                dialogue[OUTPUT_END_TIME] = current_time / 1000.0
                final_audio += AudioSegment.silent(duration=PAUSE_DURATION)
                current_time += PAUSE_DURATION
    
    # 记录场景结束时间
    scene[OUTPUT_SCENE_END_TIME] = current_time / 1000.0
    
    return final_audio, current_time

def process_episode(episode_data, voice_config):
    """处理整个剧集"""
    final_audio = AudioSegment.empty()
    current_time = 0
    
    for scene in episode_data[EPISODE_SCENES]:
        final_audio, current_time = process_scene(
            scene, 
            voice_config, 
            final_audio, 
            current_time
        )
    
    return final_audio, episode_data

def save_results(audio, episode_data, output_file):
    """保存结果文件"""
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 保存音频文件
    audio.export(
        output_file,
        format="wav",
        parameters=[
            "-ar", str(SAMPLE_RATE),
            "-ab", str(BITRATE),
            "-ac", str(CHANNELS)
        ]
    )
    
    # 保存更新后的JSON
    json_output = os.path.splitext(output_file)[0] + '_timing.json'
    with open(json_output, 'w', encoding='utf-8') as f:
        json.dump(episode_data, f, ensure_ascii=False, indent=2)

def get_default_output_path(episode_file, voice_file):
    """生成默认输出路径"""
    # 使用episode文件所在的目录
    episode_dir = os.path.dirname(episode_file)
    # 使用episode文件的名称（不含扩展名）
    episode_name = os.path.splitext(os.path.basename(episode_file))[0]
    output_path = os.path.join(episode_dir, f"{episode_name}.wav")
    # 清理文件名中的后缀
    return clean_filename(output_path)

def main():
    parser = argparse.ArgumentParser(description='Episode TTS Generator')
    parser.add_argument('--episode', required=True, help='Episode JSON file path')
    parser.add_argument('--voice', required=True, help='Voice configuration JSON file path')
    parser.add_argument('--output', help='Output WAV file path')
    
    args = parser.parse_args()
    
    # 如果没有指定输出文件，使用默认命名（在voice文件同目录下）
    if not args.output:
        args.output = get_default_output_path(args.episode, args.voice)
    
    # 确保临时目录存在
    os.makedirs(TEMP_FILE_DIR, exist_ok=True)
    
    # 加载配置文件
    episode_data = load_json_file(args.episode)
    voice_config = load_json_file(args.voice)
    
    # 处理TTS
    final_audio, updated_episode_data = process_episode(episode_data, voice_config)
    
    # 保存结果
    save_results(final_audio, updated_episode_data, args.output)
    
    logger.info(f"处理完成。音频文件已保存至: {args.output}")

if __name__ == "__main__":
    main() 