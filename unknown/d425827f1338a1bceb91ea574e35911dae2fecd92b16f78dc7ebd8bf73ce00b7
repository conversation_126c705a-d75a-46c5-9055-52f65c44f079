import json
import random
import requests
import argparse
import os
import sys
from tqdm import tqdm
import time
from config import logger
import copy
from modules import comfyui_client

# 全局配置
QUEUE_TIMEOUT = 1800  # 队列超时时间（秒）
CHECK_INTERVAL = 10   # 状态检查间隔（秒）

# 注意：工作流配置已移至 modules/comfyui_client.py

# 人物角色与图片及外观描述的映射关系
CHARACTER_MAPPING = {
    "罗兰·温布顿": {
        "image_index": 1,    # 对应 image0
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/actor_1.png",
        "appearance": "A young man with light brown, shoulder-length hair, light skin, and a gentle expression. He has soft, brown eyes and a subtle, calm smile. He is wearing a blue tunic with ornate gold floral patterns, and blue pants. He has a red belt with a gold buckle. He has a small brown pouch. He wears a blue cloak, lined in red, with gold accents. He wears a beige collar with gold circle accents. He wears brown boots with decorative gold accents. He is holding a silver sword with a wooden handle."
    },
    "安娜": {
        "image_index": 0,    # 对应 image1
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/actress_1.png",
        "appearance": "A young woman with long, flowing red hair, styled with loose waves, and light skin. Her is brown eyes. She has a light blue mark on her forehead, resembling a cross with a small square in the middle. She also wears small light blue teardrop earrings, and a light blue stone necklace with gold accents. She wears a long, flowing cream dress with light blue swirling motifs near the hem. A dark green cloak with decorative swirls around the edges is draped over her shoulders. She carries a brown wooden spear adorned with green leaves. She has a brown belt with a gold buckle. On the belt are two pouches, one light brown, and one dark brown, a light blue cuff is visible around her left wrist. She is barefoot."
    },
    "巴罗夫": {
        "image_index": 2,    # 对应 image2
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/character_1.png",
        "appearance": "A middle-aged man with a dignified presence, wearing a long, dark green tunic adorned with gold embroidery. He has a neatly trimmed beard and carries a scroll, symbolizing his role as an assistant to the minister. His attire is complemented by a leather belt and sturdy boots."
    },
    "卡特·兰尼斯": {
        "image_index": 2,    # 对应 image3
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/character_2.png",
        "appearance": "A strong and noble knight, dressed in shining armor with a blue and silver crest. He wears a flowing cape that billows behind him, and his sword is sheathed at his side. His expression is serious, reflecting his role as the chief knight."
    },
    "培罗": {
        "image_index": 2,    # 对应 image4
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/character_3.png",
        "appearance": "A young envoy dressed in a simple yet elegant tunic, with a messenger's satchel slung over his shoulder. He has a friendly demeanor and carries a parchment, ready to deliver important messages."
    },
    "卡尔·梵伯特": {
        "image_index": 2,    # 对应 image5
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/character_4.png",
        "appearance": "An elderly man with wise eyes, wearing a long robe with intricate patterns. He carries a set of tools, indicating his mastery as a stonemason. His attire reflects his scholarly background as a teacher."
    },
    "夜莺": {
        "image_index": 2,    # 对应 image6
        "reference_image": "/home/<USER>/aigc/ComfyUI/input/character_5.png",
        "appearance": "A mysterious woman dressed in dark, flowing robes adorned with silver runes. She has long, wavy hair and carries a staff topped with a crystal. Her presence is enchanting, embodying her role as a member of the witch's council."
    }
}

def load_workflow(workflow_path):
    try:
        with open(workflow_path, 'r') as file:
            workflow_data = json.load(file)
            # 检查是否是 API 格式（使用节点 ID 作为键）
            if all(isinstance(k, (str, int)) and isinstance(v, dict) for k, v in workflow_data.items()):
                return workflow_data
            # 检查是否是标准工作流格式（包含 nodes 数组）
            elif "nodes" in workflow_data:
                # 转换为 API 格式
                api_format = {}
                for node in workflow_data["nodes"]:
                    node_data = {
                        "inputs": node.get("inputs", {}),
                        "class_type": node["type"],
                    }
                    if "widgets_values" in node:
                        node_data["widgets_values"] = node["widgets_values"]
                    api_format[str(node["id"])] = node_data
                return api_format
            else:
                raise ValueError(f"工作流文件 '{workflow_path}' 格式无效")
    except json.JSONDecodeError:
        raise ValueError(f"工作流文件 '{workflow_path}' 不是有效的 JSON 格式")
    except FileNotFoundError:
        raise FileNotFoundError(f"找不到工作流文件：'{workflow_path}'")

def find_node_by_type(workflow_data, node_type, title=None):
    """根据节点类型和标题查找节点

    Args:
        workflow_data: 工作流数据
        node_type: 节点类型 (如 'CLIPTextEncode', 'KSampler')
        title: 节点标题 (可选)

    Returns:
        找到的节点和节点ID
    """
    for node_id, node in workflow_data.items():
        if node["class_type"] == node_type:
            return node_id, node
    raise ValueError(f"未找到类型为 '{node_type}' 的节点")

def set_noise_parameters(workflow_data):
    """设置 workflow 中的随机噪声节点的种子"""
    try:
        # 更新随机噪声节点的种子
        noise_node_ids = [node_id for node_id, node in workflow_data.items() if node["class_type"] == 'RandomNoise']
        for noise_node_id in noise_node_ids:
            noise_node = workflow_data[noise_node_id]
            seed = random.randint(1, 1_000_000_000_000)
            if 'inputs' in noise_node:
                noise_node['inputs']['noise_seed'] = seed

        return workflow_data

    except Exception as e:
        logger.error(f"设置噪声参数时出错: {e}")
        raise

def queue_prompt(server_address, prompt_data):
    data = json.dumps({"prompt": prompt_data})
    url = f"http://{server_address}/prompt"
    headers = {'Content-Type': 'application/json'}
    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()
        result = response.json()
        logger.info(f"任务ID: {result.get('prompt_id')}")
        return result.get('prompt_id')
    except requests.exceptions.RequestException as e:
        raise ConnectionError(f"提交生成任务失败: {e}")

def check_queue_status(server_address, prompt_id, timeout=QUEUE_TIMEOUT):
    """检查队列状态，使用 WebSocket 或长轮询"""
    url = f"http://{server_address}/api/history"
    start_time = time.time()
    # 使用 comfyui_client 配置
    workflow_config = comfyui_client.WORKFLOW_CONFIG["workflows"]["storytelling-api"]
    output_node_id = workflow_config["OUTPUT_NODE"]["node_id"]

    while time.time() - start_time < timeout:
        try:
            response = requests.get(url)
            response.raise_for_status()
            history = response.json()

            # 按时间戳排序历史记录
            sorted_history = sorted(history.items(),
                                key=lambda x: x[1].get('created_at', 0)
                                if isinstance(x[1], dict) else 0,
                                reverse=True)

            for history_id, entry in sorted_history:
                if isinstance(entry, dict) and 'prompt' in entry:
                    prompt_data = entry['prompt']
                    if isinstance(prompt_data, list) and len(prompt_data) > 1:
                        current_prompt_id = prompt_data[1]

                        if current_prompt_id == prompt_id:
                            if 'status' in entry:
                                status = entry['status'].get('status_str')
                                logger.debug(f"任务状态: {status}")

                                if status == 'success':
                                    if 'outputs' in entry:
                                        # 直接使用配置的输出节点ID
                                        if output_node_id in entry['outputs']:
                                            output_data = entry['outputs'][output_node_id]
                                            if 'images' in output_data and output_data['images']:
                                                filename = output_data['images'][0].get('filename') or output_data['images'][0]
                                                logger.info(f"找到输出图片 - 节点{output_node_id}: {filename}")
                                                return filename

                                elif status == 'error':
                                    raise Exception(f"任务执行失败: {entry['status'].get('error', '未知错误')}")

            time.sleep(CHECK_INTERVAL)

        except requests.exceptions.RequestException as e:
            logger.error(f"检查状态时发生错误: {e}")
            time.sleep(CHECK_INTERVAL)
            continue

    raise TimeoutError(f"生成任务超时 (超过 {timeout} 秒)")


def download_image(server_address, filename, output_dir, index, prompts_file, max_retries=3):
    """从服务器下载生成的图片"""
    url = f"http://{server_address}/api/view"

    for attempt in range(max_retries):
        try:
            params = {
                "filename": filename,
                "type": "output",
                "subfolder": ""
            }

            logger.info(f"尝试下载图片 (第{attempt + 1}次), URL参数: {params}")
            response = requests.get(url, params=params, stream=True, timeout=30)
            response.raise_for_status()

            # 直接使用 images 目录，不再创建子目录
            output_path = os.path.join(output_dir, f"image_{index:03d}.png")

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            if os.path.getsize(output_path) > 0:  # 验证文件大小
                logger.info(f"图片成功保存到: {output_path}")
                return output_path
            else:
                logger.warning("下载的文件大小为0，将重试")
                continue

        except Exception as e:
            logger.error(f"下载失败 (第{attempt + 1}次尝试): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
                continue
            raise ConnectionError(f"下载图片失败，已重试{max_retries}次: {str(e)}")

def generate_and_queue(prompt_data, server_address="127.0.0.1:8188", workflow_path="workflow_api.json", output_dir="outputs", index=0, prompts_file=None):
    try:
        # 加载工作流
        workflow_data = load_workflow(workflow_path)

        # 设置工作流参数
        workflow_data = set_workflow_parameters(workflow_data, prompt_data)

        # 提交任务
        prompt_id = queue_prompt(server_address, workflow_data)

        # 等待结果
        filename = check_queue_status(server_address, prompt_id)
        if filename:
            logger.info("开始下载图片...")
            return download_image(server_address, filename, output_dir, index, prompts_file)
        return None

    except Exception as e:
        logger.error(f"生成过程中发生错误: {str(e)}")
        raise

def save_prompts_with_images(prompts_data, output_dir, prompts_file):
    """保存带有图片路径的提示词数据"""
    # 输出文件与输入文件在同一目录
    prompts_dir = os.path.dirname(os.path.abspath(prompts_file))
    base_name = os.path.splitext(os.path.basename(prompts_file))[0]
    output_file = os.path.join(prompts_dir, f"{base_name}_images.json")

    try:
        # 保存 JSON 文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(prompts_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已保存更新后的提示词数据到: {output_file}")
    except Exception as e:
        logger.error(f"保存 JSON 文件时出错: {e}")
        raise

def validate_files(prompts_file, workflow_file):
    """验证输入文件是否存在且格式正确

    Args:
        prompts_file: 提示词配置文件路径
        workflow_file: 工作流文件路径

    Returns:
        dict: 加载的示词数据
    """
    # 检查文件是否存在
    if not os.path.isfile(prompts_file):
        raise FileNotFoundError(f"错误：配置文件 {prompts_file} 不存在")
    if not os.path.isfile(workflow_file):
        raise FileNotFoundError(f"错误：工作流文件 {workflow_file} 不存在")

    # 加载并验证配置文件
    try:
        with open(prompts_file, 'r', encoding='utf-8') as f:
            prompts_data = json.load(f)
        return prompts_data
    except json.JSONDecodeError:
        raise ValueError(f"错误：配置文件 {prompts_file} 不是有效的 JSON 格式")

def check_existing_image(prompt_item, output_dir):
    """检查提示词对应的图片是否已存在

    Args:
        prompt_item: 提示词数据
        output_dir: 输出目录

    Returns:
        bool: 如果图片存在且有效返回True，否则返回False
    """
    # 检查是否有 image 字段
    if 'image' in prompt_item:
        image_path = prompt_item['image']
        # 检查文件是否存在且有效
        if os.path.isfile(image_path) and os.path.getsize(image_path) > 0:
            logger.info(f"图片已存在且有效: {image_path}")
            return True

    # 如果没有 image 字段或文件无效，返回 False
    return False

def load_existing_outputs(prompts_file):
    """加载已存在的输出文件

    Args:
        prompts_file: 原始提示词文件路径

    Returns:
        dict: 已存在的输出数据，如果不存在返回None
    """
    # 构造输出文件路径
    prompts_dir = os.path.dirname(os.path.abspath(prompts_file))
    base_name = os.path.splitext(os.path.basename(prompts_file))[0]
    output_file = os.path.join(prompts_dir, f"{base_name}_images.json")

    if os.path.isfile(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                logger.info(f"已加载现有输出文件: {output_file}")
                return existing_data
        except json.JSONDecodeError:
            logger.warning(f"输出文件 {output_file} 格式无效")
    return None

def process_prompts(prompts_data, server_address, workflow_path, output_dir, prompts_file):
    """处理所有提示词并生成图片"""
    # 首先加载已存在的输出数据
    existing_data = load_existing_outputs(prompts_file)
    if existing_data:
        prompts_data = existing_data
        logger.info("使用已存在的输出数据继续处理")

    # 获取场景列表
    scenes = prompts_data.get('scenes', [])

    if not scenes:
        logger.warning("未找到任何场景")
        return

    with tqdm(total=len(scenes), desc="生成进度") as pbar:
        for index, scene in enumerate(scenes, 1):
            try:
                # 获取场景的 prompt 数据
                prompt_data = scene.get('prompt', {})
                if not prompt_data:
                    logger.warning(f"跳过没有提示词数据的场景 {index}")
                    continue

                # 检查是否已经生成过图片
                if 'image' in scene and check_existing_image(scene, output_dir):
                    logger.info(f"跳过已处理的场景 {index}")
                    pbar.update(1)
                    continue

                output_path = generate_and_queue(
                    prompt_data,
                    server_address=server_address,
                    workflow_path=workflow_path,
                    output_dir=output_dir,
                    index=index,
                    prompts_file=prompts_file
                )

                if output_path:
                    # 图片路径添加到场景中
                    scene['image'] = os.path.abspath(output_path)

                    # 实时保存更新后的JSON文件
                    save_prompts_with_images(prompts_data, output_dir, prompts_file)

                pbar.update(1)

            except Exception as e:
                logger.error(f"处理场景 {index} 时失败: {str(e)}")
                logger.error(f"失败的提示词内容: {prompt_data}")
                continue

def setup_output_directory(output_dir, prompts_file):
    """设置并验证输出目录

    Args:
        output_dir: 输出目录路径
        prompts_file: 提示词配置文件路径
    """
    try:
        # 如果没有指定输出目录，则使用提示词文件所在目录下的 images 目录
        if output_dir == 'outputs':
            prompts_dir = os.path.dirname(os.path.abspath(prompts_file))
            output_dir = os.path.join(prompts_dir, 'images')

        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    except Exception as e:
        raise RuntimeError(f"创建输出目录失败: {str(e)}")

def set_workflow_parameters(workflow_data, prompt_data):
    """设置工作流参数

    Args:
        workflow_data: 工作流数据
        prompt_data: 提示词数据，包含 environment 和 characters 两个主要部分

    Returns:
        dict: 更新后的工作流数据
    """
    logger.info("\n========== 工作流参数设置 ==========")

    try:
        # 获取工作流配置
        workflow_config = comfyui_client.WORKFLOW_CONFIG["workflows"]["storytelling-api"]

        # 转换提示词数据格式
        flux_prompt_data = {
            "flux_prompt": json.dumps(prompt_data, ensure_ascii=False, indent=2)
        }

        # 使用 comfyui_client 模块设置参数
        updated_workflow = comfyui_client.set_workflow_parameters(
            workflow_data,
            flux_prompt_data,
            workflow_config
        )

        return updated_workflow

    except Exception as e:
        logger.error(f"设置工作流参数时出错: {str(e)}")
        raise

def find_output_node(workflow_data):
    """查找输出节点

    Returns:
        tuple: (node_id, node) 输出节点信息
    """
    # 按优先级查找输出节点
    output_types = ["SaveImage", "PreviewImage"]
    for node_id, node in workflow_data.items():
        if node["class_type"] in output_types:
            return node_id, node

    return None, None

def set_character_image(workflow_data, node_id, character):
    """
    设置角色对应的图片索引

    Args:
        workflow_data: 工作流数据
        node_id: 需要设置的节点ID
        character: 角色名称
    """
    if character not in CHARACTER_MAPPING:
        raise ValueError(f"未知的角色: {character}")

    # 获取角色对应的图片索引
    image_index = CHARACTER_MAPPING[character]["image_index"]

    # 设置节点的index值
    if node_id in workflow_data:
        workflow_data[node_id]["inputs"]["index"] = image_index
        # 新增字段 reference_image
        workflow_data[node_id]["inputs"]["reference_image"] = CHARACTER_MAPPING[character]["reference_image"]
    else:
        raise ValueError(f"节点 {node_id} 不存在")

def setup_character_images(workflow_data, character_positions):
    """
    根据提供的角色位置信息设置工作流中的图片

    Args:
        workflow_data: 工作流数据
        character_positions: 包含角色位置信息的字典，例如:
            {
                "136": "male_lead",    # 第一个位置放置男主角
                "137": "female_lead"   # 第二个位置放置女主角
            }
    """
    for node_id, character in character_positions.items():
        set_character_image(workflow_data, node_id, character)



def set_character_switches(workflow_data, scene_data):
    """根据场景中的角色设置图片切换"""
    # 注意：此函数已废弃，角色切换逻辑已移至 comfyui_client 模块
    logger.warning("set_character_switches 函数已废弃，请使用 comfyui_client 模块")
    pass

def main():
    """主函数，处理命令行参数并协调整体流程"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="ComfyUI 批量图像生成器")
    parser.add_argument('--prompts', type=str, required=True, help='提示词配置文件路径')
    parser.add_argument('--workflow', type=str, default='assets/workflow/image_with_redux_api.json', help='ComfyUI 工作流文件路径')
    parser.add_argument('--server', type=str, default='jp.abxy.fun:50018', help='ComfyUI 服务器地址')
    parser.add_argument('--output', type=str, default='outputs', help='输出目录路径')
    args = parser.parse_args()

    try:
        # 设置输出目录
        output_dir = setup_output_directory(args.output, args.prompts)

        # 验证文件并加载提示词数据
        prompts_data = validate_files(args.prompts, args.workflow)

        # 处理提示词并生成图片
        process_prompts(
            prompts_data,
            args.server,
            args.workflow,
            output_dir,
            args.prompts
        )

        logger.info("处理完成！")

    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()