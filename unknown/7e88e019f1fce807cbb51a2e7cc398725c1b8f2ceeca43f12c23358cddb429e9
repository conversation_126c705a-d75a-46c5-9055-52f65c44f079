#!/usr/bin/env python3
"""
Fetches videos from Pexels API based on keywords and saves them.
"""
import httpx
import asyncio
import math
from pathlib import Path
import os
import functools # For run_in_executor
import json # Added for monthly counter
import datetime # Added for monthly counter

# Assuming these modules are in the same directory or properly in PYTHONPATH
import video_config
import token_bucket as tb # Functional token bucket
import downloader_common as common
from downloader_common import VideoProcessStatus # Import the Enum

PEXELS_API_URL = "https://api.pexels.com/videos/search"
PER_PAGE_DEFAULT = 80 # Pexels API default is 15, max is 80
SOURCE_NAME = "pexels"

# Helper functions for Pexels monthly counter (can be at module level)
def _get_pexels_monthly_request_status_sync():
    """
    Reads Pexels monthly request counter.
    Returns: (requests_this_month, year_month_string_for_file, is_over_limit)
    Synchronous, to be run in executor.
    Requires video_config.PEXELS_COUNTER_FILE_PATH and video_config.PEXELS_MONTHLY_API_QUOTA.
    """
    current_ym_str = datetime.datetime.now().strftime("%Y-%m")
    
    # Ensure PEXELS_COUNTER_FILE_PATH and PEXELS_MONTHLY_API_QUOTA are defined in video_config
    # Fallback for safety if not defined, though they should be.
    counter_file = getattr(video_config, "PEXELS_COUNTER_FILE_PATH", Path("pexels_monthly_counter.json"))
    monthly_quota = getattr(video_config, "PEXELS_MONTHLY_API_QUOTA", 20000)


    if not counter_file.exists():
        return 0, current_ym_str, False

    try:
        with open(counter_file, 'r') as f:
            data = json.load(f)
        
        saved_ym = data.get("last_reset_year_month")
        count = data.get("requests_this_month", 0)

        if saved_ym == current_ym_str:
            return count, current_ym_str, count >= monthly_quota
        else: # New month
            return 0, current_ym_str, False # Effectively a reset
    except (IOError, json.JSONDecodeError) as e:
        print(f"Error reading Pexels counter file {counter_file}: {e}. Assuming reset.")
        return 0, current_ym_str, False

def _update_pexels_monthly_request_count_sync(previous_count_this_month, year_month_str_of_count):
    """
    Increments and writes Pexels monthly request counter.
    Returns: new_count_this_month
    Synchronous, to be run in executor.
    Requires video_config.PEXELS_COUNTER_FILE_PATH.
    """
    counter_file = getattr(video_config, "PEXELS_COUNTER_FILE_PATH", Path("pexels_monthly_counter.json"))
    new_count = previous_count_this_month + 1
    try:
        with open(counter_file, 'w') as f:
            json.dump({
                "last_reset_year_month": year_month_str_of_count,
                "requests_this_month": new_count
            }, f)
        return new_count
    except IOError as e:
        print(f"Error writing Pexels counter file {counter_file}: {e}. Count not updated in file.")
        return previous_count_this_month # Return old count if write failed

def _parse_description_from_pexels_url(pexels_url: str) -> str:
    """Helper to parse a description-like slug from a Pexels video URL."""
    if not pexels_url:
        return ""
    try:
        path_part = Path(pexels_url.split('?')[0]).name
        # The slug is typically like 'video-title-words-1234567' or 'photo-title-words-1234567'
        # We want to remove the ID at the end and the prefix if it's generic like 'video-'
        # Split by hyphen, check if the last part is numeric (ID)
        parts = path_part.split('-')
        if parts:
            if parts[-1].isdigit():
                parts = parts[:-1] # Remove numeric ID
            # Remove common prefixes if they are the only part left or are generic
            if parts and (parts[0].lower() == 'video' or parts[0].lower() == 'photo') and len(parts) > 1:
                 parts = parts[1:] # Remove 'video' or 'photo' prefix if more parts exist
            elif parts and (parts[0].lower() == 'video' or parts[0].lower() == 'photo') and len(parts) == 1:
                return "" # Only prefix and ID, no real description
            
            return " ".join(parts).strip()
    except Exception as e:
        print(f"Error parsing Pexels URL slug from {pexels_url}: {e}")
    return ""

async def fetch_pexels_page(session: httpx.AsyncClient, pexels_api_key: str, query: str, page: int, per_page: int):
    """Fetches a single page of video results from Pexels API."""
    headers = {"Authorization": pexels_api_key}
    params = {"query": query, "page": page, "per_page": per_page}
    
    print(f"Fetching Pexels: Query='{query}', Page={page}, PerPage={per_page}")
    try:
        response = await session.get(PEXELS_API_URL, headers=headers, params=params, timeout=video_config.API_REQUEST_TIMEOUT_SECONDS)
        response.raise_for_status() # Raise an exception for HTTP errors (4xx or 5xx)
        return response.json()
    except httpx.HTTPStatusError as e:
        print(f"HTTP error fetching Pexels page for query '{query}', page {page}: {e.response.status_code} - {e.response.text}")
        if e.response.status_code == 429: # Rate limit hit
            print("Pexels API rate limit hit. The token bucket should prevent this, but if it happens, consider a longer wait or checking API key limits.")
            # Extract retry-after header if present and wait
            retry_after = e.response.headers.get("Retry-After")
            if retry_after:
                print(f"Retry-After header suggests waiting for {retry_after} seconds.")
                await asyncio.sleep(int(retry_after) + 1) # wait a bit longer
        # For other errors like 401, 403, 404, this will propagate up
        raise
    except httpx.RequestError as e:
        print(f"Request error fetching Pexels page for query '{query}', page {page}: {e}")
        raise
    except Exception as e:
        print(f"Unexpected error fetching Pexels page for query '{query}', page {page}: {e}")
        raise

async def process_pexels_video(session: httpx.AsyncClient, video_data: dict, query_keyword: str):
    """Processes a single video item from Pexels API response. Returns VideoProcessStatus."""
    video_id = video_data.get("id")
    if not video_id:
        print("Skipping video item due to missing ID.")
        return VideoProcessStatus.FAILED

    # Check if video already processed and downloaded
    loop = asyncio.get_event_loop()
    db_check_func = functools.partial(common.check_if_video_exists_in_db, SOURCE_NAME, str(video_id))
    video_exists_in_db = await loop.run_in_executor(None, db_check_func)

    if video_exists_in_db:
        print(f"Pexels video ID {video_id} already in DB. Skipping download and metadata save.")
        return VideoProcessStatus.SKIPPED_EXISTS
    
    # Preliminary check on the main video dimensions if available at top level of video_data
    # This can quickly filter out portrait videos if the primary dimensions are portrait.
    main_width = video_data.get("width")
    main_height = video_data.get("height")
    if main_width and main_height and main_width < main_height: # Check if width < height (portrait)
        print(f"Pexels video ID {video_id} appears to be portrait (width: {main_width}, height: {main_height}) based on main dimensions. Skipping.")
        return VideoProcessStatus.SKIPPED_FILTERED # Use a specific status for filtered out videos

    video_files = video_data.get("video_files", [])
    if not video_files:
        print(f"No video files found for Pexels ID {video_id}")
        return VideoProcessStatus.FAILED # Changed from return to return status

    # Select the best quality video (typically largest width or height)
    best_video_link = ""
    max_quality = 0
    chosen_file_ext = ".mp4" # Default

    for vf in video_files:
        vf_width = vf.get("width")
        vf_height = vf.get("height")
        vf_link = vf.get("link")
        vf_file_type = vf.get("file_type")

        if vf_link and vf_width and vf_height and vf_file_type == "video/mp4":
            # Ensure it's a landscape video (width > height)
            if vf_width <= vf_height:
                print(f"  Skipping Pexels video file (ID: {vf.get('id')}, W:{vf_width}, H:{vf_height}) as it is not landscape.")
                continue

            # Prioritize by width, then height, but ensure it's mp4 and landscape
            current_quality = vf_width * vf_height
            if current_quality > max_quality:
                max_quality = current_quality
                best_video_link = vf_link
                # Try to get suffix from link
                try:
                    chosen_file_ext = Path(best_video_link.split('?')[0]).suffix or ".mp4"
                except Exception:
                    chosen_file_ext = ".mp4"
    
    if not best_video_link:
        print(f"No suitable MP4 video link found for Pexels ID {video_id}")
        return VideoProcessStatus.FAILED # Changed from return to return status

    output_path = common.get_video_download_path(SOURCE_NAME, video_id, chosen_file_ext)
    
    print(f"Processing Pexels video ID {video_id}: URL {best_video_link}")
    download_successful = await common.download_video_content(session, best_video_link, output_path)

    if download_successful and output_path.exists():
        print(f"Pexels video ID {video_id} downloaded to {output_path}")
        # 1. Attempt to parse description from the Pexels URL
        video_page_url = video_data.get('url')
        parsed_description = _parse_description_from_pexels_url(video_page_url)
        
        # 2. Get tags from API if available (often empty for videos)
        api_tags_list = []
        raw_api_tags = video_data.get("tags", []) # Pexels API for videos usually has this empty
        if isinstance(raw_api_tags, list):
            for tag_item in raw_api_tags:
                if isinstance(tag_item, dict) and 'text' in tag_item:
                    api_tags_list.append(tag_item['text'].strip().lower())
                elif isinstance(tag_item, str): # Sometimes it might be a list of strings
                    api_tags_list.append(tag_item.strip().lower())
        
        # 3. Combine parsed description and API tags. Use query_keyword as fallback.
        final_tags_set = set()
        if parsed_description:
            final_tags_set.add(parsed_description) # Add parsed description as a primary tag/semantic info
        
        for api_tag in api_tags_list:
            if api_tag: final_tags_set.add(api_tag)
            
        # The above logic combines description and API keywords into final_tags_set.
        # For the new structure, we need to separate them.
        # Description will primarily be parsed_description.
        # Keywords will be api_tags_list, and query_keyword if others are empty.

        video_description_to_save = parsed_description if parsed_description else ""
        
        keywords_to_save = set()
        for api_tag in api_tags_list:
            if api_tag: keywords_to_save.add(api_tag)
        
        # If no specific description and no API keywords, use query_keyword for keywords.
        # Or, always add query_keyword to keywords? For now, only if others are missing.
        if not video_description_to_save and not keywords_to_save and query_keyword:
            keywords_to_save.add(query_keyword.strip().lower())
        elif video_description_to_save and not keywords_to_save and query_keyword: # If we have desc but no keywords, query can be keyword
            keywords_to_save.add(query_keyword.strip().lower())

        final_keywords_list = sorted(list(keywords_to_save))

        common.save_video_metadata(
            source_name=SOURCE_NAME,
            source_video_id=str(video_id),
            video_path=output_path,
            description=video_description_to_save,
            keywords=final_keywords_list,
            raw_api_data=video_data,
            width=video_data.get("width"),
            height=video_data.get("height"),
            duration_override=float(video_data["duration"]) if video_data.get("duration") is not None else None
        )
        return VideoProcessStatus.PROCESSED_NEWLY # Return success status
    else:
        print(f"Failed to download Pexels video ID {video_id}.")
        return VideoProcessStatus.FAILED # Return failure status

async def crawl_pexels_for_keyword(session: httpx.AsyncClient, pexels_api_key: str, pexels_bucket_state: dict, keyword: str, max_videos_per_keyword: int | None = None):
    """Crawls Pexels for a single keyword until no more videos or max_videos limit is reached."""
    page = 1
    newly_processed_videos_for_keyword = 0 # New counter for successfully processed *new* videos
    attempted_to_process_count = 0 # Counter for total videos considered (for logging or breaking infinite loops on all-existing pages)
    max_pages_to_fetch = video_config.PEXELS_MAX_PAGES_PER_KEYWORD # Safety limit
    
    loop = asyncio.get_event_loop() # Get event loop

    while page <= max_pages_to_fetch:
        if max_videos_per_keyword is not None and newly_processed_videos_for_keyword >= max_videos_per_keyword:
            print(f"Reached max_new_videos_per_keyword ({max_videos_per_keyword}) for Pexels query '{keyword}'. Moving to next keyword.")
            break
        # Safety break if we process too many items across pages without finding new ones
        # This can happen if all videos on subsequent pages are already downloaded or filtered out.
        if attempted_to_process_count > max_videos_per_keyword * video_config.PEXELS_MAX_ATTEMPTS_FACTOR_FOR_NEW: # e.g., 20 * 5 = 100 attempts
             print(f"Pexels: Keyword '{keyword}'. Attempted to process {attempted_to_process_count} videos but only found {newly_processed_videos_for_keyword} new ones. Max attempts reached. Moving on.")
             break
        
        # Check Pexels MONTHLY quota before acquiring hourly token & making API call
        monthly_req_count, ym_for_file, is_monthly_quota_exceeded = \
            await loop.run_in_executor(None, _get_pexels_monthly_request_status_sync)

        if is_monthly_quota_exceeded:
            # Ensure PEXELS_MONTHLY_API_QUOTA is accessible from video_config
            monthly_quota_val = getattr(video_config, "PEXELS_MONTHLY_API_QUOTA", 20000)
            print(f"Pexels: Monthly API quota ({monthly_quota_val} reqs) reached for {ym_for_file}. "
                  f"Current count: {monthly_req_count}. Halting Pexels downloads for keyword '{keyword}'.")
            break # Stop processing this keyword for this cron run.

        # Acquire token from bucket before making an API call
        print(f"Pexels: Attempting to acquire token for '{keyword}' page {page}...")
        if not await tb.acquire_async_token(pexels_bucket_state):
            print(f"Pexels: Failed to acquire token for '{keyword}' page {page}. Rate limit might be too strict or an issue with bucket.")
            # This ideally shouldn't happen if bucket is configured correctly & sleeps. Could add a small safety sleep.
            await asyncio.sleep(5) # Safety sleep if acquire fails unexpectedly
            continue # Retry acquiring for the same page
        
        # Log current monthly count status before making the call
        monthly_quota_val_log = getattr(video_config, "PEXELS_MONTHLY_API_QUOTA", 20000)
        print(f"Pexels: Token acquired for '{keyword}' page {page}. Monthly count for {ym_for_file}: {monthly_req_count}/{monthly_quota_val_log}.")

        try:
            page_data = await fetch_pexels_page(session, pexels_api_key, keyword, page, PER_PAGE_DEFAULT)
            # If fetch_pexels_page was successful (no exception), increment the monthly counter
            monthly_req_count = await loop.run_in_executor(None, 
                                                            _update_pexels_monthly_request_count_sync,
                                                            monthly_req_count, # The count before this request
                                                            ym_for_file)
            # Optional: Log successful increment
            # print(f"Pexels API call for '{keyword}' page {page} successful. Updated monthly count for {ym_for_file}: {monthly_req_count}")

        except Exception as e:
            print(f"Could not fetch Pexels page {page} for keyword '{keyword}': {e}. Stopping for this keyword.")
            # Do NOT increment monthly counter here if fetch_pexels_page failed before a successful API response
            # This is generally correct as a failed request might not count against quota (e.g. network error).
            break # Stop processing this keyword on page fetch failure

        if not page_data or not page_data.get("videos"):
            print(f"No more Pexels videos found for keyword '{keyword}' on page {page}. Total found: {page_data.get('total_results',0) if page_data else 0}")
            break

        videos_on_page = page_data["videos"]
        print(f"Found {len(videos_on_page)} Pexels videos on page {page} for keyword '{keyword}'. Total results for query: {page_data.get('total_results')}")

        page_processing_tasks = []
        videos_to_consider_on_this_page = 0
        for video_item in videos_on_page:
            if max_videos_per_keyword is not None and newly_processed_videos_for_keyword >= max_videos_per_keyword:
                break # Already got enough new videos
            page_processing_tasks.append(process_pexels_video(session, video_item, keyword))
            videos_to_consider_on_this_page +=1
            attempted_to_process_count += 1
        
        if page_processing_tasks:
            results = await asyncio.gather(*page_processing_tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, Exception):
                    print(f"Error processing a Pexels video item: {result}")
                elif result == VideoProcessStatus.PROCESSED_NEWLY:
                    newly_processed_videos_for_keyword += 1

        if not page_data.get("next_page"):
            print(f"No next_page URL for Pexels keyword '{keyword}' after page {page}. Assuming end of results.")
            break
        
        # If the current page had videos but none were processed (e.g., all existed or filtered)
        # and we haven't reached the target of new videos, we should continue to the next page.
        # The attempted_to_process_count check at the beginning of the loop will eventually break if we are stuck.
        if videos_to_consider_on_this_page == 0 and len(videos_on_page) > 0: # No videos were even considered for processing on this page (should not happen with current logic)
            print(f"Pexels: Keyword '{keyword}'. Page {page} had videos, but none were queued for processing. Check logic.")
            # This might indicate all videos were filtered before task creation if such logic existed there.
            # Or that the loop for creating tasks had a condition that was met prematurely.

        # If no videos were processed on this page (all skipped or failed) AND we didn't find any new videos, AND there are more pages
        # it's useful to log this. The `attempted_to_process_count` helps break out eventually.
        newly_processed_on_this_page = sum(1 for r in results if r == VideoProcessStatus.PROCESSED_NEWLY) if 'results' in locals() and results else 0
        if newly_processed_on_this_page == 0 and videos_to_consider_on_this_page > 0:
            print(f"Pexels: Keyword '{keyword}'. Page {page}. Processed {videos_to_consider_on_this_page} video items, but 0 were new. Total new so far: {newly_processed_videos_for_keyword}.")
        
        page += 1
        # Pexels API usually limits total_results accessible, often around 1000-2000 for videos via search.
        # `total_results` in response can be large, but `page` might not go that far.
        # `math.ceil(page_data["total_results"] / PER_PAGE_DEFAULT)` can be misleading for actual traversable pages.
        # Relying on `next_page` or empty `videos` list is safer.

        # Optional: a small delay between pages if not handled by token bucket sufficiently
        # await asyncio.sleep(1) 

    print(f"Finished Pexels crawl for keyword '{keyword}'. Processed {newly_processed_videos_for_keyword} new videos (attempted: {attempted_to_process_count}).")


async def main_pexels_downloader(keywords: list[str] | None = None, keywords_file: str | Path | None = None, max_videos_per_keyword: int | None = None):
    """Main function to orchestrate Pexels video downloading."""
    if not video_config.PEXELS_API_KEY:
        print("PEXELS_API_KEY not configured in video_config.py. Skipping Pexels download.")
        return

    all_keywords = []
    if keywords:
        all_keywords.extend(keywords)
    if keywords_file:
        all_keywords.extend(common.read_keywords_from_file(keywords_file))
    
    if not all_keywords:
        # Default to keywords file from config if no keywords provided directly
        all_keywords = common.read_keywords_from_file(video_config.KEYWORDS_FILE_PATH)
        if not all_keywords:
             print("No keywords provided and default keywords file is empty or not found. Skipping Pexels download.")
             return

    # Remove duplicates and empty strings
    all_keywords = sorted(list(set(kw for kw in all_keywords if kw.strip())))
    if not all_keywords:
        print("No valid keywords to process for Pexels. Exiting.")
        return

    print(f"Starting Pexels downloader for keywords: {all_keywords}")
    
    # Initialize Pexels token bucket (e.g., 200 requests per hour)
    # The example from user: PEXELS_RATE = (200, 3600)
    # Using capacity from config, period is 1 hour (3600s)
    pexels_bucket_state = tb.create_async_token_bucket(capacity=video_config.PEXELS_RATE_LIMIT_CAPACITY, period=video_config.PEXELS_RATE_LIMIT_PERIOD_SECONDS)

    # Standard headers for httpx client, e.g., User-Agent
    client_headers = {
        "User-Agent": video_config.DOWNLOADER_USER_AGENT
    }

    async with httpx.AsyncClient(headers=client_headers, timeout=video_config.API_REQUEST_TIMEOUT_SECONDS, follow_redirects=True) as session:
        keyword_tasks = []
        for keyword in all_keywords:
            if not keyword.strip(): continue
            # Run keyword crawls sequentially to respect overall API limits more gently
            # and to make logging clearer, rather than all keywords in parallel.
            # If truly parallel keyword fetching is desired, ensure token bucket can handle it
            # or create one bucket per keyword task (more complex state mgmt).
            print(f"\n--- Starting Pexels crawl for keyword: '{keyword}' ---")
            await crawl_pexels_for_keyword(session, video_config.PEXELS_API_KEY, pexels_bucket_state, keyword, max_videos_per_keyword)
    
    print("\n--- Pexels video download process completed. ---")

if __name__ == '__main__':
    # Example usage:
    # Ensure video_config.py has PEXELS_API_KEY and other relevant settings.
    # Create a dummy keywords.txt or use the direct keywords list.
    # video_config.ensure_directories() # Ensure download directories exist
    # metadata_db.create_tables()     # Ensure DB tables exist

    # Create a dummy keywords.txt for testing if it doesn't exist
    if not Path(video_config.KEYWORDS_FILE_PATH).exists():
        with open(video_config.KEYWORDS_FILE_PATH, 'w') as f:
            f.write("nature timelapse\n")
            f.write("city lights\n")
        print(f"Created dummy keywords file: {video_config.KEYWORDS_FILE_PATH}")

    print("Running Pexels Downloader directly (for testing purposes)...")
    # asyncio.run(main_pexels_downloader(keywords=["office work", "technology code"], max_videos_per_keyword=5))
    asyncio.run(main_pexels_downloader(keywords_file=video_config.KEYWORDS_FILE_PATH, max_videos_per_keyword=3)) # Limit for testing 