#!/bin/bash
#set -x

# 首先定义所有服务器相关的变量
SERVER_USER="gpuuser"
SERVER_HOST="*************"
REMOTE_BASE_DIR="/home/<USER>/source-video-clips"
REMOTE_LOG_DIR="/home/<USER>/source-video-clips/logs"
REMOTE_COOKIE_FILE="/home/<USER>/source-video-clips/settings/cookies.txt"
REMOTE_TOPICS_DIR="/home/<USER>/source-video-clips/topics"

# 检查是否提供了目录参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

# 转换为绝对路径
WORK_DIR=$(realpath "${1%/}")
THEME_NAME=$(basename "$WORK_DIR" | sed 's/\\//g')
SSH_KEY="/Users/<USER>/.ssh/gpu.pem"

# 添加调试信息
echo "Debug: Absolute working directory: $WORK_DIR"
echo "Debug: SSH key path: $SSH_KEY"
echo "Debug: Remote path: $REMOTE_TOPICS_DIR/$THEME_NAME/"

# 测试 SSH 连接
echo "Testing SSH connection..."
if ! ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "echo 'SSH connection successful'"; then
    echo "SSH connection failed. Please check your connection."
    exit 1
fi

# 检查工作目录是否存在
if [ ! -d "$WORK_DIR" ]; then
    echo "Error: Directory '$WORK_DIR' does not exist."
    exit 1
fi

# 检查必需的文件
if [ ! -f "$WORK_DIR/urls.txt" ] || [ ! -f "$WORK_DIR/www.youtube.com_cookies.txt" ]; then
    echo "Error: Required files (urls.txt and www.youtube.com_cookies.txt) not found in $WORK_DIR"
    exit 1
fi

# 创建远程目录
ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "mkdir -p $REMOTE_TOPICS_DIR/$THEME_NAME"

# 修改 rsync 命令，添加详细输出
echo "Transferring files to remote server..."
rsync -avvz --progress \
    -e "ssh -o StrictHostKeyChecking=no -i $SSH_KEY" \
    "$WORK_DIR/urls.txt" \
    "$WORK_DIR/www.youtube.com_cookies.txt" \
    "$SERVER_USER@$SERVER_HOST:$REMOTE_TOPICS_DIR/$THEME_NAME/"

# 覆盖远程服务器上的 cookies.txt
echo "Updating cookies file..."
ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" \
    "cp '$REMOTE_TOPICS_DIR/$THEME_NAME/www.youtube.com_cookies.txt' '$REMOTE_COOKIE_FILE'"

# 获取当前时间戳
TIMESTAMP=$(date '+%Y%m%d_%H%M')

# 执行远程命令
echo "Executing video_clipper.py on remote server..."
ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" \
    "mkdir -p $REMOTE_LOG_DIR && \
    cd $REMOTE_BASE_DIR && \
    source /home/<USER>/miniconda3/etc/profile.d/conda.sh && \
    conda activate base && \
    tsp python video_clipper.py --theme \"$THEME_NAME\" --urls \"$REMOTE_TOPICS_DIR/$THEME_NAME/urls.txt\" --exec"

echo "Process started in background with tsp command"
echo "Process completed!"
