import re
from urllib.parse import urlparse, urlunparse, urljoin, urlencode, parse_qs
from PIL import Image
from io import BytesIO
from config import logger

EXCLUDE_KEYWORDS = ['button', 'logo', 'icon', 'copyright', 'sprite', 'ad', 'footer', 'placeholder', 'thumbnail', 'profile', 'banner', 'advertisement']

def normalize_and_enhance_image_url(img_url, base_url=None):
    if '/thumb/' in img_url:
        img_url = img_url.replace('/thumb/', '/').rsplit('/', 1)[0]

    parsed_url = urlparse(img_url)
    if not parsed_url.scheme and base_url:
        img_url = urljoin(base_url, img_url)

    query_params = parse_qs(parsed_url.query)
    resize_params = ['w', 'h', 'width', 'height', 'resize', 'crop', 'quality', 'format']
    filtered_params = {k: v for k, v in query_params.items() if k.lower() not in resize_params}
    new_query = urlencode(filtered_params, doseq=True)
    enhanced_url = urlunparse(parsed_url._replace(query=new_query))

    if enhanced_url.endswith('?'):
        enhanced_url = enhanced_url[:-1]

    return enhanced_url

def is_valid_image(img_url):
    lower_img_url = img_url.lower()
    if lower_img_url.endswith('.svg'):
        #logger.debug(f"URL excluded because it is an SVG image: {img_url}")
        return False
    # Improved regex to handle both singular and plural forms
    for keyword in EXCLUDE_KEYWORDS:
        if re.search(rf'\b{keyword}s?\b', lower_img_url):  # Added 's?' to handle plural forms
            #logger.debug(f"URL excluded due to keyword '{keyword}': {img_url}")
            return False
    #logger.debug(f"URL is valid: {img_url}")
    return True

def get_image_size(image_content, img_url):
    try:
        image = Image.open(BytesIO(image_content))
        return image.size
    except Exception as e:
        #logger.debug(f"Failed to process image: {img_url}, error: {e}")
        return None