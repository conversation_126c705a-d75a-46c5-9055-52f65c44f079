#modules/episode_outline.py
import os
import json
from typing import List, Dict, Any, <PERSON><PERSON>
from config import logger, get_param
from modules.langchain_interface import call_llm_json_response
from modules.utils import calculate_token_count, extract_chapter_info
from tqdm import tqdm
from modules.gpt_parameters import (
    LLM_PROVIDER_AZURE,
    LLM_PROVIDER_OPENAI,
    LLM_PROVIDER_ANTHROPIC,
    LLM_PROVIDER_DEEPSEEK,
    MODEL_KEY_GPT41,
    MODEL_KEY_O3_MINI,
    MODEL_KEY_DEEPSEEK_REASONER
)
# 定义 LLM 类型和模型配置，便于修改
LLM_TYPE = get_param("LLM_TYPE", default="azure")  # 默认使用 azure
MODEL_KEY = get_param("MODEL_KEY", default="o3-mini")  # 默认使用 o3-mini 模型

# Constants for token limits
GPT_CONTEXT_LIMIT = 128000  # Maximum context window size ( 128K tokens)
GPT_OUTPUT_LIMIT = 4000     # Maximum output tokens per request

def validate_global_outline(response: Dict[str, Any]) -> tuple[bool, List[str], str]:
    """验证全局大纲响应数据的有效性"""
    if not response:
        return False, [], "响应数据为空"
        
    # 验证顶层必需字段
    required_fields = {
        "story_phases": list,
        "plot_threads": list,
        "character_developments": list,
        "world_overview": dict
    }
    
    # 验证子字段结构
    sub_field_requirements = {
        "story_phases": {
            "phase": str,
            "groups": list,
            "key_events": list,
            "tension_level": float,
            "pacing": str,
            "phase_theme": str
        },
        "plot_threads": {
            "thread": str,
            "type": str,
            "importance": float,
            "developments": list
        },
        "character_developments": {
            "name": str,
            "type": str,
            "importance": float,
            "key_moments": list,
            "relationships": list
        },
        "world_overview": {
            "setting": str,
            "time_period": str,
            "social_context": str,
            "key_locations": list,
            "special_rules": list,
            "world_evolution": list
        }
    }
    
    missing_fields = []
    invalid_fields = []
    
    # 验证顶层字段
    for field, expected_type in required_fields.items():
        if field not in response:
            missing_fields.append(field)
        elif not isinstance(response[field], expected_type):
            invalid_fields.append(f"{field} (类型错误)")
            continue
            
        # 验证非空数组
        if expected_type == list and not response[field]:
            invalid_fields.append(f"{field} (空数组)")
            continue
            
        # 验证子字段结构
        if field in sub_field_requirements:
            if isinstance(response[field], list):
                for item in response[field]:
                    for sub_field, sub_type in sub_field_requirements[field].items():
                        if sub_field not in item:
                            missing_fields.append(f"{field}.{sub_field}")
                        elif not isinstance(item[sub_field], sub_type):
                            invalid_fields.append(f"{field}.{sub_field} (类型错误)")
            elif isinstance(response[field], dict):
                for sub_field, sub_type in sub_field_requirements[field].items():
                    if sub_field not in response[field]:
                        missing_fields.append(f"{field}.{sub_field}")
                    elif not isinstance(response[field][sub_field], sub_type):
                        invalid_fields.append(f"{field}.{sub_field} (类型错误)")
            
    error_msg = ""
    if missing_fields:
        error_msg += f"缺少必需字段: {missing_fields}; "
    if invalid_fields:
        error_msg += f"字段验证失败: {invalid_fields}"
        
    is_valid = not (missing_fields or invalid_fields)
    
    if not is_valid:
        logger.error(f"全局大纲验证失败: {error_msg}")
        logger.debug(f"实际返回数据: {json.dumps(response, ensure_ascii=False, indent=2)}")
    
    return is_valid, missing_fields, error_msg

def generate_global_outline(
    group_summaries: Dict[str, Any], 
    style: str, 
    language: str,
    input_file: str
) -> Dict[str, Any]:
    try:
        if not isinstance(group_summaries, dict) or 'groups' not in group_summaries:
            raise ValueError("Invalid group_summaries format")

        # 添加日志记录组和章节信息
        groups = group_summaries.get('groups', [])
        logger.debug(f"总共有 {len(groups)} 个组")
        # for group in groups:
        #     chapter_numbers = group.get('chapter_numbers', [])
        #     logger.debug(f"组 {group.get('group_id', '未知')}: 包含章节 {chapter_numbers}")

        # 准备输入数据
        input_data = {
            "group_summaries": group_summaries,
            "style": style,
            "language": language,
            "groups": [g.get('group_id') for g in groups]  # 添加组ID列表
        }
        
        # 使用 call_llm_json_response 替代 retry_gpt_call
        global_outline = call_llm_json_response(
            api_function="generate_global_outline",
            prompt_data=input_data,
            max_retries=5,
            using_cache=True,
            llm_type=LLM_TYPE,
            model_key=MODEL_KEY
        )
        
        logger.info("全局大纲生成完成")
        #logger.debug(f"大纲内容: {json.dumps(global_outline, ensure_ascii=False, indent=2)}")
        
        return global_outline
        
    except Exception as e:
        logger.error(f"生成全局大纲时出错: {str(e)}")
        raise

def group_chapter_summaries(
    chapter_summaries: List[Dict[str, Any]], 
    total_context_size: int = GPT_CONTEXT_LIMIT,
    group_output_size: int = GPT_OUTPUT_LIMIT
) -> List[List[Dict[str, Any]]]:
    """将章节摘要分组
    
    Args:
        chapter_summaries: 章节摘要列表
        total_context_size: GPT上下文总大小限制
        group_output_size: 每组输出大小限制
    
    Returns:
        List[List[Dict[str, Any]]]: 分组后的章节列表
    """
    try:
        if not chapter_summaries:
            raise ValueError("章节摘要列表为空")
            
        # 验证数据结构
        for idx, chapter in enumerate(chapter_summaries):
            required_fields = ['basic_info', 'narrative', 'key_events', 'key_characters']
            missing_fields = [field for field in required_fields if field not in chapter]
            if missing_fields:
                logger.error(f"第 {idx} 个章节缺少必要字段: {missing_fields}")
                logger.debug(f"章节数据: {json.dumps(chapter, ensure_ascii=False, indent=2)}")
                raise ValueError(f"章节数据结构不完整: 缺少 {missing_fields}")
        
        # 计算总token数
        total_tokens = sum(
            calculate_token_count(json.dumps(chapter, ensure_ascii=False)) 
            for chapter in chapter_summaries
        )
        logger.info(f"总计 {len(chapter_summaries)} 个章节, {total_tokens} tokens")
        
        # 计算需要的组数和每组的平均token数
        num_groups = max(1, total_context_size // group_output_size)
        tokens_per_group = total_tokens // num_groups
        logger.debug(f"预计分组数: {num_groups}, 每组平均token数: {tokens_per_group}")
        
        # 分组
        groups = []
        current_group = []
        current_tokens = 0
        
        for chapter in chapter_summaries:
            chapter_tokens = calculate_token_count(json.dumps(chapter, ensure_ascii=False))
            
            # 如果当前组的token数超过平均值且还有足够的组数可用
            if (current_tokens + chapter_tokens > tokens_per_group and 
                len(groups) < num_groups - 1):
                groups.append(current_group)
                current_group = [chapter]
                current_tokens = chapter_tokens
            else:
                current_group.append(chapter)
                current_tokens += chapter_tokens
                
        if current_group:
            groups.append(current_group)
            
        # 记录分组信息
        for i, group in enumerate(groups):
            start_chapter = group[0]['basic_info']['chapter_number']
            end_chapter = group[-1]['basic_info']['chapter_number']
            group_tokens = sum(
                calculate_token_count(json.dumps(ch, ensure_ascii=False)) 
                for ch in group
            )
            # logger.debug(
            #     f"组 {i+1}: {len(group)} 章节 "
            #     f"(第{start_chapter}章-第{end_chapter}章), "
            #     f"{group_tokens} tokens"
            # )
            
        return groups
        
    except Exception as e:
        logger.error(f"章节分组失败: {str(e)}")
        if chapter_summaries and len(chapter_summaries) > 0:
            logger.error(f"示例章节数据结构: {json.dumps(chapter_summaries[0], ensure_ascii=False, indent=2)}")
        raise

def validate_group_response(response: Dict[str, Any], group_idx: int) -> tuple[bool, List[str], str]:
    """验证组摘要响应数据的有效性
    
    Args:
        response: GPT返回的响应数据
        group_idx: 当前处理的组索引
        
    Returns:
        tuple[bool, List[str], str]: 
            - 是否有效
            - 缺失的必需字段列表
            - 错误信息
    """
    if not response:
        return False, [], "响应数据为空"
        
    # 必需字段及其验证规则
    required_fields = {
        "group_summary": lambda x: isinstance(x, str) and len(x.strip()) > 0,
        "main_events": lambda x: isinstance(x, list) and len(x) > 0 and all(isinstance(e, str) for e in x),
        "character_arcs": lambda x: isinstance(x, list) and len(x) > 0 and all(
            isinstance(arc, dict) and 
            'character' in arc and 
            'development' in arc and
            len(arc['character'].strip()) > 0 and  # 确保 character 不为空
            len(arc['development'].strip()) > 0    # 确保 development 不为空
            for arc in x if arc.get('character') and arc.get('development')  # 只验证非空项
        )
    }
    
    # 可选字段及其验证规则
    optional_fields = {
        "narrative_importance": lambda x: isinstance(x, (int, float)) and 0 <= float(x) <= 1,
        "story_phase": lambda x: isinstance(x, str) and x in {
            'setup', 'rising_action', 'climax', 'resolution'
        }
    }
    
    # 验证必需字段
    missing_fields = []
    invalid_fields = []
    
    for field, validator in required_fields.items():
        if field not in response:
            missing_fields.append(field)
        elif not validator(response[field]):
            invalid_fields.append(f"{field} (格式无效)")
            
    # 验证可选字段（如果存在）
    for field, validator in optional_fields.items():
        if field in response and not validator(response[field]):
            invalid_fields.append(f"{field} (值无效)")
            
    # 生成错误信息
    error_msg = ""
    if missing_fields:
        error_msg += f"缺少必需字段: {missing_fields}; "
    if invalid_fields:
        error_msg += f"字段验证失败: {invalid_fields}"
        
    is_valid = not (missing_fields or invalid_fields)
    
    if not is_valid:
        logger.error(f"组 {group_idx + 1} 响应数据验证失败: {error_msg}")
        logger.debug(f"实际返回数据: {json.dumps(response, ensure_ascii=False, indent=2)}")
        
    return is_valid, missing_fields, error_msg

def summarize_groups(
    groups: List[List[Dict[str, Any]]], 
    style: str, 
    language: str
) -> Dict[str, Any]:
    """将章节摘要分组并生成摘要"""
    result = {"groups": []}
    
    for group_idx, group in enumerate(groups):
        try:
            # 输入验证
            if not group:
                logger.warning(f"组 {group_idx + 1} 为空，过处理")
                continue
                
            # 验证章节数据
            summaries = []
            for chapter in group:
                if not isinstance(chapter, dict) or 'basic_info' not in chapter:
                    logger.warning(f"组 {group_idx + 1} 包含无效的章节数据")
                    continue
                summaries.append(chapter)
            
            # 检查 token 数量
            total_tokens = calculate_token_count(json.dumps(summaries, ensure_ascii=False))
            if total_tokens > GPT_CONTEXT_LIMIT:
                logger.warning(f"组 {group_idx + 1} token数 ({total_tokens}) 超过限制 ({GPT_CONTEXT_LIMIT})")
            
            # 使用优化后的GPT调用
            valid_response = call_llm_json_response(
                api_function="summarize_group",
                prompt_data={
                    "summaries": summaries,
                    "language": language,
                    "style": style,
                    "max_length": GPT_OUTPUT_LIMIT
                },
                max_retries=5,
                using_cache=True,  # 启用缓存
                llm_type=LLM_TYPE,
                model_key=MODEL_KEY,
                expected_fields=["group_summary", "main_events", "character_arcs"]
            )
            
            #logger.debug(f"当前 group {group_idx + 1} 字符数: {len(json.dumps(valid_response, ensure_ascii=False))} token数: {calculate_token_count(json.dumps(valid_response, ensure_ascii=False))}")
            
            # 构建精简的组数据结构
            group_data = {
                "group_id": f"g_{str(group_idx + 1).zfill(3)}",
                "chapter_numbers": [
                    chapter.get('basic_info', {}).get('chapter_number', 0)
                    for chapter in summaries
                    if chapter.get('basic_info', {}).get('chapter_number') is not None
                ],
                "group_summary": valid_response['group_summary'],
                "main_events": valid_response['main_events'],
                "character_arcs": valid_response['character_arcs']
            }
            
            # 添加可选字段
            if 'narrative_importance' in valid_response:
                group_data['narrative_importance'] = float(valid_response['narrative_importance'])
            if 'story_phase' in valid_response:
                group_data['story_phase'] = valid_response['story_phase']
            
            result['groups'].append(group_data)
            #logger.debug(f"成功处理组 {group_idx + 1}, 包含 {len(summaries)} 个章节")
            
        except Exception as e:
            logger.error(f"处理组 {group_idx + 1} 时出错: {str(e)}")
            logger.debug("错误详情: ", exc_info=True)
            continue
            
    return result

def generate_episode_outline(
    episode_number: int,
    episode_allocation: Dict[str, Any],
    story_outline: Dict[str, Any],
    chapter_summaries: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """生成单集大纲"""
    try:
        # 获取相关章节内容
        relevant_chapters = [
            chapter for chapter in chapter_summaries
            if chapter.get('basic_info', {}).get('chapter_number') in episode_allocation.get('chapters', [])
        ]
        
        # 调用GPT生成大纲
        response = call_llm_json_response(
            api_function="generate_episode_outline",
            prompt_data={
                "episode_number": episode_number,
                "episode_allocation": episode_allocation,
                "story_outline": story_outline,
                "chapter_summaries": relevant_chapters
            },
            max_retries=5,
            using_cache=True,
            llm_type=LLM_TYPE,
            model_key=MODEL_KEY
        )
        
        if not response:
            raise ValueError(f"第 {episode_number} 集大纲生成失败")
            
        logger.info(f"第 {episode_number} 集大纲生成完成")
        logger.debug(f"大纲内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
        
        return response
        
    except Exception as e:
        logger.error(f"生成第 {episode_number} 集大纲时出错: {str(e)}")
        raise
