# --- Standard library imports
import os
import sys
import azure.cognitiveservices.speech as speechsdk
import json
from pydub import AudioSegment
import logging
import argparse
import hashlib
import shutil
from config import AUDIO_CACHE_DIR
import io
import re
from config import (
    SPEECH_KEY,
    <PERSON>EECH_REGION,
    logger,
    AUDIO_DIR,
    VOICE_DELAY_TIME
)
# Import the Azure TTS proxy configuration helper
# from azure_tts_proxy_config import configure_azure_tts_network, restore_environment, create_speech_config_with_direct_connection
from modules.nlp_model import get_language_code
from modules.utils import process_script
import time
os.environ['OS_ACTIVITY_MODE'] = 'disable'

# Maximum UTF-16 code units per Azure request (4800 leaves SSML headroom)
MAX_UTF16_CHARS = 4800

def split_by_utf16(text, limit=MAX_UTF16_CHARS):
    """Yield chunks of text no longer than `limit` UTF-16 code units."""
    count = 0
    chunk = []
    for ch in text:
        count += 2 if ord(ch) > 0xFFFF else 1
        if count > limit:
            yield ''.join(chunk)
            chunk = [ch]
            count = 2 if ord(ch) > 0xFFFF else 1
        else:
            chunk.append(ch)
    if chunk:
        yield ''.join(chunk)

# Constants
# SAMPLE_RATE = 44100
# BITS_PER_SAMPLE = 16
# CHANNELS = 2

#logging.basicConfig(level=logging.DEBUG)  # Set to DEBUG to see detailed logs

# 为每种语言和声音创建独特的风格映射
VOICE_STYLE_MAPPING = {
    'English': {
        'voice': "en-US-Adam:DragonHDLatestNeural",
        'default_style': "Default",
        # 'styles': removed
    },
    'English-Story': {  # 新增的英语女声
        'voice': "en-US-TonyNeural",
        'default_style': "Default",
        # 'styles': removed
    },
    'Japanese': {
        'voice': "ja-JP-Masaru:DragonHDLatestNeural",
        'default_style': "default",
        # 'styles': removed
    },
    'Chinese': {
        'voice': "zh-CN-XiaoxiaoMultilingualNeural",
        'default_style': "calm",
        # 'styles': removed
    },
     'Spanish': {
        'voice': "es-ES-Alvaro:DragonHDLatestNeural",
        'default_style': "default",
        # 'styles': removed
    },
     'Portuguese': {
        'voice': "en-US-AndrewMultilingualNeural", # Note: This voice is en-US, consider pt-PT or pt-BR voice
        'default_style': "default",
        # 'styles': removed
    },
    # 为其他语言添加类似的映射 (ensure 'styles' are removed if they exist)
}

# For dynamic xml:lang in SSML
XML_LANG_MAP = {
    'English': 'en-US',
    'English-Story': 'en-US',
    'Japanese': 'ja-JP',
    'Chinese': 'zh-CN',
    'Spanish': 'es-ES',
    'Portuguese': 'pt-BR', # Assuming Brazilian Portuguese for AndrewMultilingualNeural or use specific PT voice
    # Add other mappings as needed
}

# 添加重试相关常量
MAX_RETRY_COUNT = 3
RETRY_INITIAL_DELAY_SECONDS = 5  # Changed from 60 to 5
RETRY_BACKOFF_MULTIPLIER = 1.5

def calculate_backoff_time(retry_count):
    """计算指数退避等待时间"""
    return RETRY_INITIAL_DELAY_SECONDS * (RETRY_BACKOFF_MULTIPLIER ** (retry_count - 1))

def get_voice_and_style(language):
    return VOICE_STYLE_MAPPING.get(language, VOICE_STYLE_MAPPING['English'])

def process_segments(json_file, audio_dir, theme, language='Chinese'):
    """处理 JSON 文件中的每个段落并生成音频"""
    logger.info(f"Processing segments from {json_file} in {language}")

    # 获取语言代码
    lang_code = get_language_code(language)
    if not lang_code:
        logger.warning(f"Unsupported language: {language}, using default 'en'")
        lang_code = 'en'

    # 读取输入的 JSON 文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
    except FileNotFoundError:
        logger.error(f"JSON file not found: {json_file}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from file: {json_file}")
        raise

    # 从加载的JSON数据中提取段落列表
    # 仅支持根元素为字典且包含 'segments' 键（其值为列表）的格式
    if isinstance(json_data, dict) and 'segments' in json_data and isinstance(json_data.get('segments'), list):
        segments = json_data['segments']
    elif isinstance(json_data, list): # Support for flat list of segments directly
        segments = json_data
    else:
        logger.error(f"JSON file {json_file} is not in the expected format. "
                       f"Expected a dictionary with a 'segments' key (list value), or a direct list of segments.")
        segments = []

    # 创建音频输出目录
    os.makedirs(audio_dir, exist_ok=True)
    # Ensure global cache directory exists
    os.makedirs(AUDIO_CACHE_DIR, exist_ok=True)

    # 准备输出的 JSON 数据
    output_segments = []

    mapping = get_voice_and_style(language)
    voice_name = mapping['voice']

    # Initialize SpeechConfig and Synthesizer once per process_segments call for reuse
    speech_config = speechsdk.SpeechConfig(subscription=SPEECH_KEY, region=SPEECH_REGION)
    speech_config.speech_synthesis_voice_name = voice_name
    # audio_config=None for in-memory processing of chunks by synthesize_segment
    synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=None)

    # Simplified loop: directly iterate over segments
    for idx, segment_data_item in enumerate(segments):
        audio_filename = f"{theme}_segment_{idx+1}_{lang_code}.wav"
        audio_path = os.path.join(audio_dir, audio_filename)

        # Extract text: robustly handle if segment_data_item is a string or a dict
        if isinstance(segment_data_item, dict):
            text_to_synthesize = segment_data_item.get('paragraph_text', '')
            current_segment_id = segment_data_item.get('segment_id', idx + 1)
        elif isinstance(segment_data_item, str):
            text_to_synthesize = segment_data_item
            current_segment_id = idx + 1 # Use index if it's just a list of strings
        else:
            logger.warning(f"Segment {idx+1} is not a dictionary or string, skipping.")
            continue

        if not text_to_synthesize:
            logger.warning(f"Segment {idx+1} (ID: {current_segment_id}) has no text, skipping.")
            continue

        # --- paragraph‑level cache lookup ---------------------------------
        text_hash = hashlib.sha256(text_to_synthesize.encode('utf-8')).hexdigest()
        cache_file = os.path.join(AUDIO_CACHE_DIR, f"{voice_name}_{text_hash}.wav")

        if os.path.exists(cache_file):
            # Cache hit: copy cached WAV into the correct output location.
            shutil.copy(cache_file, audio_path)
            logger.info(f"Using cached audio for segment {idx+1} (ID: {current_segment_id})")
            success = True
        else:
            success = None  # will be set after synthesis

        # Logging before synthesis
        logger.info(f"Starting synthesis for segment {idx+1} (ID: {current_segment_id})")

        if success is None:
            success = synthesize_segment(
                synthesizer,
                text_to_synthesize,
                audio_path,
                voice_name,
                language
            )
            # On first successful synthesis, store a copy in the cache.
            if success and not os.path.exists(cache_file):
                try:
                    shutil.copy(audio_path, cache_file)
                except Exception as e:
                    logger.warning(f"Could not write cache file {cache_file}: {e}")

        if success:
            # Logging after successful synthesis
            logger.info(f"Succeeded synthesis for segment {idx+1} (ID: {current_segment_id}) -> {audio_path}")
            # 获取音频时长
            try:
                audio = AudioSegment.from_wav(audio_path)
                duration = len(audio) / 1000.0  # 转换为秒
            except Exception as e:
                logger.error(f"Failed to get audio duration for segment {idx+1}: {e}")
                duration = 0

            # Start with a shallow copy of the original segment dict to preserve other fields
            if isinstance(segment_data_item, dict):
                output_segment_info = dict(segment_data_item)
            else:
                # fallback for string-only segments
                output_segment_info = {
                    "segment_id": current_segment_id,
                }
            # Override paragraph_text with the text we actually synthesized
            output_segment_info["paragraph_text"] = text_to_synthesize
            # Replace or add the latest audio info
            output_segment_info["audio"] = {
                "path": audio_path,
                "duration": duration
            }
            output_segments.append(output_segment_info)
        else:
            logger.error(f"Failed synthesis for segment {idx+1} (ID: {current_segment_id})")
            raise RuntimeError(f"Audio synthesis failed for segment {idx+1}. Stopping process.")

    # 保存更新后的 JSON
    # Derive output filename: replace "_images_final_<lang_code>.json" or ".json" with "_<lang_code>_audio.json"
    basename = os.path.basename(json_file)
    m = re.match(r'(.+)_images_final_' + re.escape(lang_code) + r'\.json$', basename)
    if m:
        new_base = f"{m.group(1)}_{lang_code}_audio.json"
    else:
        new_base = basename.replace('.json', f"_{lang_code}_audio.json")
    output_json = os.path.join(os.path.dirname(json_file), new_base)
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(output_segments, f, ensure_ascii=False, indent=2)

    logger.info(f"Processing completed. Output JSON saved to {output_json}")
    return output_json

# Updated synthesize_segment to accept synthesizer instance and language, removed prosody_start/end
def synthesize_segment(synthesizer, text, output_file, voice_name, language):
    """合成单个段落的音频。若文本较长，则使用UTF-16 code units分块分别合成后合并"""
    text_no_quotes = text # Assuming direct text, no nlp/quote stripping

    final_audio = AudioSegment.empty()
    chunks = list(split_by_utf16(text_no_quotes))
    num_chunks = len(chunks)

    xml_lang = XML_LANG_MAP.get(language, 'en-US') # Get dynamic xml:lang

    # speech_config and synthesizer are now passed in or handled at a higher level
    # speech_config = speechsdk.SpeechConfig(subscription=SPEECH_KEY, region=SPEECH_REGION)
    # speech_config.speech_synthesis_voice_name = voice_name
    # synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=None)

    for i, chunk in enumerate(chunks):
        # Minimal XML escaping for & < > and ' for SSML.
        escaped_chunk = chunk.translate(str.maketrans({'&':'&amp;','<':'&lt;','>':'&gt;', "'":"&apos;"}))
        ssml = (
            f"<speak version='1.0' xml:lang='{xml_lang}'>" # Dynamic xml:lang
            f"<voice name='{voice_name}'>"
            f"{escaped_chunk}" # prosody_start/end removed, were ""
            f"</voice></speak>"
        )
        retry_count = 0
        success = False
        while retry_count < MAX_RETRY_COUNT and not success:
            try:
                # Use speak_ssml_async for non-blocking call, then .get() to wait for result.
                # Audio data will be in result.audio_data.
                result = synthesizer.speak_ssml_async(ssml).get()

                if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                    audio_data = result.audio_data
                    # Create AudioSegment from in-memory wav data
                    chunk_audio = AudioSegment.from_file(io.BytesIO(audio_data), format="wav")
                    final_audio += chunk_audio
                    # No temp_file to unlink for the chunk
                    success = True
                else:
                    if result.reason == speechsdk.ResultReason.Canceled:
                        cancellation_details = result.cancellation_details
                        error_code = cancellation_details.error_code
                        retry_count += 1
                        if retry_count < MAX_RETRY_COUNT:
                            if 'Retry-After' in cancellation_details.error_details:
                                match = re.search(r'Retry-After: (\d+)', cancellation_details.error_details)
                                if match:
                                    wait = int(match.group(1))
                                else:
                                    wait = calculate_backoff_time(retry_count)
                            else:
                                wait = calculate_backoff_time(retry_count)
                            logger.warning(f"Chunk synthesis failed with error code {error_code}, retrying {retry_count}/{MAX_RETRY_COUNT} after {wait:.1f} seconds...")
                            time.sleep(wait)
                            continue
                        logger.error(f"Chunk synthesis canceled: {cancellation_details.reason}")
                        logger.error(f"Error details: {cancellation_details.error_details}")
                        logger.error(f"Error code: {error_code}")
                    else:
                        logger.error(f"Chunk synthesis failed: {result.reason}")
                        retry_count += 1
                        if retry_count < MAX_RETRY_COUNT:
                            wait = calculate_backoff_time(retry_count)
                            logger.warning(f"Retrying chunk for reason {result.reason}: {retry_count}/{MAX_RETRY_COUNT} after {wait:.1f} seconds...")
                            time.sleep(wait)
                            continue
                    # No temp_path to unlink here if chunk synthesis failed before creating it
                    break
            except Exception as e:
                logger.error(f"Error synthesizing chunk: {e}")
                retry_count += 1
                if retry_count < MAX_RETRY_COUNT:
                    wait = calculate_backoff_time(retry_count)
                    logger.warning(f"Retrying chunk: {retry_count}/{MAX_RETRY_COUNT} after {wait:.1f} seconds...")
                    time.sleep(wait)
                # No temp_path to unlink here
        if not success:
            logger.error(f"Failed to synthesize chunk after {MAX_RETRY_COUNT} retries. Aborting for this segment.")
            return False
        # after each chunk, if more chunks remain, append a single <break strength="strong"/> by:
        if i < num_chunks - 1:
            final_audio += AudioSegment.silent(duration=0)

    # 获取段落索引并添加延迟
    segment_idx = int(output_file.split('_')[-2])
    delay_time = int(VOICE_DELAY_TIME) if isinstance(VOICE_DELAY_TIME, str) else VOICE_DELAY_TIME
    if segment_idx > 1:
        silence = AudioSegment.silent(duration=delay_time)
        final_audio = silence + final_audio
    try:
        final_audio.export(output_file, format="wav")
        return True
    except Exception as e:
        logger.error(f"Error exporting merged audio: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Text-to-Speech Synthesis for text or JSON segments")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--json", type=str, help="Path to the input JSON file")
    group.add_argument("--text", type=str, help="Path to the input text file")
    parser.add_argument("--theme", type=str, required=True, help="Theme name for audio directory")
    parser.add_argument("--lang", type=str, default="Chinese",
                       help="Language for TTS (default: Chinese, options: English, English-Story, Japanese, Chinese, Spanish, Portuguese)")
    args = parser.parse_args()

    lang_code = get_language_code(args.lang)

    base_audio_dir = AUDIO_DIR.format(theme=args.theme)

    theme_audio_dir = (
        base_audio_dir if args.lang.lower() == 'english'
        else f"{base_audio_dir}_{lang_code}"
    )
    os.makedirs(theme_audio_dir, exist_ok=True)

    if args.text:
        paragraphs = process_script(args.text)

        segments = []
        for para in paragraphs:
            segment = {
                "segment_id": para["segment_id"],
                "paragraph_text": para["paragraph_text"]
            }
            segments.append(segment)

        base_name = os.path.splitext(os.path.basename(args.text))[0]
        text_json = os.path.join(os.path.dirname(args.text), f"{base_name}.json")
        with open(text_json, 'w', encoding='utf-8') as f:
            json.dump(segments, f, ensure_ascii=False, indent=2)

        input_json = text_json
    else:
        input_json = args.json

    output_json = process_segments(input_json, theme_audio_dir, args.theme, args.lang)
    logger.info(f"Processing completed. Results saved to {output_json}")
