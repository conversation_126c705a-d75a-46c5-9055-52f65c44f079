import logging
import requests
import time
from typing import Dict, List, Optional
from .direct_downloader import BaseDownloader

class JDownloader(BaseDownloader):
    def __init__(self, api_url: str, email: str, password: str):
        self.api_url = api_url
        self.email = email
        self.password = password
        self.session_token = None
        self.logger = logging.getLogger("JDownloader")

    def authenticate(self) -> bool:
        """JDownloader API 认证"""
        payload = {
            "email": self.email,
            "password": self.password
        }
        try:
            response = requests.post(f"{self.api_url}/myjd/auth", json=payload, timeout=15)
            response.raise_for_status()
            self.session_token = response.json().get("sessiontoken")
            return bool(self.session_token)
        except Exception as e:
            self.logger.error(f"JDownloader 认证失败: {e}")
            return False

    def add_download(self, url: str, destination: str) -> Optional[str]:
        """添加下载任务到 JDownloader"""
        if not self.session_token:
            self.logger.error("未经认证，请先调用 authenticate()")
            return None
            
        headers = {"Authorization": f"Bearer {self.session_token}"}
        payload = {
            "urls": [url],
            "packageName": "Downloaded Videos",
            "destinationFolder": destination
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/myjd/addLinks",
                json=payload,
                headers=headers,
                timeout=15
            )
            response.raise_for_status()
            return response.json().get("downloadIds")
        except Exception as e:
            self.logger.error(f"添加下载任务失败: {e}")
            return None

    def monitor_downloads(self, urls: List[str], timeout: int = 600) -> Dict[str, Optional[str]]:
        """监控 JDownloader 下载进度"""
        if not self.session_token:
            return {url: None for url in urls}
            
        start_time = time.time()
        video_to_file = {url: None for url in urls}
        
        while time.time() - start_time < timeout:
            try:
                headers = {"Authorization": f"Bearer {self.session_token}"}
                response = requests.get(
                    f"{self.api_url}/myjd/downloads",
                    headers=headers,
                    timeout=15
                )
                downloads = response.json().get("downloads", [])
                
                for download in downloads:
                    link = download.get("link")
                    if link in video_to_file and download.get("status") == "FINISHED":
                        video_to_file[link] = download.get("filePath")
                
                if all(path is not None for path in video_to_file.values()):
                    break
                    
                time.sleep(10)
                
            except Exception as e:
                self.logger.error(f"监控下载状态失败: {e}")
                
        return video_to_file 