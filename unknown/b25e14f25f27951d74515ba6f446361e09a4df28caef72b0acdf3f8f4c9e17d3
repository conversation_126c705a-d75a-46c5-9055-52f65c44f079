#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import time
import backoff  # 需要安装: pip install backoff
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from googleapiclient.errors import HttpError
from google.oauth2 import service_account
import gspread
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import socket
import ssl

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.langchain_interface import call_llm_json_response
from config import logger, LANGUAGE_CODES, get_param, GOOGLE_OAUTH_JSON
from modules.gpt_parameters import LLM_PROVIDER_DEEPSEEK, MODEL_KEY_ARK_DEEPSEEK_R1
# 配置参数
GOOGLE_SHEET_ID = "12Fa3JV9d7D4bqgODIOmxqBcA9iZUAY9nRdTdo76ze7E"  # 替换为实际的Google Sheet ID
DRIVE_FOLDERS = {
    "english": "1bEzSwNqXP3F60UMHakih1W9pdJiL8Voe",  # 替换为实际的Google Drive文件夹ID
    "japanese": "1c5oP2nSfBC-fpNt-2I0527_Gcva4woou",
    "spanish": "1RCG1rHyVVRMam6GMvk8_vsHtVYxWzaoW",
    "portuguese": "1IsMkW7SnJVAG9V5HSHcKqzGfrTR2_3PX"
}

# 定义所有权转移的目标账号
TARGET_OWNER_EMAIL = "<EMAIL>"

# 网络请求的重试装饰器
@backoff.on_exception(
    backoff.expo, 
    (HttpError, socket.timeout, ssl.SSLError, ConnectionError), 
    max_tries=5,
    giveup=lambda e: isinstance(e, HttpError) and e.resp.status >= 400 and e.resp.status not in [429, 500, 502, 503, 504]
)
def api_call_with_retry(func, *args, **kwargs):
    """使用指数退避策略的API调用重试包装器"""
    try:
        return func(*args, **kwargs)
    except (HttpError, socket.timeout, ssl.SSLError, ConnectionError) as e:
        logger.warning(f"API调用失败，正在重试: {str(e)}")
        raise

def authenticate_google():
    """
    使用服务账号凭据验证Google API
    
    Returns:
        Tuple: (drive_service, sheets_client)
    """
    try:
        # 使用配置中定义的 GOOGLE_OAUTH_JSON 路径
        if not os.path.exists(GOOGLE_OAUTH_JSON):
            raise FileNotFoundError(f"Google OAuth JSON 文件不存在: {GOOGLE_OAUTH_JSON}")
            
        # 从服务账号文件创建凭据
        credentials = service_account.Credentials.from_service_account_file(
            GOOGLE_OAUTH_JSON,
            scopes=['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/spreadsheets']
        )
        
        # 创建Drive和Sheets服务，增加超时设置
        drive_service = build('drive', 'v3', credentials=credentials, cache_discovery=False)
        sheets_client = gspread.authorize(credentials)
        
        logger.info("Google OAuth 认证成功")
        return drive_service, sheets_client
    except Exception as e:
        logger.error(f"Google认证失败: {e}")
        raise

def delete_old_files(drive_service, folder_id: str, hours_old: int = 24, batch_size: int = 10) -> None:
    """
    删除指定文件夹中超过指定小时数的文件，采用分批处理和重试机制
    
    Args:
        drive_service: Google Drive服务
        folder_id: 目标文件夹ID
        hours_old: 多少小时前的文件需要删除，默认24小时
        batch_size: 每批处理的文件数量
    """
    try:
        # 计算时间阈值
        time_threshold = datetime.utcnow() - timedelta(hours=hours_old)
        # 转换为ISO格式的字符串
        time_threshold_str = time_threshold.isoformat("T") + "Z"
        
        # 构建查询条件，查找文件夹中在阈值时间之前创建的文件
        query = f"'{folder_id}' in parents and modifiedTime < '{time_threshold_str}' and trashed = false"
        
        # 分页获取文件列表，使用页码令牌
        page_token = None
        all_files = []
        
        while True:
            try:
                # 使用重试机制获取文件列表
                results = api_call_with_retry(
                    drive_service.files().list,
                    q=query,
                    fields="nextPageToken, files(id, name, modifiedTime)",
                    pageSize=100,
                    pageToken=page_token
                ).execute()
                
                files = results.get('files', [])
                all_files.extend(files)
                
                # 检查是否有更多页
                page_token = results.get('nextPageToken')
                if not page_token:
                    break
                    
            except Exception as e:
                logger.error(f"获取文件列表时出错: {str(e)}")
                # 如果获取列表时出错，使用已获取的部分文件继续
                break
        
        if not all_files:
            logger.info(f"未找到超过 {hours_old} 小时的旧文件需要删除")
            return
        
        logger.info(f"找到 {len(all_files)} 个旧文件需要删除")
        
        # 分批处理文件删除
        deleted_count = 0
        total_files = len(all_files)
        
        for i in range(0, total_files, batch_size):
            batch_files = all_files[i:i+batch_size]
            batch_num = i // batch_size + 1
            logger.info(f"开始处理第 {batch_num} 批文件 ({len(batch_files)} 个文件)")
            
            for file in batch_files:
                file_id = file.get('id')
                file_name = file.get('name')
                
                try:
                    # 使用重试机制删除文件
                    api_call_with_retry(
                        drive_service.files().delete,
                        fileId=file_id
                    ).execute()
                    
                    deleted_count += 1
                    logger.info(f"已删除旧文件 ({deleted_count}/{total_files}): {file_name}")
                    
                except Exception as e:
                    logger.warning(f"删除文件 {file_name} (ID: {file_id}) 失败: {str(e)}")
            
            # 批处理间添加短暂延迟，避免请求过于频繁
            if i + batch_size < total_files:
                time.sleep(1)
        
        logger.info(f"删除操作完成: 成功删除 {deleted_count}/{total_files} 个旧文件")
        
    except Exception as e:
        logger.error(f"删除旧文件时出错: {str(e)}")
        # 不抛出异常，以避免影响主要的上传功能

def generate_metadata(script_content: str, language: str = "en") -> dict:
    """
    生成视频元数据
    
    Args:
        script_content: 脚本内容
        language: 目标语言代码
        
    Returns:
        Dict: 包含标题、描述等元数据的字典
    """
    try:
        data = {
            "script": script_content,
            "LANGUAGE": language
        }
        
        expected_fields = ["title", "description", "tags"]
        metadata = call_llm_json_response(
            api_function="gen-metadata",
            prompt_data=data,
            max_retries=5,
            llm_type=LLM_PROVIDER_DEEPSEEK,
            model_key=MODEL_KEY_ARK_DEEPSEEK_R1,
            using_cache=True,
            expected_fields=expected_fields
        )
        
        return metadata
        
    except Exception as e:
        logger.error(f"生成元数据时出错: {e}")
        raise

def upload_file_to_drive(drive_service, file_path: str, folder_id: str, file_name: Optional[str] = None) -> str:
    """
    上传文件到Google Drive，如果存在同名文件则覆盖
    
    Args:
        drive_service: Google Drive服务
        file_path: 文件的本地路径
        folder_id: 目标文件夹ID
        file_name: 可选，上传后的文件名
        
    Returns:
        str: 上传文件的ID
    """
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        file_name = file_name if file_name else os.path.basename(file_path)
        
        # 查找文件夹中是否已有同名文件
        try:
            query = f"name = '{file_name}' and '{folder_id}' in parents and trashed = false"
            results = api_call_with_retry(
                drive_service.files().list,
                q=query,
                spaces='drive',
                fields='files(id, name)'
            ).execute()
            
            existing_files = results.get('files', [])
        except Exception as e:
            logger.warning(f"查询现有文件失败: {e}，将创建新文件")
            existing_files = []
        
        file_metadata = {
            'name': file_name,
            'parents': [folder_id]
        }
        
        media = MediaFileUpload(file_path, resumable=True)
        
        if existing_files:
            # 如果文件已存在，则更新文件内容
            file_id = existing_files[0]['id']
            file = api_call_with_retry(
                drive_service.files().update,
                fileId=file_id,
                body={'name': file_name},
                media_body=media,
                fields='id'
            ).execute()
            logger.info(f"已更新Google Drive中的文件 {file_name}，文件ID: {file.get('id')}")
        else:
            # 如果文件不存在，则创建新文件
            file = api_call_with_retry(
                drive_service.files().create,
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            logger.info(f"已上传文件 {file_path} 到Google Drive，文件ID: {file.get('id')}")
            
        return file.get('id')
        
    except Exception as e:
        logger.error(f"上传文件到Google Drive时出错: {e}")
        raise

def update_google_sheet(sheets_client, sheet_id: str, metadata: dict, video_file_name: str, language: str) -> None:
    """
    更新Google Sheet中的元数据：如果已存在相同文件名的行则更新，否则追加新行。
    
    参数:
        sheets_client: Google Sheets客户端
        sheet_id: Google Sheet的ID
        metadata: 包含视频元数据的字典
        video_file_name: 视频文件名
        language: 视频语言（此参数可根据需求扩展使用）
    """
    try:
        # 打开指定的Google Sheet，并选择第一个工作表
        sheet = sheets_client.open_by_key(sheet_id)
        worksheet = sheet.get_worksheet(0)

        # 将标签列表转换为逗号分隔的字符串
        if isinstance(metadata.get("tags", []), list):
            tags = ", ".join(metadata.get("tags", []))
        else:
            tags = str(metadata.get("tags", ""))
            
        # 准备要更新或添加的行数据
        row_data = [video_file_name, metadata.get("title", ""), metadata.get("description", ""), tags]

        # 尝试查找包含该文件名的单元格
        cell = worksheet.find(video_file_name)
        if cell is not None:
            row_number = cell.row
            # 批量更新整行数据
            worksheet.update(values=[row_data], range_name=f"A{row_number}:D{row_number}")
            logger.info(f"已更新Google Sheet中文件名为 {video_file_name} 的条目")
        else:
            # 如果找不到匹配的行，则追加新行
            worksheet.append_row(row_data, value_input_option="USER_ENTERED")
            logger.info(f"已在Google Sheet中添加新的元数据条目，文件名: {video_file_name}")
        
    except gspread.exceptions.APIError as api_err:
        logger.error(f"更新Google Sheet时遇到APIError: {api_err}。请确保在Google Cloud Console中启用了Google Sheets API。")
        raise
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"更新Google Sheet时出错: {e}\n{error_details}")
        raise

def main():
    parser = argparse.ArgumentParser(description="Generate metadata and upload files to Google Drive")
    parser.add_argument('--script', required=True, help="Path to the script file or script content")
    parser.add_argument('--video', required=True, help="Path to the video file")
    parser.add_argument('--cover', required=True, help="Path to the cover image file")
    parser.add_argument('--lang', required=True, help="Language (e.g., English, Spanish, Portuguese)")
    parser.add_argument('--no-cleanup', action='store_true', help="禁用自动清理旧文件")
    parser.add_argument('--batch-size', type=int, default=10, help="删除文件时的批处理大小")
    args = parser.parse_args()
    
    try:
        # 检查文件是否存在
        if not os.path.exists(args.video):
            raise FileNotFoundError(f"视频文件不存在: {args.video}")
        
        if not os.path.exists(args.cover):
            raise FileNotFoundError(f"封面图片不存在: {args.cover}")
        
        # 读取脚本内容
        script_content = ""
        if os.path.exists(args.script):
            with open(args.script, 'r', encoding='utf-8') as file:
                script_content = file.read()
        else:
            script_content = args.script
        
        # 获取完整的语言代码
        language_code = args.lang.lower()
        if language_code not in DRIVE_FOLDERS:
            logger.warning(f"未找到语言 {language_code} 的文件夹配置，使用默认语言(en)")
            language_code = "en"
            
        # 认证Google服务
        drive_service, sheets_client = authenticate_google()
        
        # 获取目标文件夹ID
        folder_id = DRIVE_FOLDERS[language_code]
        
        # 在上传前清理旧文件，使用try/except包装以确保即使清理失败也能继续上传
        if not args.no_cleanup:
            try:
                logger.info(f"开始清理 {language_code} 文件夹中24小时前的旧文件")
                delete_old_files(drive_service, folder_id, hours_old=24, batch_size=args.batch_size)
            except Exception as cleanup_error:
                logger.error(f"清理旧文件失败，但将继续进行上传: {cleanup_error}")
        
        # 生成元数据
        metadata = generate_metadata(script_content, language_code)
        
        # 获取视频文件名
        video_file_name = os.path.basename(args.video)

        # 更新Google Sheet，只传入需要的四个值
        update_google_sheet(sheets_client, GOOGLE_SHEET_ID, metadata, video_file_name, language_code)        
        
        # 上传视频和封面到Google Drive
        upload_file_to_drive(
            drive_service, 
            args.video, 
            folder_id
        )
        
        upload_file_to_drive(
            drive_service, 
            args.cover, 
            folder_id
        )
        
        logger.info("所有任务已完成")
        
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 