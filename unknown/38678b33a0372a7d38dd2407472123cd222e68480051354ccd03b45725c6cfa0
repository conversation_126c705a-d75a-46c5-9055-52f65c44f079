#!/bin/bash

#set -e
#set -x

# 参数：主题批次文件
BATCH_FILE=$1

# 检查是否提供了批次文件参数
if [ -z "$BATCH_FILE" ]; then
    echo "错误：必须提供批次文件。"
    exit 1
fi

# 配置
STATUS_DIR="/home/<USER>/theme-talker/topics/"
BATCH_DATE=$(basename "$BATCH_FILE" .txt)

# 将批次文件读入数组
topics=()
while IFS= read -r line || [ -n "$line" ]; do
    topics+=("$line")
done < "$BATCH_FILE"

# 处理每个主题
for TOPIC in "${topics[@]}"; do
    # 检查 TOPIC 是否为空
    if [ -z "$TOPIC" ]; then
        echo "警告：发现空的主题行，跳过处理。"
        continue
    fi

    echo "开始处理主题: $TOPIC"

    # 切换到指定目录
    cd ~/theme-talker

    # 激活 conda 环境
    source ~/miniconda3/etc/profile.d/conda.sh
    conda activate base

    # 生成 embedding
    IMAGE_DIR="$PWD/results/$TOPIC/images"
    echo "生成 embedding: python fetch_process_images.py --embedding '$IMAGE_DIR' --theme '$TOPIC'"
    if python fetch_process_images.py --embedding "$IMAGE_DIR" --theme "$TOPIC"; then
        echo "成功生成 $TOPIC 的 embedding。"
    else
        echo "错误：生成 $TOPIC 的 embedding 时发生错误。"
        exit 1
    fi

    echo "执行处理脚本命令:"
    tsp bash run_all.sh "$TOPIC" 2 5 6 7 15 10 11 12

    echo "所有语言的处理任务已添加到 task spooler 队列中。"
    echo "您可以使用 'tsp -l' 命令查看任务状态。"

    echo "$TOPIC 处理完成。"
done

echo "所有主题处理完毕！"
