#!/bin/bash

# 配置工作目录
WORK_DIR=~/ai-video

# 参数：主题批次文件
BATCH_FILE=$1

# 检查是否提供了批次文件参数
if [ -z "$BATCH_FILE" ]; then
    echo "错误：必须提供批次文件。"
    exit 1
fi

# 配置
BATCH_DATE=$(basename "$BATCH_FILE" .txt)

# 将批次文件读入数组
topics=()
while IFS= read -r line || [ -n "$line" ]; do
    topics+=("$line")
done < "$BATCH_FILE"

# 处理每个主题
for TOPIC in "${topics[@]}"; do
    # 检查 TOPIC 是否为空
    if [ -z "$TOPIC" ]; then
        echo "警告：发现空的主题行，跳过处理。"
        continue
    fi

    echo "开始处理主题: $TOPIC"

    # 切换到指定目录
    cd $WORK_DIR

    # 激活 conda 环境
    source ~/miniconda3/etc/profile.d/conda.sh
    conda activate base

    # 结果目录
    RESULT_DIR="$WORK_DIR/1-theme-talker/results/${TOPIC}"
    
    # 使用 find 动态查找文件
    # TEXT_FILE=$(find "$RESULT_DIR" -type f -name "${TOPIC}_cn.txt" 2>/dev/null)
    
    # if [ -z "$TEXT_FILE" ]; then
    #     echo "错误：在 $RESULT_DIR 中未找到 ${TOPIC}_cn.txt 文件"
    #     exit 1
    # fi

    # 翻译文本 - 直接使用 TOPIC 作为主题名称
    # echo "翻译文本: python translate_script.py \"$TEXT_FILE\" --lang English --orig_lang Chinese --theme \"$TOPIC\""
    # if ! python translate_script.py "$TEXT_FILE" --lang English --orig_lang Chinese --theme "$TOPIC"; then
    #     echo "错误：翻译 $TOPIC 的文本时发生错误。"
    #     exit 1
    # fi
    
    #echo "成功翻译 $TOPIC 的文本。"

    echo "执行处理脚本命令:"
    tsp sleep 99999
    tsp bash run_by_lang.sh "$TOPIC" --lang English all
    tsp bash run_by_lang.sh "$TOPIC" --lang Spanish all
    tsp bash run_by_lang.sh "$TOPIC" --lang Portuguese all
    tsp bash run_by_lang.sh "$TOPIC" --lang Japanese all

    echo "所有语言的处理任务已添加到 task spooler 队列中。"
    echo "您可以使用 'tsp -l' 命令查看任务状态。"

    echo "$TOPIC 处理完成。"
done

echo "所有主题处理完毕！"
