{"1": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader"}, "2": {"inputs": {"lora_name": "Art_<PERSON><PERSON><PERSON><PERSON>_吉卜力动画风格_V1.safetensors", "strength_model": 1, "model": ["141", 0]}, "class_type": "LoraLoaderModelOnly"}, "3": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 800, "height": 1280, "model": ["2", 0]}, "class_type": "ModelSamplingFlux"}, "12": {"inputs": {"width": 800, "height": 1280, "batch_size": 1}, "class_type": "EmptyLatentImage"}, "53": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "54": {"inputs": {"text": "\"prompt\": \n          \"story_moment\": \"<PERSON> wakes up in a medieval setting as <PERSON>, the fourth prince of Graycastle\",\n          \"narrative\": \n            \"plot_point\": \"<PERSON>, a modern engineer, finds himself in the body of <PERSON>\",\n            \"emotional_beat\": \"Confusion and realization\",\n            \"tension_elements\": \"Struggle to comprehend the new reality and identity\"\n          ,\n          \"scene\": \n            \"main_focus\": \n              \"primary_action\": \"Waking up in a medieval room\",\n              \"emotional_tone\": \"Disoriented and bewildered\"\n            ,\n            \"characters\": \n              \"main_subject\": \"Roland Wimbledon (inhabited by <PERSON>)\",\n              \"actions_and_expressions\": [\n                \n                  \"name\": \"<PERSON> (inhabited by <PERSON>)\",\n                  \"pose\": \"Sitting up abruptly in bed, looking around the room\",\n                  \"expression\": \"Wide-eyed with confusion and shock\",\n                  \"interaction\": \"Reacting to the unfamiliar surroundings\",\n                  \"appearance\": \n                    \"age\": \"Early 20s\",\n                    \"gender\": \"Male\",\n                    \"height\": \"Tall\",\n                    \"build\": \"Lean but fit\",\n                    \"facial_features\": \"Sharp features, blue eyes\",\n                    \"distinctive_features\": \"Royal demeanor, often seen with a thoughtful expression\",\n                    \"attire_summary\": \"Royal medieval attire, often in rich fabrics and colors indicating his status\"\n                  \n                \n              ]\n            ,\n            \"environment\": \n              \"location\": \"Medieval bedroom in a castle\",\n              \"time\": \"Morning with soft sunlight filtering through a small window\",\n              \"props\": \"Medieval furniture, tapestries, a wooden chest, a small table with a basin\"\n            ,\n            \"composition\": \n              \"shot_type\": \"Medium\",\n              \"angle\": \"Eye-level\",\n              \"focus_point\": \"<PERSON> <PERSON> sitting up in bed\"\n            ,\n            \"technical\": \n              \"special_effects\": \"Soft light rays entering through the window\",\n              \"important_details\": \"Blend of medieval and subtle modern elements in the room\"\n            \n          \n        ", "clip": ["53", 0]}, "class_type": "CLIPTextEncode"}, "55": {"inputs": {"text": "<PERSON><PERSON><PERSON><PERSON>, KikiLaPetiteSorciere style image, ", "clip": ["53", 0]}, "class_type": "CLIPTextEncode"}, "56": {"inputs": {"conditioning": ["54", 0]}, "class_type": "ConditioningZeroOut"}, "57": {"inputs": {"conditioning_to": ["55", 0], "conditioning_from": ["54", 0]}, "class_type": "ConditioningConcat"}, "59": {"inputs": {"guidance": 2, "conditioning": ["57", 0]}, "class_type": "FluxGuidance"}, "95": {"inputs": {"noise": ["96", 0], "guider": ["97", 0], "sampler": ["98", 0], "sigmas": ["99", 0], "latent_image": ["12", 0]}, "class_type": "SamplerCustomAdvanced"}, "96": {"inputs": {"noise_seed": 387120993434332}, "class_type": "RandomNoise"}, "97": {"inputs": {"model": ["3", 0], "conditioning": ["59", 0]}, "class_type": "BasicGuider"}, "98": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "99": {"inputs": {"scheduler": "normal", "steps": 10, "denoise": 1, "model": ["3", 0]}, "class_type": "BasicScheduler"}, "103": {"inputs": {"samples": ["95", 0], "vae": ["105", 0]}, "class_type": "VAEDecode"}, "104": {"inputs": {"filename_prefix": "ComfyUI", "images": ["103", 0]}, "class_type": "SaveImage"}, "105": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "112": {"inputs": {"width": 1024, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "disabled", "image": ["103", 0]}, "class_type": "ImageResizeKJ"}, "114": {"inputs": {"upscale_model": ["115", 0], "image": ["112", 0]}, "class_type": "ImageUpscaleWithModel"}, "115": {"inputs": {"model_name": "4x_NMKD-Siax_200k.pth"}, "class_type": "UpscaleModelLoader"}, "116": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 4, "image": ["114", 0]}, "class_type": "ImageScaleToTotalPixels"}, "117": {"inputs": {"width_factor": 2, "height_factor": 4, "overlap_rate": 0.05, "image": ["116", 0]}, "class_type": "TTP_Tile_image_size"}, "118": {"inputs": {"tile_width": ["117", 0], "tile_height": ["117", 1], "image": ["116", 0]}, "class_type": "TTP_Image_Tile_Batch"}, "119": {"inputs": {"image": ["118", 0]}, "class_type": "easy imageBatchToImageList"}, "120": {"inputs": {"padding": 128, "tiles": ["121", 0], "positions": ["118", 1], "original_size": ["118", 2], "grid_size": ["118", 3]}, "class_type": "TTP_Image_Assy"}, "121": {"inputs": {"images": ["122", 0]}, "class_type": "ImageListToImageBatch"}, "122": {"inputs": {"tile_size": 1024, "overlap": 64, "samples": ["124", 0], "vae": ["123", 0]}, "class_type": "VAEDecodeTiled"}, "123": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "124": {"inputs": {"noise": ["125", 0], "guider": ["128", 0], "sampler": ["133", 0], "sigmas": ["127", 0], "latent_image": ["134", 0]}, "class_type": "SamplerCustomAdvanced"}, "125": {"inputs": {"noise_seed": 316430325547060}, "class_type": "RandomNoise"}, "126": {"inputs": {"text": "<PERSON><PERSON><PERSON><PERSON>, KikiLaPetiteSorciere style image, high quality, detailed, photograph , hd, 8k , 4k , sharp, highly detailed", "clip": ["132", 1]}, "class_type": "CLIPTextEncode"}, "127": {"inputs": {"scheduler": "simple", "steps": 4, "denoise": 0.3, "model": ["132", 0]}, "class_type": "BasicScheduler"}, "128": {"inputs": {"model": ["132", 0], "conditioning": ["129", 0]}, "class_type": "BasicGuider"}, "129": {"inputs": {"guidance": 2.5, "conditioning": ["126", 0]}, "class_type": "FluxGuidance"}, "132": {"inputs": {"lora_name": "Flux Dev模型4步出图lora_Flux Dev 4-step.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["142", 0], "clip": ["53", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "133": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "134": {"inputs": {"pixels": ["119", 0], "vae": ["123", 0]}, "class_type": "VAEEncode"}, "135": {"inputs": {"filename_prefix": "ComfyUI", "images": ["120", 0]}, "class_type": "SaveImage"}, "136": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_qpboy_00007_.png&type=temp&subfolder=&rand=0.024081125096593547"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_qpboy_00008_.png&type=temp&subfolder=&rand=0.10549939356705784"}]}, "image_a": ["103", 0], "image_b": ["120", 0]}, "class_type": "Image Comparer (rgthree)"}, "141": {"inputs": {"lora_name": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.1, "model": ["1", 0]}, "class_type": "LoraLoaderModelOnly"}, "142": {"inputs": {"lora_name": "Art_<PERSON><PERSON><PERSON><PERSON>_吉卜力动画风格_V1.safetensors", "strength_model": 1, "model": ["1", 0]}, "class_type": "LoraLoaderModelOnly"}}