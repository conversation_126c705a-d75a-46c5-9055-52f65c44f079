#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
from datetime import datetime
from googleapiclient.discovery import build
from google.oauth2 import service_account
from tabulate import tabulate

# 将项目根目录添加到路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import logger, GOOGLE_OAUTH_JSON

# 文件夹映射
DRIVE_FOLDERS = {
    "english": "1bEzSwNqXP3F60UMHakih1W9pdJiL8Voe",
    "japanese": "1c5oP2nSfBC-fpNt-2I0527_Gcva4woou",
    "spanish": "1RCG1rHyVVRMam6GMvk8_vsHtVYxWzaoW",
    "portuguese": "1IsMkW7SnJVAG9V5HSHcKqzGfrTR2_3PX"
}

def authenticate_google():
    """使用服务账号凭据验证Google API"""
    try:
        if not os.path.exists(GOOGLE_OAUTH_JSON):
            raise FileNotFoundError(f"Google OAuth JSON 文件不存在: {GOOGLE_OAUTH_JSON}")
            
        credentials = service_account.Credentials.from_service_account_file(
            GOOGLE_OAUTH_JSON,
            scopes=['https://www.googleapis.com/auth/drive']
        )
        
        drive_service = build('drive', 'v3', credentials=credentials)
        logger.info("Google OAuth 认证成功")
        return drive_service
    except Exception as e:
        logger.error(f"Google认证失败: {e}")
        raise

def get_storage_usage(drive_service):
    """获取Google Drive存储空间使用情况"""
    try:
        about = drive_service.about().get(fields="storageQuota").execute()
        quota = about.get('storageQuota', {})
        
        usage = int(quota.get('usage', 0))
        limit = int(quota.get('limit', 0))
        
        # 转换为MB/GB用于显示
        usage_mb = usage / (1024 * 1024)
        limit_mb = limit / (1024 * 1024) if limit > 0 else 0
        
        usage_gb = usage_mb / 1024
        limit_gb = limit_mb / 1024 if limit_mb > 0 else 0
        
        percent = (usage / limit * 100) if limit > 0 else 0
        
        return {
            'usage_bytes': usage,
            'limit_bytes': limit,
            'usage_mb': usage_mb,
            'limit_mb': limit_mb,
            'usage_gb': usage_gb,
            'limit_gb': limit_gb,
            'percent': percent
        }
    except Exception as e:
        logger.error(f"获取存储使用情况时出错: {e}")
        raise

def list_files(drive_service, folder_id=None, order_by="modifiedTime desc", page_size=100):
    """列出文件夹中的文件"""
    try:
        query = ""
        if folder_id:
            query = f"'{folder_id}' in parents and trashed = false"
        else:
            query = "trashed = false"
            
        results = drive_service.files().list(
            q=query,
            pageSize=page_size,
            fields="nextPageToken, files(id, name, mimeType, size, createdTime, modifiedTime)",
            orderBy=order_by
        ).execute()
        
        return results.get('files', [])
    except Exception as e:
        logger.error(f"列出文件时出错: {e}")
        raise

def format_date(date_string):
    """格式化日期时间字符串"""
    try:
        date_obj = datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        return date_obj.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return date_string

def format_size(size_bytes):
    """将字节转换为人类可读格式"""
    if size_bytes is None:
        return "N/A"
    
    size_bytes = int(size_bytes)
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0 or unit == 'GB':
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0

def delete_file(drive_service, file_id):
    """删除指定ID的文件"""
    try:
        drive_service.files().delete(fileId=file_id).execute()
        return True
    except Exception as e:
        logger.error(f"删除文件时出错: {e}")
        return False

def delete_files_by_pattern(drive_service, folder_id=None, name_pattern=None, older_than=None):
    """根据条件批量删除文件"""
    try:
        # 构建查询条件
        query_parts = []
        
        if folder_id:
            query_parts.append(f"'{folder_id}' in parents")
        
        if name_pattern:
            query_parts.append(f"name contains '{name_pattern}'")
            
        # 添加未删除的条件
        query_parts.append("trashed = false")
        
        query = " and ".join(query_parts)
        
        # 查询文件
        results = drive_service.files().list(
            q=query,
            fields="nextPageToken, files(id, name, mimeType, size, createdTime, modifiedTime)"
        ).execute()
        
        files = results.get('files', [])
        deleted_count = 0
        deleted_size = 0
        
        # 如果指定了日期筛选
        if older_than:
            try:
                cutoff_date = datetime.strptime(older_than, "%Y-%m-%d")
                filtered_files = []
                for file in files:
                    file_date = datetime.fromisoformat(file['modifiedTime'].replace('Z', '+00:00'))
                    if file_date.date() < cutoff_date.date():
                        filtered_files.append(file)
                files = filtered_files
            except ValueError:
                logger.error(f"日期格式错误: {older_than}，应为YYYY-MM-DD")
        
        # 删除符合条件的文件
        for file in files:
            file_id = file['id']
            file_name = file['name']
            file_size = int(file.get('size', 0))
            
            if delete_file(drive_service, file_id):
                deleted_count += 1
                deleted_size += file_size
                logger.info(f"已删除: {file_name} (ID: {file_id})")
        
        return {
            'count': deleted_count,
            'size': deleted_size,
            'size_formatted': format_size(deleted_size)
        }
    except Exception as e:
        logger.error(f"批量删除文件时出错: {e}")
        raise

def empty_folder(drive_service, folder_id):
    """清空指定文件夹中的所有文件"""
    try:
        files = list_files(drive_service, folder_id)
        deleted_count = 0
        deleted_size = 0
        
        for file in files:
            file_id = file['id']
            file_name = file['name']
            file_size = int(file.get('size', 0))
            
            if delete_file(drive_service, file_id):
                deleted_count += 1
                deleted_size += file_size
                logger.info(f"已删除: {file_name} (ID: {file_id})")
        
        return {
            'count': deleted_count,
            'size': deleted_size,
            'size_formatted': format_size(deleted_size)
        }
    except Exception as e:
        logger.error(f"清空文件夹时出错: {e}")
        raise

def main():
    parser = argparse.ArgumentParser(description="Google Drive存储空间管理工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # info命令
    info_parser = subparsers.add_parser('info', help='显示存储空间使用情况')
    
    # list命令
    list_parser = subparsers.add_parser('list', help='列出文件')
    list_parser.add_argument('--folder', choices=list(DRIVE_FOLDERS.keys()), help='指定语言文件夹')
    list_parser.add_argument('--limit', type=int, default=50, help='显示文件数量限制')
    list_parser.add_argument('--sort', default='modified', choices=['modified', 'created', 'name', 'size'], help='排序方式')
    
    # delete命令
    delete_parser = subparsers.add_parser('delete', help='删除文件')
    delete_parser.add_argument('--file-id', help='要删除的特定文件ID')
    delete_parser.add_argument('--folder', choices=list(DRIVE_FOLDERS.keys()), help='指定语言文件夹')
    delete_parser.add_argument('--pattern', help='文件名包含的字符串模式')
    delete_parser.add_argument('--older-than', help='删除早于指定日期的文件 (格式: YYYY-MM-DD)')
    
    # empty命令
    empty_parser = subparsers.add_parser('empty', help='清空文件夹')
    empty_parser.add_argument('--folder', required=True, choices=list(DRIVE_FOLDERS.keys()), help='指定要清空的语言文件夹')
    empty_parser.add_argument('--confirm', action='store_true', help='确认清空操作')
    
    args = parser.parse_args()
    
    try:
        # 认证Google服务
        drive_service = authenticate_google()
        
        if args.command == 'info':
            # 显示存储空间使用情况
            usage_info = get_storage_usage(drive_service)
            
            print("\n=== Google Drive 存储空间使用情况 ===")
            print(f"已使用: {usage_info['usage_gb']:.2f} GB / {usage_info['limit_gb']:.2f} GB")
            print(f"使用率: {usage_info['percent']:.2f}%")
            print("=====================================\n")
            
        elif args.command == 'list':
            # 确定排序方式
            sort_map = {
                'modified': 'modifiedTime desc',
                'created': 'createdTime desc',
                'name': 'name',
                'size': 'quotaBytesUsed desc'
            }
            order_by = sort_map.get(args.sort, 'modifiedTime desc')
            
            # 确定要列出的文件夹ID
            folder_id = DRIVE_FOLDERS.get(args.folder) if args.folder else None
            folder_name = args.folder if args.folder else "所有文件"
            
            # 列出文件
            files = list_files(drive_service, folder_id, order_by, args.limit)
            
            print(f"\n=== {folder_name} (显示 {len(files)} 个文件) ===")
            
            # 准备表格数据
            table_data = []
            for file in files:
                table_data.append([
                    file['name'],
                    format_size(file.get('size')),
                    format_date(file.get('modifiedTime')),
                    file['id']
                ])
            
            # 使用tabulate打印表格
            print(tabulate(table_data, headers=['文件名', '大小', '修改时间', '文件ID'], tablefmt='grid'))
            print("\n")
            
        elif args.command == 'delete':
            if args.file_id:
                # 删除特定文件
                file_info = drive_service.files().get(fileId=args.file_id, fields="name").execute()
                file_name = file_info.get('name', args.file_id)
                
                if delete_file(drive_service, args.file_id):
                    print(f"成功删除文件: {file_name}")
                else:
                    print(f"删除文件失败: {file_name}")
            else:
                # 批量删除文件
                folder_id = DRIVE_FOLDERS.get(args.folder) if args.folder else None
                folder_name = args.folder if args.folder else "所有文件"
                
                if not (args.pattern or args.older_than):
                    print("错误: 请指定删除条件 (--pattern 或 --older-than)")
                    return
                
                print(f"\n准备从 {folder_name} 中删除文件")
                if args.pattern:
                    print(f"文件名包含: {args.pattern}")
                if args.older_than:
                    print(f"早于日期: {args.older_than}")
                
                confirm = input("确认删除这些文件? (y/n): ")
                if confirm.lower() != 'y':
                    print("操作已取消")
                    return
                
                result = delete_files_by_pattern(
                    drive_service, 
                    folder_id=folder_id, 
                    name_pattern=args.pattern, 
                    older_than=args.older_than
                )
                
                print(f"\n删除完成:")
                print(f"已删除 {result['count']} 个文件")
                print(f"释放空间: {result['size_formatted']}")
                
        elif args.command == 'empty':
            # 清空文件夹
            folder_id = DRIVE_FOLDERS.get(args.folder)
            
            if not folder_id:
                print(f"错误: 找不到文件夹 '{args.folder}'")
                return
            
            if not args.confirm:
                print(f"\n警告: 您即将清空 {args.folder} 文件夹中的所有文件!")
                print("此操作不可恢复。")
                confirm = input("确认清空? (输入'yes'确认): ")
                if confirm.lower() != 'yes':
                    print("操作已取消")
                    return
            
            result = empty_folder(drive_service, folder_id)
            
            print(f"\n清空完成:")
            print(f"已删除 {result['count']} 个文件")
            print(f"释放空间: {result['size_formatted']}")
            
        else:
            # 如果没有指定命令或命令无效
            parser.print_help()
        
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 