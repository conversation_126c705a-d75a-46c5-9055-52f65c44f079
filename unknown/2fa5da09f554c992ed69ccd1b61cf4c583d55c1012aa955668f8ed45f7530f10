# modules/utils.py
from typing import Union
import re
import unicodedata
import numpy as np
import os
from langdetect import detect
# 将数据类和验证器移到单独的文件
from dataclasses import dataclass
from typing import List, Dict, Tuple, Any, Callable, Optional
import tiktoken
from functools import wraps
import json
import logging
import datetime
import shutil
# 设置日志记录
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

DEFAULT_ENCODING = 'cl100k_base'  # Default tokenizer encoding

@dataclass
class ChunkSummary:
    plot: str
    characters: str
    scenes: str
    mood: str

@dataclass
class SummaryComponent:
    plot: str
    characters: List[str]
    scenes: List[str]
    key_events: List[str]
    emotions: List[str]

# validators.py
class SummaryValidator:
    @staticmethod
    def validate_summary(summary: Dict[str, Any]) -> bool:
        required_keys = {'plot', 'characters', 'scenes', 'mood'}
        return all(key in summary for key in required_keys)

    @staticmethod
    def validate_merged_summary(summary: Dict[str, Any]) -> bool:
        required_keys = {'plot_thread', 'character_arc', 'dramatic_scenes', 'final_script'}
        return all(key in summary for key in required_keys)

def read_file_with_encoding(file_path: str) -> str:
    """
    尝试使用不同的编码格式读取文件
    
    Args:
        file_path: 文件路径
    
    Returns:
        str: 文件内容
    """
    # 常见的中文编码格式
    encodings = ['utf-8', 'gb18030', 'gbk', 'gb2312', 'big5']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                logger.info(f"Successfully read file with {encoding} encoding")
                return content
        except UnicodeDecodeError:
            continue
        except Exception as e:
            logger.error(f"Error reading file with {encoding} encoding: {str(e)}")
            continue
    
    # 如果所有编码都失败了，尝试使用 chardet 检测编码
    try:
        import chardet
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        detected = chardet.detect(raw_data)
        encoding = detected['encoding']
        
        if encoding:
            try:
                content = raw_data.decode(encoding)
                logger.info(f"Successfully read file with detected encoding: {encoding}")
                return content
            except UnicodeDecodeError:
                pass
    except ImportError:
        logger.warning("chardet not installed. Unable to detect file encoding.")
    except Exception as e:
        logger.error(f"Error detecting file encoding: {str(e)}")
    
    raise UnicodeDecodeError(f"Unable to read file {file_path} with any known encoding")

def clean_text(text: str) -> str:
    """清理文本"""
    # 统一全角/半角字符
    text = unicodedata.normalize('NFKC', text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除特殊字符
    text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?，。！？""《》「」\-]', '', text)
    
    # 统一标点符号
    text = text.replace('"', '"').replace('"', '"')  # 统一引号
    text = text.replace(''', "'").replace(''', "'")  # 统一单引号
    text = text.replace('…', '...')  # 统一省略号
    
    return text.strip()

def clean_filename(file_path: str, suffixes: List[str] = None) -> str:
    """
    从文件路径中移除特定后缀并返回清理后的文件路径
    
    Args:
        file_path (str): 原始文件路径
        suffixes (List[str], optional): 需要移除的后缀列表。如果为None，使用默认后缀列表
        
    Returns:
        str: 清理后的文件路径
    """
    # 默认需要移除的后缀
    default_suffixes = [
        '_prompts_images_timing',
        '_prompts_timing_images',
        '_prompts_images',
        '_timing',
        '_prompts'
    ]
    
    suffixes = suffixes or default_suffixes
    
    # 分离目录、文件名和扩展名
    directory = os.path.dirname(file_path)
    filename = os.path.basename(file_path)
    basename, ext = os.path.splitext(filename)
    
    # 检查并移除已知后缀
    clean_name = basename
    for suffix in suffixes:
        if basename.endswith(suffix):
            clean_name = basename[:-len(suffix)]
            break
    
    # 重新组合文件路径
    return os.path.join(directory, f"{clean_name}{ext}")

def calculate_token_count(text: str, encoding: str = DEFAULT_ENCODING) -> int:
    """计算文本Token数"""
    try:
        tokenizer = tiktoken.get_encoding(encoding)
        return len(tokenizer.encode(text))
    except Exception as e:
        logger.error(f"Token计算错误: {str(e)}")
        return 0

def extract_chapter_info(text: str, default_title: str = "开篇") -> dict:
    """从文本中提取章节信息
    
    Args:
        text: 需要处理的文本/标题
        default_title: 默认标题，当无法匹配到章节标题时使用
        
    Returns:
        dict: 包含章节信息的字典
    """
    # 使用与 generate_chapter_summary.py 相同的正则表达式
    chapter_pattern = re.compile(r'^第([零一二三四五六七八九十百千万\d]+)([章节回集卷])(.*)$')
    
    # 初始化返回结果
    result = {
        "title": default_title,
        "chapter_type": "章",
        "chapter_number": "0",
        "is_valid": False
    }
    
    # 如果输入为空，返回默认值
    if not text:
        return result
        
    # 尝试匹配章节标题
    match = chapter_pattern.match(text)
    if match:
        number_str = match.group(1)    # 章节数字
        chapter_type = match.group(2)  # 章节类型
        title_rest = match.group(3)    # 标题剩余部分
        
        result.update({
            "title": text,
            "chapter_number": number_str,
            "chapter_type": chapter_type,
            "is_valid": True
        })
    
    return result


def copy_script_files(source_file: str, theme: str):
    """复制脚本文件到目标目录"""
    target_dir = SCRIPT_DIR.format(theme=theme)
    os.makedirs(target_dir, exist_ok=True)

    def copy_file(src: str, dst: str):
        if os.path.abspath(src) != os.path.abspath(dst):
            shutil.copy2(src, dst)
            logger.info(f"已将文件 {src} 复制到 {dst}")
        else:
            logger.info(f"源文件和目标文件相同，跳过复制: {src}")

    main_target = os.path.join(target_dir, f"{theme}.txt")
    copy_file(source_file, main_target)

    source_json = os.path.splitext(source_file)[0] + '.json'
    if os.path.exists(source_json):
        json_target = os.path.join(target_dir, f"{theme}.json")
        copy_file(source_json, json_target)
        
        source_wav = os.path.splitext(source_file)[0] + '.wav'
        if os.path.exists(source_wav):
            wav_target = os.path.join(target_dir, f"{theme}.wav")
            copy_file(source_wav, wav_target)

    base_name_without_ext = os.path.splitext(os.path.basename(source_file))[0]
    for lang, code in LANGUAGE_CODES.items():
        if lang == "English":
            continue
        
        lang_file = os.path.join(os.path.dirname(source_file), f"{base_name_without_ext}_{code}.txt")
        if os.path.exists(lang_file):
            lang_target = os.path.join(target_dir, f"{theme}_{code}.txt")
            copy_file(lang_file, lang_target)

    today = datetime.date.today().strftime("%Y%m%d")
    os.makedirs(TOPIC_DIR, exist_ok=True)
    topics_file = os.path.join(TOPIC_DIR, f"topic_{today}.txt")

    try:
        with open(topics_file, 'a+') as f:
            f.seek(0, os.SEEK_END)
            if f.tell() == 0:
                f.write(f"{theme}\n")
            else:
                f.write(f"\n{theme}")
        logger.info(f"已将主题 '{theme}' 添加到文件 {topics_file}")
    except IOError as e:
        logger.error(f"无法写入文件 {topics_file}: {e}")


def write_temp_file(content: Union[str, dict, list], filename_prefix: str, base_dir: str = '.') -> str:
    """
    在指定目录的temp文件夹下写入文件，添加时间戳并根据内容类型选择文件扩展名
    
    Args:
        content (Union[str, dict, list]): 要写入的内容，可以是字符串、字典或列表
        filename_prefix (str): 文件名前缀
        base_dir (str): 基础目录路径，默认为当前目录
        
    Returns:
        str: 写入文件的完整路径
    """
    # 创建temp目录
    temp_dir = os.path.join(base_dir, 'temp')
    os.makedirs(temp_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 根据内容类型确定文件扩展名和处理方式
    if isinstance(content, (dict, list)) or (
        isinstance(content, str) and content.strip().startswith('[') and content.strip().endswith(']')
    ):
        file_ext = '.json'
        # 如果内容是字符串形式的JSON，尝试解析它
        if isinstance(content, str):
            try:
                content = json.loads(content)
            except json.JSONDecodeError:
                file_ext = '.txt'
        content = json.dumps(content, ensure_ascii=False, indent=2)
    else:
        file_ext = '.txt'
        content = str(content)
    
    # 构建完整的文件路径（包含时间戳）
    file_path = os.path.join(temp_dir, f"{filename_prefix}_{timestamp}{file_ext}")
    
    try:
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
        logger.info(f"成功写入文件: {file_path}")
        return file_path
    except Exception as e:
        logger.error(f"写入文件失败: {str(e)}")
        raise


def normalize_language(language: str) -> str:
    """
    将输入的语言标准化为两字母代码
    """
    language = language.lower()
    for lang, code in LANGUAGE_CODES.items():
        if language in {lang.lower(), code}:
            return code
    raise ValueError(f"不支持的语言: {language}")


def split_into_chunks(text: str, max_tokens: int = 2000, language: str = 'zh') -> List[str]:
    """
    将文本按token数量分块，确保句子完整性并尽量均匀分配
    
    Args:
        text: 要分块的文本
        max_tokens: 每块最大token数
        language: 文本语言，默认为中文 'zh'
        
    Returns:
        List[str]: 分块后的文本列表
    """
    # 计算总token数和目标块大小
    total_tokens = calculate_token_count(text)
    chunk_number = max(1, (total_tokens + max_tokens - 1) // max_tokens)
    target_chunk_size = total_tokens // chunk_number
    
    logger.debug(f"总tokens: {total_tokens}, 目标块数: {chunk_number}, " 
                f"目标块大小: {target_chunk_size}, 语言: {language}")

    # 使用传入的语言参数进行分句
    sentences = smart_paragraph_split(text, language=language)
    logger.debug(f"分割后的句子数量: {len(sentences)}")

    chunks = []
    current_chunk = []
    current_tokens = 0
    
    for sentence in sentences:
        sentence_tokens = calculate_token_count(sentence)
        
        # 如果当前块已经达到或超过目标大小，开始新的块
        if current_tokens >= target_chunk_size and current_chunk:
            chunks.append(" ".join(current_chunk).strip())
            current_chunk = []
            current_tokens = 0
            
        # 将新句子添加到当前块
        current_chunk.append(sentence)
        current_tokens += sentence_tokens
    
    # 添加最后一个块
    if current_chunk:
        chunks.append(" ".join(current_chunk).strip())
    
    logger.info(f"文本已分割为 {len(chunks)} 个块，平均大小: "
                f"{sum(calculate_token_count(c) for c in chunks) / len(chunks):.0f} tokens")
    
    return chunks

def normalize_words(text: str) -> str:
    """
    规范化文本，将特殊字符替换为下划线，处理 Unicode 字符
    
    Args:
        text (str): 需要规范化的文本
            例如: "My Text (1)" 或 "user's text" 或 "résumé"
            
    Returns:
        str: 规范化后的文本
            例如: "My_Text_1" 或 "users_text" 或 "resume"
            
    Raises:
        ValueError: 如果输入为空
    """
    if not text:
        raise ValueError("输入文本不能为空")
    
    # Unicode 标准化处理：将组合字符转换为单个字符
    # NFKD 将字符分解为基本字符和组合字符
    text = unicodedata.normalize('NFKD', text)
    
    # 移除变音符号和其他组合字符
    text = ''.join(c for c in text if not unicodedata.combining(c))
    
    # 替换非字母数字和连字符的字符为下划线
    # 保留基本的ASCII字母数字字符和连字符
    normalized = re.sub(r'[^\x00-\x7F\w\-]|[^\w\-]', '_', text)
    
    # 合并连续的下划线
    normalized = re.sub(r'_+', '_', normalized)
    
    # 移除首尾的下划线
    return normalized.strip('_')

def normalized_filename(filename: str) -> str:
    """
    规范化文件名（不包含路径），将特殊字符替换为下划线
    
    Args:
        filename (str): 需要规范化的文件名
            
    Returns:
        str: 规范化后的文件名
            
    Raises:
        ValueError: 如果输入为空
    """
    if not filename:
        raise ValueError("文件名不能为空")
    
    # 只获取文件名部分（移除路径）
    filename = os.path.basename(filename)
    
    # 如果是特殊路径标记，直接返回
    if filename in {'.', '..'}:
        return filename
        
    # 处理带扩展名的文件
    if '.' in filename:
        name, ext = os.path.splitext(filename)
        # 使用新的 normalize_words 函数处理文件名部分
        normalized = normalize_words(name)
        return f"{normalized}{ext}"
    else:
        # 处理没有扩展名的文件名
        return normalize_words(filename)

def get_image_paths(directory: str, extensions: Optional[List[str]] = None) -> List[str]:
    """
    获取指定目录下的所有图像文件路径。

    :param directory: 图像目录路径
    :param extensions: 支持的图像扩展名列表
    :return: 图像文件路径列表
    """
    if extensions is None:
        extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp']
    image_paths = [
        os.path.join(directory, img)
        for img in os.listdir(directory)
        if os.path.splitext(img.lower())[1] in extensions
    ]
    logger.info(f"找到 {len(image_paths)} 张图像。")
    return image_paths