import os
import httpx # Using httpx for async requests, can be aiohttp as in example
import asyncio
import json
from pathlib import Path
from enum import Enum # Added Enum

import metadata_db # Changed from .. to .
import video_config  # Changed from .. to .
import utils         # Changed from .. to .

# === START MODIFICATION ===
class VideoProcessStatus(Enum):
    PROCESSED_NEWLY = 1
    SKIPPED_EXISTS = 2
    FAILED = 0
    SKIPPED_FILTERED = 3
# === END MODIFICATION ===

async def download_video_content(session: httpx.AsyncClient, url: str, output_path: Path, progress_bar=True):
    """
    Asynchronously downloads video content from a URL to a specified path.
    Includes basic retry logic and ensures idempotency by checking if file exists.
    """
    if output_path.exists() and output_path.stat().st_size > 0:
        print(f"Video already exists: {output_path}, size: {output_path.stat().st_size}. Skipping download.")
        return True

    # Ensure parent directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)

    temp_output_path = output_path.with_suffix(output_path.suffix + ".tmp")
    max_retries = 3
    base_delay = 1 # seconds

    for attempt in range(max_retries):
        try:
            print(f"Attempt {attempt + 1}/{max_retries}: Downloading {url} to {temp_output_path}...")
            async with session.stream("GET", url, timeout=video_config.DOWNLOAD_TIMEOUT_SECONDS) as response:
                response.raise_for_status() # Will raise an exception for 4XX/5XX status

                total_size = int(response.headers.get("content-length", 0))
                downloaded_size = 0

                with open(temp_output_path, "wb") as f:
                    async for chunk in response.aiter_bytes(chunk_size=8192): # 8KB chunks
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        if progress_bar and total_size > 0:
                            done = int(50 * downloaded_size / total_size)
                            print(f"\r[{'=' * done}{' ' * (50 - done)}] {downloaded_size / (1024 * 1024):.2f}/{total_size / (1024 * 1024):.2f} MB", end="")
                if progress_bar and total_size > 0:
                    print("\nDownload complete.")
                
                # Move temp file to final path after successful download
                if temp_output_path.exists() and temp_output_path.stat().st_size > 0:
                    temp_output_path.rename(output_path)
                    print(f"Successfully downloaded and saved to {output_path}")
                    return True
                else:
                    print(f"Error: Temporary download file {temp_output_path} is empty or missing after download attempt.")
                    if temp_output_path.exists():
                        temp_output_path.unlink() # Clean up empty temp file
                    return False # Treat as failure

        except httpx.HTTPStatusError as e:
            print(f"HTTP error during download: {e.response.status_code} - {e.response.text}")
            if e.response.status_code in [403, 404, 401]: # Non-recoverable errors
                print("Non-recoverable HTTP error. Aborting download for this URL.")
                if temp_output_path.exists(): temp_output_path.unlink(missing_ok=True)
                return False
            # For other errors, retry
        except httpx.RequestError as e: # Includes ConnectTimeout, ReadTimeout, etc.
            print(f"Request error during download: {e}")
        except Exception as e:
            print(f"An unexpected error occurred during download: {e}")
            import traceback
            traceback.print_exc()

        if attempt < max_retries - 1:
            delay = base_delay * (2 ** attempt) # Exponential backoff
            print(f"Retrying in {delay} seconds...")
            await asyncio.sleep(delay)
        else:
            print(f"Failed to download {url} after {max_retries} attempts.")
            if temp_output_path.exists(): # Clean up failed partial download
                temp_output_path.unlink(missing_ok=True)
            return False
    return False # Ensure a boolean is always returned

def save_video_metadata(
    source_name: str,
    source_video_id: str, # ID from the API (Pexels ID, Pixabay ID)
    video_path: Path,
    description: str | None = None, # Changed from tags (list[str]) to description (str)
    keywords: list[str] | None = None, # New field for list of keywords
    raw_api_data: dict | None = None, # raw_api_data can now be None
    width: int | None = None,
    height: int | None = None,
    duration_override: float | None = None # If API provides duration, use it, else calculate
):
    """
    Saves video metadata to the database.
    Checks if the video (by source and source_id) already exists.
    Now handles separate 'description' and 'keywords'.
    """
    conn = metadata_db.get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT id, video_path, description, keywords FROM long_videos WHERE source = ? AND source_id = ?",
            (source_name, str(source_video_id))
        )
        existing_video = cursor.fetchone()

        # Prepare description and keywords strings for DB
        description_str = description.strip() if description and description.strip() else ""
        keywords_str = " ".join(sorted(list(set(k.strip().lower() for k in keywords if k.strip())))) if keywords else ""

        if existing_video:
            existing_db_id = existing_video["id"]
            existing_path_str = existing_video["video_path"]
            existing_desc_str = existing_video["description"]
            existing_keywords_str = existing_video["keywords"]
            print(f"Video {source_name}_{source_video_id} (DB ID: {existing_db_id}) already exists in DB.")

            path_updated = False
            if not Path(existing_path_str).exists() or (video_path and Path(existing_path_str) != video_path and video_path.exists()):
                 print(f"Updating video path for DB ID {existing_db_id} from '{existing_path_str}' to {video_path}")
                 cursor.execute("UPDATE long_videos SET video_path = ? WHERE id = ?", (str(video_path), existing_db_id))
                 path_updated = True
            elif not video_path.exists() and Path(existing_path_str).exists():
                video_path = Path(existing_path_str)
            
            desc_updated = False
            if description_str and description_str != existing_desc_str:
                print(f"Updating description for DB ID {existing_db_id} from '{existing_desc_str[:50]}...' to '{description_str[:50]}...'")
                cursor.execute("UPDATE long_videos SET description = ? WHERE id = ?", (description_str, existing_db_id))
                desc_updated = True
            
            keywords_updated = False
            if keywords_str and keywords_str != existing_keywords_str:
                print(f"Updating keywords for DB ID {existing_db_id} from '{existing_keywords_str[:50]}...' to '{keywords_str[:50]}...'")
                cursor.execute("UPDATE long_videos SET keywords = ? WHERE id = ?", (keywords_str, existing_db_id))
                keywords_updated = True
            
            # Update raw_api_metadata if new data is provided and different (simple check)
            api_meta_updated = False
            if raw_api_data:
                existing_raw_meta_str = cursor.execute("SELECT raw_api_metadata FROM long_videos WHERE id = ?", (existing_db_id,)).fetchone()
                new_api_data_json = json.dumps(raw_api_data)
                if existing_raw_meta_str and existing_raw_meta_str[0] != new_api_data_json:
                    print(f"Updating raw_api_metadata for DB ID {existing_db_id}.")
                    cursor.execute("UPDATE long_videos SET raw_api_metadata = ? WHERE id = ?", (new_api_data_json, existing_db_id))
                    api_meta_updated = True

            if path_updated or desc_updated or keywords_updated or api_meta_updated:
                conn.commit()
            return existing_db_id

        # If not existing, add new entry
        actual_duration_seconds = duration_override
        if actual_duration_seconds is None:
            if video_path.exists():
                actual_duration_seconds = utils.get_video_duration(str(video_path))
            if actual_duration_seconds is None:
                print(f"Warning: Could not determine duration for {video_path}. Setting to 0.")
                actual_duration_seconds = 0.0

        api_data_json = json.dumps(raw_api_data) if raw_api_data else "{}"

        print(f"Adding new video to DB: {source_name}_{source_video_id}, Path: {video_path}, Desc: '{description_str[:50]}...', Keywords: '{keywords_str[:50]}...'")
        try:
            # Use the add_long_video function from metadata_db which now handles these fields
            new_id = metadata_db.add_long_video(
                video_path=str(video_path),
                source=source_name,
                source_id=str(source_video_id),
                description=description_str,
                keywords=keywords if keywords else [], # Pass list to add_long_video
                raw_api_metadata=raw_api_data,
                width=width,
                height=height,
                duration_seconds=actual_duration_seconds,
                full_transcript_text="" # Initially empty
            )
            # conn.commit() # add_long_video handles its own commit
            if new_id:
                print(f"Successfully added {source_name}_{source_video_id} to DB with ID {new_id}.")
            return new_id
        except Exception as e:
            print(f"Error inserting video metadata for {source_name}_{source_video_id} via add_long_video: {e}")
            # conn.rollback() # add_long_video handles its own errors/rollback potentially
            # Fallback to check if inserted concurrently, similar to add_long_video's own logic
            existing_id_after_error = metadata_db.get_long_video_id(source=source_name, source_id=str(source_video_id))
            if existing_id_after_error:
                print(f"Video {source_name}_{source_video_id} was likely inserted concurrently. DB ID: {existing_id_after_error}")
                return existing_id_after_error
            return None

    except Exception as e:
        print(f"Database error in save_video_metadata: {e}")
        conn.rollback()
        return None
    finally:
        if conn:
            conn.close()


def read_keywords_from_file(keywords_file_path: Path | str | None) -> list[str]:
    """Reads keywords from a file, one keyword per line."""
    if not keywords_file_path:
        return []
    try:
        with open(keywords_file_path, 'r', encoding='utf-8') as f:
            keywords = [line.strip() for line in f if line.strip()]
        if not keywords:
            print(f"Warning: Keyword file {keywords_file_path} is empty or contains only whitespace.")
        return keywords
    except FileNotFoundError:
        print(f"Error: Keyword file not found: {keywords_file_path}")
        return []
    except Exception as e:
        print(f"Error reading keyword file {keywords_file_path}: {e}")
        return []

def get_video_download_path(source_name: str, source_video_id: str | int, original_filename_suffix: str = ".mp4") -> Path:
    """
    Generates a consistent download path for a video based on its source and ID.
    Example: data/long_videos/pexels_12345.mp4
    """
    # Sanitize original_filename_suffix if needed, ensure it starts with a dot
    if not original_filename_suffix.startswith('.'):
        original_filename_suffix = '.' + original_filename_suffix.split('.')[-1] # take last part if multiple dots

    filename = f"{source_name.lower()}_{str(source_video_id)}{original_filename_suffix}"
    return Path(video_config.LONG_VIDEOS_DIR) / filename

def check_if_video_exists_in_db(source_name: str, source_video_id: str | int) -> bool:
    """
    Checks if a video from a specific source and with a specific source ID already exists in the database.
    Returns True if exists, False otherwise.
    This is a synchronous function.
    """
    conn = None
    try:
        conn = metadata_db.get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT 1 FROM long_videos WHERE source = ? AND source_id = ?",
            (source_name, str(source_video_id)) # Ensure source_video_id is string for DB
        )
        exists = cursor.fetchone() is not None
        return exists
    except Exception as e:
        print(f"Error checking if video {source_name}_{str(source_video_id)} exists in DB: {e}")
        return False # Assume not exists on error to allow download attempt
    finally:
        if conn:
            conn.close() 