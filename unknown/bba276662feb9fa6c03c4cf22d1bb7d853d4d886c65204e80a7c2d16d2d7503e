{"2": {"inputs": {"lora_name": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.1, "model": ["14", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "3": {"inputs": {"value": 1024}, "class_type": "easy int", "_meta": {"title": "<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"value": 512}, "class_type": "easy int", "_meta": {"title": "Height"}}, "8": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "11": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "12": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "14": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "19": {"inputs": {"text": "Soft Low-Saturation Style ", "clip": ["8", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "20": {"inputs": {"conditioning_to": ["19", 0], "conditioning_from": ["21", 0]}, "class_type": "ConditioningConcat", "_meta": {"title": "Conditioning (Concat)"}}, "21": {"inputs": {"text": "\"prompt\": \n            \"visual_style\": \"- Creates introspective, oppressive emotional atmosphere, emphasizing characters' psychological struggles. Muted steel gray, washed taupe, and pale dusty cream tones dominate, with soft, diffused natural light and subtle film grain. The mood is melancholic and reflective, with textures of peeling paint, worn fabric, and rusted metal. Medium and close-up shots with gentle vignettes, slightly off-center framing, and layered depth emphasize characters' psychological states.\",\n            \"scene\": \"A modest living room scene with <PERSON> slumped on a faded sofa, her face half-turned away, as young J.D<PERSON> stands uncertainly in the doorway; generous head-room and foot-room, layered foreground-midground-background with an old lamp in the foreground, the sofa in the midground, and a window with rain streaks in the background.\",\n            \"camera\": \n              \"shot_distance\": \"medium\",\n              \"camera_angle\": \"eye-level\",\n              \"lens\": \"35mm lens for gentle perspective and focus on emotional nuance\"\n            ,\n            \"environment\": \n              \"setting\": \n                \"location\": \"modest family living room in a small Rust Belt town, showing wear and economic hardship\",\n                \"time_period\": \"late 1980s\",\n                \"time_of_day\": \"late afternoon\",\n                \"weather\": \"gentle rain outside, overcast sky visible through the window\",\n                \"mood\": \"oppressive, introspective, and quietly tense, underscoring emotional struggle\"\n              ,\n              \"visual_elements\": \n                \"lighting\": \"Soft, diffused natural light from a rain-streaked window; minimal harsh shadows, gentle vignetting for an introspective atmosphere\",\n                \"props\": \"Worn, patterned sofa; a faded family photo on the wall; peeling wallpaper; an old lamp casting a faint glow\",\n                \"architecture\": \"Simple, boxy mid-century American living room with visible signs of age and neglect\",\n                \"special_effects\": \"Subtle film grain and matte finish, moisture on window glass for depth and memory-like effect\"\n              \n            ,\n            \"elements\": [\n              \n                \"name\": \"Beverly Vance\",\n                \"description\": \"A weary woman in her 30s, slumped on the sofa, face marked by fatigue and distant worry, rendered in muted taupe and gray\",\n                \"emotion\": \n                  \"expression\": \"Downcast eyes, slouched posture\",\n                  \"state\": \"Exhaustion, emotional withdrawal\",\n                  \"intensity\": \"moderate\"\n                \n              ,\n              \n                \"name\": \"J.D. (child)\",\n                \"description\": \"Young boy (about 7) standing at the threshold, small hands clenched, looking toward Beverly with uncertainty\",\n                \"emotion\": \n                  \"expression\": \"Wide, searching eyes, tense stance\",\n                  \"state\": \"Anxiety, longing for connection\",\n                  \"intensity\": \"moderate\"\n                \n              \n            ]\n          \n        },", "clip": ["8", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Prompt Action"}}, "22": {"inputs": {"model": ["26", 0], "conditioning": ["21", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "23": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "24": {"inputs": {"noise_seed": 976973818834289}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "25": {"inputs": {"width": ["3", 0], "height": ["4", 0], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "26": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["3", 0], "height": ["4", 0], "model": ["2", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "28": {"inputs": {"samples": ["33", 0], "vae": ["34", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "32": {"inputs": {"scheduler": "normal", "steps": 10, "denoise": 1, "model": ["26", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "33": {"inputs": {"noise": ["24", 0], "guider": ["22", 0], "sampler": ["23", 0], "sigmas": ["32", 0], "latent_image": ["25", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "34": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "36": {"inputs": {"width": 1024, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "disabled", "image": ["28", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "37": {"inputs": {"upscale_model": ["38", 0], "image": ["36", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "38": {"inputs": {"model_name": "4x_NMKD-Siax_200k.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "39": {"inputs": {"tile_width": ["53", 0], "tile_height": ["53", 1], "image": ["55", 0]}, "class_type": "TTP_Image_Tile_Batch", "_meta": {"title": "🪐TTP_Image_Tile_Batch"}}, "40": {"inputs": {"image": ["39", 0]}, "class_type": "easy imageBatchToImageList", "_meta": {"title": "Image Batch To Image List"}}, "41": {"inputs": {"images": ["42", 0]}, "class_type": "ImageListToImageBatch", "_meta": {"title": "Image List to Image Batch"}}, "42": {"inputs": {"tile_size": 1024, "overlap": 64, "temporal_size": 64, "temporal_overlap": 8, "samples": ["44", 0], "vae": ["43", 0]}, "class_type": "VAEDecodeTiled", "_meta": {"title": "VAE Decode (Tiled)"}}, "43": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "44": {"inputs": {"noise": ["45", 0], "guider": ["47", 0], "sampler": ["49", 0], "sigmas": ["46", 0], "latent_image": ["50", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced(Upscale)"}}, "45": {"inputs": {"noise_seed": 316430325547060}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "46": {"inputs": {"scheduler": "simple", "steps": 4, "denoise": 0.3, "model": ["59", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "47": {"inputs": {"model": ["59", 0], "conditioning": ["48", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "48": {"inputs": {"guidance": 2.5, "conditioning": ["54", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "49": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "50": {"inputs": {"pixels": ["40", 0], "vae": ["43", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "53": {"inputs": {"width_factor": 2, "height_factor": 4, "overlap_rate": 0.1, "image": ["55", 0]}, "class_type": "TTP_Tile_image_size", "_meta": {"title": "🪐TTP_Tile_image_size"}}, "54": {"inputs": {"text": "high quality, detailed , hd, 8k , 4k , sharp, highly detailed", "clip": ["59", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "55": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 2, "image": ["37", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "Scale Image to Total Pixels"}}, "57": {"inputs": {"padding": 128, "tiles": ["41", 0], "positions": ["39", 1], "original_size": ["39", 2], "grid_size": ["39", 3]}, "class_type": "TTP_Image_Assy", "_meta": {"title": "🪐TTP_Image_Assy"}}, "58": {"inputs": {"filename_prefix": "ComfyUI", "images": ["57", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "59": {"inputs": {"lora_name": "Flux Dev模型4步出图lora_Flux Dev模型4步出图lora_Flux Dev 4-step.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["2", 0], "clip": ["8", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "60": {"inputs": {"images": ["28", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}}