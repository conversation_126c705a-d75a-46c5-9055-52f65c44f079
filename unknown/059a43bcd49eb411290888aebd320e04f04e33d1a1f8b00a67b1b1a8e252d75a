"""
modules/run_srl_predictor.py

Reads a JSON object {"sentence": "..."} from stdin,
runs AllenNLP’s SemanticRoleLabelerPredictor on it,
and writes the full JSON output to stdout.
"""

import sys
import json
import logging
import os
from allennlp.predictors.predictor import Predictor

# Use local cached SRL model archive to avoid repeated downloads
MODEL_PATH = "/Users/<USER>/models/srl-bert.tar.gz"

def setup_logger():
    logging.basicConfig(
        level=logging.ERROR,
        format="%(asctime)s [%(levelname)s] %(message)s",
        stream=sys.stderr
    )
    return logging.getLogger(__name__)

logger = setup_logger()

def load_predictor():
    try:
        # Ensure Hugging Face transformers uses offline cache only
        #os.environ["TRANSFORMERS_OFFLINE"] = "1"
        # Load local model with overrides for transformer files
        return Predictor.from_path(
            MODEL_PATH
            #overrides={
            #    "dataset_reader.token_indexers.tokens.model_name": "/Users/<USER>/models/bert-base-uncased",
            #    "model.text_field_embedder.tokens.model_name": "/Users/<USER>/models/bert-base-uncased"
            #}
        )
    except Exception as e:
        logger.error(f"Failed to load AllenNLP predictor from {MODEL_PATH}: {e}")
        sys.exit(1)

def read_input():
    raw = sys.stdin.read()
    if not raw.strip():
        logger.error("No input received on stdin. Expecting JSON with 'sentence'.")
        sys.exit(1)
    try:
        data = json.loads(raw)
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON input: {e}")
        sys.exit(1)
    sentence = data.get("sentence")
    if not sentence or not isinstance(sentence, str):
        logger.error("Input JSON must contain a non-empty string field 'sentence'.")
        sys.exit(1)
    return sentence

def main():
    predictor = load_predictor()
    sentence = read_input()

    try:
        srl_output = predictor.predict(sentence=sentence)
    except Exception as e:
        logger.error(f"Error during SRL prediction: {e}")
        sys.exit(1)

    # Emit the full predictor JSON to stdout
    sys.stdout.write(json.dumps(srl_output))

if __name__ == "__main__":
    main()