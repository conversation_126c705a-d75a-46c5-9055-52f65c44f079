from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import logging
from typing import List, Optional
from modules.utils import normalized_filename

logger = logging.getLogger(__name__)

from config import YOUTUBE_API_KEY, MAX_RESULTS

def search_videos(search_keywords: str, max_results: int = MAX_RESULTS) -> List[str]:
    """
    Search for YouTube videos using YouTube Data API v3.
    
    Args:
        search_keywords (str): Search query string
        
    Returns:
        List[str]: List of YouTube video URLs
    """
    try:
        # Keep original search_keywords for API search
        # but normalize for logging and debugging
        normalized_keywords = normalized_filename(search_keywords)
        logger.info(f"Searching YouTube for: {search_keywords} (normalized: {normalized_keywords})")
        
        youtube = build('youtube', 'v3', developerKey=YOUTUBE_API_KEY)
        
        # Use original search_keywords for the actual search
        request = youtube.search().list(
            q=search_keywords,
            part='id,snippet',
            type='video',
            maxResults=max_results,
            videoEmbeddable='true',  # Only return embeddable videos
            fields='items(id/videoId,snippet/title)',  # Only get fields we need
            relevanceLanguage='zh'  # Optimize for Chinese results
        )
        
        response = request.execute()
        
        # Extract video URLs from response
        video_urls = []
        for item in response.get('items', []):
            if item['id'].get('videoId'):
                video_id = item['id']['videoId']
                video_url = f'https://www.youtube.com/watch?v={video_id}'
                video_urls.append(video_url)
                # Use normalized title for logging
                normalized_title = normalized_filename(item['snippet']['title'])
                logger.debug(f"Found video: {item['snippet']['title']} "
                           f"(normalized: {normalized_title}) ({video_url})")
        
        logger.info(f"Found {len(video_urls)} videos matching the search criteria")
        return video_urls
        
    except HttpError as e:
        if e.resp.status == 403:
            logger.error("YouTube API quota exceeded or invalid API key")
        elif e.resp.status == 400:
            logger.error("Invalid request parameters")
        else:
            logger.error(f"YouTube API error: {e}")
        return []
        
    except Exception as e:
        logger.error(f"Unexpected error during video search: {e}")
        return []

def get_video_details(video_id: str) -> Optional[dict]:
    """
    Get detailed information about a specific video.
    
    Args:
        video_id (str): YouTube video ID
        
    Returns:
        Optional[dict]: Video details or None if error occurs
    """
    try:
        youtube = build('youtube', 'v3', developerKey=YOUTUBE_API_KEY)
        
        request = youtube.videos().list(
            part='snippet,contentDetails,statistics',
            id=video_id,
            fields='items(snippet(title,description),contentDetails(duration),statistics(viewCount))'
        )
        
        response = request.execute()
        
        if response['items']:
            return response['items'][0]
        return None
        
    except Exception as e:
        logger.error(f"Error fetching video details for {video_id}: {e}")
        return None 