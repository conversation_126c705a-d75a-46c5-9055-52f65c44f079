import json
import argparse
from typing import Dict, List, Set
import random
import os
from modules.gpt4_interface import call_gpt_json_response
from config import logger
import tiktoken

# 文件路径配置
AZURE_VOICES_PATH = 'setting/azure_chinese_voices.json'
DEFAULT_OUTPUT_DIR = '2-animation-drama/outputs'  # 添加默认输出目录变量

# 文件头部添加常量
MAX_TOKENS = 80000  # 最大token数限制

# 旁白和主角声音定义
NARRATOR_FEATURE = {
    'voice': {
        'voice': "zh-CN-YunfengNeural",
        'default_style': "default",
        'styles': {
            'Neutral': ('default', '1.0'),
            'Cheerful': ('cheerful', '0.8'),
            'Sad': ('depressed', '0.8'),
            'Angry': ('angry', '0.8'),
            'Nervous': ('fearful', '0.8')
        }
    }
}

MALE_LEAD_FEATURE = {
    'voice': {
        'voice': "zh-<PERSON><PERSON>-YunxiNeural",
        'default_style': "chat",
        'styles': {
            'Neutral': ('default', '1.0'),
            'Cheerful': ('cheerful', '0.8'),
            'Sad': ('sad', '0.8'),
            'Angry': ('angry', '0.8'),
            'Nervous': ('fearful', '0.8')
        }
    },
    'appearance': {
        "age": "25",
        "gender": "male", 
        "height": "180cm",
        "build": "Athletic and well-proportioned",
        "facial_features": {
            "eyes": "Sharp and determined dark eyes",
            "nose": "Straight, well-defined nose",
            "mouth": "Firm lips with a gentle curve",
            "face_shape": "Strong jawline with balanced features",
            "hair": "Short black hair with styled layers",
            "facial_hair": "Clean shaven"
        },
        "distinctive_features": "Confident posture and commanding presence",
        "attire_summary": "Modern, well-fitted casual wear with occasional formal attire"
    }
}

FEMALE_LEAD_FEATURE = {
    'voice': {
        'voice': "zh-CN-XiaoyiNeural",
        'default_style': "calm",
        'styles': {
            'Neutral': ('default', '1.0'),
            'Cheerful': ('cheerful', '0.8'),
            'Sad': ('sad', '0.8'),
            'Angry': ('angry', '0.8'),
            'Nervous': ('fearful', '0.8')
        }
    },
    'appearance': {
        "age": "23",
        "gender": "female",
        "height": "165cm",
        "build": "Graceful and elegant",
        "facial_features": {
            "eyes": "Bright, expressive eyes",
            "nose": "Delicate, well-proportioned nose",
            "mouth": "Naturally curved lips",
            "face_shape": "Oval face with soft features",
            "hair": "Long flowing dark hair",
            "facial_hair": "none"
        },
        "distinctive_features": "Natural grace and elegant bearing",
        "attire_summary": "Stylish modern clothing with a mix of casual and formal wear"
    }
}

# 添加主角默认外观设定
MALE_LEAD_APPEARANCE = {
    "age": "25",
    "gender": "male",
    "height": "180cm",
    "build": "Athletic and well-proportioned",
    "facial_features": {
        "eyes": "Sharp and determined dark eyes",
        "nose": "Straight, well-defined nose",
        "mouth": "Firm lips with a gentle curve",
        "face_shape": "Strong jawline with balanced features",
        "hair": "Short black hair with styled layers",
        "facial_hair": "Clean shaven"
    },
    "distinctive_features": "Confident posture and commanding presence",
    "attire_summary": "Modern, well-fitted casual wear with occasional formal attire"
}

FEMALE_LEAD_APPEARANCE = {
    "age": "23",
    "gender": "female",
    "height": "165cm",
    "build": "Graceful and elegant",
    "facial_features": {
        "eyes": "Bright, expressive eyes",
        "nose": "Delicate, well-proportioned nose",
        "mouth": "Naturally curved lips",
        "face_shape": "Oval face with soft features",
        "hair": "Long flowing dark hair",
        "facial_hair": "none"
    },
    "distinctive_features": "Natural grace and elegant bearing",
    "attire_summary": "Stylish modern clothing with a mix of casual and formal wear"
}

# 读取 azure_voices.json 并过滤掉已使用的声音
def load_available_voices():
    with open(AZURE_VOICES_PATH, 'r', encoding='utf-8') as f:
        voices_data = json.load(f)
    
    # 已使用的声音列表
    used_voices = {
        NARRATOR_FEATURE['voice']['voice'],
        MALE_LEAD_FEATURE['voice']['voice'],
        FEMALE_LEAD_FEATURE['voice']['voice']
    }
    
    # 过滤可用声音，并保留age信息
    available_voices = {
        'male': [
            {
                'voice': v['voice'],
                'default_style': v['default_style'],
                'age': v['age'],  # 添加age属性
                'styles': {
                    mood: style
                    for mood, style in v['mapping_styles'].items()
                }
            }
            for v in voices_data['male']
            if v['voice'] not in used_voices
        ],
        'female': [
            {
                'voice': v['voice'],
                'default_style': v['default_style'],
                'age': v['age'],  # 添加age属性
                'styles': {
                    mood: style
                    for mood, style in v['mapping_styles'].items()
                }
            }
            for v in voices_data['female']
            if v['voice'] not in used_voices
        ]
    }
    
    return available_voices

# 替换原有的 AVAILABLE_VOICES
AVAILABLE_VOICES = load_available_voices()

def get_voice_config(feature_data: Dict) -> Dict:
    """生成声音配置"""
    voice_data = feature_data['voice']
    return {
        "voice": voice_data['voice'],
        "default_style": voice_data['default_style'],
        "styles": {
            mood: style if isinstance(style, str) else style[0]
            for mood, style in voice_data['styles'].items()
        }
    }

def normalize_age_category(age_category: str) -> str:
    """标准化年龄类别为四种基本类型之一"""
    age_mapping = {
        'CHILD': 'Child',
        'YOUNG': 'Young',
        'ADULT': 'Adult',
        'SENIOR': 'Senior',
        'ELDER': 'Senior',
        'OLD': 'Senior',
        'TEENAGE': 'Young',
        'YOUTH': 'Young',
        'MIDDLE': 'Adult',
        'MIDDLE_AGED': 'Adult'
    }
    
    normalized = age_mapping.get(age_category.upper(), 'Adult')  # 默认为Adult
    return normalized

def is_voice_age_compatible(voice: Dict, age_category: str) -> bool:
    """检查声音年龄是否与角色年龄类别匹配"""
    voice_age = voice.get('age', 'Adult')  # 获取声音的年龄属性
    character_age = normalize_age_category(age_category)
    
    # 直接匹配
    if voice_age == character_age:
        return True
    
    # 年龄兼容性规则
    age_compatibility = {
        'Child': ['Young'],           # Child声音可以用于Young角色
        'Young': ['Child', 'Adult'],  # Young声音可以用于Child或Adult角色
        'Adult': ['Young', 'Senior'], # Adult声音可以用于Young或Senior角色
        'Senior': ['Adult']           # Senior声音可以用于Adult角色
    }
    
    # 检查是否有兼容的年龄类别
    compatible_ages = age_compatibility.get(voice_age, [])
    return character_age in compatible_ages

def assign_voice(character: str, gender: str, age_category: str, is_lead: bool, 
                used_voices: Set[str], is_top5: bool,
                top5_used_voices: Set[str]) -> Dict:
    """为角色分配声音，考虑年龄匹配"""
    if is_lead:
        voice_data = MALE_LEAD_FEATURE if gender == 'male' else FEMALE_LEAD_FEATURE
        return get_voice_config(voice_data)
    
    normalized_age = normalize_age_category(age_category)
    
    # 获取可用的声音列表
    if is_top5:
        # 首先尝试完全匹配年龄的声音
        available = [v for v in AVAILABLE_VOICES[gender] 
                    if v['voice'] not in top5_used_voices and
                    v['age'] == normalized_age]
        
        # 如果没有完全匹配的，尝试兼容的年龄
        if not available:
            available = [v for v in AVAILABLE_VOICES[gender] 
                        if v['voice'] not in top5_used_voices and
                        is_voice_age_compatible(v, normalized_age)]
        
        if available:
            voice_data = random.choice(available)
            top5_used_voices.add(voice_data['voice'])
            return {
                'voice': {
                    'voice': voice_data['voice'],
                    'default_style': voice_data['default_style'],
                    'styles': voice_data['styles']
                }
            }
    
    # 非top5角色或top5但没有可用声音时
    # 首先尝试完全匹配年龄的声音
    available = [v for v in AVAILABLE_VOICES[gender] 
                if v['voice'] not in used_voices 
                and v['voice'] not in top5_used_voices
                and v['age'] == normalized_age]
    
    # 如果没有完全匹配的，尝试兼容的年龄
    if not available:
        available = [v for v in AVAILABLE_VOICES[gender] 
                    if v['voice'] not in used_voices 
                    and v['voice'] not in top5_used_voices
                    and is_voice_age_compatible(v, normalized_age)]
    
    # 如果仍然没有可用的声音，使用任何可用的声音
    if not available:
        available = [v for v in AVAILABLE_VOICES[gender]
                    if v['voice'] not in used_voices 
                    and v['voice'] not in top5_used_voices]
    
    # 如果所有声音都被使用了，重置普通声音池
    if not available:
        available = [v for v in AVAILABLE_VOICES[gender]
                    if v['voice'] not in top5_used_voices]
        used_voices.clear()
    
    voice_data = random.choice(available)
    used_voices.add(voice_data['voice'])
    return {
        'voice': {
            'voice': voice_data['voice'],
            'default_style': voice_data['default_style'],
            'styles': voice_data['styles']
        }
    }

def create_character_mapping(characters_data: Dict, story_analysis: Dict = None) -> Dict:
    """创建角色到声音和外观的映射"""
    result = {
        "background": story_analysis.get("background", {}) if story_analysis else {},
        "narration": get_voice_config(NARRATOR_FEATURE),
        "characters": {}
    }
    
    # 获取角色列表并按对话数量排序
    characters = [(name, info) for name, info in characters_data.items()]
    characters.sort(key=lambda x: x[1].get('dialogue_count', 0), reverse=True)
    
    used_voices = set()
    top5_used_voices = set()
    
    # 处理每个角色
    for char_name, char_info in characters:
        try:
            is_lead = char_name == characters[0][0]  # 判断是否为主角
            is_top5 = characters.index((char_name, char_info)) < 5
            
            gender = char_info.get('gender', 'male').lower()
            age_category = char_info.get('age', 'Adult')
            
            # 分配声音
            voice_config = assign_voice(
                char_name, gender, age_category, is_lead, 
                used_voices, is_top5, top5_used_voices
            )
            
            # 获取外观信息
            appearance = None
            if story_analysis and "characters" in story_analysis:
                appearance = story_analysis["characters"].get(char_name, {}).get("appearance")
            
            # 使用默认外观（如果适用）
            if is_lead:
                appearance = appearance or (MALE_LEAD_APPEARANCE if gender == 'male' else FEMALE_LEAD_APPEARANCE)
            
            # 创建角色配置
            result["characters"][char_name] = {
                "voice": voice_config.get('voice', {}),
                "appearance": appearance or {},
                "aliases": char_info.get('aliases', []),
                "role": char_info.get('role', [])
            }
            
        except Exception as e:
            logger.error(f"处理角色 {char_name} 时出错: {str(e)}")
            continue
            
    return result

def get_output_path(input_path: str) -> str:
    """根据输入文件路径生成输出文件路径"""
    dir_name = os.path.dirname(input_path)
    base_name = os.path.basename(input_path)
    name_without_ext = os.path.splitext(base_name)[0]
    return os.path.join(dir_name, f"{name_without_ext}_assign.json")

def collect_characters_from_episodes(episodes_dir: str) -> Dict[str, Dict]:
    """从episodes目录中收集所有角色信息
    返回格式: {
        'character_name': {
            'dialogue_count': int,
            'gender': str,
            'age': str,
            'role': List[str],
            'aliases': List[str]
        }
    }
    """
    characters = {}
    
    # 遍历目录下所有json文件
    for root, _, files in os.walk(episodes_dir):
        for file in files:
            if not file.endswith('.json'):
                continue
                
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    episode = json.load(f)
                    
                # 从episode的c列表中提取角色信息
                for char in episode.get('c', []):
                    name = char.get('name')
                    if not name:
                        continue
                        
                    if name not in characters:
                        characters[name] = {
                            'dialogue_count': 0,
                            'gender': char.get('gender', 'male'),
                            'age': char.get('age', 'Adult'),
                            'role': char.get('role', []),
                            'aliases': char.get('aliases', [])
                        }
                    
                # 处理每个场景中的对话
                for scene in episode.get('s', []):
                    for dialogue in scene.get('d', []):
                        char_name = dialogue.get('c')
                        if not char_name:
                            continue
                            
                        # 检查是否是某个角色的别名
                        found = False
                        for name, info in characters.items():
                            if char_name == name or char_name in info['aliases']:
                                info['dialogue_count'] += 1
                                found = True
                                break
                                
                        if not found and char_name not in characters:
                            # 查找原始角色信息
                            original_char = next(
                                (c for c in episode.get('c', []) if c.get('name') == char_name),
                                None
                            )
                            if original_char:
                                characters[char_name] = {
                                    'dialogue_count': 1,
                                    'gender': original_char.get('gender', 'male'),
                                    'age': original_char.get('age', 'Adult'),
                                    'role': original_char.get('role', []),
                                    'aliases': original_char.get('aliases', [])
                                }
                            else:
                                characters[char_name] = {
                                    'dialogue_count': 1,
                                    'gender': 'male',
                                    'age': 'Adult',
                                    'role': [],
                                    'aliases': []
                                }
                        
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                
    return characters

def create_characters_json(characters: Dict[str, Dict], output_dir: str, episodes_dir_name: str) -> str:
    """创建角色列表JSON文件"""
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{episodes_dir_name}_characters.json")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(characters, f, ensure_ascii=False, indent=4)
    
    return output_path

def collect_story_text(episodes_dir: str, max_tokens: int = MAX_TOKENS) -> str:
    """收集故事文本"""
    tokenizer = tiktoken.get_encoding('cl100k_base')
    collected_text = []
    total_tokens = 0
    episode_count = 0
    
    # 获取所有json文件并按序号排序
    json_files = []
    try:
        for file in os.listdir(episodes_dir):
            if file.endswith('.json'):
                try:
                    # 从文件名中提取episode编号 (episode_XX.json)
                    episode_num = int(file.split('_')[1].split('.')[0])
                    json_files.append((episode_num, file))
                except (IndexError, ValueError):
                    continue
        
        # 按episode编号排序
        json_files.sort(key=lambda x: x[0])
        json_files = [f[1] for f in json_files]
        
        logger.debug(f"找到以下episode文件: {json_files}")
        
        # 收集文本直到达到token限制
        for file in json_files:
            file_path = os.path.join(episodes_dir, file)
            logger.debug(f"处理文件: {file_path}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    episode = json.load(f)
                
                # 提取标题和场景信息
                title = episode.get('t', '')
                episode_text = [f"第{episode.get('n', '')}集：{title}"] if title else []
                
                # 处理每个场景
                for scene in episode.get('s', []):
                    scene_text = []
                    
                    # 场景描述
                    if scene.get('i'):
                        scene_text.append(scene['i'])
                    if scene.get('nr'):
                        scene_text.append(scene['nr'])
                        
                    # 添加对话内容
                    for dialogue in scene.get('d', []):
                        if dialogue.get('t'):
                            speaker = dialogue.get('c', '')
                            mood = dialogue.get('m', '')
                            text = dialogue.get('t', '')
                            scene_text.append(f"{speaker}({mood}): {text}")
                    
                    if scene_text:
                        episode_text.append("\n".join(scene_text))
                
                if episode_text:
                    current_text = "\n\n".join(episode_text)
                    tokens = tokenizer.encode(current_text)
                    
                    if total_tokens + len(tokens) > max_tokens:
                        logger.debug(f"达到token限制。处理了{episode_count}个episodes，总token数：{total_tokens}")
                        break
                    
                    total_tokens += len(tokens)
                    collected_text.append(current_text)
                    episode_count += 1
                    logger.debug(f"成功处理episode {file}，当前token数：{total_tokens}")
                
            except Exception as e:
                logger.error(f"处理文件 {file} 时出错: {str(e)}")
                continue
                
    except Exception as e:
        logger.error(f"处理episodes目录时出错: {str(e)}")
    
    logger.debug(f"完成文本收集。共处理{episode_count}个episodes，总token数：{total_tokens}")
    return "\n\n".join(collected_text)

def analyze_story_characters(text: str) -> Dict:
    """使用GPT分析故事文本，提取角色描述和背景"""
    try:
        data = {
            "text": text,
        }
        
        logger.debug(f"准备发送角色分析请求，文本长度: {len(text)}, text: {text[:100]}...")
        
        try:
            response = call_gpt_json_response(
                "analyze_story_context",
                data
            )
        except Exception as e:
            logger.error(f"GPT调用失败: {str(e)}")
            return {"background": {}, "characters": {}}
        
        #logger.debug(f"收到GPT响应: {response}")
        
        if not response or not isinstance(response, dict):
            logger.warning("获取角色分析失败或响应格式错误")
            return {"background": {}, "characters": {}}
            
        # 提取背景信息
        background = {
            "time_period": response.get("time_period", ""),
            "location_setting": response.get("location_setting", ""),
            "cultural_style": response.get("cultural_style", ""),
            "clothing_style": response.get("clothing_style", ""),
            "architectural_style": response.get("architectural_style", ""),
            "technology_level": response.get("technology_level", ""),
            "overall_tone": response.get("overall_tone", "")
        }
        
        # 处理角色信息
        characters = {}
        for char in response.get("character_descriptions", []):
            char_id = char.get("id", "")
            if not char_id:
                continue
                
            # 获取角色的主要名称（优先使用中文名）
            char_name = char.get("names", {}).get("zh", "") or char.get("names", {}).get("en", "")
            if not char_name:
                continue
                
            # 提取角色外观信息
            appearance = char.get("appearance", {})
            if appearance:
                characters[char_name] = {
                    "appearance": {
                        "age": appearance.get("age", ""),
                        "gender": appearance.get("gender", ""),
                        "height": appearance.get("height", ""),
                        "build": appearance.get("build", ""),
                        "facial_features": {
                            "eyes": appearance.get("facial_features", {}).get("eyes", ""),
                            "nose": appearance.get("facial_features", {}).get("nose", ""),
                            "mouth": appearance.get("facial_features", {}).get("mouth", ""),
                            "face_shape": appearance.get("facial_features", {}).get("face_shape", ""),
                            "hair": appearance.get("facial_features", {}).get("hair", ""),
                            "facial_hair": appearance.get("facial_features", {}).get("facial_hair", "")
                        },
                        "distinctive_features": appearance.get("distinctive_features", ""),
                        "attire_summary": appearance.get("attire_summary", "")
                    }
                }
        
        return {
            "background": background,
            "characters": characters
        }
        
    except Exception as e:
        logger.error(f"分析故事角色时出错: {str(e)}", exc_info=True)
        return {"background": {}, "characters": {}}

def main():
    parser = argparse.ArgumentParser(description='为角色分配声音')
    parser.add_argument('--characters', help='包含角色信息的JSON文件路径')
    parser.add_argument('--episodes_dir', help='包含剧集文件的目录路径')
    parser.add_argument('--output', help='输出文件路径（可选，默认为outputs目录）')
    args = parser.parse_args()
    
    if not args.characters and not args.episodes_dir:
        print("错误：必须提供 --characters 或 --episodes_dir 参数之一")
        return
        
    try:
        story_analysis = None
        if args.episodes_dir:
            logger.info(f"开始处理episodes目录: {args.episodes_dir}")
            
            # 从episodes目录生成角色列表
            characters = collect_characters_from_episodes(args.episodes_dir)
            if not characters:
                logger.error("未能从episodes中收集到角色信息")
                return
                
            # 收集故事文本并分析
            story_text = collect_story_text(args.episodes_dir)
            if story_text:
                logger.info(f"收集到故事文本，长度: {len(story_text)}")
                story_analysis = analyze_story_characters(story_text)
                logger.info("完成故事分析")
            else:
                logger.warning("未能收集到故事文本")
            
            episodes_dir_name = os.path.basename(os.path.normpath(args.episodes_dir))
            output_dir = args.output if args.output else os.path.join(DEFAULT_OUTPUT_DIR, episodes_dir_name)
            os.makedirs(output_dir, exist_ok=True)
            
            characters_path = create_characters_json(characters, output_dir, episodes_dir_name)
            logger.info(f"已生成角色列表文件: {characters_path}")
            
            # 使用生成的角色列表创建映射
            with open(characters_path, 'r', encoding='utf-8') as f:
                characters_data = json.load(f)
        else:
            # 使用提供的角色文件
            with open(args.characters, 'r', encoding='utf-8') as f:
                characters_data = json.load(f)
        
        # 生成映射并保存
        if args.output:
            output_path = args.output
        else:
            # 使用默认输出路径
            if args.episodes_dir:
                episodes_dir_name = os.path.basename(os.path.normpath(args.episodes_dir))
                output_path = os.path.join(DEFAULT_OUTPUT_DIR, episodes_dir_name, f"{episodes_dir_name}_assign.json")
            else:
                output_path = get_output_path(args.characters)
        
        # 创建输出目录（如果不存在）
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 创建映射
        mapping = create_character_mapping(characters_data, story_analysis)
        
        # 保存结果
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=4)
        print(f"映射结果已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"处理过程中出错: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main() 