import yt_dlp
import logging
from pathlib import Path
from typing import List
import json
from config import COOKIE_FILE, MAX_RESULTS, DOWNLOAD_DIR
import datetime

logger = logging.getLogger(__name__)

class VideoLogger:
    """Custom logger for yt-dlp"""
    def debug(self, msg):
        if not msg.startswith('[debug] '):
            logger.debug(msg)
    
    def info(self, msg):
        logger.info(msg)
    
    def warning(self, msg):
        logger.warning(msg)
    
    def error(self, msg):
        logger.error(msg)
        
def download_videos(urls: List[str], search_term: str, output_dir: str = str(DOWNLOAD_DIR), cookies_file: str = str(COOKIE_FILE)) -> List[str]:
    """
    Download videos from YouTube URLs using yt-dlp.
    
    Args:
        urls: List of YouTube URLs
        search_term: The search term used to find these videos
        output_dir: Directory to save downloaded videos
        cookies_file: Path to cookies file
    """
    # Create theme-specific directory
    base_output_path = Path(output_dir)
    theme_dir = base_output_path / search_term
    theme_dir.mkdir(parents=True, exist_ok=True)
    
    # Create JSON filename in theme directory
    json_filename = theme_dir / f"{search_term}.json"
    
    # 确保 cookies 文件存在并且可用
    cookies_path = Path(cookies_file)
    if not cookies_path.exists():
        logger.error(f"Cookies file not found at: {cookies_file}")
        raise FileNotFoundError(f"Cookies file not found at: {cookies_file}")
    
    # Load existing downloads if JSON exists
    existing_downloads = []
    if json_filename.exists():
        try:
            with open(json_filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                existing_downloads = data.get('videos', [])
                # Check if we already have enough videos
                valid_videos = [v for v in existing_downloads 
                              if Path(v['filepath']).exists()]
                if len(valid_videos) >= MAX_RESULTS:
                    logger.info(f"Already have {len(valid_videos)} videos for '{search_term}', skipping download")
                    return [v['filepath'] for v in valid_videos[:MAX_RESULTS]]
        except Exception as e:
            logger.error(f"Error reading existing JSON file: {e}")
    
    ydl_opts = {
        'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',  # Prefer MP4 format
        'outtmpl': str(theme_dir / '%(title)s.%(ext)s'),
        'merge_output_format': 'mp4',
        'postprocessors': [{
            'key': 'FFmpegVideoConvertor',
            'preferedformat': 'mp4',
        }],
        'keepvideo': False,
        'quiet': False,
        'verbose': True,
        'logger': VideoLogger(),
        'cookiefile': str(cookies_path),
    }
    
    downloaded_files = []
    new_downloads = []
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            for url in urls:
                # Skip if we already have enough videos
                if len(downloaded_files) >= MAX_RESULTS:
                    break
                    
                # 检查 URL 是否已经成功下载过
                existing_video = next((d for d in existing_downloads 
                    if d['url'] == url and Path(d['filepath']).exists()), None)
                if existing_video:
                    logger.info(f"Video already downloaded: {existing_video['title']}")
                    downloaded_files.append(existing_video['filepath'])
                    continue

                try:
                    # First extract info without downloading
                    logger.info(f"Extracting info for: {url}")
                    info = ydl.extract_info(url, download=False)
                    
                    if info:
                        # Log sanitized video info
                        sanitized_info = ydl.sanitize_info(info)
                        #logger.debug(f"Video info: {json.dumps(sanitized_info, indent=2)}")
                        
                        # Save info to temporary file and download using it
                        # info_file = theme_dir / f"{sanitized_info['id']}.info.json"
                        # with open(info_file, 'w', encoding='utf-8') as f:
                        #     json.dump(sanitized_info, f, indent=2)
                        
                        # Download video
                        logger.info(f"Downloading: {sanitized_info['title']}")
                        error_code = ydl.download([url])
                        
                        if error_code == 0:
                            filename = ydl.prepare_filename(info)
                            video_path = Path(filename)
                            if video_path.exists():
                                downloaded_files.append(str(video_path))
                                
                                # Add to new downloads list
                                video_data = {
                                    'url': url,
                                    'filepath': str(video_path),
                                    'title': sanitized_info['title'],
                                    'video_info': sanitized_info
                                }
                                new_downloads.append(video_data)
                                
                                logger.info(f"Successfully downloaded: {video_path.name}")
                            else:
                                logger.warning(f"Download seemed successful but file not found: {video_path}")
                        else:
                            logger.error(f"Download failed with error code {error_code}")
                    
                except Exception as e:
                    logger.error(f"Error processing {url}: {str(e)}")
                    continue
                    
        # Update JSON file with all downloads
        if new_downloads:
            all_downloads = existing_downloads + new_downloads
            json_data = {
                'search_term': search_term,
                'total_videos': len(all_downloads),
                'last_updated': str(datetime.datetime.now()),
                'videos': all_downloads
            }
            
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
                
    except Exception as e:
        logger.error(f"Fatal error in download process: {str(e)}")
    
    return downloaded_files

def test_youtube_access():
    """Test YouTube access and cookie availability."""
    logger.info("Testing YouTube access...")
    
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # 一个流行的视频作为测试
    
    ydl_opts = {
        'format': 'worst',  # 使用最低质量以快速测试
        'logger': VideoLogger(),
        'verbose': True,
        'extract_flat': True,
        'quiet': False,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(test_url, download=False)
            if info:
                logger.info("Successfully accessed YouTube!")
                return True
    except Exception as e:
        logger.error(f"YouTube access test failed: {str(e)}")
    return False

if __name__ == "__main__":
    # 运行测试
    test_youtube_access() 