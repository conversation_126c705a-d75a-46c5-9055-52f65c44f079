#!/bin/bash
#set -e  # 如果你希望任何一步失败都立刻退出，可以取消注释

# --- 配置：全部本地 ---
LOCAL_BASE_DIR="/Users/<USER>/ai-video/data"
LOCAL_LOG_DIR="$LOCAL_BASE_DIR/logs"
LOCAL_COOKIE_FILE="$LOCAL_BASE_DIR/settings/cookies.txt"
LOCAL_CODE_DIR="/Users/<USER>/ai-video/source-video-clips"

# Conda 环境名（如需要激活）
CONDA_ENV="base"

# 检查参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

# 绝对路径和主题名
WORK_DIR=$(realpath "${1%/}")
THEME_NAME=$(basename "$WORK_DIR" | sed 's/\\//g')
TOPIC_DIR="$WORK_DIR"

# 调试信息
echo "Debug: WORK_DIR = $WORK_DIR"
echo "Debug: THEME_NAME = $THEME_NAME"

# 验证工作目录存在
if [ ! -d "$WORK_DIR" ]; then
    echo "Error: Directory '$WORK_DIR' does not exist."
    exit 1
fi

# 验证必需文件
if [ ! -f "$TOPIC_DIR/urls.txt" ] || [ ! -f "$TOPIC_DIR/www.youtube.com_cookies.txt" ]; then
    echo "Error: Required files (urls.txt and www.youtube.com_cookies.txt) not found in $TOPIC_DIR"
    exit 1
fi

# 更新全局 cookies 文件
echo "Updating central cookie file at $LOCAL_COOKIE_FILE..."
mkdir -p "$(dirname "$LOCAL_COOKIE_FILE")"
cp "$TOPIC_DIR/www.youtube.com_cookies.txt" "$LOCAL_COOKIE_FILE"

# 确保日志目录存在
mkdir -p "$LOCAL_LOG_DIR"

# 时间戳
TIMESTAMP=$(date '+%Y%m%d_%H%M')

# 如果需要激活 conda 环境，可在这里插入：
# source ~/miniconda3/etc/profile.d/conda.sh
# conda activate "$CONDA_ENV"

# 执行脚本（如不需要 tsp，直接用 python）
echo "Executing video_clipper.py..."
ts python "$LOCAL_CODE_DIR/video_clipper.py" \
    --theme "$THEME_NAME" \
    --urls "$TOPIC_DIR/urls.txt" \
    --exec \
    > "$LOCAL_LOG_DIR/log_${THEME_NAME}_$TIMESTAMP.log" 2>&1

echo "Process queued; logs → $LOCAL_LOG_DIR/log_${THEME_NAME}_$TIMESTAMP.log"