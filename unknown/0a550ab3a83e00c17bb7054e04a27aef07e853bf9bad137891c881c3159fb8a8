import os
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Optional

from moviepy.editor import VideoFileClip
from scenedetect import detect, ContentDetector, split_video_ffmpeg
from scenedetect import open_video, SceneManager, FrameTimecode

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def extract_audio(input_video: Path, output_audio: Path) -> None:
    """
    从输入视频中提取音频轨道。

    Args:
        input_video (Path): 输入视频文件路径
        output_audio (Path): 输出音频文件路径
    """
    logging.info("正在从 %s 提取音频", input_video)
    try:
        video = VideoFileClip(str(input_video))
        audio = video.audio
        if audio is None:
            logging.warning("视频中没有音频轨道")
            return
        
        audio.write_audiofile(str(output_audio))
        logging.info("音频已提取到 %s", output_audio)
        
        # 清理资源
        audio.close()
        video.close()
    except Exception as e:
        logging.error("音频提取失败: %s", str(e))
        raise RuntimeError("音频提取失败") from e

def detect_and_split_scenes(
    input_video: Path,
    output_dir: Path,
    threshold: float = 30.0,
    min_scene_len: int = 15
) -> List[Path]:
    """
    使用 PySceneDetect 检测和分割场景。

    Args:
        input_video (Path): 输入视频文件路径
        output_dir (Path): 输出目录路径
        threshold (float): 场景检测阈值
        min_scene_len (int): 最小场景长度（秒）

    Returns:
        List[Path]: 分割后的视频片段路径列表
    """
    logging.info("正在检测和分割场景")
    video = None
    try:
        # 初始化 PySceneDetect
        video = open_video(str(input_video))
        scene_manager = SceneManager()
        scene_manager.add_detector(ContentDetector(threshold=threshold))

        # 检测场景
        scene_manager.detect_scenes(video=video)
        scene_list = scene_manager.get_scene_list()
        
        if not scene_list:
            logging.warning("未检测到场景分割点")
            return []
            
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 分割视频
        clip_files = []
        for i, (start, end) in enumerate(scene_list, start=1):
            clip_path = output_dir / f"clip_{i:03d}.mp4"
            start_sec = start.get_seconds()
            end_sec = end.get_seconds()
            duration = end_sec - start_sec
            
            if duration < min_scene_len:
                continue
                
            cmd = [
                "ffmpeg",
                "-i", str(input_video),
                "-ss", str(start_sec),
                "-t", str(duration),
                "-c", "copy",
                str(clip_path)
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            clip_files.append(clip_path)
            logging.info(f"已生成片段 {i}: {clip_path}")
            
        return clip_files
        
    except Exception as e:
        logging.error("场景检测和分割失败: %s", str(e))
        raise RuntimeError("场景检测和分割失败") from e
    finally:
        pass

def process_video(
    input_video_path: str,
    output_dir: str,
    remover_script_path: str,
    subtitle_regions: Optional[List[str]] = None,
    threshold: float = 30.0,
    min_scene_len: int = 15
) -> Dict[str, Path]:
    """
    处理视频：提取音频、移除字幕并分割场景。

    Args:
        input_video_path (str): 输入视频路径
        output_dir (str): 输出目录
        remover_script_path (str): VSR 脚本路径
        subtitle_regions (List[str], optional): 字幕区域列表
        threshold (float): 场景检测阈值
        min_scene_len (int): 最小场景长度（秒）

    Returns:
        Dict[str, Path]: 处理结果路径字典
    """
    input_video = Path(input_video_path).resolve()
    output_directory = Path(output_dir).resolve()
    output_directory.mkdir(parents=True, exist_ok=True)

    # Define output paths
    audio_path = output_directory / "extracted_audio.mp3"
    subtitle_removed_video = output_directory / "subtitle_removed_video.mp4"
    clips_dir = output_directory / "clips"

    # Step 1: Extract Audio
    extract_audio(input_video, audio_path)

    # Step 2: Remove Subtitles
    remove_subtitles(input_video, subtitle_removed_video, Path(remover_script_path), subtitle_regions)

    # Step 3: Split Video into Clips
    scene_list = detect_scenes(
        subtitle_removed_video,
        threshold=threshold,
        min_scene_len=min_scene_len
    )
    if not scene_list:
        # This case should ideally not be hit if detect_scenes is robust as modified.
        # If it is hit, it means detect_scenes failed to produce even a fallback.
        logging.error(f"process_video: detect_scenes returned an empty list for {subtitle_removed_video}, even after attempting fallback. Cannot proceed with splitting.")
        # split_video_into_clips will handle an empty scene_list gracefully (by doing nothing).
    
    split_video_into_clips(subtitle_removed_video, scene_list, clips_dir)

    return {
        "audio_path": audio_path,
        "subtitle_removed_video": subtitle_removed_video,
        "clips_dir": clips_dir
    }

def remove_subtitles(input_video: Path, output_video: Path) -> None:
    """
    使用 video-subtitle-remover 移除视频中的硬字幕。

    Args:
        input_video (Path): 输入视频文件路径
        output_video (Path): 输出视频文件路径
        remover_script (Path): VSR 脚本路径
        regions (List[str], optional): 字幕区域列表，格式为 'x,y,width,height'
    """
    logging.info("正在移除字幕")
    
    # 首先尝试移除软字幕
    temp_video = input_video.parent / f"{input_video.stem}_temp{input_video.suffix}"
    cmd_soft = [
        "ffmpeg", "-i", str(input_video),
        "-c", "copy", "-sn",
        str(temp_video)
    ]
    
    try:
        # 移除软字幕
        subprocess.run(cmd_soft, check=True, capture_output=True)
        logging.info("软字幕已移除")
        
        # 使用 VSR 移除硬字幕
        cmd_hard = [
            "python",
            str(remover_script),
            "--input", str(temp_video),
            "--output", str(output_video)
        ]
        
        # 如果指定了字幕区域，添加到命令中
        if regions:
            for region in regions:
                cmd_hard.extend(["--region", region])
                
        subprocess.run(cmd_hard, check=True, capture_output=True)
        logging.info("硬字幕已移除")
        
    except subprocess.CalledProcessError as e:
        logging.error("字幕移除失败: %s", e.stderr.decode())
        raise RuntimeError("字幕移除失败") from e
    finally:
        # 清理临时文件
        temp_video.unlink(missing_ok=True)

def detect_scenes(video_path: Path, threshold: float = 30.0, min_scene_len: int = 15) -> List[tuple]:
    """
    使用 PySceneDetect 检测场景。
    注意：此函数与上面的 `detect_and_split_scenes` 不同，它只返回场景列表。
    此函数由 `process_video` 调用。
    """
    logging.info("正在检测场景 (in detect_scenes function)")
    video = None
    try:
        video = open_video(str(video_path))
        scene_manager = SceneManager()
        scene_manager.add_detector(ContentDetector(threshold=threshold))

        scene_manager.detect_scenes(video=video)
        scene_list = scene_manager.get_scene_list()
        
        if not scene_list:
            logging.warning(f"detect_scenes: No cuts found in {video_path}. Creating a single scene for the whole video.")
            if video.framerate > 0 and video.duration.get_frames() > 0:
                start_time = FrameTimecode(time_in_seconds=0, framerate=video.framerate)
                end_time = FrameTimecode(time_in_seconds=video.duration.get_seconds(), framerate=video.framerate)
                scene_list = [(start_time, end_time)]
            else:
                logging.error(f"detect_scenes: Could not determine duration/framerate for {video_path} to create fallback scene.")
                return []

        filtered_scenes = []
        for start, end in scene_list:
            duration = end.get_seconds() - start.get_seconds()
            if duration >= min_scene_len:
                filtered_scenes.append((start, end))
                
        logging.info(f"检测到 {len(filtered_scenes)} 个有效场景")
        return filtered_scenes
    except Exception as e:
        logging.error("场景检测失败 (in detect_scenes function): %s", str(e))
        if video is not None and hasattr(video, 'close') and callable(getattr(video, 'close')):
            try:
                video.close()
            except Exception as e_close_exc:
                logging.warning(f"Error closing video in detect_scenes exception block: {e_close_exc}")
        raise RuntimeError("场景检测失败") from e
    finally:
        if video is not None and hasattr(video, 'close') and callable(getattr(video, 'close')):
            try:
                video.close()
            except Exception as e_close_final:
                logging.warning(f"Error closing video in detect_scenes finally block: {e_close_final}")

def split_video_into_clips(video_path: Path, scene_list: List[tuple], output_dir: Path) -> List[Path]:
    """
    根据场景列表分割视频。

    Args:
        video_path (Path): 输入视频文件路径
        scene_list (List[tuple]): 场景列表，每个场景包含 (start_time, end_time)
        output_dir (Path): 输出目录路径

    Returns:
        List[Path]: 分割后的视频片段路径列表
    """
    logging.info("正在分割视频")
    output_dir.mkdir(parents=True, exist_ok=True)

    clip_files = []
    for i, (start, end) in enumerate(scene_list, start=1):
        clip_path = output_dir / f"clip_{i:03d}.mp4"
        start_sec = start.get_seconds()
        end_sec = end.get_seconds()
        duration = end_sec - start_sec
        
        cmd = [
            "ffmpeg",
            "-i", str(video_path),
            "-ss", str(start_sec),
            "-t", str(duration),
            "-c", "copy",
            str(clip_path)
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        clip_files.append(clip_path)
        logging.info(f"已生成片段 {i}: {clip_path}")
        
    return clip_files

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="处理视频：提取音频并分割成片段")
    parser.add_argument("input_video", help="输入视频文件路径")
    parser.add_argument(
        "--threshold",
        type=float,
        default=30.0,
        help="场景检测阈值（默认：30.0）"
    )
    parser.add_argument(
        "--min-scene-len",
        type=int,
        default=15,
        help="最小场景长度（秒）（默认：15）"
    )
    
    args = parser.parse_args()
    
    try:
        results = process_video(
            args.input_video,
            args.threshold,
            args.min_scene_len
        )
        
        logging.info("视频处理完成")
        logging.info("处理结果：")
        for key, path in results.items():
            logging.info(" - %s: %s", key, path)
            
    except Exception as e:
        logging.error("处理过程中出错: %s", str(e))
        exit(1) 