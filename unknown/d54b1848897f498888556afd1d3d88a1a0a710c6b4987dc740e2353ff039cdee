#!/bin/bash

# 设置日志文件
TIMESTAMP=$(date '+%Y%m%d%H%M')
mkdir -p logs  # 创建 logs 目录
LOG_FILE="logs/generate_novel_video_${TIMESTAMP}.log"
touch "$LOG_FILE"

# Logging functions
log_info() {
    echo "[shell:INFO] $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[shell:ERROR] $1" | tee -a "$LOG_FILE"
}

# Run command function
run_command() {
    log_info "Running command: $1"
    eval "$1"
    if [ $? -ne 0 ]; then
        log_error "Error running command: $1"
        exit 1
    fi
}

# 获取输入文件路径
INPUT_FILE="$1"

# 检查文件是否存在的通用函数
check_file_exists() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        return 0
    else
        return 1
    fi
}

# 生成图片提示
generate_prompts() {
    local output_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_prompts.json"
    local characters_file="${OUTPUT_DIR}/${PREFIX}/${PREFIX}_characters_assign.json"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_file"; then
        log_info "开始生成图片提示..."
        run_command "python generate_image_prompts.py \
            --config \"$characters_file\" \
            --episode \"$EPISODE_FILE\" \
            --prompt image \
            --output \"$output_file\""
    else
        log_info "提示文件已存在，跳过生成"
    fi
}

# 合成音频
synthesize_audio() {
    local characters_file="${OUTPUT_DIR}/${PREFIX}/${PREFIX}_characters_assign.json"
    local output_audio="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.wav"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_audio"; then
        log_info "开始合成音频..."
        run_command "python episode_tts.py \
            --episode \"$EPISODE_FILE\" \
            --voice \"$characters_file\" \
            --output \"$output_audio\""
    else
        log_info "音频文件已存在，跳过合成"
    fi
}

# 图片转视频
convert_images_to_video() {
    local timing_json="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_timing.json"
    local output_video="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_image.mp4"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_video"; then
        log_info "开始转换图片为视频..."
        run_command "python images2video.py \"$timing_json\""
    else
        log_info "视频文件已存在，跳过转换"
    fi
}

# 合并声音和音乐
merge_audio() {
    local video_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_image.mp4"
    local voice_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.wav"
    local output_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_voice.mp4"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_file"; then
        log_info "开始合并音频..."
        run_command "python merge_voice_music.py \
            --video \"$video_file\" \
            --voice \"$voice_file\" \
            --music \"$MUSIC_FILE\" \
            --music-volume 0.15"
    else
        log_info "音频已合并，跳过处理"
    fi
}

# 生成字幕
generate_subtitles() {
    local audio_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.wav"
    local json_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_prompts.json"
    local output_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.srt"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_file"; then
        log_info "开始生成字幕..."
        run_command "python generate_subtitles.py --audio \"$audio_file\" --json \"$json_file\""
    else
        log_info "字幕文件已存在，跳过生成"
    fi
}

# 嵌入字幕
embed_subtitles() {
    local video_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_voice.mp4"
    local srt_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.srt"
    local output_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.mp4"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_file"; then
        log_info "开始嵌入字幕..."
        run_command "python embed_subtitles.py --video \"$video_file\" --sub \"$srt_file\""
    else
        log_info "字幕已嵌入，跳过处理"
    fi
}

# 生成视频片段
generate_video_clip() {
    local json_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_timing.json"
    local output_file="${VIDEO_DIR}/scene_1.mp4"
    
    if [ "$FORCE_MODE" = true ] || ! check_file_exists "$output_file"; then
        log_info "开始生成视频片段..."
        run_command "python video_generator.py \
            --json \"$json_file\" \
            --scene_number 1 \
            --output \"$output_file\""
    fi
}

# 替换视频片段
replace_video_clip() {
    local json_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_timing.json"
    local video_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_voice.mp4"
    local scene_number="1"
    local clip_file="${BASE_OUTPUT_DIR}/video/scene_${scene_number}.mp4"
    local srt_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}.srt"
    local final_output="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_animated.mp4"
    
    log_info "开始替换视频片段..."
    run_command "python replace_video_clip.py --json \"$json_file\" --video \"$video_file\" --scene_number \"$scene_number\" --clip \"$clip_file\" --output \"${BASE_OUTPUT_DIR}/${EPISODE_NAME}_video.mp4\"" 
    
    log_info "重新嵌入字幕到替换后的视频..."
    run_command "python embed_subtitles.py --video \"${BASE_OUTPUT_DIR}/${EPISODE_NAME}_video.mp4\" --sub \"$srt_file\" --output \"$final_output\""
}

# 修改 check_all_images_exist 函数
check_all_images_exist() {
    local json_file="$1"
    
    if [ ! -f "$json_file" ]; then
        log_error "JSON 文件不存在: $json_file"
        return 1
    fi
    
    local missing_images=0
    
    # 直接遍历场景数组
    while IFS= read -r scene_data; do
        # 解析场景编号
        local scene_number=$(echo "$scene_data" | jq -r '.scene_number // empty')
        
        if [ -z "$scene_number" ]; then
            log_error "无法获取场景编号"
            continue
        fi
        
        # 检查是否存在 image 字段
        local image_path=$(echo "$scene_data" | jq -r '.image // empty')
        if [ -z "$image_path" ]; then
            log_error "场景 $scene_number 缺少 image 字段"
            missing_images=1
            continue
        fi
        
        # 检查图片文件是否存在
        if [ ! -f "$image_path" ]; then
            log_error "场景 $scene_number 的图片文件不存在: $image_path"
            missing_images=1
        fi
    done < <(jq -c '.scenes[]' "$json_file")
    
    if [ $missing_images -eq 0 ]; then
        log_info "所有场景的图片都已就绪"
        return 0
    else
        log_error "存在未就绪的图片"
        return 1
    fi
}

# 修改 generate_images 函数
generate_images() {
    local prompts_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_prompts.json"
    local output_file="${BASE_OUTPUT_DIR}/${EPISODE_NAME}_prompts_images.json"
    local max_attempts=30
    local current_attempt=0
    
    while true; do
        # 检查图片状态
        if check_all_images_exist "$output_file"; then
            log_info "所有图片已成功生成"
            return 0
        fi
        
        # 尝试生成缺失的图片
        if [ $current_attempt -ge $max_attempts ]; then
            log_error "达到最大重试次数 ($max_attempts)，退出"
            return 1
        fi
        
        ((current_attempt++))
        log_info "开始第 $current_attempt 次尝试生成图片..."
        run_command "python generate_images.py --prompts \"$prompts_file\""
        sleep 10
    done
}

# 显示帮助信息
show_help() {
    echo "使用方法: $0 <episode_file> --novel <novel_name> [--output-dir <dir>] [--force] [functions...]"
    echo "选项:"
    echo "  --novel       指定小说名称"
    echo "  --output-dir  指定输出目录（默认: outputs）"
    echo "  --force       强制执行所有步骤，忽略已存在的文件���查"
    echo "可用的功能编号:"
    echo "  1) 生成图片提示 (generate_prompts)"
    echo "  2) 生成图片 (generate_images)"
    echo "  3) 合成音频 (synthesize_audio)"
    echo "  4) 图片转视频 (convert_images_to_video)"
    echo "  5) 合并音频 (merge_audio)"
    echo "  6) 生成字幕 (generate_subtitles)"
    echo "  7) 嵌入字幕 (embed_subtitles)"
    echo "  8) 生成视频片段 (generate_video_clip)"
    echo "  9) 替换视频片段 (replace_video_clip)"
    echo "使用 'all' 运行所有功能"
}

# 主函数
main() {
    log_info "接收到的所有参数: $@"
    log_info "参数个数: $#"
    
    # 初始化变量
    FORCE_MODE=false
    functions=()
    PREFIX=""
    OUTPUT_DIR="animation-drama/outputs"  # 添加默认输出目录
    
    # 首先获取输入文件
    if [ $# -lt 1 ]; then
        show_help
        exit 1
    fi

    # 首先处理帮助参数
    if [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi

    # 获取输入文件
    EPISODE_FILE="$1"
    EPISODE_PATH=$(dirname "$EPISODE_FILE")
    EPISODE_NAME=$(basename "$EPISODE_FILE" .json)
    
    # 设置基础资源目录
    ASSETS_DIR="assets"
    MUSIC_FILE="${ASSETS_DIR}/the-northern-halls.mp3"
    
    # 设置输出目录结构
    BASE_OUTPUT_DIR="${OUTPUT_DIR}/${PREFIX}/${EPISODE_NAME}"
    VIDEO_DIR="${BASE_OUTPUT_DIR}/video"
    mkdir -p "$BASE_OUTPUT_DIR" "$VIDEO_DIR"

    shift

    # 解析剩余参数
    while [ $# -gt 0 ]; do
        log_info "处理参数: '$1'"
        case "$1" in
            --novel)
                shift
                if [ $# -eq 0 ]; then
                    log_error "未指小说名称"
                    show_help
                    exit 1
                fi
                PREFIX="$1"
                log_info "小说名称参数: '$PREFIX'"
                ;;
            --output-dir)
                shift
                if [ $# -eq 0 ]; then
                    log_error "未指定输出目录"
                    show_help
                    exit 1
                fi
                OUTPUT_DIR="$1"
                log_info "输出目录参数: '$OUTPUT_DIR'"
                ;;
            --force)
                FORCE_MODE=true
                log_info "强制模式已启用"
                ;;
            all)
                functions=(1 2 3 4 5 6 7 8 9)
                log_info "设置执行所有功能"
                ;;
            [0-9]*)
                functions+=("$1")
                log_info "添加功能编号: $1"
                ;;
            *)
                log_error "无效的参数: '$1'"
                show_help
                exit 1
                ;;
        esac
        shift
    done

    # 验证必需参数
    if [ -z "$PREFIX" ]; then
        log_error "缺少必需的 --novel 参数"
        show_help
        exit 1
    fi

    if [ ${#functions[@]} -eq 0 ]; then
        log_error "未指定要执行的功能"
        show_help
        exit 1
    fi

    # 导出 FORCE_MODE 变量
    export FORCE_MODE

    log_info "开始处理文件: $EPISODE_FILE"
    log_info "使用小说名称: $PREFIX"
    log_info "输出目录: $BASE_OUTPUT_DIR"
    log_info "强制模式: $FORCE_MODE"
    log_info "要执行的功能: ${functions[*]}"

    # 执行功能
    for func in "${functions[@]}"; do
        case "$func" in
            1) generate_prompts ;;
            2) generate_images ;;
            3) synthesize_audio ;;
            4) convert_images_to_video ;;
            5) merge_audio ;;
            6) generate_subtitles ;;
            7) embed_subtitles ;;
            8) generate_video_clip ;;
            9) replace_video_clip ;;
            *) 
                log_error "无效的功能编号: $func"
                show_help
                exit 1
                ;;
        esac
    done

    log_info "所有处理完成"
}

main "$@" 