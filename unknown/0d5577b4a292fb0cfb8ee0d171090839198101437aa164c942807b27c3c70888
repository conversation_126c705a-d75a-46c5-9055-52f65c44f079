#!/usr/bin/env python3
# image_assignment.py - 为段落分配媒体资源（图片或视频片段）

import os
import numpy as np
import json
import math
import argparse
from collections import defaultdict
from typing import List, Dict, Any, Tuple, Optional
from ortools.linear_solver import pywraplp
import logging
from PIL import Image
import sys
import cv2
import random
import datetime
import traceback
import copy

from config import logger, VECTOR_STORE_DIR, get_image_metadata_path
from modules.vector_store import VectorStore
from modules.langchain_interface import generate_embedding
from modules.describe_images import generate_and_save_descriptions

# 设置向量存储路径
FAISS_INDEX_PATH = os.path.join(VECTOR_STORE_DIR, 'image_embeddings.index')
METADATA_PATH = os.path.join(VECTOR_STORE_DIR, 'metadata.json')

# 设置参数
SIM_THRESHOLD_PERCENTILE = 0  # 使用10%分位数作为动态阈值
NULL_REWARD = 0.2    # null 分配奖励
REPEAT_PENALTY = 1   # 重复使用惩罚系数
MIN_DISTANCE_BETWEEN_REUSE = 3  # 图片重复使用的最小间距
MAX_TOTAL_USES = 3   # 每张图片的最大使用次数
MIN_WORDS = 20     # 每段最小字数
MAX_WORDS = 40     # 每段最大字数
SIM_THRESHOLD = float('-inf')
SIM_THRESHOLD_FIRST = 0.6  # 第一轮图片分配的高相似度阈值

def get_valid_images(directory: str) -> List[str]:
    """
    获取目录下的有效图片文件路径列表（返回绝对路径）
    
    Args:
        directory: 图片目录路径
        
    Returns:
        List[str]: 有效图片文件的绝对路径列表
    """
    valid_images = []
    valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    
    try:
        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            if not os.path.isfile(filepath):
                continue
                
            # 检查文件扩展名
            ext = os.path.splitext(filename)[1].lower()
            if ext not in valid_extensions:
                continue
                
            # 尝试打开图片验证其有效性
            try:
                with Image.open(filepath) as img:
                    img.verify()  # 验证图片文件完整性
                # 统一返回绝对路径
                valid_images.append(os.path.abspath(filepath))
            except Exception as e:
                logger.warning(f"无效的图片文件 {filepath}: {str(e)}")
                
    except Exception as e:
        logger.error(f"处理目录 {directory} 时出错: {str(e)}")
        raise
        
    if not valid_images:
        logger.warning(f"在目录 {directory} 中未找到有效图片")
        
    return valid_images


def print_assignment_status(paragraphs: List[Dict], total_valid_images_count: Optional[int] = None, stage_name: Optional[str] = None) -> None:
    """
    打印每个段落的媒体分配状态,并汇总统计信息
    
    Args:
        paragraphs: 段落列表
        total_valid_images_count: 可用的总图片数量 (用于计算未分配图片)
        stage_name: 当前分配阶段的名称 (用于日志)
    """
    log_prefix = f"========== 段落媒体分配状态 ({stage_name if stage_name else 'N/A'}) =========="
    logger.debug(log_prefix)

    total_paras = len(paragraphs)
    assigned_paras_count = 0
    fully_assigned_paras_count = 0
    partially_assigned_paras_count = 0
    
    total_image_slots_filled = 0
    all_used_image_paths = set()

    for idx, para in enumerate(paragraphs):
        media = para.get('assigned_media', {})
        media_type = media.get('type', 'unassigned')
        num_images_needed = para.get('num_images_needed', 1)
        
        para_is_assigned_something = False
        para_is_fully_assigned = False
        para_is_partially_assigned = False

        log_msg = f"段落 {idx} (需{num_images_needed}): "

        if media_type == 'anchor':
            log_msg += "[anchor]"
            para_is_assigned_something = True
            para_is_fully_assigned = True # Anchors are considered 'full' for their purpose
        elif media_type == 'image':
            filepath = media.get('filepath')
            log_msg += f"[image] {filepath if filepath else 'unknown'}"
            if filepath:
                all_used_image_paths.add(filepath)
                total_image_slots_filled += 1
            para_is_assigned_something = True
            if num_images_needed == 1: # Assuming single image para needs 1
                 para_is_fully_assigned = True
            # else: if num_images_needed > 1 but assigned 'image', it's a mis-assignment or handled by other logic
        elif media_type == 'clip':
            filepath = media.get('filepath', 'unknown')
            start_time = media.get('start_time', 0)
            end_time = media.get('end_time', 0)
            log_msg += f"[clip] {filepath} ({start_time}s - {end_time}s)"
            # Clips don't use image slots from the image library in the same way
            para_is_assigned_something = True
            para_is_fully_assigned = True # Clips are considered 'full'
        elif media_type == 'multi_image' or media_type == 'multi_image_pending':
            filepaths = media.get('filepaths', [])
            num_assigned_in_para = len(filepaths)
            total_image_slots_filled += num_assigned_in_para
            for fp in filepaths:
                all_used_image_paths.add(fp)
            
            status_tag = "[multi_image]" if media_type == 'multi_image' else "[multi_image_pending]"
            if filepaths:
                filepath_str = os.path.basename(filepaths[0]) if filepaths else ""
                if num_assigned_in_para > 1:
                    filepath_str += f" 等{num_assigned_in_para}张 (共需{num_images_needed}张)"
                else:
                    filepath_str += f" (共需{num_images_needed}张)"
                log_msg += f"{status_tag} {filepath_str}"
            else:
                log_msg += f"{status_tag} 无有效图片 (共需{num_images_needed}张)"
            
            para_is_assigned_something = True # Even if 0 filepaths but type is multi_image*, it's an attempt
            if num_assigned_in_para >= num_images_needed:
                para_is_fully_assigned = True
            elif num_assigned_in_para > 0:
                para_is_partially_assigned = True
            # If num_assigned_in_para is 0 but type is multi_image*, it's assigned 'nothing' but still counts as an assigned para for this category.
            
        elif media_type == 'pending':
            needs_gen = media.get('needs_generated_image', False)
            img_gen_count = media.get('img_need_generate', 0)
            if needs_gen:
                log_msg += f"[pending] 待生成 ({img_gen_count} 张)"
            else:
                log_msg += "[pending] 待分配"
            # 'pending' type is not counted as 'assigned' for summary stats unless it's pending generation after partial multi_image
        elif media_type == 'null':
            log_msg += "[null] 无适用媒体"
            para_is_assigned_something = True # 'null' is an explicit assignment outcome
            # para_is_fully_assigned = True # Or false, depending on definition. Let's say false for 'fullness' metric.

        else: # unassigned or other
            log_msg += "[未分配]"
            
        logger.debug(log_msg)

        if para_is_assigned_something:
            assigned_paras_count += 1
        if para_is_fully_assigned:
            fully_assigned_paras_count +=1
        elif para_is_partially_assigned: # elif to avoid double counting
            partially_assigned_paras_count +=1

    logger.debug("--------------------------------------")
    logger.debug(f"分配统计 ({stage_name if stage_name else 'N/A'}):")
    logger.debug(f"  总段落数: {total_paras}")
    logger.debug(f"  已分配段落数 (有图片/视频/anchor/null): {assigned_paras_count}")
    logger.debug(f"  完全分配段落数: {fully_assigned_paras_count}")
    logger.debug(f"  部分分配段落数 (仅multi-image): {partially_assigned_paras_count}")
    
    unassigned_paras_count = total_paras - (fully_assigned_paras_count + partially_assigned_paras_count) 
    # This definition of unassigned might differ from paras with type 'pending' or 'unassigned'
    # For this summary, "unassigned" means not fully or partially satisfied with concrete media.
    # Let's refine this: unassigned_paras_count = total_paras - assigned_paras_count (where assigned_paras_count includes null/anchor)
    
    pending_or_unassigned_explicitly = 0
    for para in paragraphs:
        mt = para.get('assigned_media',{}).get('type')
        if mt is None or mt == 'pending' or mt == 'unassigned':
            pending_or_unassigned_explicitly +=1
    logger.debug(f"  待处理/未分配段落数 (状态为pending/unassigned/None): {pending_or_unassigned_explicitly}")


    logger.debug(f"  图片分配槽位总数: {total_image_slots_filled}")
    logger.debug(f"  独立图片使用数量: {len(all_used_image_paths)}")

    if total_valid_images_count is not None:
        logger.debug(f"  总有效候选图片数: {total_valid_images_count}")
        images_not_assigned_from_library = total_valid_images_count - len(all_used_image_paths)
        logger.debug(f"  媒体库中未使用图片数: {images_not_assigned_from_library}")
    else:
        logger.debug(f"  总有效候选图片数: (未提供)")

    logger.debug("======================================")


def update_attribute_json(attribute_json_path: str, image_dir: str, vector_store: VectorStore = None) -> None:
    """
    更新属性 JSON 文件，删除不存在图片的条目，同时清理向量数据库
    统一使用绝对路径作为键
    
    Args:
        attribute_json_path: 属性 JSON 文件路径
        image_dir: 图片目录路径
        vector_store: 向量存储实例
    """
    try:
        # 如果 JSON 文件不存在，直接返回
        if not os.path.exists(attribute_json_path):
            logger.info(f"属性文件不存在: {attribute_json_path}")
            return
            
        # 读取现有的 JSON 文件
        with open(attribute_json_path, 'r', encoding='utf-8') as f:
            attributes = json.load(f)
            
        # 获取目录下实际存在的图片文件，转换为绝对路径
        valid_images = set()
        for img_path in get_valid_images(image_dir):
            valid_images.add(os.path.abspath(img_path))
            
        # 过滤出仍然存在的图片条目
        updated_attributes = []
        removed_count = 0
        removed_paths = []  # 记录被删除的图片路径
        
        for item in attributes:
            filepath = item.get('filepath', '')
            if not filepath:
                continue
                
            # 统一转换为绝对路径
            abs_path = os.path.abspath(filepath)
            
            # 检查绝对路径是否存在于有效图片集合中
            if abs_path in valid_images:
                # 更新为绝对路径
                item['filepath'] = abs_path
                updated_attributes.append(item)
            else:
                removed_count += 1
                removed_paths.append(filepath)
                logger.debug(f"移除不存在的图片: {filepath}")
        
        # 如果有条目被删除，则更新文件和向量数据库
        if removed_count > 0:
            # 在写入之前先做备份
            backup_path = attribute_json_path + '.backup'
            import shutil
            shutil.copy2(attribute_json_path, backup_path)
            
            # 更新 JSON 文件
            with open(attribute_json_path, 'w', encoding='utf-8') as f:
                json.dump(updated_attributes, f, ensure_ascii=False, indent=2)
            logger.info(f"已从属性文件中删除 {removed_count} 个不存在的图片条目")
            logger.info(f"原文件已备份至: {backup_path}")
            
            # 如果提供了向量存储实例，清理向量数据库中的相关条目
            if vector_store is not None:
                cleaned_count = 0
                for filepath in removed_paths:
                    # 清理绝对路径的元数据
                    abs_path = os.path.abspath(filepath)
                    
                    # 遍历元数据，删除匹配的条目
                    indices_to_remove = []
                    for idx_str, metadata in vector_store.metadata.items():
                        stored_path = metadata.get('filepath', '')
                        if os.path.abspath(stored_path) == abs_path:
                            indices_to_remove.append(idx_str)
                            cleaned_count += 1
                    
                    # 从元数据和文件路径索引中删除条目
                    for idx_str in indices_to_remove:
                        if idx_str in vector_store.metadata:
                            del vector_store.metadata[idx_str]
                            # 更新文件路径索引
                            if abs_path in vector_store._filepath_index:
                                del vector_store._filepath_index[abs_path]
                
                # 保存更新后的向量存储
                if cleaned_count > 0:
                    vector_store.save()
                    logger.info(f"已从向量数据库中清理 {cleaned_count} 个不存在图片的条目")
        else:
            logger.info("属性文件中的所有图片都存在，无需更新")
            
    except Exception as e:
        logger.error(f"更新属性文件时出错: {str(e)}")
        raise

def load_clips_data(clip_dir: str, theme: str) -> List[Dict[str, Any]]:
    """加载视频片段数据
    
    Args:
        clip_dir: 视频片段目录路径 
        theme: 主题名称 
    
    Returns:
        List[Dict[str, Any]]: 视频片段数据列表
    """
    try:
        # clip_dir 已经包含到 clips 目录，直接拼接元数据文件名
        metadata_path = os.path.join(clip_dir, f'{theme}_metadata.json')
        
        logger.debug(f"尝试加载metadata文件: {metadata_path}")
        
        if not os.path.exists(metadata_path):
            logger.warning(f"Clips metadata文件不存在: {metadata_path}")
            return []
            
        with open(metadata_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 验证数据格式
        if not isinstance(data, dict) or 'segments' not in data:
            logger.warning("Clips metadata格式错误或缺少segments字段")
            return []
            
        # 验证必要字段
        valid_clips = []
        for clip in data['segments']:
            if all(k in clip for k in ['clip_path', 'description']):
                # 构建完整路径
                full_clip_path = os.path.join(clip_dir, clip['clip_path'])
                
                # 检查视频文件是否存在
                if not os.path.exists(full_clip_path):
                    logger.warning(f"视频文件不存在: {full_clip_path}")
                    continue
                
                # 从视频文件获取实际时长
                try:
                    cap = cv2.VideoCapture(full_clip_path)
                    if not cap.isOpened():
                        logger.warning(f"无法打开视频文件: {full_clip_path}")
                        continue
                        
                    # 获取帧率和总帧数
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    
                    # 计算视频时长（秒）
                    actual_duration = frame_count / fps if fps > 0 else 0
                    cap.release()
                    
                    # 如果元数据中有时长，与实际时长比较并记录差异
                    if 'duration' in clip:
                        metadata_duration = clip['duration']
                        if abs(metadata_duration - actual_duration) > 0.5:  # 允许0.5秒误差
                            logger.warning(f"视频 {clip['clip_path']} 时长不匹配: 元数据={metadata_duration}秒, 实际={actual_duration:.2f}秒")
                    
                    # 使用实际测量的时长
                    clip['duration'] = actual_duration
                    
                except Exception as e:
                    logger.warning(f"获取视频 {full_clip_path} 时长失败: {str(e)}")
                    # 如果无法获取实际时长但元数据中有时长，则使用元数据中的值
                    if 'duration' not in clip:
                        logger.warning(f"视频 {full_clip_path} 无法确定时长，跳过")
                        continue
                
                # 更新路径为完整路径
                clip['clip_path'] = full_clip_path
                valid_clips.append(clip)
                
        logger.info(f"成功加载 {len(valid_clips)} 个有效视频片段")
        return valid_clips
        
    except Exception as e:
        logger.error(f"加载视频片段数据失败: {str(e)}")
        return []


def assign_images_main(
    paragraphs: List[Dict], 
    image_dir: str, 
    clip_dir: str, 
    theme: str, 
    using_anchor: bool = False,
    generate_image: bool = False  # 新增参数：是否生成图片作为兜底
) -> List[Dict]:
    """
    为段落分配媒体资源的主函数，实现"每分配即完成"的流程：
    1. 第一轮分配高相似度阈值的图片
    2. 分配视频片段 
    3. 如果启用generate_image，则对剩余未分配段落创建占位符并结束
    4. 否则继续进行后续图片分配，确保所有段落都有图片
    
    Args:
        paragraphs: 段落列表
        image_dir: 图片目录
        clip_dir: 视频片段目录
        theme: 主题名称
        using_anchor: 是否使用anchor
        generate_image: 是否为未分配段落创建图片占位符
    
    Returns:
        带有媒体分配信息的段落列表
    """
    
    try:
        # 处理图片分配
        # 为指定目录的图片生成embeddings
        attribute_json_path = get_image_metadata_path(image_dir, theme)
        update_attribute_json(attribute_json_path, image_dir)
        generate_and_save_descriptions(attribute_json_path)
        
        # 计算有效图片路径数量并打印
        valid_image_paths = get_valid_images(image_dir)
        # 确保图片路径唯一
        valid_image_paths = list(dict.fromkeys(valid_image_paths))  # 保持顺序的去重方法
        logger.info(f"有效图片总数: {len(valid_image_paths)}")

        # 计算相似度矩阵 - 确保使用相同的有效图片列表
        similarity_matrix, valid_image_paths, image_descriptions = calculate_description_similarity_matrix(
            paragraphs,
            image_dir,
            theme,
            valid_image_paths  # 添加这个参数传递有效图片列表
        )
        
        # 第一轮：高相似度图片分配
        # 找出所有相似度高于阈值的图片
        logger.debug(f"第一轮: 高相似度图片分配 (阈值 >= {SIM_THRESHOLD_FIRST})")
        high_sim_image_indices = []
        for img_idx, img_path in enumerate(valid_image_paths):
            # 检查该图片是否有任何段落的相似度超过阈值
            max_sim = np.max(similarity_matrix[:, img_idx]) if similarity_matrix.size > 0 else 0
            if max_sim >= SIM_THRESHOLD_FIRST:
                high_sim_image_indices.append(img_idx)
        
        high_sim_image_paths = [valid_image_paths[idx] for idx in high_sim_image_indices]
        logger.debug(f"找到高相似度图片: {len(high_sim_image_paths)}/{len(valid_image_paths)}张")
        logger.debug(f"优化使用图片数量: {len(high_sim_image_paths)}")
        
        # 获取未分配的段落，区分单图和多图需求
        unassigned_indices = []  # 单图段落
        multi_image_indices = []  # 多图段落
        
        for i, para in enumerate(paragraphs):
            # 检查是否已有媒体分配
            media_type = para.get('assigned_media', {}).get('type')
            if media_type not in ['anchor', 'image', 'clip', 'multi_image']:
                # 检查该段落需要的图片数量
                num_needed = para.get('num_images_needed', 1)
                if num_needed > 1:
                    multi_image_indices.append(i)
                else:
                    unassigned_indices.append(i)
            # 对于multi_image类型，检查是否分配了足够数量的图片
            elif media_type == 'multi_image':
                num_needed = para.get('num_images_needed', 1)
                filepaths = para.get('assigned_media', {}).get('filepaths', [])
                if len(filepaths) < num_needed:
                    logger.debug(f"段落 {i} 需要 {num_needed} 张图片，但只有 {len(filepaths)} 张，标记为未分配")
                    multi_image_indices.append(i)
        
        logger.debug(f"待分配段落: 单图={len(unassigned_indices)}个, 多图={len(multi_image_indices)}个")
        
        # 创建已使用图片跟踪集合 - 第一轮不允许重复使用
        used_image_paths = set()
        
        # 优先处理单图需求的段落
        if high_sim_image_paths and unassigned_indices:
            logger.debug(f"第一轮: 优先处理 {len(unassigned_indices)} 个单图段落")
            # 创建子相似度矩阵，只包含高相似度图片
            sub_similarity = similarity_matrix[:, high_sim_image_indices]
            
            # 使用ILP优化分配高相似度图片
            first_assignments = ilp_optimization(
                unassigned_indices,
                high_sim_image_paths, 
                sub_similarity,
                max_usage_per_image=1,  # 每张图片只使用一次
                min_distance_between_reuse=3,
                using_anchor=using_anchor,
                prioritize_unused=True
            )
            
            # 输出优化结果信息
            logger.debug(f"ILP求解状态: {0}")  # 假设成功
            logger.debug(f"是否找到最优解: {True}")
            logger.debug(f"已使用图片数: {len(set([img for _, img in first_assignments]))}, 未使用图片数: {len(high_sim_image_paths) - len(set([img for _, img in first_assignments]))}")
            logger.debug(f"有效图片数量: {len(high_sim_image_paths)}")
            logger.debug(f"相似度矩阵维度: {sub_similarity.shape}")
            logger.debug(f"未分配段落数: {len(unassigned_indices)}")
            
            # 更新分配结果
            for para_idx, img_path in first_assignments:
                img_idx = valid_image_paths.index(img_path)
                similarity = similarity_matrix[para_idx, img_idx]
                paragraphs[para_idx]['assigned_media'] = {
                    'type': 'image',
                    'filepath': img_path,
                    'duration': None,
                    'start_time': None,
                    'end_time': None,
                    'similarity': float(similarity)
                }
                # 记录已使用的图片
                used_image_paths.add(img_path)
            
            logger.debug(f"第一轮分配后，单图段落已分配 {len(first_assignments)} 个")
        
        # 然后处理多图段落（确保完整分配）
        if high_sim_image_paths and multi_image_indices:
            logger.debug(f"第一轮: 然后处理 {len(multi_image_indices)} 个需要多张图片的段落")
            
            successfully_assigned_multi = []  # 成功完整分配的多图段落
            
            # 为每个多图需求段落分配图片
            for para_idx in multi_image_indices:
                # 获取需要的图片数量
                num_needed = paragraphs[para_idx].get('num_images_needed', 1)
                
                # 已经分配的图片列表
                assigned_filepaths = []
                if paragraphs[para_idx].get('assigned_media', {}).get('type') == 'multi_image':
                    assigned_filepaths = paragraphs[para_idx]['assigned_media'].get('filepaths', [])
                    
                # 还需要分配的图片数
                still_needed = num_needed - len(assigned_filepaths)
                
                if still_needed <= 0:
                    successfully_assigned_multi.append(para_idx)
                    continue
                    
                logger.debug(f"段落 {para_idx} 需要 {num_needed} 张图片")
                
                # 计算该段落与所有高相似度图片的相似度
                para_similarities = []
                for i, img_idx in enumerate(high_sim_image_indices):
                    img_path = valid_image_paths[img_idx]
                    # 第一轮中不使用已分配的图片
                    if img_path in used_image_paths:
                        continue
                    
                    sim = similarity_matrix[para_idx, img_idx]
                    if sim >= SIM_THRESHOLD_FIRST:
                        para_similarities.append((i, img_path, sim))
                
                # 按相似度降序排序
                para_similarities.sort(key=lambda x: x[2], reverse=True)
                
                # 选取前N张高相似度图片，确保不重复使用
                new_filepaths = []
                for _, img_path, sim in para_similarities[:still_needed * 2]:  # 选择更多备选图片
                    # 检查图片是否已被使用（第一轮严格限制不重复）
                    if img_path not in used_image_paths and img_path not in assigned_filepaths and img_path not in new_filepaths:
                        new_filepaths.append(img_path)
                        # 将图片标记为已使用
                        used_image_paths.add(img_path)
                        if len(new_filepaths) >= still_needed:
                            break
                
                # 只有能够完整满足需求时才更新段落的媒体分配
                if len(new_filepaths) == still_needed:
                    combined_filepaths = assigned_filepaths + new_filepaths
                    paragraphs[para_idx]['assigned_media'] = {
                        'type': 'multi_image',
                        'filepaths': combined_filepaths,
                        'duration': None,
                        'start_time': None,
                        'end_time': None,
                        'similarity': 1.0  # 使用固定值表示多图匹配
                    }
                    logger.debug(f"段落 {para_idx} 成功完整分配 {len(new_filepaths)} 张新图片，总共 {len(combined_filepaths)} 张")
                    successfully_assigned_multi.append(para_idx)
                elif len(new_filepaths) > 0:
                    # 虽然不能完整满足需求，但已找到部分图片，先保存这些图片，稍后处理
                    combined_filepaths = assigned_filepaths + new_filepaths
                    paragraphs[para_idx]['assigned_media'] = {
                        'type': 'multi_image',
                        'filepaths': combined_filepaths,
                        'duration': None,
                        'start_time': None,
                        'end_time': None,
                        'similarity': 1.0
                    }
                    logger.debug(f"段落 {para_idx} 部分满足多图需求，找到 {len(new_filepaths)}/{still_needed} 张图片")
                    # 不将这个段落标记为完全分配成功，后续还需处理
                else:
                    logger.debug(f"段落 {para_idx} 无法完整满足多图需求，找到 {len(new_filepaths)}/{still_needed} 张图片，暂不分配")
            
            # 移除已完整分配的多图段落
            multi_image_indices = [idx for idx in multi_image_indices if idx not in successfully_assigned_multi]
            logger.debug(f"多图段落处理完成，成功完整分配 {len(successfully_assigned_multi)} 个，剩余 {len(multi_image_indices)} 个未完整分配")
        
        # 更新未分配的段落
        unassigned_indices = []
        for i, para in enumerate(paragraphs):
            media_type = para.get('assigned_media', {}).get('type')
            if media_type not in ['anchor', 'image', 'clip', 'multi_image']:
                unassigned_indices.append(i)
        
        logger.debug(f"第一轮分配后，还有 {len(unassigned_indices)} 个段落未分配")
        
        # 打印分配状态
        print_assignment_status(paragraphs, len(valid_image_paths), "第一轮")

        # 第二阶段：视频片段分配
        if clip_dir and os.path.exists(clip_dir):
            # 读取视频片段数据
            clips_data = load_clips_data(clip_dir, theme)
            if clips_data and unassigned_indices:
                logger.debug("第二阶段 - 视频片段分配")
                
                # 计算视频片段相似度矩阵
                clip_descriptions = [clip.get('description', '') for clip in clips_data]
                para_texts = [p.get('paragraph_text', '') for p in paragraphs]
                clip_similarity_matrix = calculate_clip_similarity_matrix(para_texts, clip_descriptions)
                
                # 筛选未分配的段落
                filtered_paragraphs = copy.deepcopy(paragraphs)
                
                # 使用 assign_clips 函数，传递段落列表和未分配段落索引
                filtered_paragraphs = assign_clips(
                    filtered_paragraphs,
                    clips_data,
                    clip_similarity_matrix,
                    unassigned_indices=unassigned_indices,  # 传递未分配段落索引
                    using_anchor=using_anchor
                )
                
                # 将视频分配结果合并回原始段落列表
                for i in unassigned_indices:
                    # 如果分配了视频，更新原始段落
                    if filtered_paragraphs[i].get('assigned_media', {}).get('type') == 'clip':
                        paragraphs[i]['assigned_media'] = filtered_paragraphs[i]['assigned_media']
        
        # 更新未分配的段落
        unassigned_indices = []
        multi_image_indices = []
        for i, para in enumerate(paragraphs):
            media_type = para.get('assigned_media', {}).get('type')
            if media_type not in ['anchor', 'image', 'clip', 'multi_image']:
                # 检查是否是多图段落
                num_needed = para.get('num_images_needed', 1)
                if num_needed > 1:
                    multi_image_indices.append(i)
                else:
                    unassigned_indices.append(i)
            # 对于multi_image类型，检查是否分配了足够数量的图片
            elif media_type == 'multi_image':
                num_needed = para.get('num_images_needed', 1)
                filepaths = para.get('assigned_media', {}).get('filepaths', [])
                if len(filepaths) < num_needed:
                    multi_image_indices.append(i)
        
        # 第三阶段：如果启用了图片生成选项，为未分配段落创建占位符
        if generate_image and (unassigned_indices or multi_image_indices):
            logger.debug("第三阶段 - 为未分配段落创建图片生成占位符")
            
            # 统计多图需求
            total_images_needed = 0
            for idx in unassigned_indices:
                total_images_needed += paragraphs[idx].get('num_images_needed', 1)
            
            for idx in multi_image_indices:
                # 计算还需要生成多少张图片
                num_needed = paragraphs[idx].get('num_images_needed', 1)
                current_filepaths = []
                if paragraphs[idx].get('assigned_media', {}).get('type') == 'multi_image':
                    current_filepaths = paragraphs[idx]['assigned_media'].get('filepaths', [])
                
                still_needed = num_needed - len(current_filepaths)
                if still_needed > 0:
                    total_images_needed += still_needed
            
            logger.debug(f"需要生成的图片总数: {total_images_needed}")
            
            # 标记需要生成图片的段落
            for idx in unassigned_indices:
                # 为单图段落添加pending标记
                paragraphs[idx]['assigned_media'] = {
                    'type': 'pending',
                    'needs_generated_image': True,
                    'num_images_needed': paragraphs[idx].get('num_images_needed', 1)
                }
                logger.info(f"段落 {idx} 标记为需要生成图片")
            
            for idx in multi_image_indices:
                # 为多图段落更新pending标记
                num_needed = paragraphs[idx].get('num_images_needed', 1)
                current_filepaths = []
                if paragraphs[idx].get('assigned_media', {}).get('type') == 'multi_image':
                    current_filepaths = paragraphs[idx]['assigned_media'].get('filepaths', [])
                
                still_needed = num_needed - len(current_filepaths)
                
                if still_needed > 0:
                    # 保存现有的图片路径
                    paragraphs[idx]['assigned_media'] = {
                        'type': 'multi_image_pending',
                        'needs_generated_image': True,
                        'num_images_needed': still_needed,
                        'existing_filepaths': current_filepaths
                    }
                    logger.info(f"段落 {idx} 标记为需要生成部分图片 ({still_needed} 张，已有 {len(current_filepaths)} 张)")
            
            # 不再实际生成图片，而是直接返回占位符信息
            return paragraphs
        
        # 第四阶段：如果有任何段落仍然未分配，分配anchor或现有图片
        unassigned_indices = []
        for i, para in enumerate(paragraphs):
            media_type = para.get('assigned_media', {}).get('type')
            if media_type not in ['anchor', 'image', 'clip', 'multi_image'] or media_type == 'pending':
                unassigned_indices.append(i)
        
        if unassigned_indices:
            logger.warning(f"第四阶段 - 仍有 {len(unassigned_indices)} 个段落未分配，使用fallback策略")
            
            # 使用锚点填充策略
            if using_anchor:
                total_paragraphs = len(paragraphs)
                max_anchor_fraction = 0.1  # 最大允许10%的段落为锚点
                current_anchor_count = sum(1 for p in paragraphs if p.get('assigned_media',{}).get('type') == 'anchor')
                max_anchor_count = math.ceil(total_paragraphs * max_anchor_fraction)
                
                if current_anchor_count < max_anchor_count:
                    allowed_new_anchors = max_anchor_count - current_anchor_count
                    anchor_candidates = unassigned_indices[:min(allowed_new_anchors, len(unassigned_indices))]
                    
                    for idx in anchor_candidates:
                        paragraphs[idx]['assigned_media'] = {
                            'type': 'anchor',
                            'filepath': None,
                            'duration': paragraphs[idx]['audio']['duration'],
                            'start_time': None,
                            'end_time': None
                        }
                    
                    # 更新未分配列表
                    unassigned_indices = [idx for idx in unassigned_indices if idx not in anchor_candidates]
            
            # 如果还有未分配段落，使用现有图片（可能重复使用）
            if unassigned_indices and valid_image_paths:
                # 创建图片使用计数
                image_usage = {}
                for para in paragraphs:
                    media = para.get('assigned_media', {})
                    if media.get('type') == 'image':
                        filepath = media.get('filepath')
                        if filepath:
                            image_usage[filepath] = image_usage.get(filepath, 0) + 1
                
                # 动态计算合理的重复使用次数
                max_usage = 3  # 默认每张图片最多使用3次
                if valid_image_paths:
                    max_usage = min(5, math.ceil(len(unassigned_indices) / len(valid_image_paths)) + 1)
                
                # 调整相似度矩阵，降低已频繁使用图片的权重
                adjusted_sim = similarity_matrix.copy()
                for i, path in enumerate(valid_image_paths):
                    usage = image_usage.get(path, 0)
                    if usage > 0:
                        adjusted_sim[:, i] *= (1.0 / (usage + 1))
                
                # 使用ILP优化分配剩余图片
                final_assignments = ilp_optimization(
                    unassigned_indices,
                    valid_image_paths,
                    adjusted_sim,
                    max_usage_per_image=max_usage,
                    min_distance_between_reuse=2,
                    using_anchor=using_anchor,
                    prioritize_unused=True
                )
                
                # 更新分配结果
                for para_idx, img_path in final_assignments:
                    img_idx = valid_image_paths.index(img_path)
                    similarity = similarity_matrix[para_idx, img_idx]
                    # 检查是否需要多张图片
                    num_needed = paragraphs[para_idx].get('num_images_needed', 1)
                    if num_needed > 1:
                        # 创建多图分配
                        paragraphs[para_idx]['assigned_media'] = {
                            'type': 'multi_image',
                            'filepaths': [img_path] * num_needed,  # 重复使用同一张图片
                            'duration': None,
                            'start_time': None,
                            'end_time': None,
                            'similarity': float(similarity)
                        }
                    else:
                        # 创建单图分配
                        paragraphs[para_idx]['assigned_media'] = {
                            'type': 'image',
                            'filepath': img_path,
                            'duration': None,
                            'start_time': None,
                            'end_time': None,
                            'similarity': float(similarity)
                        }
        
        # 打印最终分配状态
        print_assignment_status(paragraphs, len(valid_image_paths), "最终")
        
        return paragraphs
    
    except Exception as e:
        logger.error(f"分配媒体资源时出错: {str(e)}")
        logger.debug("错误详情:", exc_info=True)
        return paragraphs

def assign_clips(
    paragraphs: List[Dict[str, Any]],
    clips: List[Dict[str, Any]],
    similarity_matrix: np.ndarray,
    sim_threshold: float = SIM_THRESHOLD,
    using_anchor: bool = False,
    unassigned_indices: List[int] = None
) -> List[Dict[str, Any]]:
    """
    为段落分配视频片段，支持跨段落共享片段
    
    Args:
        paragraphs: 段落列表
        clips: 视频片段列表
        similarity_matrix: 相似度矩阵
        sim_threshold: 相似度阈值
        using_anchor: 是否使用anchor模式（首段为anchor）
        unassigned_indices: 未分配段落的索引列表，如果提供，则只处理这些段落
    
    Returns:
        更新后的段落列表
    """
    # 如果没有视频片段，直接返回
    if not clips:
        logger.info("没有视频片段可供分配")
        return paragraphs
        
    # 跟踪哪些段落已分配媒体
    assigned_indices = set()
    
    # 将已分配媒体的段落加入已分配列表
    for i, para in enumerate(paragraphs):
        if para.get('assigned_media', {}).get('type') in ['anchor', 'image', 'clip', 'multi_image']:
            assigned_indices.add(i)
    
    # 如果提供了未分配段落索引，则过滤出实际可分配的段落
    valid_indices_to_process = []
    if unassigned_indices is not None:
        valid_indices_to_process = [idx for idx in unassigned_indices if idx not in assigned_indices]
        logger.debug(f"视频分配将处理 {len(valid_indices_to_process)}/{len(unassigned_indices)} 个未分配段落")
    else:
        # 处理所有未分配段落
        valid_indices_to_process = [i for i in range(len(paragraphs)) if i not in assigned_indices]
        logger.debug(f"视频分配将处理所有 {len(valid_indices_to_process)} 个未分配段落")
    
    logger.debug(f"分配前已有 {len(assigned_indices)} 个段落被分配媒体")
    
    # 对每个视频片段进行分配
    for clip_idx, clip in enumerate(clips):
        # 检查必要字段
        if not all(k in clip for k in ['clip_path', 'description', 'duration']):
            logger.warning(f"视频片段 {clip_idx} 缺少必要字段, 跳过")
            continue
            
        clip_path = clip['clip_path']
        clip_duration = clip['duration']
        
        # 跳过过短的视频片段
        if clip_duration < 3.0:  # 设置最短时长阈值
            logger.debug(f"视频片段 {os.path.basename(clip_path)} 时长 {clip_duration}秒 过短，跳过")
            continue
            
        # 计算每个段落的相似度，并按索引排序，只考虑未分配的段落
        valid_para_similarities = []
        for para_idx in valid_indices_to_process:
            sim = similarity_matrix[para_idx, clip_idx]
            valid_para_similarities.append((para_idx, sim))
        
        # 按相似度降序排序
        valid_para_similarities.sort(key=lambda x: x[1], reverse=True)
        
        # 跳过无匹配段落的视频
        if not valid_para_similarities or valid_para_similarities[0][1] < sim_threshold:
            logger.debug(f"视频片段 {os.path.basename(clip_path)} 没有找到相似度高于阈值的段落")
            continue
        
        # 查找最匹配的起始段落
        start_para_idx, start_sim = valid_para_similarities[0]
        
        # 计算连续段落的累计时长，直到达到视频片段时长
        total_duration = 0
        matched_paragraphs = []
        
        # 从起始段落开始，尝试找到连续段落
        current_idx = start_para_idx
        while current_idx < len(paragraphs) and total_duration < clip_duration:
            # 检查当前段落是否已分配或不在处理范围内
            if current_idx in assigned_indices or (unassigned_indices is not None and current_idx not in unassigned_indices):
                break
                
            # 获取当前段落的音频时长
            para_duration = paragraphs[current_idx].get('audio', {}).get('duration', 0)
            if para_duration <= 0:
                logger.warning(f"段落 {current_idx} 音频时长无效: {para_duration}秒")
                current_idx += 1
                continue
                
            # 检查如果再加上这段会不会超过视频总长度太多
            if total_duration + para_duration > clip_duration * 1.2:  # 允许20%的超出
                # 如果超出太多，检查是否可以部分使用（前提是至少有一个段落）
                if matched_paragraphs:
                    logger.debug(f"段落 {current_idx} 累计时长将超过视频时长的120%，停止添加")
                    break
            
            # 检查当前段落的相似度
            current_sim = similarity_matrix[current_idx, clip_idx]
            if current_sim < sim_threshold * 0.8:  # 允许相似度降低到阈值的80%
                logger.debug(f"段落 {current_idx} 相似度 {current_sim:.4f} 过低，停止累加")
                break
                
            # 将当前段落添加到匹配列表
            matched_paragraphs.append((current_idx, current_sim))
            total_duration += para_duration
            
            # 移动到下一个段落
            current_idx += 1
        
        # 如果没有匹配的段落，继续下一个视频片段
        if not matched_paragraphs:
            logger.debug(f"视频片段 {os.path.basename(clip_path)} 没有找到合适的连续段落")
            continue
            
        # 如果累计时长太短，也不分配
        if total_duration < clip_duration * 0.6:  # 至少达到视频时长的60%
            logger.debug(f"匹配段落总时长 {total_duration:.2f}秒 太短（视频时长 {clip_duration:.2f}秒），不分配")
            continue
            
        logger.info(f"为 {len(matched_paragraphs)} 个连续段落分配视频片段 {os.path.basename(clip_path)}，累计时长 {total_duration:.2f}秒")
        
        # 计算时间点分配
        current_time = 0
        for idx, (para_idx, sim) in enumerate(matched_paragraphs):
            para_duration = paragraphs[para_idx].get('audio', {}).get('duration', 0)
            end_time = min(current_time + para_duration, clip_duration)
            
            # 分配视频片段
            paragraphs[para_idx]['assigned_media'] = {
                'type': 'clip',
                'filepath': clip_path,
                'duration': para_duration,
                'start_time': current_time,
                'end_time': end_time,
                'similarity': float(sim)
            }
            
            # 更新已分配列表
            assigned_indices.add(para_idx)
            
            # 更新当前时间点
            current_time = end_time
            
            logger.debug(f"段落 {para_idx} 分配视频时间段 {current_time:.2f}s - {end_time:.2f}s，相似度: {sim:.4f}")
            
            # 如果已达到视频末尾，终止分配
            if end_time >= clip_duration:
                break
    
    # 统计分配结果
    if unassigned_indices is not None:
        assigned_count = sum(1 for i in unassigned_indices if i in assigned_indices)
        logger.info(f"视频片段分配完成，从 {len(unassigned_indices)} 个未分配段落中分配了 {assigned_count} 个")
    else:
        assigned_count = sum(1 for i in range(len(paragraphs)) if i in assigned_indices)
        logger.info(f"视频片段分配完成，共分配 {assigned_count} 个段落")
    
    return paragraphs

def calculate_clip_similarity_matrix(
    paragraphs: List[str],
    clip_descriptions: List[str]
) -> np.ndarray:
    """
    计算段落与视频片段之间的相似度矩阵
    
    Args:
        paragraphs: 段落文本列表
        clip_descriptions: 视频片段描述列表
    
    Returns:
        相似度矩阵，shape=(len(paragraphs), len(clip_descriptions))
    """
    # 如果没有段落或视频片段，返回空矩阵
    if not paragraphs or not clip_descriptions:
        return np.zeros((len(paragraphs), len(clip_descriptions)))
        
    try:
        # 初始化矩阵
        similarity_matrix = np.zeros((len(paragraphs), len(clip_descriptions)))
        
        # 为段落和视频片段描述生成嵌入向量
        paragraph_embeddings = []
        for para in paragraphs:
            embedding = generate_embedding(para)
            paragraph_embeddings.append(embedding)
            
        clip_embeddings = []
        for desc in clip_descriptions:
            embedding = generate_embedding(desc)
            clip_embeddings.append(embedding)
            
        # 计算相似度矩阵
        for i, para_embedding in enumerate(paragraph_embeddings):
            for j, clip_embedding in enumerate(clip_embeddings):
                # 计算余弦相似度
                similarity = np.dot(para_embedding, clip_embedding) / (
                    np.linalg.norm(para_embedding) * np.linalg.norm(clip_embedding)
                )
                similarity_matrix[i, j] = similarity
                
        return similarity_matrix
        
    except Exception as e:
        logger.error(f"计算视频片段相似度矩阵失败: {str(e)}")
        # 返回全零矩阵
        return np.zeros((len(paragraphs), len(clip_descriptions)))

def calculate_description_similarity_matrix(
    paragraphs: List[Dict],
    image_dir: str,
    theme: str,
    valid_image_list: List[str] = None
) -> Tuple[np.ndarray, List[str], Dict[str, str]]:
    """计算段落与图片描述之间的相似度矩阵，统一使用绝对路径"""
    try:
        # 加载或初始化向量存储
        vector_store = VectorStore(FAISS_INDEX_PATH, METADATA_PATH)
        
        # 获取图片属性文件路径
        image_attribute_path = get_image_metadata_path(image_dir, theme)
        logger.debug(f"图片属性文件路径: {image_attribute_path}")
        
        # 如果没有提供有效图片列表，则获取目录下所有有效图片
        if valid_image_list is None:
            valid_image_list = get_valid_images(image_dir)
            
        # 确保图片唯一性并转换为绝对路径
        abs_image_paths = [os.path.abspath(img) for img in valid_image_list]
        abs_image_paths = list(dict.fromkeys(abs_image_paths))  # 保持顺序的去重
        
        # 读取属性JSON文件，获取图片描述
        image_descriptions = {}
        
        if os.path.exists(image_attribute_path):
            with open(image_attribute_path, 'r', encoding='utf-8') as f:
                attributes = json.load(f)
                
            # 提取描述并仅使用绝对路径
            for item in attributes:
                filepath = item.get('filepath')
                description = item.get('description')
                
                if filepath and description:
                    # 统一使用绝对路径作为键
                    abs_path = os.path.abspath(filepath)
                    image_descriptions[abs_path] = description
        
        # 确保只使用有效的图片，并且有描述
        valid_images_with_desc = []
        
        for img_path in abs_image_paths:
            if img_path in image_descriptions:
                valid_images_with_desc.append(img_path)
            else:
                logger.warning(f"图片没有描述: {img_path}")
        
        # 获取段落文本
        paragraph_texts = [p['paragraph_text'] for p in paragraphs]
        
        # 获取图片描述
        image_desc_texts = [image_descriptions.get(img_path, '') for img_path in valid_images_with_desc]
        
        # 初始化相似度矩阵
        similarity_matrix = np.zeros((len(paragraph_texts), len(valid_images_with_desc)))
        
        # 如果没有有效图片或段落，返回空结果
        if not valid_images_with_desc or not paragraph_texts:
            logger.warning("没有有效图片或段落，返回空相似度矩阵")
            return similarity_matrix, valid_images_with_desc, image_descriptions
        
        # 为段落生成嵌入向量
        paragraph_embeddings = []
        for text in paragraph_texts:
            embedding = generate_embedding(text)
            paragraph_embeddings.append(embedding)
            
        # 为图片描述生成嵌入向量
        image_embeddings = []
        for desc in image_desc_texts:
            embedding = generate_embedding(desc)
            image_embeddings.append(embedding)
            
        # 计算相似度矩阵
        for i, para_embedding in enumerate(paragraph_embeddings):
            for j, img_embedding in enumerate(image_embeddings):
                # 计算余弦相似度
                similarity = np.dot(para_embedding, img_embedding) / (
                    np.linalg.norm(para_embedding) * np.linalg.norm(img_embedding)
                )
                similarity_matrix[i, j] = similarity
                
        return similarity_matrix, valid_images_with_desc, image_descriptions
        
    except Exception as e:
        logger.error(f"计算图片描述相似度矩阵失败: {str(e)}")
        # 返回全零矩阵
        return np.zeros((len(paragraphs), len(valid_image_list) if valid_image_list else 0)), valid_image_list or [], {}

def ilp_optimization(unassigned_indices, image_paths, similarity_matrix,
                    max_usage_per_image=1,
                    min_distance_between_reuse=2,
                    dynamic_threshold=float('-inf'),
                    using_anchor=False,
                    prioritize_unused=False):
    """
    ILP优化分配函数
    
    Args:
        prioritize_unused: 是否优先使用未使用过的图片
    """
    # 移除重复去重操作，确保与相似度矩阵一致
    # 确保image_paths无重复（仅作为安全措施，实际上calculate_description_similarity_matrix已确保唯一）
    image_paths_len_before = len(image_paths)
    image_paths = list(dict.fromkeys(image_paths))  # 保持顺序的去重方法
    if image_paths_len_before != len(image_paths):
        logger.warning(f"图片路径列表包含重复项！去重前:{image_paths_len_before} 去重后:{len(image_paths)}")
    
    logger.debug(f"优化使用图片数量: {len(image_paths)}")
    
    # 验证输入参数
    assert similarity_matrix.shape[1] == len(image_paths), \
        f"相似度矩阵列数({similarity_matrix.shape[1]})与图片数量({len(image_paths)})不匹配"
    
    # 使用传入的未分配索引
    solver = pywraplp.Solver.CreateSolver('SCIP')
    
    # 创建决策变量
    x = {}
    for para_idx in unassigned_indices:
        for img_idx in range(len(image_paths)):
            x[para_idx, img_idx] = solver.IntVar(0, 1, f'x_{para_idx}_{img_idx}')
    
    # 约束也只应用于未分配的段落
    for para_idx in unassigned_indices:
        solver.Add(
            solver.Sum([x[para_idx, img_idx] for img_idx in range(len(image_paths))]) == 1
        )

    # 初始化图片使用追踪变量
    first_use = {}  # 图片首次使用变量
    overuse = {}  # 超额使用变量
    
    # 图片使用决策变量
    is_used = {}  # 表示图片是否被使用
    for img_idx in range(len(image_paths)):
        first_use[img_idx] = solver.BoolVar(f'first_use[{img_idx}]')
        overuse[img_idx] = solver.BoolVar(f'overuse[{img_idx}]')
        is_used[img_idx] = solver.BoolVar(f'is_used[{img_idx}]')
        
        # 图片使用追踪约束
        if max_usage_per_image == 1:
            # 严格限制：每张图片最多使用一次
            solver.Add(
                solver.Sum([x[para_idx, img_idx] for para_idx in unassigned_indices]) <= 1
            )
        else:
            # 允许超额使用
            solver.Add(
                solver.Sum([x[para_idx, img_idx] for para_idx in unassigned_indices]) 
                <= max_usage_per_image + (len(unassigned_indices) - max_usage_per_image) * overuse[img_idx]
            )
        
        # 首次使用约束
        solver.Add(
            solver.Sum([x[para_idx, img_idx] for para_idx in unassigned_indices]) 
            <= len(unassigned_indices) * first_use[img_idx]
        )
        
        # 设置图片使用变量 is_used
        solver.Add(is_used[img_idx] <= solver.Sum([x[para_idx, img_idx] for para_idx in unassigned_indices]))
        solver.Add(is_used[img_idx] * len(unassigned_indices) >= 
                   solver.Sum([x[para_idx, img_idx] for para_idx in unassigned_indices]))

    # 重复使用间隔约束
    if max_usage_per_image > 1:
        for img_idx in range(len(image_paths)):
            for i in range(len(unassigned_indices) - min_distance_between_reuse):
                window_indices = unassigned_indices[i:i + min_distance_between_reuse + 1]
                if len(window_indices) > 1:
                    window_vars = [x[para_idx, img_idx] for para_idx in window_indices]
                    solver.Add(solver.Sum(window_vars) <= 1)
    
    # 新增约束：优先使用未使用过的图片
    if prioritize_unused:
        # 计算已使用和未使用的图片数量
        total_images_used = solver.Sum([is_used[img_idx] for img_idx in range(len(image_paths))])
        
        # 如果未分配段落数大于图片数，我们希望所有图片都被使用
        if len(unassigned_indices) >= len(image_paths):
            # 尽可能使用所有图片
            solver.Add(total_images_used == len(image_paths))
        else:
            # 至少使用与未分配段落数相等的图片数量
            solver.Add(total_images_used >= min(len(unassigned_indices), len(image_paths)))

    # 构建目标函数
    objective_terms = []
    
    # 相似度项
    for para_idx in unassigned_indices:
        for img_idx in range(len(image_paths)):
            sim = similarity_matrix[para_idx, img_idx]
            # 当这是该段落的最高相似度时，即使为负也允许匹配
            if sim == np.max(similarity_matrix[para_idx]):
                solver.Add(solver.Sum([x[para_idx, img_idx]] * 1) >= 0)  # 强制允许分配
            objective_terms.append(sim * x[para_idx, img_idx])
    
    # 重复使用惩罚项 - 大幅增加重复使用的惩罚
    REPEAT_PENALTY_MULTIPLIER = 3.0  # 增大惩罚系数
    for img_idx in range(len(image_paths)):
        objective_terms.append(-REPEAT_PENALTY * REPEAT_PENALTY_MULTIPLIER * overuse[img_idx])
    
    # 首次使用奖励项 - 增加首次使用的奖励
    FIRST_USE_REWARD = 0.5  # 增大奖励系数
    for img_idx in range(len(image_paths)):
        objective_terms.append(FIRST_USE_REWARD * first_use[img_idx])
        
    # 增加新的目标函数项：优先使用未使用的图片
    if prioritize_unused:
        UNUSED_PRIORITIZATION = 1.0  # 优先使用未使用图片的权重
        objective_terms.append(UNUSED_PRIORITIZATION * solver.Sum([is_used[img_idx] for img_idx in range(len(image_paths))]))

    # 设置目标函数
    solver.Maximize(solver.Sum(objective_terms))

    # 求解
    status = solver.Solve()
    
    # 添加求解状态检查日志
    logger.debug(f"ILP求解状态: {status}")
    logger.debug(f"是否找到最优解: {status == pywraplp.Solver.OPTIMAL}")
    
    if status == pywraplp.Solver.OPTIMAL:
        assignments = []
        assigned_count = 0  # 用于调试
        
        # 计算每个图片的使用情况
        image_usage = {img_idx: 0 for img_idx in range(len(image_paths))}
        for para_idx in unassigned_indices:
            for img_idx in range(len(image_paths)):
                if x[para_idx, img_idx].solution_value() > 0.5:
                    assignments.append((para_idx, image_paths[img_idx]))
                    image_usage[img_idx] += 1
                    assigned_count += 1
        
        # 记录使用和未使用的图片数量
        used_images = sum(1 for count in image_usage.values() if count > 0)
        unused_images = len(image_paths) - used_images
        logger.debug(f"已使用图片数: {used_images}, 未使用图片数: {unused_images}")
        
        # 记录最终分配结果
        if using_anchor:
            logger.debug(f"ILP优化完成 - 分配结果是否包含第一段: {0 in assignments}")
        
        logger.debug(f"有效图片数量: {len(image_paths)}")
        logger.debug(f"相似度矩阵维度: {similarity_matrix.shape}")
        logger.debug(f"未分配段落数: {len(unassigned_indices)}")
        
        return assignments
    else:
        logger.error(f"ILP求解失败: {status}")
        raise Exception(f"ILP求解失败: {status}")

if __name__ == "__main__":
    # 简单的命令行接口，用于单独测试
    parser = argparse.ArgumentParser(description="为文本段落分配媒体资源")
    parser.add_argument("--json", type=str, required=True, help="输入JSON文件路径")
    parser.add_argument("--image_dir", type=str, required=True, help="图片目录路径")
    parser.add_argument("--clip_dir", type=str, help="视频片段目录路径（可选）")
    parser.add_argument("--theme", type=str, help="主题名称（可选）")
    parser.add_argument("--using_anchor", action="store_true", help="是否使用anchor")
    parser.add_argument("--generate_image", action="store_true", help="是否使用图片生成作为兜底策略")
    args = parser.parse_args()
    
    # 读取输入文件
    with open(args.json, 'r', encoding='utf-8') as f:
        paragraphs = json.load(f)
    
    # 如果未指定主题，从文件名获取
    if not args.theme:
        filename = os.path.basename(args.json)
        args.theme = filename.replace('_audio.json', '')
    
    # 分配媒体资源
    
    modified_paragraphs = assign_images_main(
        paragraphs,
        args.image_dir,
        args.clip_dir,
        args.theme,
        args.using_anchor,
        args.generate_image
    )
    
    # 打印分配状态
    print_assignment_status(modified_paragraphs, len(get_valid_images(args.image_dir)), "最终")
    
    # 保存结果
    output_path = args.json.replace('.json', '_result.json')
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(modified_paragraphs, f, ensure_ascii=False, indent=2)
    
    print(f"已保存结果到 {output_path}") 