#!/bin/bash

# 检查是否提供了目录参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <目录路径>"
    exit 1
fi

SYNC_DIR="$1"
SOURCE_SCRIPT="/home/<USER>/ai-video/utils/source_text_images.sh"

# 检查目录是否存在
if [ ! -d "$SYNC_DIR" ]; then
    echo "错误：目录 '$SYNC_DIR' 不存在"
    exit 1
fi

# 定义服务器信息和路径
SERVER_USER="azureuser"
SERVER_HOST="**************"
TOPIC_DIR="/home/<USER>/ai-video/data/topics"
REMOTE_LOG_DIR="/home/<USER>/ai-video/logs"
UTILS_DIR="/home/<USER>/ai-video/utils"
REMOTE_LOG_FILE="${REMOTE_LOG_DIR}/source_text_images_$(date +%Y%m%d_%H%M).log"

# 定义远程同步目录
REMOTE_SYNC_DIR="${TOPIC_DIR}/$(basename "$SYNC_DIR")/"

# 检查远程目录是否存在，如果不存在则创建
echo "检查远程目录是否存在: ssh ${SERVER_USER}@${SERVER_HOST} 'if [ ! -d ${REMOTE_SYNC_DIR} ]; then mkdir -p ${REMOTE_SYNC_DIR}; fi'"
ssh ${SERVER_USER}@${SERVER_HOST} "if [ ! -d ${REMOTE_SYNC_DIR} ]; then mkdir -p ${REMOTE_SYNC_DIR}; fi"

# 使用 rsync 将指定目录中的文件同步到服务器的 topics 目录下
echo "同步命令: rsync -avz $SYNC_DIR/ ${SERVER_USER}@${SERVER_HOST}:${REMOTE_SYNC_DIR}"
rsync -avz "$SYNC_DIR/" "${SERVER_USER}@${SERVER_HOST}:${REMOTE_SYNC_DIR}"

if [ $? -eq 0 ]; then
    echo "目录同步成功。"
else
    echo "目录同步失败。" >&2
    exit 1
fi

# 登录到服务器并调用 SOURCE_SCRIPT 处理文件
echo "SSH 登录命令: ssh -t -i ~/.ssh/videoadmin.pem ${SERVER_USER}@${SERVER_HOST}"
ssh -t -i ~/.ssh/videoadmin.pem ${SERVER_USER}@${SERVER_HOST} << EOF
    echo "调用 SOURCE_SCRIPT 处理文件: bash "$SOURCE_SCRIPT" "$REMOTE_SYNC_DIR" "
    nohup bash "$SOURCE_SCRIPT" "$REMOTE_SYNC_DIR" > "$REMOTE_LOG_FILE" 2>&1 &
EOF

if [ $? -eq 0 ]; then
    echo "远程文件处理已成功完成。"
else
    echo "远程文件处理失败。" >&2
    exit 1
fi