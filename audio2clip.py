import os
import subprocess
import argparse
import logging
import random
import requests
import hashlib
import json
import time
import datetime
from pathlib import Path
from pydub import AudioSegment
from tempfile import NamedTemporaryFile
from config import (
    logger, VIDEO_RESOLUTION, VIDEO_FPS, get_param
)

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


# 视频参数常量
DEFAULT_FPS = 25
DEFAULT_RESOLUTION = (800, 1280)
DEFAULT_BACKGROUND_COLOR = "black"

# HeyGen API 配置
HEYGEN_API_KEY = os.getenv("HEYGEN_API_KEY")
HEYGEN_CACHE_FILE = os.path.expanduser("~/data/cache/heygen_avatar_cache.json")
HEYGEN_AUDIO_CACHE_FILE = os.path.expanduser("~/data/cache/heygen_audio_cache.json")
HEYGEN_UPLOAD_URL = "https://upload.heygen.com/v1/asset"
HEYGEN_TALKING_PHOTO_URL = "https://upload.heygen.com/v1/talking_photo"
HEYGEN_LIST_TALKING_PHOTOS_URL = "https://api.heygen.com/v1/talking_photo.list"
HEYGEN_GENERATE_VIDEO_URL = "https://api.heygen.com/v2/video/generate"
HEYGEN_VIDEO_STATUS_URL = "https://api.heygen.com/v1/video_status.get"

HEYGEN_HEADERS_JSON = {
    "Content-Type": "application/json",
    "X-Api-Key": HEYGEN_API_KEY
}

def get_audio_duration(audio_path: str) -> float:
    """获取音频文件的持续时间（秒）"""
    try:
        audio = AudioSegment.from_file(audio_path)
        return len(audio) / 1000.0  # 转换为秒
    except Exception as e:
        logger.error(f"获取音频时长失败 {audio_path}: {e}")
        raise

def get_avatar_path(lang_code='en'):
    """根据语言代码获取对应的头像路径"""
    # 使用 config.py 中的 get_param 获取配置
    avatar_settings = get_param('AVATAR_GEN', {})
    lang_code = lang_code.lower()
    
    if lang_code == 'en':
        avatar_path = avatar_settings.get('EN_AVATAR_PATH')
    else:
        # 尝试查找特定语言的头像路径，如果没有则使用英语
        avatar_path = avatar_settings.get(f'{lang_code.upper()}_AVATAR_PATH', 
                                         avatar_settings.get('EN_AVATAR_PATH'))
    
    if not avatar_path:
        logger.warning(f"未找到语言 {lang_code} 的头像路径配置，请检查settings.yaml")
        return None
    
    return avatar_path

def get_random_avatar_image(avatar_dir):
    """从头像目录中随机选择一张图片"""
    try:
        if not os.path.exists(avatar_dir):
            logger.error(f"头像目录不存在: {avatar_dir}")
            return None
            
        # 获取所有图片文件
        image_files = []
        for ext in ['jpg', 'jpeg', 'png']:
            image_files.extend(list(Path(avatar_dir).glob(f'*.{ext}')))
            
        if not image_files:
            logger.error(f"头像目录中没有发现图片文件: {avatar_dir}")
            return None
            
        # 设置基于日期的随机种子
        current_date = datetime.datetime.now()
        random_seed = int(f"{current_date.year}{current_date.month:02d}{current_date.day:02d}")
        random.seed(random_seed)
        logger.info(f"在 get_random_avatar_image 中设置随机种子为: {random_seed}")
            
        # 随机选择一张图片
        random_image = random.choice(image_files)
        logger.info(f"随机选择的头像图片: {random_image}")
        return str(random_image)
        
    except Exception as e:
        logger.error(f"选择随机头像时出错: {e}")
        return None

# ============ HeyGen API 功能 ============

def load_heygen_cache(cache_file=HEYGEN_CACHE_FILE):
    """加载HeyGen API缓存"""
    try:
        if os.path.exists(cache_file):
            with open(cache_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"加载缓存失败: {e}, 将使用空缓存")
    return {}

def save_heygen_cache(cache, cache_file=HEYGEN_CACHE_FILE):
    """保存HeyGen API缓存"""
    try:
        # Ensure cache directory exists
        cache_dir = os.path.dirname(cache_file)
        os.makedirs(cache_dir, exist_ok=True)
        with open(cache_file, 'w') as f:
            json.dump(cache, f, indent=2)
    except Exception as e:
        logger.warning(f"保存缓存失败: {e}")

def file_hash(path):
    """计算文件的SHA-256哈希值"""
    h = hashlib.sha256()
    with open(path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            h.update(chunk)
    return h.hexdigest()

def upload_image_as_talking_photo(image_path):
    """上传图片并直接创建Talking Photo，返回talking_photo_id"""
    if not HEYGEN_API_KEY:
        raise ValueError("未设置HEYGEN_API_KEY环境变量")
        
    # 检查图片是否存在
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
    # 计算哈希值以用于缓存
    h = file_hash(image_path)
    
    # 检查缓存
    cache = load_heygen_cache()
    if h in cache and cache[h].get("type") == "talking_photo":
        logger.info(f"缓存命中talking_photo: {cache[h]['talking_photo_id']}")
        return cache[h]["talking_photo_id"]
    
    # 上传图片并创建Talking Photo
    content_type = "image/jpeg" if image_path.lower().endswith((".jpg", ".jpeg")) else "image/png"
    try:
        with open(image_path, "rb") as img:
            up = requests.post(
                HEYGEN_TALKING_PHOTO_URL,
                headers={"Content-Type": content_type, "X-Api-Key": HEYGEN_API_KEY},
                data=img
            )
        up.raise_for_status()
        
        data = up.json().get("data", {})
        talking_photo_id = data.get("talking_photo_id")
        
        if not talking_photo_id:
            raise ValueError(f"无法获取talking_photo_id, API返回: {up.text}")
        
        # 更新缓存
        cache[h] = {
            "type": "talking_photo", 
            "talking_photo_id": talking_photo_id,
            "timestamp": time.time()
        }
        save_heygen_cache(cache)
        
        logger.info(f"Talking Photo创建成功，ID: {talking_photo_id}")
        return talking_photo_id
    except Exception as e:
        logger.error(f"创建Talking Photo失败: {e}")
        raise

def upload_audio_asset(audio_path):
    """上传音频并返回audio_asset_id"""
    if not HEYGEN_API_KEY:
        raise ValueError("未设置HEYGEN_API_KEY环境变量")
        
    # 检查缓存
    audio_cache = load_heygen_cache(HEYGEN_AUDIO_CACHE_FILE)
    audio_hash = file_hash(audio_path)
    
    if audio_hash in audio_cache:
        logger.info(f"缓存命中音频资产: {audio_cache[audio_hash]['id']}")
        return audio_cache[audio_hash]['id']
    
    try:
        # 如果是 WAV，先转码为 MP3
        upload_path = audio_path
        if audio_path.lower().endswith('.wav'):
            logger.debug("检测到 WAV 格式，正在转码为 MP3")
            sound = AudioSegment.from_wav(audio_path)
            tmp = NamedTemporaryFile(suffix=".mp3", delete=False)
            sound.export(tmp.name, format="mp3")
            upload_path = tmp.name
        
        content_type = "audio/mpeg"
        with open(upload_path, "rb") as af:
            au = requests.post(
                HEYGEN_UPLOAD_URL,
                headers={"Content-Type": content_type, "X-Api-Key": HEYGEN_API_KEY},
                data=af
            )
        logger.debug(f"Audio upload response: {au.status_code}, {au.text}")
        au.raise_for_status()
        
        audio_data = au.json()["data"]
        audio_id = audio_data["id"]
        
        # 更新缓存
        audio_cache[audio_hash] = {
            "id": audio_id, 
            "timestamp": time.time()
        }
        save_heygen_cache(audio_cache, HEYGEN_AUDIO_CACHE_FILE)
        
        logger.info(f"音频上传成功，audio_asset_id: {audio_id}")
        return audio_id
    except Exception as e:
        logger.error(f"上传音频失败: {e}")
        raise
    finally:
        # 清理临时 MP3 文件
        if 'tmp' in locals() and upload_path != audio_path:
            try:
                os.unlink(upload_path)
            except Exception:
                pass

def download_video(video_url, output_path):
    """下载视频到指定路径"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        response = requests.get(video_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        if percent % 20 == 0:  # 每下载20%记录一次
                            logger.debug(f"视频下载进度: {percent:.1f}%")
        
        logger.info(f"视频下载完成: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"下载视频失败: {e}")
        raise

def generate_video_from_audio(
    audio_path: str,
    output_path: str,
    resolution: tuple = VIDEO_RESOLUTION,
    fps: int = VIDEO_FPS,
    background_color: str = DEFAULT_BACKGROUND_COLOR,
    using_cache: bool = True,
    lang_code: str = 'en'
) -> str:
    """从音频文件生成视频片段，使用HeyGen API
    
    Args:
        audio_path: 音频文件路径
        output_path: 输出视频路径
        resolution: 视频分辨率
        fps: 帧率
        background_color: 背景颜色(不使用)
        using_cache: 是否使用缓存
        lang_code: 语言代码
        
    Returns:
        str: 输出视频路径
    """
    try:
        # 验证输入参数
        if not audio_path or not os.path.exists(audio_path):
            raise ValueError(f"音频文件不存在: {audio_path}")
        if not output_path:
            raise ValueError("输出路径不能为空")
            
        logger.info(f"开始处理音频文件: {audio_path}")
        logger.info(f"输出路径: {output_path}")
        
        # 检查是否已经有缓存的视频文件
        if using_cache and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f"找到缓存视频，直接使用: {output_path}")
            return output_path
        
        # 获取头像路径并随机选择一张图片
        avatar_dir = get_avatar_path(lang_code)
        if not avatar_dir:
            raise ValueError(f"未找到语言 {lang_code} 的头像目录配置")
            
        avatar_image_path = get_random_avatar_image(avatar_dir)
        if not avatar_image_path:
            raise ValueError(f"无法从目录 {avatar_dir} 中获取头像图片")
        
        # 1. 上传图片并创建Talking Photo
        talking_photo_id = upload_image_as_talking_photo(avatar_image_path)
        if not talking_photo_id:
            raise ValueError("无法创建Talking Photo")
        
        # 2. 上传音频
        audio_id = upload_audio_asset(audio_path)
        if not audio_id:
            raise ValueError("上传音频失败")
        
        # 3. 请求生成视频
        try:
            payload = {
                "video_inputs": [
                    {
                        "character": {
                            "type": "talking_photo",
                            "talking_photo_id": talking_photo_id
                        },
                        "voice": {
                            "type": "audio",
                            "audio_asset_id": audio_id
                        }
                    }
                ],
                "dimension": {
                    "width": resolution[0],
                    "height": resolution[1]
                }
            }
            
            logger.debug(f"生成视频请求参数: {payload}")
            
            gv = requests.post(
                HEYGEN_GENERATE_VIDEO_URL,
                headers=HEYGEN_HEADERS_JSON,
                json=payload
            )
            gv.raise_for_status()
            
            resp_data = gv.json()
            if "error" in resp_data and resp_data["error"]:
                raise ValueError(f"API返回错误: {resp_data['error']}")
                
            video_id = resp_data["data"]["video_id"]
            logger.info(f"视频生成请求成功，video_id: {video_id}")
        except Exception as e:
            logger.error(f"请求生成视频失败: {e}")
            raise
        
        # 4. 轮询状态直到完成
        attempts = 0
        max_attempts = 60  # 最多轮询60次，每次5秒
        while attempts < max_attempts:
            try:
                vs = requests.get(
                    HEYGEN_VIDEO_STATUS_URL,
                    headers={"X-Api-Key": HEYGEN_API_KEY},
                    params={"video_id": video_id}
                )
                vs.raise_for_status()
                
                data = vs.json().get("data", {})
                status = data.get("status")
                
                logger.info(f"视频状态查询 ({attempts + 1}/{max_attempts}): {status}")
                
                if status == "completed":
                    video_url = data["video_url"]
                    logger.info(f"视频生成完成，URL: {video_url}")
                    # 下载视频
                    return download_video(video_url, output_path)
                elif status == "failed":
                    error_msg = data.get("error", "未知错误")
                    raise RuntimeError(f"视频生成失败: {error_msg}")
                    
                attempts += 1
                time.sleep(5)
            except requests.exceptions.RequestException as e:
                logger.warning(f"查询视频状态失败 (尝试 {attempts + 1}/{max_attempts}): {e}")
                attempts += 1
                time.sleep(5)
                continue
        
        raise TimeoutError("视频生成超时，请稍后检查HeyGen控制台")
        
    except Exception as e:
        logger.error(f"生成视频时出错: {e}")
        # 如果有部分生成的输出文件，删除它
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
                logger.info(f"已删除部分生成的文件: {output_path}")
            except:
                pass
        raise

def main():
    parser = argparse.ArgumentParser(description='将音频文件转换为视频片段')
    parser.add_argument('--audio', required=True, help='输入音频文件路径')
    parser.add_argument('--output', required=True, help='输出视频文件路径')
    parser.add_argument('--width', type=int, default=DEFAULT_RESOLUTION[0], help='视频宽度')
    parser.add_argument('--height', type=int, default=DEFAULT_RESOLUTION[1], help='视频高度')
    parser.add_argument('--fps', type=int, default=DEFAULT_FPS, help='视频帧率')
    parser.add_argument('--background', default=DEFAULT_BACKGROUND_COLOR, help='背景颜色')
    parser.add_argument('--lang', type=str, default='en', help='语言代码 (en, ja, es, pt)')
    
    args = parser.parse_args()
    
    try:
        generate_video_from_audio(
            args.audio,
            args.output,
            (args.width, args.height),
            args.fps,
            args.background,
            lang_code=args.lang
        )
    except Exception as e:
        logger.error(f"处理失败: {e}")
        raise

if __name__ == "__main__":
    main()