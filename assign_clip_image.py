#!/usr/bin/env python3
# assign_clip_image.py - Enhanced media assignment using BGE, BM25, Cross-Encoder, and CLIP post-filter

import os
import sys
import numpy as np
import json
import argparse
import logging
import copy
import math
import re
from typing import List, Dict, Any, Tu<PERSON>, Optional
from collections import defaultdict
from PIL import Image
import cv2
import torch
import warnings
import shutil
import io
import hashlib
from functools import lru_cache
from botocore.exceptions import ClientError


# BGE for text embeddings and reranking
from sentence_transformers import SentenceTransformer, CrossEncoder
# CLIP for visual post-filtering
from sentence_transformers import SentenceTransformer as ClipSentenceTransformer
# BLIP-2 for object-rich captions (still used for initial description generation)
from transformers import Blip2Processor, Blip2ForConditionalGeneration
# BM25 for sparse retrieval
from rank_bm25 import BM25Okapi
# For text processing in BM25
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
# AWS for object detection (still used for initial tag generation)
import boto3

# FAISS for dense retrieval
import faiss

# Import LLM interface for summarization
from modules.langchain_interface import call_llm_json_response
# Import modules from utils
from modules.utils import resize_large_image

# Import new modularized code
from modules.similarity_assignment import (
    preprocess, create_safe_zero_vector,
    get_clip_model_for_filter, get_bge_bi_encoder, get_bge_reranker,
    encode_image_clip_for_filter, encode_text_clip_for_filter, encode_text_bge,
    build_bge_index_and_data, calculate_bge_similarity_and_rerank,
    apply_clip_post_filter, enhanced_ilp_optimization
)

from modules.metadata_enrichment import (
    _to_jpeg_bytes, get_blip_models, generate_blip_caption,
    _rekognition_cached, extract_tags_rekognition,
    summarize_image_text_with_llm, enrich_image_metadata
)

# Import from existing modules
from config import logger, VECTOR_STORE_DIR, get_image_metadata_path, FILTERED_IMAGE_DIR
from modules.image_assignment import (update_attribute_json, get_valid_images, print_assignment_status,
                                     load_clips_data, assign_clips) # Removed ilp_optimization, use enhanced_ilp_optimization

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning, module='torch.amp.autocast_mode')
warnings.filterwarnings("ignore", category=FutureWarning)

# Check for MPS (Metal Performance Shaders) availability and CUDA
if torch.cuda.is_available():
    device = "cuda"
elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
    device = "mps"
else:
    device = "cpu"
print(f"Using primary device: {device}")


# Constants
NULL_REWARD = 0.2 # Keep for potential future use, but ILP handles null internally
REPEAT_PENALTY = 1 # Keep for ILP
MIN_DISTANCE_BETWEEN_REUSE = 3 # Default reuse distance for first round ILP
MAX_TOTAL_USES = 3 # Default max uses for fallback ILP calculation
MIN_WORDS = 20 # Keep for potential future use
MAX_WORDS = 40 # Keep for potential future use
TOP_K_RETRIEVAL = 20  # Number of candidates to retrieve using BGE bi-encoder
TOP_N_POST_FILTER = 5  # Number of top candidates to check with CLIP visual filter (from similarity_assignment)
MAX_DIVERSITY_PAIRS_PER_SEGMENT = 5000 # Keep for ILP diversity constraint
BGE_EMBEDDING_DIM = 768  # Dimension for BGE-base-en-v1.5
CLIP_EMBEDDING_DIM = 512  # Dimension for CLIP ViT-B/32 (used for post-filter)
# **: 绝对质量阈值：高相似度候选必须 ≥ 该分数
RERANKER_SCORE_MIN_THRESHOLD = 0.1
# Round 3 candidate control
TOP_M_FOR_ROUND3 = 10  # Max number of BGE candidates to consider per paragraph
BGE_SIMILARITY_THRESHOLD_ROUND3 = 0.2 # Min BGE cosine similarity in Round 3 (adjustable via CLI)
SECONDS_PER_IMAGE = 8

# Download nltk data if needed
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    logger.info("NLTK 'punkt' tokenizer not found. Attempting download...")
    try:
        nltk.download('punkt')
        logger.info("NLTK 'punkt' downloaded successfully.")
    except Exception as e:
        logger.warning(f"Failed to download NLTK 'punkt' tokenizer: {e}")
        logger.warning("Sentence tokenization might be less accurate. Proceeding anyway.")

# Add global flag for dry run mode
DRY_RUN = False


# 添加计算需要图片数量的函数
def calculate_num_images_needed(duration: float) -> int:
    """根据音频持续时间计算需要的图片数量

    Args:
        duration: 音频持续时间（秒）

    Returns:
        int: 需要的图片数量，公式为 ceil(duration / SECONDS_PER_IMAGE)
    """
    return math.ceil(duration / SECONDS_PER_IMAGE)

def load_existing_clips_assignment(paragraphs: List[Dict], clips_json_path: str, unassigned_indices: List[int]) -> int:
    """从已有的clips分配JSON中加载视频分配

    Args:
        paragraphs: 段落列表，将被就地修改
        clips_json_path: clips分配JSON文件路径
        unassigned_indices: 未分配的段落索引列表

    Returns:
        int: 成功分配的段落数量
    """
    try:
        with open(clips_json_path, 'r', encoding='utf-8') as f:
            clips_data = json.load(f)

        logger.info(f"Loading existing clips assignment from: {clips_json_path}")
        logger.info(f"Found {len(clips_data)} segments in clips assignment file")

        assigned_count = 0
        clips_dir = os.path.dirname(clips_json_path)  # CLIPS目录

        # 创建segment_id到clips数据的映射
        clips_by_segment = {}
        for clip_segment in clips_data:
            segment_id = str(clip_segment['segment_id'])
            clips_by_segment[segment_id] = clip_segment['assigned_clips']

        # 为未分配的段落分配视频
        for i in unassigned_indices:
            para = paragraphs[i]
            segment_id = str(para['segment_id'])

            if segment_id in clips_by_segment:
                assigned_clips = clips_by_segment[segment_id]

                if assigned_clips:  # 如果有分配的clips
                    best_clip = assigned_clips[0]  # 取相似度最高的
                    clip_filepath = os.path.join(clips_dir, best_clip['filename'])

                    # 检查文件是否存在
                    if os.path.exists(clip_filepath):
                        paragraphs[i]['assigned_media'] = {
                            'type': 'clip',
                            'filepath': clip_filepath,
                            'duration': best_clip.get('duration', 0.0),
                            'start_time': None,
                            'end_time': None,
                            'similarity': best_clip.get('similarity', 0.0),
                            'clip_similarity': None,
                            'source_round': 2
                        }
                        assigned_count += 1
                        logger.debug(f"Assigned clip to paragraph {segment_id}: {best_clip['filename']} (similarity: {best_clip.get('similarity', 0.0):.4f})")
                    else:
                        logger.warning(f"Clip file not found for segment {segment_id}: {clip_filepath}")
                else:
                    logger.debug(f"No clips assigned for segment {segment_id} in the clips JSON")
            else:
                logger.debug(f"Segment {segment_id} not found in clips assignment")

        logger.info(f"Successfully loaded clips assignment: {assigned_count} paragraphs assigned from existing clips")
        return assigned_count

    except FileNotFoundError:
        logger.error(f"Clips assignment file not found: {clips_json_path}")
        return 0
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding clips assignment JSON: {e}")
        return 0
    except Exception as e:
        logger.error(f"Error loading clips assignment: {e}")
        return 0

# --- Round 3: Assign remaining existing images ---
def assign_remaining_images(
    paragraphs: List[Dict],
    filtered_metadata: List[Dict],
    image_bge_embeddings: Optional[np.ndarray],
    device: str,
    max_usage_per_image: int = 1,
    min_distance_between_reuse: int = 2
) -> None:
    """
    Round 3 assignment: use all remaining unassigned images (ignore TOP_K_RETRIEVAL),
    assign to paragraphs not yet assigned after clip assignment.
    Single-image paragraphs via ILP, multi-image greedily.
    """
    global args
    # load Round 3 parameters from CLI or use defaults
    top_m_r3 = getattr(args, "top_m_round3", TOP_M_FOR_ROUND3)
    r3_thr = getattr(args, "round3_threshold", BGE_SIMILARITY_THRESHOLD_ROUND3)
    logger.info("Starting Round 3: Assign remaining existing images.")
    # 1. Identify unassigned paragraphs using the same calculation as Round 4 placeholders
    unassigned_single = []
    unassigned_multi = []
    for idx, para in enumerate(paragraphs):
        # Skip anchors and clips entirely - clips have higher priority than images
        assigned_type = para.get('assigned_media', {}).get('type')
        if assigned_type in ['anchor', 'clip']:
            continue

        # Determine how many images have already been assigned
        assigned_info = para.get('assigned_media', {})
        existing_paths = []
        if assigned_type == 'image' and assigned_info.get('filepath'):
            existing_paths = [assigned_info['filepath']]
        elif assigned_type == 'multi_image':
            existing_paths = list(assigned_info.get('filepaths', []))

        # Compute remaining needed images exactly as in Round 4
        total_needed = para.get('num_images_needed', 1)
        remaining_to_assign = total_needed - len(existing_paths)
        if remaining_to_assign <= 0:
            continue
        if remaining_to_assign == 1:
            unassigned_single.append(idx)
        else:
            unassigned_multi.append(idx)

    if not unassigned_single and not unassigned_multi:
        logger.info("Round 3: No paragraphs require assignment in this round.")
        return
    logger.info(f"Round 3: Identified {len(unassigned_single)} single-assign and {len(unassigned_multi)} multi-assign paragraphs for this round.")

    # 2. Collect used image paths from previous rounds
    used_image_filepaths = set()
    for para in paragraphs:
        media_info = para.get('assigned_media', {})
        media_type = media_info.get('type')
        if media_type == 'image' and media_info.get('filepath'):
            used_image_filepaths.add(media_info['filepath'])
        elif media_type == 'multi_image': # Fully assigned multi_image from R1
            used_image_filepaths.update(media_info.get('filepaths', []))
        # With all-or-nothing strategy, there should be no partial assignments to complete.

    logger.info(f"Round 3: Found {len(used_image_filepaths)} images used in prior assignments (R1 Images, R2 Clips don't add to this set).")

    # 3. Build list of candidate image meta_indices (all *unused* images from filtered_metadata with embeddings)
    candidate_meta_indices = []
    if image_bge_embeddings is not None:
        for i, meta in enumerate(filtered_metadata):
            if meta.get('filepath') not in used_image_filepaths and \
               i < image_bge_embeddings.shape[0] and \
               np.any(image_bge_embeddings[i]): # Ensure embedding exists and is not all zero
                candidate_meta_indices.append(i)

    if not candidate_meta_indices:
        logger.info("Round 3: No unused candidate images with valid embeddings available. Skipping.")
        return
    logger.info(f"Round 3: Found {len(candidate_meta_indices)} unused candidate images with embeddings for assignment.")

    candidate_filtered_metadata = [filtered_metadata[i] for i in candidate_meta_indices]
    candidate_bge_embeddings = image_bge_embeddings[candidate_meta_indices] if image_bge_embeddings is not None else None


    # 4. Compute paragraph embeddings for *all* paragraphs once, then select.
    # This matches the user's original simpler approach for this function.
    all_para_texts = [p.get('paragraph_text','') for p in paragraphs]
    all_para_embeddings = None
    if any(t.strip() for t in all_para_texts):
        all_para_embeddings = encode_text_bge(all_para_texts, device)
    else:
        logger.warning("Round 3: All paragraph texts in the script are empty. Using zero embeddings.")
        all_para_embeddings = np.zeros((len(all_para_texts), BGE_EMBEDDING_DIM), dtype=np.float32)


    # 5. Build similarity scores for each unassigned paragraph
    # candidate_scores: {orig_para_idx: [(local_candidate_idx, score, None), ...]}
    # where local_candidate_idx is index into candidate_filtered_metadata
    candidate_scores_for_assignment = {}
    relevant_para_indices_for_scoring = unassigned_single + unassigned_multi

    for para_idx in relevant_para_indices_for_scoring:
        if all_para_embeddings is None:
            continue  # Should not happen if check above is correct
        para_embedding = all_para_embeddings[para_idx]
        if not np.any(para_embedding) or candidate_bge_embeddings is None or candidate_bge_embeddings.shape[0] == 0:
            continue
        similarities = np.dot(para_embedding, candidate_bge_embeddings.T)  # (1xD) . (Ncand x D).T = (1 x Ncand)

        # build and sort all BGE scores, take top M, then filter by threshold
        all_scores = [(i, float(similarities[i])) for i in range(candidate_bge_embeddings.shape[0])]
        all_scores.sort(key=lambda x: x[1], reverse=True)
        top_scores = all_scores[:top_m_r3]
        current_para_sims = [(idx, score, None) for idx, score in top_scores if score >= r3_thr]

        if current_para_sims:
            candidate_scores_for_assignment[para_idx] = current_para_sims

    # The filtering of negative scores is now handled by the threshold above.
    # (Old negative-score filtering block removed.)

    # 6. Assign single-image paragraphs via ILP
    ilp_eligible_paras = [p_idx for p_idx in unassigned_single if p_idx in candidate_scores_for_assignment]

    if ilp_eligible_paras and candidate_filtered_metadata:
        logger.info(f"Round 3a ILP: Processing {len(ilp_eligible_paras)} single-image paragraphs.")
        scores_for_ilp = {p: candidate_scores_for_assignment[p] for p in ilp_eligible_paras}

        ilp_assignments = enhanced_ilp_optimization(
            ilp_eligible_paras,
            scores_for_ilp,
            candidate_filtered_metadata,
            candidate_bge_embeddings,
            max_usage_per_image=max_usage_per_image,
            min_distance_between_reuse=min_distance_between_reuse,
            prioritize_unused=True,
            null_image_reward=0.0
        )
        assigned_ilp_count = 0
        for para_idx, assigned_img_path in ilp_assignments:
            if assigned_img_path is not None:
                assigned_score = 0.0
                if para_idx in scores_for_ilp:
                    for local_cand_idx, score, _ in scores_for_ilp[para_idx]:
                        if 0 <= local_cand_idx < len(candidate_filtered_metadata) and \
                           candidate_filtered_metadata[local_cand_idx].get('filepath') == assigned_img_path:
                            assigned_score = score
                            break

                paragraphs[para_idx]['assigned_media'] = {
                    'type': 'image',
                    'filepath': assigned_img_path,
                    'similarity': assigned_score,
                    'clip_similarity': None,
                    'source_round': 3
                }
                used_image_filepaths.add(assigned_img_path) # Add to used set
                assigned_ilp_count +=1
        logger.info(f"Round 3a ILP: Assigned images to {assigned_ilp_count} single-image paragraphs.")
    else:
        logger.info("Round 3a ILP: Skipped.")

    # 7. Assign multi-image paragraphs greedily
    greedy_eligible_paras = [p_idx for p_idx in unassigned_multi if p_idx in candidate_scores_for_assignment]
    assigned_greedy_count = 0
    if greedy_eligible_paras and candidate_filtered_metadata:
        logger.info(f"Round 3b Greedy: Processing {len(greedy_eligible_paras)} multi-image paragraphs.")
        for para_idx in greedy_eligible_paras:
            if paragraphs[para_idx].get('assigned_media', {}).get('type') == 'image' and \
               paragraphs[para_idx]['assigned_media'].get('source_round') == 3:
                continue # Already assigned by ILP in this round

            num_total_needed = paragraphs[para_idx].get('num_images_needed', 1)

            existing_filepaths_for_para = []
            current_media_info = paragraphs[para_idx].get('assigned_media', {})
            if current_media_info.get('type') == 'multi_image':
                existing_filepaths_for_para = list(current_media_info.get('filepaths', []))

            num_still_needed = num_total_needed - len(existing_filepaths_for_para)
            if num_still_needed <= 0: continue

            para_cand_scores_sorted = candidate_scores_for_assignment.get(para_idx, [])
            newly_assigned_for_this_para = []
            current_best_score = 0.0

            for local_cand_idx, score, _ in para_cand_scores_sorted:
                if len(newly_assigned_for_this_para) >= num_still_needed: break

                image_path = candidate_filtered_metadata[local_cand_idx].get('filepath')

                if image_path and image_path not in used_image_filepaths and \
                   image_path not in existing_filepaths_for_para and \
                   image_path not in newly_assigned_for_this_para:
                    newly_assigned_for_this_para.append(image_path)
                    used_image_filepaths.add(image_path)
                    if score > current_best_score: current_best_score = score

            # ALL-OR-NOTHING: Only assign if we can satisfy ALL remaining image requirements
            if len(newly_assigned_for_this_para) == num_still_needed:
                final_paths = existing_filepaths_for_para + newly_assigned_for_this_para
                assigned_media_update = {
                    'type': 'multi_image', # Only assign when fully satisfied
                    'filepaths': final_paths,
                    'similarity': current_best_score,
                    'clip_similarity': None,
                    'source_round': 3
                }
                # Since we're using all-or-nothing, final_paths should always equal num_total_needed
                # Remove the partial assignment logic
                assigned_media_update.pop('needs_generated_image', None)
                assigned_media_update.pop('img_need_generate', None)

                paragraphs[para_idx]['assigned_media'] = assigned_media_update
                assigned_greedy_count +=1
                logger.debug(f"Round 3b: Para {para_idx} fully assigned {len(newly_assigned_for_this_para)} images (total {len(final_paths)} of {num_total_needed} needed).")
            else:
                # Cannot satisfy all requirements, skip assignment
                logger.debug(f"Round 3b: Para {para_idx} needs {num_still_needed} images but only found {len(newly_assigned_for_this_para)} available images. Skipping assignment (all-or-nothing policy).")
        logger.info(f"Round 3b Greedy: Fully assigned images to {assigned_greedy_count} multi-image paragraphs (all-or-nothing policy).")
    else:
        logger.info("Round 3b Greedy: Skipped.")
    logger.info("Finished Round 3: Assign remaining existing images.")

# --- Main Assignment Logic (Updated to match image_assignment.py flow) ---

def assign_images_enhanced(
    paragraphs: List[Dict],
    image_dir: str,
    clip_dir: Optional[str], # Made optional
    theme: str,
    using_anchor: bool = False,
    generate_image: bool = False,
    max_anchor_sec: float = 30.0 # Keep anchor logic
) -> List[Dict]:
    """
    Enhanced assignment using BGE/BM25/Reranker/CLIP, but following the
    workflow defined in image_assignment.py:
    1. High-similarity image assignment (prioritizing single, then multi).
    2. Clip assignment.
    3. Assignment of remaining existing images.
    4. Optional placeholder generation OR fallback assignment (anchor, final ILP).
    """
    try:
        # 1. Initial Metadata Update & Enrichment
        attribute_json_path = get_image_metadata_path(image_dir, theme)
        # Basic update (e.g., find new files, remove deleted)
        update_attribute_json(attribute_json_path, image_dir)
        # Adds captions, tags, and summary using BLIP, Rekognition, LLM
        enrich_image_metadata(attribute_json_path, image_dir, theme, device, DRY_RUN)

        # 2. Get Valid Images and Load Metadata
        valid_image_paths = get_valid_images(image_dir)
        valid_image_paths = list(dict.fromkeys([os.path.abspath(img) for img in valid_image_paths]))
        logger.info(f"Valid images found after enrichment: {len(valid_image_paths)}")

        if not valid_image_paths:
            logger.warning("No valid images found. Skipping assignment.")
            if generate_image:
                # Mark all non-anchor paragraphs as pending
                for i, para in enumerate(paragraphs):
                    if para.get('assigned_media', {}).get('type') != 'anchor':
                        paragraphs[i]['assigned_media'] = {
                            'type': 'pending',
                            'filepath': None,
                            'duration': para.get('audio', {}).get('duration', 0),
                            'start_time': None,
                            'end_time': None,
                            'images_needed': para.get('num_images_needed', 1)
                        }
                logger.info("Marked all assignable paragraphs for image generation as no valid images were found.")
            return paragraphs

        try:
            with open(attribute_json_path, 'r', encoding='utf-8') as f:
                image_metadata = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Failed to load metadata file {attribute_json_path}: {e}. Cannot proceed.")
            return paragraphs

        # 3. Build Indices, Get Embeddings and Candidates
        faiss_index, bm25_index, faiss_id_to_meta_idx, filtered_metadata, image_bge_embeddings = build_bge_index_and_data(
            image_metadata,
            valid_image_paths,
            theme,
            attribute_json_path,
            device
        )

        if faiss_index is None or not filtered_metadata:
            logger.error("Failed to build necessary indices or find valid data. Aborting assignment.")
            if generate_image:
                 # Mark as pending if generate_image is true
                 for i, para in enumerate(paragraphs):
                     if para.get('assigned_media', {}).get('type') != 'anchor':
                         paragraphs[i]['assigned_media'] = {
                             'type': 'pending',
                             'filepath': None,
                             'duration': para.get('audio', {}).get('duration', 0),
                             'start_time': None,
                             'end_time': None,
                             'images_needed': para.get('num_images_needed', 1)
                         }
                 logger.info("Marked all assignable paragraphs for image generation as index building failed.")
            return paragraphs

        # Calculate Similarity, Rerank, Fuse Scores
        # Returns: Dict[int, List[Tuple[int, float]]] -> {para_idx: [(meta_idx, fused_score), ...]}
        # Note: This uses the reranker score as the primary score now.
        reranked_results = calculate_bge_similarity_and_rerank(
            paragraphs,
            device,
            faiss_index,
            bm25_index,
            faiss_id_to_meta_idx,
            filtered_metadata
        )

        # Apply CLIP Visual Post-Filter
        # Returns: Dict[int, List[Tuple[int, float, Optional[float]]]] -> {para_idx: [(meta_idx, rerank_score, clip_score), ...]}
        final_candidates = apply_clip_post_filter(
            reranked_results,
            paragraphs,
            filtered_metadata,
            device
        )

        # ---------- Round-1 阈值 ----------
        # Access args from the main function context where parse_args() result is available
        # We need to pass 'args' to assign_images_enhanced or access it globally if set in main
        # Assuming args is accessible here. If not, it needs to be passed as an argument.
        # For now, let's assume it's accessible via a closure or global scope (less ideal)
        # A better approach would be to pass 'args' to this function.
        # Let's modify the function signature later if needed.
        # We'll access it via the global `args` variable set in `main` for now.
        global args # Need to declare global if accessing args set in main()
        user_thr = getattr(args, "reranker_threshold", None)  # argparse 会自动给到
        if user_thr is not None:
            RERANKER_SCORE_THRESHOLD_FIRST = user_thr
            logger.info(f"使用用户指定的 reranker 阈值: {RERANKER_SCORE_THRESHOLD_FIRST:.4f}")
        else:
            RERANKER_SCORE_THRESHOLD_FIRST = RERANKER_SCORE_MIN_THRESHOLD
            logger.info(f"使用默认 reranker 阈值: {RERANKER_SCORE_THRESHOLD_FIRST:.4f}")
        # ----------------------------------

        # --- Round 1: High Similarity Image Assignment ---
        logger.info(f"Round 1: High Similarity Image Assignment (Reranker Score > {RERANKER_SCORE_THRESHOLD_FIRST:.4f})")

        # Identify paragraphs needing assignment (not already assigned anchor/clip)
        unassigned_single_indices_r1 = []
        unassigned_multi_indices_r1 = []
        for i, para in enumerate(paragraphs):
            media_type = para.get('assigned_media', {}).get('type')
            # Assign if 'pending' or not set, exclude 'anchor' and 'clip'
            if media_type == 'pending' or media_type is None:
                num_needed = para.get('num_images_needed', 1)
                if num_needed == 1:
                    unassigned_single_indices_r1.append(i)
                elif num_needed > 1:
                     # Check if already partially assigned (shouldn't happen here, but safety check)
                     filepaths = para.get('assigned_media', {}).get('filepaths', [])
                     if len(filepaths) < num_needed:
                         unassigned_multi_indices_r1.append(i)
                # else: # num_needed <= 0? ignore
            # Handle case where a multi_image was somehow assigned but incomplete
            elif media_type == 'multi_image':
                num_needed = para.get('num_images_needed', 1)
                filepaths = para.get('assigned_media', {}).get('filepaths', [])
                if len(filepaths) < num_needed:
                    unassigned_multi_indices_r1.append(i)


        logger.info(f"Round 1: Found {len(unassigned_single_indices_r1)} single-image and {len(unassigned_multi_indices_r1)} multi-image paragraphs needing assignment.")

        # Prepare high-similarity candidates for ILP and greedy assignment
        high_sim_candidates = {}
        high_sim_meta_indices_set = set()
        logger.debug("--- R1 Candidate Scores (Before Threshold) ---")
        for para_idx, candidates in final_candidates.items():
            # Skip paragraphs already assigned (e.g., anchor)
            if paragraphs[para_idx].get('assigned_media', {}).get('type') not in ['pending', None]:
                 continue

            para_high_sim = []
            # Sort candidates by rerank_score to easily see the best ones
            sorted_candidates = sorted(candidates, key=lambda x: x[1], reverse=True)
            # Take only the top‐K highest‐scoring candidates, then sort

            for i, (meta_idx, rerank_score, clip_score) in enumerate(sorted_candidates):
                 # Log top few scores for brevity
                #  if i < 5: # Log only top 5 scores
                #     logger.debug(f"  Rank {i+1}: MetaIdx {meta_idx}, Rerank={rerank_score:.4f}, CLIP={clip_score}")

                 # Existing threshold check
                 if rerank_score >= RERANKER_SCORE_THRESHOLD_FIRST:
                     para_high_sim.append((meta_idx, rerank_score, clip_score))
                     high_sim_meta_indices_set.add(meta_idx)
                 # else: # Optional: log scores that *failed* the check (might be too verbose)
                 #    if i < 5: logger.debug(f"    -> Failed Threshold ({RERANKER_SCORE_THRESHOLD_FIRST:.4f})")

            if para_high_sim:
                high_sim_candidates[para_idx] = para_high_sim # Store the filtered high-sim candidates
        logger.debug("--- End R1 Candidate Scores ---")

        if not high_sim_meta_indices_set:
             logger.warning("Round 1: No images met the high similarity threshold. Skipping image assignment for this round.")
        else:
            # Filter metadata and embeddings for high-similarity images
            high_sim_meta_indices_list = sorted(list(high_sim_meta_indices_set))
            high_sim_filtered_metadata = [filtered_metadata[i] for i in high_sim_meta_indices_list if 0 <= i < len(filtered_metadata)]
            # Map original meta_idx to its new index in the high_sim list
            meta_idx_to_high_sim_idx = {orig_idx: new_idx for new_idx, orig_idx in enumerate(high_sim_meta_indices_list)}

            # Adjust high_sim_candidates to use the new high_sim index
            high_sim_candidates_adjusted = {}
            for para_idx, candidates in high_sim_candidates.items():
                 adjusted_cand = []
                 for meta_idx, rerank_score, clip_score in candidates:
                      if meta_idx in meta_idx_to_high_sim_idx:
                           new_meta_idx = meta_idx_to_high_sim_idx[meta_idx]
                           adjusted_cand.append((new_meta_idx, rerank_score, clip_score))
                 if adjusted_cand:
                      high_sim_candidates_adjusted[para_idx] = adjusted_cand

            # Get corresponding embeddings
            high_sim_bge_embeddings = None
            if image_bge_embeddings is not None:
                try:
                     high_sim_bge_embeddings = image_bge_embeddings[high_sim_meta_indices_list]
                except IndexError:
                     logger.error("Error slicing BGE embeddings for high similarity round. Skipping ILP diversity.")
                     high_sim_bge_embeddings = None


            # Track image usage within Round 1
            used_image_paths_round1 = set()
            assigned_paras_r1 = set()

            # 1a. Prioritize Single-Image Paragraphs with ILP
            if unassigned_single_indices_r1 and high_sim_filtered_metadata:
                logger.info(f"Round 1a: Assigning high-similarity images to {len(unassigned_single_indices_r1)} single-image paragraphs via ILP (max_usage=1).")
                # Ensure we only pass relevant paragraphs to ILP
                paras_for_ilp_r1 = [p for p in unassigned_single_indices_r1 if p in high_sim_candidates_adjusted]
                if paras_for_ilp_r1:
                    # Pass adjusted candidates and high_sim metadata/embeddings
                    round1_single_assignments = enhanced_ilp_optimization(
                        paras_for_ilp_r1,
                        high_sim_candidates_adjusted, # Use adjusted candidates with new meta indices
                        high_sim_filtered_metadata,   # Use filtered metadata
                        high_sim_bge_embeddings,      # Use filtered embeddings
                        max_usage_per_image=1,        # Strict single use
                        min_distance_between_reuse=3, # Less relevant here, but keep default
                        prioritize_unused=True,       # Prioritize coverage
                        null_image_reward=0.0 # Penalize null assignments strongly in this round
                    )

                    # Update paragraph assignments and track usage
                    for para_idx, img_path in round1_single_assignments:
                        if img_path is not None:
                            assigned_rerank_score = 0.0
                            assigned_clip_score = None
                            # Find scores from original high_sim_candidates (using original meta_idx)
                            original_meta_idx = -1
                            if para_idx in high_sim_candidates:
                                for meta_idx, rerank_score, clip_score in high_sim_candidates[para_idx]:
                                     # Check if metadata entry exists and path matches
                                     if 0 <= meta_idx < len(filtered_metadata) and filtered_metadata[meta_idx].get('filepath') == img_path:
                                         assigned_rerank_score = rerank_score
                                         assigned_clip_score = clip_score
                                         original_meta_idx = meta_idx # For logging if needed
                                         break

                            paragraphs[para_idx]['assigned_media'] = {
                                'type': 'image',
                                'filepath': img_path,
                                'duration': None, 'start_time': None, 'end_time': None,
                                'similarity': float(assigned_rerank_score),
                                'clip_similarity': assigned_clip_score
                            }
                            used_image_paths_round1.add(img_path)
                            assigned_paras_r1.add(para_idx)
                            # logger.debug(f"R1a ILP: Para {para_idx} assigned {os.path.basename(img_path)} (Score: {assigned_rerank_score:.4f}, MetaIdx: {original_meta_idx})")
                        # else: Para assigned null by ILP, remains unassigned for now

                    logger.info(f"Round 1a: ILP assigned images to {len(assigned_paras_r1)} single-image paragraphs.")
                else:
                    logger.info("Round 1a: No single-image paragraphs had high-similarity candidates.")
            else:
                logger.info("Round 1a: No single-image paragraphs to assign or no high-similarity images found.")

            # 1b. Handle Multi-Image Paragraphs Greedily
            if unassigned_multi_indices_r1 and high_sim_filtered_metadata:
                logger.info(f"Round 1b: Assigning high-similarity images to {len(unassigned_multi_indices_r1)} multi-image paragraphs.")
                assigned_multi_count = 0
                for para_idx in unassigned_multi_indices_r1:
                     # Skip if already assigned by ILP somehow (shouldn't happen)
                     if para_idx in assigned_paras_r1: continue

                     # Get needed count, considering any potentially existing images
                     num_total_needed = paragraphs[para_idx].get('num_images_needed', 1)
                     existing_filepaths = paragraphs[para_idx].get('assigned_media', {}).get('filepaths', [])
                     num_still_needed = num_total_needed - len(existing_filepaths)

                     if num_still_needed <= 0: continue # Already fulfilled

                     # Get high-similarity candidates for this paragraph (using original meta_idx)
                     candidates_for_para = sorted(
                          high_sim_candidates.get(para_idx, []),
                          key=lambda x: x[1], # Sort by rerank_score (index 1)
                          reverse=True
                     )

                     newly_assigned_filepaths = []
                     for meta_idx, rerank_score, clip_score in candidates_for_para:
                          if len(newly_assigned_filepaths) >= num_still_needed: break # Found enough

                          # Check if metadata entry is valid
                          if not (0 <= meta_idx < len(filtered_metadata)): continue

                          current_path = filtered_metadata[meta_idx].get('filepath')
                          # Check if path is valid, not already used in this round, and not already in existing paths
                          if current_path and current_path not in used_image_paths_round1 and current_path not in existing_filepaths:
                               newly_assigned_filepaths.append(current_path)
                               used_image_paths_round1.add(current_path) # Mark as used for R1

                     # ALL-OR-NOTHING: Only assign if we can satisfy ALL image requirements
                     if len(newly_assigned_filepaths) == num_still_needed:
                          final_filepaths = existing_filepaths + newly_assigned_filepaths
                          # Use score of the best newly added image
                          best_new_score = 0.0
                          best_clip_score = None
                          # Find the best score among the *newly added* candidates
                          newly_added_scores = []
                          for meta_idx, rerank_score, clip_score in candidates_for_para:
                              cand_path = filtered_metadata[meta_idx].get('filepath') if 0 <= meta_idx < len(filtered_metadata) else None
                              if cand_path in newly_assigned_filepaths:
                                  newly_added_scores.append((rerank_score, clip_score))
                          if newly_added_scores:
                              newly_added_scores.sort(key=lambda x: x[0], reverse=True)
                              best_new_score = newly_added_scores[0][0]
                              best_clip_score = newly_added_scores[0][1]

                          paragraphs[para_idx]['assigned_media'] = {
                              'type': 'multi_image', # Only assign when fully satisfied
                              'filepaths': final_filepaths,
                              'duration': None, 'start_time': None, 'end_time': None,
                              'similarity': float(best_new_score),
                              'clip_similarity': best_clip_score
                          }
                          assigned_paras_r1.add(para_idx)
                          assigned_multi_count += 1
                          logger.debug(f"R1b Greedy: Para {para_idx} fully assigned {len(newly_assigned_filepaths)} new images (total {len(final_filepaths)} of {num_total_needed} needed).")
                     else:
                         # Cannot satisfy all requirements, skip assignment
                         logger.debug(f"R1b Greedy: Para {para_idx} needs {num_still_needed} images but only found {len(newly_assigned_filepaths)} high-sim images. Skipping assignment (all-or-nothing policy).")

                logger.info(f"Round 1b: Fully assigned images to {assigned_multi_count} multi-image paragraphs (all-or-nothing policy).")
            else:
                logger.info("Round 1b: No multi-image paragraphs to assign or no high-similarity images found.")

        logger.info("Round 1: High Similarity Assignment finished.")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 1 - High Similarity")


        # --- Round 2: Video Clip Assignment ---
        # Re-identify unassigned paragraphs after Round 1
        unassigned_indices_r2 = [
            i for i, para in enumerate(paragraphs)
            if para.get('assigned_media', {}).get('type') in ['pending', None] or \
               (para.get('assigned_media', {}).get('type') == 'multi_image' and \
                len(para.get('assigned_media', {}).get('filepaths', [])) < para.get('num_images_needed', 1))
        ]

        logger.info(f"Round 2: Video Clip Assignment for {len(unassigned_indices_r2)} paragraphs.")

        # Check if we should load existing clips assignment
        if hasattr(args, 'clips_json') and args.clips_json and os.path.exists(args.clips_json):
            logger.info("Round 2: Loading existing clips assignment from JSON file.")
            assigned_count = load_existing_clips_assignment(paragraphs, args.clips_json, unassigned_indices_r2)
            logger.info(f"Round 2: Loaded clips assignment for {assigned_count} paragraphs from existing JSON.")
        elif clip_dir and os.path.exists(clip_dir) and unassigned_indices_r2:
            logger.info("Round 2: Performing new clip retrieval and assignment.")
            clips_data = load_clips_data(clip_dir, theme)
            if clips_data:
                # Calculate video clip similarity matrix using BGE
                clip_descriptions = [clip.get('description', '') for clip in clips_data]
                # Encode paragraphs and descriptions using BGE
                # Encode all paras once, reuse embeddings
                # Ensure paragraph_embeddings_r2 is calculated if not already available
                if 'paragraph_embeddings_r2' not in locals(): # Calculate if not done already
                     paragraph_texts_all = [p.get('paragraph_text', '') for p in paragraphs]
                     paragraph_embeddings_r2 = encode_text_bge(paragraph_texts_all, device)

                clip_embeddings = encode_text_bge(clip_descriptions, device)

                # Build similarity matrix only for relevant paras and valid clips
                clip_similarity_matrix = np.zeros((len(paragraphs), len(clip_descriptions)))
                valid_clip_embed_mask = np.any(clip_embeddings, axis=1)
                valid_clip_indices = np.where(valid_clip_embed_mask)[0]
                valid_clip_embeddings = clip_embeddings[valid_clip_indices]

                if valid_clip_embeddings.shape[0] > 0:
                    for i in unassigned_indices_r2: # Only calculate for needed paras
                        para_embedding = paragraph_embeddings_r2[i]
                        if not np.any(para_embedding): continue # Skip if para embed failed
                        sims = np.dot(para_embedding, valid_clip_embeddings.T)
                        clip_similarity_matrix[i, valid_clip_indices] = sims

                # Assign clips (using the original assign_clips logic from image_assignment)
                assign_clips(
                    paragraphs, # Modifies paragraphs in-place
                    clips_data,
                    clip_similarity_matrix,
                    unassigned_indices=unassigned_indices_r2, # Tell it which ones to focus on
                    using_anchor=using_anchor # Pass anchor status if needed by assign_clips
                )
                logger.info("Round 2: Clip assignment finished.")
            else:
                logger.info("Round 2: No clip data found.")
        else:
            if hasattr(args, 'clips_json') and args.clips_json:
                logger.warning(f"Round 2: Clips JSON file specified but not found: {args.clips_json}")
            logger.info("Round 2: Skipping clip assignment (no clips_json, no clip_dir, or no unassigned paras).")

        logger.info("Assignment Status After Round 2 (Clip Assignment):")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 2 - Clip Assignment")

        # --- Round 3: Assign remaining existing images (New) ---
        logger.info("Initiating Round 3: Assign remaining existing images.")
        if filtered_metadata and image_bge_embeddings is not None:
             assign_remaining_images(
                 paragraphs,
                 filtered_metadata,
                 image_bge_embeddings,
                 device,
                 max_usage_per_image=1, # Default, can be tuned via args if exposed
                 min_distance_between_reuse=2 # Default, can be tuned
             )
        else:
            logger.info("Round 3: Skipped assignment of remaining images (no metadata/embeddings).")
        logger.info("Assignment Status After Round 3 (Remaining Images Assignment):")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 3 - Remaining Images")


        # --- Round 4: Placeholder Generation or Fallback --- (Renumbered from Round 3)
        # Identify final unassigned paragraphs (including partially assigned multi-image)
        # IMPORTANT: Skip paragraphs that already have clips assigned, as clips are generally better than poor image matches
        unassigned_indices_final = [
            i for i, para in enumerate(paragraphs)
            if para.get('assigned_media', {}).get('type') in ['pending', None] or \
               (para.get('assigned_media', {}).get('type') == 'multi_image' and \
                len(para.get('assigned_media', {}).get('filepaths', [])) < para.get('num_images_needed', 1))
        ]

        # Filter out paragraphs that already have clips - clips are better than poor image matches
        unassigned_indices_final = [
            i for i in unassigned_indices_final
            if paragraphs[i].get('assigned_media', {}).get('type') != 'clip'
        ]

        logger.info(f"End of Round 3 (Image Assignments): {len(unassigned_indices_final)} paragraphs remain unassigned or partially assigned before fallback/generation (excluding clip-assigned segments).")

        # Option 1: Generate Placeholders
        if generate_image and unassigned_indices_final:
            logger.info("Round 4: Creating image generation placeholders...")
            placeholder_count = 0
            for idx in unassigned_indices_final:
                num_total_needed = paragraphs[idx].get('num_images_needed', 1)

                # Mark them for image generation based on their full requirements
                paragraphs[idx]['assigned_media'] = {
                    'type': 'pending',
                    'filepath': None,
                    'duration': paragraphs[idx].get('audio', {}).get('duration', 0),
                    'start_time': None,
                    'end_time': None,
                    'images_needed': num_total_needed,  # Store how many images this segment needs
                    'generation_required': True  # Flag for image_generation.py
                }
                logger.debug(f"Paragraph {idx} marked for image generation ({num_total_needed} images needed).")
                placeholder_count += num_total_needed

            if placeholder_count > 0:
                logger.info(f"Round 4: Created placeholders for {placeholder_count} images to be generated.")
            logger.info("Final Assignment Status (with placeholders):")
            print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 4 - Placeholders Generated")
            return paragraphs

        # Option 2: Fallback Assignment (if not generate_image and paras remain unassigned)
        elif unassigned_indices_final:
            logger.info(f"Round 4: Fallback Assignment for {len(unassigned_indices_final)} paragraphs.")

            # 4a. Anchor Assignment (copied from image_assignment.py logic)
            if using_anchor:
                logger.info("Round 4a: Applying anchor fallback.")
                total_paragraphs = len(paragraphs)
                max_anchor_fraction = 0.1 # Max 10% anchors total
                # Count existing anchors (could be initial or from clip logic if it assigns anchors)
                current_anchor_count = sum(1 for p in paragraphs if p.get('assigned_media',{}).get('type') == 'anchor')
                max_anchor_count = math.ceil(total_paragraphs * max_anchor_fraction)
                allowed_new_anchors = max_anchor_count - current_anchor_count

                if allowed_new_anchors > 0:
                    # Select candidates from the currently unassigned list
                    anchor_candidates = unassigned_indices_final[:min(allowed_new_anchors, len(unassigned_indices_final))]
                    for idx in anchor_candidates:
                        paragraphs[idx]['assigned_media'] = {
                            'type': 'anchor',
                            'filepath': None,
                            'duration': paragraphs[idx].get('audio', {}).get('duration', 0),
                            'start_time': None,
                            'end_time': None
                        }
                    logger.info(f"Round 4a: Assigned {len(anchor_candidates)} new anchors.")
                    # Update the list of paragraphs still needing assignment
                    unassigned_indices_final = [idx for idx in unassigned_indices_final if idx not in anchor_candidates]
                    logger.info(f"Round 4a: {len(unassigned_indices_final)} paragraphs remaining after anchor fallback.")
                else:
                    logger.info("Round 4a: No more anchor quota available or no unassigned paragraphs.")
            else:
                 logger.info("Round 4a: Anchor fallback skipped (using_anchor=False).")

            # 4b. Enhanced Fallback Assignment with Relaxed Constraints
            if unassigned_indices_final and filtered_metadata:
                logger.info(f"Round 4b: Applying enhanced fallback assignment for {len(unassigned_indices_final)} paragraphs.")

                # Count unused images to determine strategy
                used_images = set()
                for para in paragraphs:
                    media = para.get('assigned_media', {})
                    if media.get('type') == 'image':
                        used_images.add(media.get('filepath'))
                    elif media.get('type') == 'multi_image':
                        used_images.update(media.get('filepaths', []))

                unused_images = [meta for meta in filtered_metadata
                               if meta.get('filepath') not in used_images]

                logger.info(f"Fallback: {len(unused_images)} unused images available for assignment")

                # Determine fallback strategy based on available resources
                if len(unused_images) >= len(unassigned_indices_final):
                    # Strategy 1: Enough unused images - use relaxed similarity threshold
                    fallback_min_similarity = 0.05  # Very relaxed threshold
                    fallback_null_reward = 0.05     # Low null reward to encourage assignment
                    max_usage_fallback = 1          # No reuse needed
                    logger.info("Fallback Strategy 1: Sufficient unused images, using relaxed similarity threshold")
                else:
                    # Strategy 2: Limited images - allow controlled reuse with moderate threshold
                    fallback_min_similarity = 0.10  # Moderate threshold
                    fallback_null_reward = 0.10     # Moderate null reward
                    max_usage_fallback = min(3, math.ceil(len(unassigned_indices_final) / len(unused_images)) + 1)
                    logger.info(f"Fallback Strategy 2: Limited images, allowing max {max_usage_fallback} uses per image")

                # Collect all candidates for fallback (with relaxed threshold)
                fallback_candidates = {}
                for para_idx in unassigned_indices_final:
                    candidates = final_candidates.get(para_idx, [])
                    if candidates:
                        # Use relaxed threshold for fallback
                        good_candidates = [(meta_idx, score, clip_score) for meta_idx, score, clip_score in candidates
                                         if score >= fallback_min_similarity]
                        if good_candidates:
                            fallback_candidates[para_idx] = good_candidates

                logger.info(f"Fallback: {len(fallback_candidates)} paragraphs have candidates above relaxed threshold ({fallback_min_similarity:.3f})")

                if fallback_candidates:
                    # Run ILP with relaxed constraints
                    fallback_assignments = enhanced_ilp_optimization(
                        unassigned_indices_final,
                        fallback_candidates,
                        filtered_metadata,
                        image_bge_embeddings,
                        max_usage_per_image=max_usage_fallback,
                        min_distance_between_reuse=1,  # Relaxed distance constraint
                        prioritize_unused=True,        # Still prefer unused images
                        null_image_reward=fallback_null_reward
                    )

                    # Process assignments with flexible strategy
                    assigned_fallback_count = 0
                    for para_idx, img_path in fallback_assignments:
                        if img_path is not None:
                            assigned_fallback_count += 1
                            assigned_rerank_score = 0.0
                            assigned_clip_score = None

                            # Find scores from fallback_candidates
                            if para_idx in fallback_candidates:
                                for meta_idx, rerank_score, clip_score in fallback_candidates[para_idx]:
                                    if 0 <= meta_idx < len(filtered_metadata) and filtered_metadata[meta_idx].get('filepath') == img_path:
                                        assigned_rerank_score = rerank_score
                                        assigned_clip_score = clip_score
                                        break

                            # Assign single image (all-or-nothing still applies for multi-image)
                            paragraphs[para_idx]['assigned_media'] = {
                                'type': 'image',
                                'filepath': img_path,
                                'duration': None, 'start_time': None, 'end_time': None,
                                'similarity': float(assigned_rerank_score),
                                'clip_similarity': assigned_clip_score,
                                'fallback_assignment': True,
                                'fallback_strategy': 1 if len(unused_images) >= len(unassigned_indices_final) else 2
                            }

                    logger.info(f"Round 4b: Fallback assigned images to {assigned_fallback_count} paragraphs using relaxed constraints.")

                    # Update unassigned list for any remaining paragraphs
                    remaining_unassigned = [idx for idx in unassigned_indices_final
                                          if paragraphs[idx].get('assigned_media', {}).get('type') == 'pending']

                    if remaining_unassigned:
                        logger.info(f"Round 4b: {len(remaining_unassigned)} paragraphs remain unassigned after initial fallback.")

                        # 4c. Ultra-Relaxed Fallback (降低阈值到接近0)
                        logger.info(f"Round 4c: Applying ultra-relaxed fallback for {len(remaining_unassigned)} paragraphs.")

                        ultra_relaxed_candidates = {}
                        for para_idx in remaining_unassigned:
                            candidates = final_candidates.get(para_idx, [])
                            if candidates:
                                # 使用极低阈值，基本接受所有候选
                                ultra_candidates = [(meta_idx, score, clip_score) for meta_idx, score, clip_score in candidates
                                                  if score >= 0.01]  # 极低阈值
                                if ultra_candidates:
                                    ultra_relaxed_candidates[para_idx] = ultra_candidates

                        if ultra_relaxed_candidates:
                            logger.info(f"Round 4c: {len(ultra_relaxed_candidates)} paragraphs have ultra-relaxed candidates.")

                            # 允许更高的重复使用率
                            ultra_max_usage = min(5, math.ceil(len(remaining_unassigned) / len(filtered_metadata)) + 2)

                            ultra_assignments = enhanced_ilp_optimization(
                                remaining_unassigned,
                                ultra_relaxed_candidates,
                                filtered_metadata,
                                image_bge_embeddings,
                                max_usage_per_image=ultra_max_usage,
                                min_distance_between_reuse=1,
                                prioritize_unused=False,  # 不再优先未使用图片
                                null_image_reward=0.01    # 极低null奖励，强烈鼓励分配
                            )

                            ultra_assigned_count = 0
                            for para_idx, img_path in ultra_assignments:
                                if img_path is not None:
                                    ultra_assigned_count += 1
                                    assigned_rerank_score = 0.0
                                    assigned_clip_score = None

                                    # 找到分数
                                    if para_idx in ultra_relaxed_candidates:
                                        for meta_idx, rerank_score, clip_score in ultra_relaxed_candidates[para_idx]:
                                            if 0 <= meta_idx < len(filtered_metadata) and filtered_metadata[meta_idx].get('filepath') == img_path:
                                                assigned_rerank_score = rerank_score
                                                assigned_clip_score = clip_score
                                                break

                                    paragraphs[para_idx]['assigned_media'] = {
                                        'type': 'image',
                                        'filepath': img_path,
                                        'duration': None, 'start_time': None, 'end_time': None,
                                        'similarity': float(assigned_rerank_score),
                                        'clip_similarity': assigned_clip_score,
                                        'fallback_assignment': True,
                                        'fallback_strategy': 'ultra_relaxed'
                                    }

                            logger.info(f"Round 4c: Ultra-relaxed fallback assigned {ultra_assigned_count} paragraphs.")

                            # 更新剩余未分配列表
                            remaining_unassigned = [idx for idx in remaining_unassigned
                                                  if paragraphs[idx].get('assigned_media', {}).get('type') == 'pending']

                        # 4d. 最后手段：随机分配 (确保100%覆盖)
                        if remaining_unassigned:
                            logger.info(f"Round 4d: Applying final random assignment for {len(remaining_unassigned)} paragraphs.")

                            # 使用所有可用图片进行随机分配
                            available_images = [meta.get('filepath') for meta in filtered_metadata if meta.get('filepath')]

                            if available_images:
                                import random
                                random.seed(42)  # 确保可重现性

                                final_assigned_count = 0
                                for para_idx in remaining_unassigned:
                                    # 随机选择一张图片
                                    selected_image = random.choice(available_images)

                                    # 检查是否会在同一segment内重复使用
                                    num_images_needed = paragraphs[para_idx].get('num_images_needed', 1)
                                    if num_images_needed > 1:
                                        # 多图段落：确保不重复使用同一张图片
                                        selected_images = []
                                        available_for_multi = available_images.copy()

                                        for _ in range(min(num_images_needed, len(available_for_multi))):
                                            if available_for_multi:
                                                img = random.choice(available_for_multi)
                                                selected_images.append(img)
                                                available_for_multi.remove(img)  # 避免在同一segment内重复

                                        if len(selected_images) == num_images_needed:
                                            # 完整分配
                                            paragraphs[para_idx]['assigned_media'] = {
                                                'type': 'multi_image',
                                                'filepaths': selected_images,
                                                'duration': None, 'start_time': None, 'end_time': None,
                                                'similarity': 0.01,  # 标记为极低相似度
                                                'clip_similarity': None,
                                                'fallback_assignment': True,
                                                'fallback_strategy': 'random_complete'
                                            }
                                        else:
                                            # 部分分配（单图）
                                            paragraphs[para_idx]['assigned_media'] = {
                                                'type': 'image',
                                                'filepath': selected_images[0] if selected_images else selected_image,
                                                'duration': None, 'start_time': None, 'end_time': None,
                                                'similarity': 0.01,
                                                'clip_similarity': None,
                                                'fallback_assignment': True,
                                                'fallback_strategy': 'random_partial'
                                            }
                                    else:
                                        # 单图段落：直接分配
                                        paragraphs[para_idx]['assigned_media'] = {
                                            'type': 'image',
                                            'filepath': selected_image,
                                            'duration': None, 'start_time': None, 'end_time': None,
                                            'similarity': 0.01,
                                            'clip_similarity': None,
                                            'fallback_assignment': True,
                                            'fallback_strategy': 'random_single'
                                        }

                                    final_assigned_count += 1

                                logger.info(f"Round 4d: Final random assignment completed for {final_assigned_count} paragraphs.")
                                logger.info("All paragraphs now have media assignments (100% coverage achieved).")
                            else:
                                logger.error("Round 4d: No images available for final assignment!")
                else:
                    logger.warning("Round 4b: No candidates found even with relaxed similarity threshold.")

            elif not filtered_metadata:
                logger.warning("Round 4b: Skipping ILP fallback as no image metadata is available.")
            elif not unassigned_indices_final:
                 logger.info("Round 4b: Skipping ILP fallback as no paragraphs remain unassigned.")

        else:
            # Case where not generate_image, but no paras were unassigned after clips/anchors
            logger.info("No remaining unassigned paragraphs after Round 2/Anchor assignment.")


        # --- Final Status ---
        logger.info("Final Assignment Status:")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Final Fallback or No Further Action")

        return paragraphs

    except Exception as e:
        logger.error(f"Error in enhanced image assignment: {str(e)}")
        logger.debug("Error details:", exc_info=True)
        # Return partially processed paragraphs on error
        return paragraphs

# --- Script Processing and Main --- (Largely unchanged, uses updated assign_images_enhanced)

def process_script(
    script_path: str,
    using_anchor: bool = False,
    max_anchor_sec: float = 30.0
) -> List[Dict[str, Any]]:
    """Process script file, segment text and prepare for image matching"""
    try:
        # Read JSON file
        with open(script_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Build paragraph structure
        paragraphs = []
        for item in data:
            paragraph = {
                'segment_id': item['segment_id'],
                'paragraph_text': item['paragraph_text'],
                'audio': item['audio']  # Keep original audio info
            }

            # Calculate needed images based on duration
            if 'audio' in paragraph and 'duration' in paragraph['audio']:
                duration = paragraph['audio']['duration']
                # Fix E-8: Prevent division by zero or negative duration
                if duration and duration > 0:
                    num_images = calculate_num_images_needed(duration)
                else:
                    num_images = 1
                    logger.warning(f"Paragraph {item['segment_id']} has invalid duration {duration}, defaulting to 1 image")
                paragraph['num_images_needed'] = num_images
                # logger.info(f"Paragraph {item['segment_id']} duration {duration:.2f}s, needs {num_images} images") # Logged later if needed
            else:
                paragraph['num_images_needed'] = 1
                logger.warning(f"Paragraph {item['segment_id']} missing audio duration, defaulting to 1 image")

            paragraphs.append(paragraph)

        # Initialize assigned_media field
        if using_anchor and paragraphs:
            total_duration = 0
            max_anchor_duration = max_anchor_sec

            for i, paragraph in enumerate(paragraphs):
                # Initialize all as pending
                paragraph['assigned_media'] = {
                    'type': 'pending',
                    'filepath': None,
                    'duration': paragraph['audio']['duration'],
                    'start_time': None,
                    'end_time': None
                }

                # First paragraph always anchor
                if i == 0:
                    paragraph['assigned_media']['type'] = 'anchor'
                    total_duration = paragraph['audio']['duration']
                    logger.info(f"Paragraph {paragraph['segment_id']} set as anchor, cumulative duration: {total_duration:.2f}s")
                # Check if can extend anchor
                elif total_duration < max_anchor_duration:
                    current_duration = paragraph['audio']['duration']
                    if total_duration + current_duration <= max_anchor_duration:
                        paragraph['assigned_media']['type'] = 'anchor'
                        total_duration += current_duration
                        logger.info(f"Paragraph {paragraph['segment_id']} set as anchor, cumulative duration: {total_duration:.2f}s")
                    else:
                         pass # Remains pending
                         # logger.info(f"Paragraph {paragraph['segment_id']} remains pending (would exceed {max_anchor_duration}s)")
                # else: # logger.info(f"Paragraph {paragraph['segment_id']} remains pending (already at {max_anchor_duration}s)")

            logger.info(f"Using anchor mode: {sum(1 for p in paragraphs if p['assigned_media']['type'] == 'anchor')} paragraphs as anchor")

        # Non-anchor mode initialization
        else:
            for paragraph in paragraphs:
                paragraph['assigned_media'] = {
                    'type': 'pending',
                    'filepath': None
                }
                # logger.info(f"Paragraph {paragraph['segment_id']} set as pending")

        logger.info(f"Successfully loaded {len(paragraphs)} paragraphs from {script_path}")
        return paragraphs

    except Exception as e:
        logger.error(f"Error processing script file {script_path}: {e}")
        raise

def save_results(paragraphs: List[Dict], output_path: str) -> None:
    """Save processed paragraph data to JSON file"""
    try:
        def convert_numpy_types(obj):
            """Convert numpy types to Python native types"""
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, (np.integer, np.int32, np.int64)):
                return int(obj)
            elif isinstance(obj, (np.floating, np.float32, np.float64)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            # 添加对 NumPy 布尔类型的处理
            elif isinstance(obj, (np.bool_, bool)):
                return bool(obj)
            # Add handling for potential faiss index objects if they leak? Should not happen.
            elif 'Index' in str(type(obj)): # Basic check
                 return f"<FAISS Index Object: {type(obj)}>"
            else:
                return obj

        # Convert numpy types and save
        sanitized_data = convert_numpy_types(paragraphs)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(sanitized_data, f, ensure_ascii=False, indent=2)

        logger.info(f"Results saved to: {output_path}")

    except Exception as e:
        logger.error(f"Error saving results to {output_path}: {str(e)}")
        logger.debug("Data structure causing save error (sample):", paragraphs[:2])
        raise

def get_theme_from_path(json_path: str) -> str:
    """Extract theme name from JSON file path"""
    filename = os.path.basename(json_path)
    theme = filename.replace('_audio.json', '').replace('_script.json', '') # More robust cleaning
    return theme

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Enhanced process: match paragraphs with images/clips using BGE, BM25, Reranker and CLIP post-filter")
    parser.add_argument("--json", type=str, required=True, help="Input JSON file path (e.g., *_script.json or *_audio.json)")
    parser.add_argument("--image_dir", type=str, required=True, help="Image directory path")
    parser.add_argument("--clip_dir", type=str, help="Video clip directory path (optional)")
    parser.add_argument("--clips_json", type=str, help="Path to existing clips assignment JSON file (from assign_clips_to_segments.py)")
    parser.add_argument("--theme", type=str, help="Theme name (optional, default: extracted from filename)")
    parser.add_argument("--using_anchor", action="store_true", help="Whether to use anchor mode")
    parser.add_argument("--generate_image", action="store_true", help="Create image placeholders for unassigned paragraphs instead of fallback assignment")
    parser.add_argument("--dry-run", action="store_true", help="Skip AWS API calls when working offline")
    parser.add_argument("--max-anchor-sec", type=float, default=30.0, help="Maximum cumulative duration (seconds) for initial anchor segments")
    # Add arguments for tuning if needed, e.g., --bm25-weight, --clip-threshold
    parser.add_argument(
        "--reranker-threshold", type=float, default=None,
        help=("Absolute minimum reranker similarity score required for高相似度筛选，"
              "若省略则使用脚本内默认 %.2f" % RERANKER_SCORE_MIN_THRESHOLD),
    )
    return parser.parse_args()

# Global variable to hold parsed arguments
args = None

def main():
    """Main function"""
    # Parse args and store globally
    global args
    args = parse_args()

    # Set global flag for dry run mode
    global DRY_RUN
    DRY_RUN = args.dry_run
    if DRY_RUN:
        logger.info("Running in dry-run mode: AWS API calls will be skipped")

    try:
        # Process script
        paragraphs = process_script(
            args.json,
            using_anchor=args.using_anchor,
            max_anchor_sec=args.max_anchor_sec
        )

        # Extract theme if not specified
        theme = args.theme
        if not theme:
            theme = get_theme_from_path(args.json)
            logger.info(f"Theme extracted from filename: {theme}")

        # Call the enhanced assignment function
        logger.info(f"Starting enhanced media assignment: image_dir={args.image_dir}, clip_dir={args.clip_dir}, generate_image={args.generate_image}")
        # No need to filter paragraphs beforehand, assign_images_enhanced handles logic internally
        final_paragraphs = assign_images_enhanced(
            paragraphs, # Pass the full list
            args.image_dir,
            args.clip_dir,
            theme,
            args.using_anchor,
            args.generate_image,
            args.max_anchor_sec # Pass anchor sec for consistency if needed inside
        )

        # Save results
        output_path = args.json.replace('_script.json', '_images.json').replace('_audio.json', '_images.json')
        save_results(final_paragraphs, output_path)

        # Print success message
        logger.info(f"Enhanced media assignment complete! Results saved to {output_path}")
        if args.generate_image:
            # Count unassigned paragraphs that could benefit from image generation
            unassigned_count = sum(1 for p in final_paragraphs
                                 if p.get('assigned_media', {}).get('type') in ['pending', None])
            if unassigned_count > 0:
                logger.info(f"Found {unassigned_count} unassigned paragraphs that could benefit from image generation")
                logger.info(f"Next step: Use image_generation.py to generate images based on {output_path}")
        else:
            null_count = sum(1 for p in final_paragraphs if p.get('assigned_media', {}).get('type') == 'null')
            if null_count > 0:
                logger.info(f"{null_count} paragraphs were assigned 'null' as no suitable media was found.")


    except Exception as e:
        logger.error(f"Processing failed: {str(e)}")
        logger.debug("Error details:", exc_info=True)
        # Consider exiting with non-zero status on failure
        sys.exit(1)

if __name__ == "__main__":
    # Example commands:
    # Basic usage with new clip retrieval:
    # python assign_clip_image.py --json path/to/your_script.json --image_dir path/to/images --clip_dir path/to/clips --theme mytheme --using_anchor --generate_image
    #
    # Usage with existing clips assignment:
    # python assign_clip_image.py --json path/to/your_script.json --image_dir path/to/images --clips_json path/to/assigned_clips.json --theme mytheme --using_anchor --generate_image
    main()