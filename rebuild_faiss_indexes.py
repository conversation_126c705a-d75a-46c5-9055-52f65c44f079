#!/usr/bin/env python3
"""
Rebuild FAISS indexes from all clips with complete embeddings in the database.

This script is useful when:
1. FAISS indexes are corrupted or missing
2. New videos have been added but indexes weren't properly updated
3. There's a mismatch between FAISS index IDs and database clip_ids

Usage:
    python rebuild_faiss_indexes.py [--force]
    
Options:
    --force: Force rebuild even if indexes already exist
"""

import argparse
import os
import sys
import numpy as np
import faiss

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from video_retrieval import video_config, metadata_db


def rebuild_faiss_indexes(force=False):
    """
    Rebuild FAISS indexes from all clips with complete embeddings in the database.
    
    Args:
        force: If True, rebuild even if indexes already exist
    """
    print("=== Rebuilding FAISS Indexes ===")
    
    # Check if indexes already exist
    visual_index_exists = os.path.exists(video_config.VISUAL_FAISS_INDEX_PATH)
    text_index_exists = os.path.exists(video_config.TRANSCRIPT_FAISS_INDEX_PATH)
    
    if not force and visual_index_exists and text_index_exists:
        print("FAISS indexes already exist. Use --force to rebuild anyway.")
        return
    
    # Get all clips with complete embeddings from database
    print("Fetching clips with complete embeddings from database...")
    all_clips_with_embeddings = metadata_db.get_all_clips_for_indexing(force=True)
    all_clips_with_embeddings = [clip for clip in all_clips_with_embeddings 
                                if clip.get('visual_embedding_path') and clip.get('text_embedding_path')]
    
    print(f"Found {len(all_clips_with_embeddings)} clips with complete embeddings")
    
    if not all_clips_with_embeddings:
        print("No clips with complete embeddings found. Nothing to index.")
        return
    
    # Load all embeddings from disk
    print("Loading embeddings from disk...")
    visual_embeddings_list = []
    text_embeddings_list = []
    clip_ids_list = []
    
    for i, clip_data in enumerate(all_clips_with_embeddings):
        if i % 100 == 0:
            print(f"  Processed {i}/{len(all_clips_with_embeddings)} clips...")
            
        clip_id = clip_data['clip_id']
        visual_embedding_path = clip_data.get('visual_embedding_path')
        text_embedding_path = clip_data.get('text_embedding_path')
        
        try:
            if visual_embedding_path and text_embedding_path:
                if os.path.exists(visual_embedding_path) and os.path.exists(text_embedding_path):
                    visual_embedding = np.load(visual_embedding_path)
                    text_embedding = np.load(text_embedding_path)
                    
                    visual_embeddings_list.append(visual_embedding)
                    text_embeddings_list.append(text_embedding)
                    clip_ids_list.append(clip_id)
                else:
                    print(f"Warning: Embedding files not found for clip_id {clip_id}")
        except Exception as e:
            print(f"Warning: Failed to load embeddings for clip_id {clip_id}: {e}")
    
    if not clip_ids_list:
        print("No valid embeddings loaded. Cannot build indexes.")
        return
    
    print(f"Successfully loaded embeddings for {len(clip_ids_list)} clips")
    print(f"Clip ID range: {min(clip_ids_list)} to {max(clip_ids_list)}")
    
    clip_ids_np = np.array(clip_ids_list, dtype=np.int64)
    
    # Ensure output directories exist
    os.makedirs(os.path.dirname(video_config.VISUAL_FAISS_INDEX_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(video_config.TRANSCRIPT_FAISS_INDEX_PATH), exist_ok=True)
    
    # Build Visual Index
    if visual_embeddings_list:
        print("Building visual FAISS index...")
        visual_embeddings_np = np.stack(visual_embeddings_list).astype('float32')
        
        if visual_embeddings_np.ndim == 2 and visual_embeddings_np.shape[0] > 0:
            d_visual = visual_embeddings_np.shape[1]
            faiss.normalize_L2(visual_embeddings_np)  # normalize in-place
            visual_index = faiss.IndexFlatIP(d_visual)  # inner-product == cosine
            visual_index_final = faiss.IndexIDMap(visual_index)  # Map FAISS index to actual clip_ids
            visual_index_final.add_with_ids(visual_embeddings_np, clip_ids_np)
            faiss.write_index(visual_index_final, video_config.VISUAL_FAISS_INDEX_PATH)
            print(f"✓ Visual FAISS index built with {visual_index_final.ntotal} vectors")
            print(f"  Saved to: {video_config.VISUAL_FAISS_INDEX_PATH}")
        else:
            print(f"Error: Visual embeddings have incorrect shape: {visual_embeddings_np.shape}")
    
    # Build Text Index
    if text_embeddings_list:
        print("Building text FAISS index...")
        text_embeddings_np = np.stack(text_embeddings_list).astype('float32')
        
        if text_embeddings_np.ndim == 2 and text_embeddings_np.shape[0] > 0:
            d_text = text_embeddings_np.shape[1]
            faiss.normalize_L2(text_embeddings_np)
            text_index = faiss.IndexFlatIP(d_text)
            text_index_final = faiss.IndexIDMap(text_index)
            text_index_final.add_with_ids(text_embeddings_np, clip_ids_np)
            faiss.write_index(text_index_final, video_config.TRANSCRIPT_FAISS_INDEX_PATH)
            print(f"✓ Text FAISS index built with {text_index_final.ntotal} vectors")
            print(f"  Saved to: {video_config.TRANSCRIPT_FAISS_INDEX_PATH}")
        else:
            print(f"Error: Text embeddings have incorrect shape: {text_embeddings_np.shape}")
    
    print("=== FAISS Index Rebuild Complete ===")


def main():
    parser = argparse.ArgumentParser(description="Rebuild FAISS indexes from database embeddings")
    parser.add_argument('--force', action='store_true', 
                       help='Force rebuild even if indexes already exist')
    
    args = parser.parse_args()
    
    # Ensure directories exist
    video_config.ensure_directories()
    
    # Rebuild indexes
    rebuild_faiss_indexes(force=args.force)


if __name__ == '__main__':
    main()
