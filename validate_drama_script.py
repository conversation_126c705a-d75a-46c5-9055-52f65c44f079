from typing import Dict, Any
from datetime import datetime
import logging
from utils import parse_duration

# 配置日志记录
logger = logging.getLogger(__name__)
from generate_audiodrama import AUDIODRAMA_CONFIG

def check_scene_emotional_depth(segment: Dict[str, Any]) -> bool:
    """Ensure that emotionally charged scenes have sufficient narrative depth and complexity."""
    try:
        prev_scene = None
        
        for scene in segment["scenes"]:
            emotional_intensity = scene.get("emotional_intensity", "low")
            required_elements = AUDIODRAMA_CONFIG["emotional"]["required_elements"][emotional_intensity]
            
            # 检查情感强度变化
            if prev_scene:
                prev_intensity = prev_scene.get("emotional_intensity", "low")
                intensity_shift = abs(
                    AUDIODRAMA_CONFIG["emotional"]["intensity_levels"].index(emotional_intensity) -
                    AUDIODRAMA_CONFIG["emotional"]["intensity_levels"].index(prev_intensity)
                )
                
                if intensity_shift > AUDIODRAMA_CONFIG["emotional"]["max_intensity_shift"]:
                    logger.warning(f"Emotional intensity shift too large: {prev_intensity} -> {emotional_intensity}")
                    return False
                
                # 检查情感过渡要求
                if emotional_intensity != prev_intensity:
                    transition_key = (
                        "high_to_low" if prev_intensity == "high" and emotional_intensity == "low"
                        else "low_to_high" if prev_intensity == "low" and emotional_intensity == "high"
                        else "same_level"
                    )
                    required_transitions = AUDIODRAMA_CONFIG["emotional"]["transition_requirements"][transition_key]
                    
                    if not all(req in scene.get("transition_elements", []) for req in required_transitions):
                        logger.warning(f"Missing required transition elements for {transition_key}")
                        return False
            
            for shot in scene["shots"]:
                # 检查情感场景的时长
                if shot["type"] in ["emotional", "monologue"]:
                    duration = parse_duration(shot["end_time"]) - parse_duration(shot["start_time"])
                    if not (AUDIODRAMA_CONFIG["duration"]["shot"]["emotional"]["min"] <= 
                           duration <= 
                           AUDIODRAMA_CONFIG["duration"]["shot"]["emotional"]["max"]):
                        logger.warning(f"Emotional shot duration invalid: {duration}s")
                        return False
                
                # 检查旁白复杂度
                if "narration" in shot:
                    narr_text = shot["narration"]["text"].lower()
                    required_narr_elements = AUDIODRAMA_CONFIG["content"]["narration"]["complexity_requirements"][emotional_intensity]
                    
                    if not all(element in narr_text for element in required_narr_elements):
                        logger.warning(f"Missing required narration elements: {required_narr_elements}")
                        return False
                    
                    # 检查字数要求
                    narr_words = len(narr_text.split())
                    if emotional_intensity != "low" and narr_words < AUDIODRAMA_CONFIG["content"]["narration"]["emotional_min_words"]:
                        logger.warning(f"Emotional narration too short: {narr_words} words")
                        return False
                
                # 检查对话复杂度
                if "dialogue" in shot:
                    for dialogue in shot["dialogue"]:
                        if dialogue.get("emotional_weight", "low") != "low":
                            dialogue_text = dialogue["line"].lower()
                            required_dialogue_elements = AUDIODRAMA_CONFIG["content"]["dialogue"]["complexity_requirements"][emotional_intensity]
                            
                            if not all(element in dialogue_text for element in required_dialogue_elements):
                                logger.warning(f"Missing required dialogue elements: {required_dialogue_elements}")
                                return False
                            
                            # 检查字数要求
                            words = len(dialogue_text.split())
                            if words < AUDIODRAMA_CONFIG["content"]["dialogue"]["emotional_min_words"]:
                                logger.warning(f"Emotional dialogue too short: {words} words")
                                return False
                
                # 检查主题共鸣
                if required_elements["thematic_resonance"]:
                    if not scene.get("thematic_elements"):
                        logger.warning("Missing thematic elements in high-intensity scene")
                        return False
                
                # 检查角色转变
                if required_elements["character_transformation"]:
                    if not scene.get("character_development"):
                        logger.warning("Missing character development in high-intensity scene")
                        return False
            
            prev_scene = scene
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking emotional depth: {str(e)}")
        return False

def check_narrative_flow(segment: Dict[str, Any]) -> bool:
    """Verify the narrative flow, thematic consistency, and scene transitions."""
    try:
        prev_scene = None
        
        for scene in segment["scenes"]:
            # 基本场景要求检查
            if not all(key in scene for key in ["scene_objective", "emotional_progression", "theme"]):
                logger.warning("Missing required scene elements")
                return False
            
            if prev_scene:
                # 检查主题转换
                curr_theme = scene.get("theme")
                prev_theme = prev_scene.get("theme")
                
                if curr_theme != prev_theme:
                    theme_shift = (
                        "light_to_dark" if prev_theme == "light" and curr_theme == "dark"
                        else "dark_to_light" if prev_theme == "dark" and curr_theme == "light"
                        else "neutral_shift"
                    )
                    
                    required_elements = AUDIODRAMA_CONFIG["narrative"]["required_elements"]["theme_shift"][theme_shift]
                    if not all(element in scene.get("transition_elements", []) for element in required_elements):
                        logger.warning(f"Missing required theme shift elements for {theme_shift}")
                        return False
                
                # 检查情感过渡
                curr_intensity = scene.get("emotional_intensity", "low")
                prev_intensity = prev_scene.get("emotional_intensity", "low")
                
                if curr_intensity != prev_intensity:
                    # 调整过渡时间
                    transition_overlap = AUDIODRAMA_CONFIG["duration"]["shot"]["transition"]["base_overlap"]
                    
                    if curr_intensity == "high" or prev_intensity == "high":
                        transition_overlap *= AUDIODRAMA_CONFIG["duration"]["shot"]["transition"]["emotional_multiplier"]
                    
                    if curr_theme != prev_theme:
                        transition_overlap *= AUDIODRAMA_CONFIG["duration"]["shot"]["transition"]["theme_change_multiplier"]
                    
                    # 验证过渡时间
                    actual_overlap = parse_duration(scene["shots"][0]["start_time"]) - parse_duration(prev_scene["shots"][-1]["end_time"])
                    if abs(actual_overlap - transition_overlap) > AUDIODRAMA_CONFIG["duration"]["timing_error_tolerance"]:
                        logger.warning(f"Invalid transition timing: expected {transition_overlap}s, got {actual_overlap}s")
                        return False
                
                # 检查叙事连贯性
                if not scene.get("narrative_connection"):
                    logger.warning("Missing narrative connection between scenes")
                    return False
                
                # 检查情感进展
                if not scene.get("emotional_progression"):
                    logger.warning("Missing emotional progression")
                    return False
                
                # 检查节奏控制
                if curr_intensity == "high":
                    # 验证对话和旁白的停顿时间
                    for shot in scene["shots"]:
                        if "dialogue" in shot:
                            pause_multiplier = AUDIODRAMA_CONFIG["narrative"]["pacing"]["high_emotion"]["dialogue_pause"]
                            expected_pause = AUDIODRAMA_CONFIG["duration"]["timing_error_tolerance"] * pause_multiplier
                            
                            for i, dialogue in enumerate(shot["dialogue"][:-1]):
                                next_dialogue = shot["dialogue"][i + 1]
                                pause_duration = parse_duration(next_dialogue["start_time"]) - parse_duration(dialogue["end_time"])
                                
                                if pause_duration < expected_pause:
                                    logger.warning(f"Insufficient pause between emotional dialogues: {pause_duration}s")
                                    return False
            
            # 检查场景内部的连贯性
            shot_types = [shot["type"] for shot in scene["shots"]]
            
            # 确保情感场景有足够的内心独白和情感描写
            if scene.get("emotional_intensity") == "high":
                if shot_types.count("monologue") < 1 or shot_types.count("emotional") < 2:
                    logger.warning("High emotional scene lacks sufficient emotional shots or monologues")
                    return False
            
            # 检查场景节奏
            if not validate_scene_pacing(scene):
                return False
            
            prev_scene = scene
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking narrative flow: {str(e)}")
        return False

def validate_scene_pacing(scene: Dict[str, Any]) -> bool:
    """验证场景的节奏控制."""
    try:
        shot_count = len(scene["shots"])
        emotional_intensity = scene.get("emotional_intensity", "low")
        
        # 检查镜头数量是否合适
        if not (AUDIODRAMA_CONFIG["duration"]["scene_shots"]["min"] <= 
                shot_count <= 
                AUDIODRAMA_CONFIG["duration"]["scene_shots"]["max"]):
            logger.warning(f"Invalid number of shots in scene: {shot_count}")
            return False
        
        # 检查镜头类型分布
        shot_types = [shot["type"] for shot in scene["shots"]]
        
        # 确保场景开始有建立镜头
        if shot_types[0] != "establishing":
            logger.warning("Scene does not start with establishing shot")
            return False
        
        # 确保场景结束有过渡镜头
        if shot_types[-1] != "transition":
            logger.warning("Scene does not end with transition shot")
            return False
        
        # 检查情感镜头的分布
        if emotional_intensity == "high":
            emotional_shots = shot_types.count("emotional")
            if emotional_shots < shot_count * 0.3:  # 至少30%的镜头应该是情感镜头
                logger.warning(f"Insufficient emotional shots in high intensity scene: {emotional_shots}/{shot_count}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating scene pacing: {str(e)}")
        return False

def validate_visual_description(segment: Dict[str, Any]) -> bool:
    """Ensure scene descriptions are rich in sensory details and suitable for audio drama format."""
    try:
        for scene in segment["scenes"]:
            emotional_intensity = scene.get("emotional_intensity", "low")
            
            for shot in scene["shots"]:
                # 检查环境描述的质量
                if "environment" in shot:
                    desc = shot["environment"]["description"].lower()
                    
                    # 计算包含的感官类别数量
                    sensory_categories = 0
                    for category, keywords in AUDIODRAMA_CONFIG["sensory"]["required_elements"].items():
                        if any(keyword in desc for keyword in keywords):
                            sensory_categories += 1
                    
                    if sensory_categories < AUDIODRAMA_CONFIG["sensory"]["minimum_categories"]:
                        logger.warning(f"Insufficient sensory categories in description: {sensory_categories}/{AUDIODRAMA_CONFIG['sensory']['minimum_categories']}")
                        return False
                    
                    # 情感场景需要更丰富的描述
                    if emotional_intensity == "high":
                        # 检查是否包含所有感官类别
                        missing_categories = []
                        for category, keywords in AUDIODRAMA_CONFIG["sensory"]["required_elements"].items():
                            if not any(keyword in desc for keyword in keywords):
                                missing_categories.append(category)
                        
                        if missing_categories:
                            logger.warning(f"High emotional scene missing sensory categories: {missing_categories}")
                            return False
                
                # 检查动作描述
                if shot["type"] == "action":
                    # 确保有声音效果
                    if not shot.get("sound_effects"):
                        logger.warning("Missing sound effects for action shot")
                        return False
                    
                    # 检查动作描述的完整性
                    if "narration" in shot:
                        narr_text = shot["narration"]["text"].lower()
                        
                        # 确保动作描述包含动作词和声音效果
                        if not any(keyword in narr_text for keyword in AUDIODRAMA_CONFIG["sensory"]["required_elements"]["movement"]):
                            logger.warning("Missing movement description in action shot")
                            return False
                        
                        if not any(keyword in narr_text for keyword in AUDIODRAMA_CONFIG["sensory"]["required_elements"]["auditory"]):
                            logger.warning("Missing auditory description in action shot")
                            return False
                
                # 检查情感场景的氛围描述
                if shot["type"] == "emotional":
                    if "narration" in shot:
                        narr_text = shot["narration"]["text"].lower()
                        
                        # 确保情感描述包含氛围和触感元素
                        if not any(keyword in narr_text for keyword in AUDIODRAMA_CONFIG["sensory"]["required_elements"]["atmospheric"]):
                            logger.warning("Missing atmosphere description in emotional shot")
                            return False
                        
                        if not any(keyword in narr_text for keyword in AUDIODRAMA_CONFIG["sensory"]["required_elements"]["tactile"]):
                            logger.warning("Missing tactile description in emotional shot")
                            return False
                
                # 检查场景过渡的连续性
                if shot["type"] == "transition":
                    # 确保过渡描述包含空间和氛围变化
                    if "narration" in shot:
                        narr_text = shot["narration"]["text"].lower()
                        
                        if not any(keyword in narr_text for keyword in AUDIODRAMA_CONFIG["sensory"]["required_elements"]["spatial"]):
                            logger.warning("Missing spatial transition description")
                            return False
                        
                        if not any(keyword in narr_text for keyword in AUDIODRAMA_CONFIG["sensory"]["required_elements"]["atmospheric"]):
                            logger.warning("Missing atmosphere transition description")
                            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating visual description: {str(e)}")
        return False
