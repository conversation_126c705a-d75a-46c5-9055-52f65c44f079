#!/usr/bin/env python3
"""
测试集数分配逻辑，验证378集问题的修复
"""

import json
import os
import sys
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ENABLE_SPINE_PIPELINE
from generate_episodes import _determine_episodes_with_spine, _merge_excessive_phases
from modules.key_event_spine import dynamic_phase_split


def create_mock_group_summaries(num_chapters: int = 100) -> Dict[str, Any]:
    """创建模拟的group_summaries数据"""
    groups = []
    chapters_per_group = 10
    
    for i in range(0, num_chapters, chapters_per_group):
        group_chapters = list(range(i + 1, min(i + chapters_per_group + 1, num_chapters + 1)))
        group = {
            "group_id": f"g_{str(i//chapters_per_group + 1).zfill(3)}",
            "chapter_numbers": group_chapters,
            "group_summary": f"Group {i//chapters_per_group + 1} summary",
            "main_events": [f"Event {i//chapters_per_group + 1}.1", f"Event {i//chapters_per_group + 1}.2"],
            "character_arcs": [f"Character arc {i//chapters_per_group + 1}"]
        }
        groups.append(group)
    
    return {"groups": groups}


def create_mock_global_outline() -> Dict[str, Any]:
    """创建模拟的global_outline数据"""
    return {
        "story_theme": "测试故事",
        "main_characters": ["主角A", "主角B"],
        "story_arc": "经典三幕式结构",
        "total_estimated_episodes": 60
    }


def test_episode_allocation():
    """测试集数分配逻辑"""
    print("=" * 60)
    print("测试集数分配逻辑")
    print("=" * 60)
    
    # 测试不同的章节数量
    test_cases = [
        {"chapters": 50, "expected_episodes": "15-25"},
        {"chapters": 100, "expected_episodes": "30-50"},
        {"chapters": 200, "expected_episodes": "60-80"},
        {"chapters": 500, "expected_episodes": "100-150"},
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['chapters']} 个章节")
        print("-" * 40)
        
        # 创建模拟数据
        group_summaries = create_mock_group_summaries(case['chapters'])
        global_outline = create_mock_global_outline()
        
        try:
            # 测试Spine流水线分配
            if ENABLE_SPINE_PIPELINE:
                result = _determine_episodes_with_spine(
                    group_summaries, global_outline, "engaging", "Chinese"
                )
                
                total_episodes = result.get("total_episodes", 0)
                phases_count = len(result.get("spine_phases", []))
                
                print(f"总章节数: {case['chapters']}")
                print(f"阶段数: {phases_count}")
                print(f"总集数: {total_episodes}")
                print(f"预期集数范围: {case['expected_episodes']}")
                
                # 检查是否合理
                if total_episodes > 200:
                    print("⚠️  警告: 集数过多，可能存在过度细分问题")
                elif total_episodes < 10:
                    print("⚠️  警告: 集数过少，可能存在过度合并问题")
                else:
                    print("✅ 集数分配合理")
                    
                # 分析阶段分布
                if result.get("spine_phases"):
                    chapter_counts = [len(phase.get("chapters", [])) for phase in result["spine_phases"]]
                    avg_chapters_per_phase = sum(chapter_counts) / len(chapter_counts)
                    print(f"平均每阶段章节数: {avg_chapters_per_phase:.1f}")
                    print(f"章节数分布: 最少{min(chapter_counts)}, 最多{max(chapter_counts)}")
                
            else:
                print("Spine流水线未启用，跳过测试")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()


def test_merge_excessive_phases():
    """测试阶段合并逻辑"""
    print("\n" + "=" * 60)
    print("测试阶段合并逻辑")
    print("=" * 60)
    
    # 创建过多的阶段
    excessive_phases = []
    for i in range(200):  # 创建200个阶段
        phase = {
            "chapters": [i + 1],
            "weight": 0.8,
            "action": "split"
        }
        excessive_phases.append(phase)
    
    print(f"原始阶段数: {len(excessive_phases)}")
    
    # 测试合并
    merged_phases = _merge_excessive_phases(excessive_phases, target_phases=60)
    
    print(f"合并后阶段数: {len(merged_phases)}")
    
    # 验证合并结果
    total_chapters_before = sum(len(phase["chapters"]) for phase in excessive_phases)
    total_chapters_after = sum(len(phase["chapters"]) for phase in merged_phases)
    
    print(f"合并前总章节数: {total_chapters_before}")
    print(f"合并后总章节数: {total_chapters_after}")
    
    if total_chapters_before == total_chapters_after:
        print("✅ 章节数量保持一致")
    else:
        print("❌ 章节数量不一致")
    
    if len(merged_phases) <= 60:
        print("✅ 成功控制阶段数量")
    else:
        print("❌ 阶段数量仍然过多")


def analyze_current_allocation():
    """分析当前的分配结果"""
    print("\n" + "=" * 60)
    print("分析当前分配结果")
    print("=" * 60)
    
    allocation_file = "2-animation-drama/temp/spine_episode_allocation_20250605_155433.json"
    
    if os.path.exists(allocation_file):
        with open(allocation_file, 'r', encoding='utf-8') as f:
            allocation = json.load(f)
        
        total_episodes = allocation.get("total_episodes", 0)
        phases = allocation.get("spine_phases", [])
        
        print(f"当前总集数: {total_episodes}")
        print(f"当前阶段数: {len(phases)}")
        
        if phases:
            # 分析阶段分布
            chapter_counts = [len(phase.get("chapters", [])) for phase in phases]
            weights = [phase.get("weight", 0) for phase in phases]
            
            print(f"每阶段章节数 - 平均: {sum(chapter_counts)/len(chapter_counts):.1f}, "
                  f"最少: {min(chapter_counts)}, 最多: {max(chapter_counts)}")
            print(f"阶段权重 - 平均: {sum(weights)/len(weights):.3f}, "
                  f"最小: {min(weights):.3f}, 最大: {max(weights):.3f}")
            
            # 检查问题
            single_chapter_phases = sum(1 for count in chapter_counts if count <= 2)
            print(f"单章节或双章节阶段数: {single_chapter_phases} ({single_chapter_phases/len(phases)*100:.1f}%)")
            
            if single_chapter_phases > len(phases) * 0.8:
                print("❌ 问题: 过度细分，大部分阶段只包含1-2个章节")
            else:
                print("✅ 阶段分布相对合理")
    else:
        print("未找到分配文件")


if __name__ == "__main__":
    print("开始测试集数分配逻辑...")
    
    # 分析当前问题
    analyze_current_allocation()
    
    # 测试合并逻辑
    test_merge_excessive_phases()
    
    # 测试新的分配逻辑
    test_episode_allocation()
    
    print("\n测试完成!")
