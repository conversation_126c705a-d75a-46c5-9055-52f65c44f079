{"3": {"inputs": {"value": 960}, "class_type": "easy int", "_meta": {"title": "<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"value": 540}, "class_type": "easy int", "_meta": {"title": "Height"}}, "8": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "11": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "12": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "14": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "15": {"inputs": {"lora_name": "<PERSON><PERSON><PERSON>_<PERSON>t_Sketch_for_Flux_1.0.5.safetensors", "strength_model": 0.9, "model": ["14", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "19": {"inputs": {"text": "sketch painting;", "clip": ["8", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "20": {"inputs": {"conditioning_to": ["19", 0], "conditioning_from": ["21", 0]}, "class_type": "ConditioningConcat", "_meta": {"title": "Conditioning (Concat)"}}, "21": {"inputs": {"text": "(<PERSON><PERSON><PERSON> emphasized through angular shadows, hollow cheeks lit by harsh gaslight) — <PERSON>'s body lying in a fog-choked alleyway, Pre-dawn hours in a gaslit Spitalfields alley (3 AM)\n", "clip": ["8", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Prompt Action"}}, "22": {"inputs": {"model": ["26", 0], "conditioning": ["20", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "23": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "24": {"inputs": {"noise_seed": 927639630706299}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "25": {"inputs": {"width": ["3", 0], "height": ["4", 0], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "26": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["3", 0], "height": ["4", 0], "model": ["15", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "28": {"inputs": {"samples": ["33", 0], "vae": ["34", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "32": {"inputs": {"scheduler": "normal", "steps": 10, "denoise": 1, "model": ["26", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "33": {"inputs": {"noise": ["24", 0], "guider": ["22", 0], "sampler": ["23", 0], "sigmas": ["32", 0], "latent_image": ["25", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "34": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "60": {"inputs": {"images": ["28", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "62": {"inputs": {"text": ""}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "63": {"inputs": {"text": "no gore, no blood, no horror", "clip": ["8", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "64": {"inputs": {"conditioning_to": ["20", 0], "conditioning_from": ["63", 0]}, "class_type": "ConditioningConcat", "_meta": {"title": "Conditioning (Concat)"}}}