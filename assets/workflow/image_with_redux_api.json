{"1": {"inputs": {"model": ["11", 0], "conditioning": ["75", 0]}, "class_type": "BasicGuider"}, "2": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "3": {"inputs": {"scheduler": "normal", "steps": 16, "denoise": 1, "model": ["11", 0]}, "class_type": "BasicScheduler"}, "4": {"inputs": {"noise_seed": 85278294907576}, "class_type": "RandomNoise"}, "5": {"inputs": {"conditioning_to": ["9", 0], "conditioning_from": ["18", 0]}, "class_type": "ConditioningConcat"}, "6": {"inputs": {"width": ["23", 0], "height": ["24", 0], "batch_size": 1}, "class_type": "EmptyLatentImage"}, "7": {"inputs": {"noise": ["4", 0], "guider": ["1", 0], "sampler": ["2", 0], "sigmas": ["3", 0], "latent_image": ["6", 0]}, "class_type": "SamplerCustomAdvanced"}, "8": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "9": {"inputs": {"text": "<PERSON><PERSON><PERSON><PERSON>,", "clip": ["45", 0]}, "class_type": "CLIPTextEncode"}, "11": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["23", 0], "height": ["24", 0], "model": ["21", 0]}, "class_type": "ModelSamplingFlux"}, "17": {"inputs": {"samples": ["7", 0], "vae": ["8", 0]}, "class_type": "VAEDecode"}, "18": {"inputs": {"text": "\"\\\"story_context\\\": \\\"narrative_moment\\\": \\\"<PERSON> is recounting a traumatic mining collapse experience to <PERSON>, demonstrating her ability to control and release fire\\\", \\\"emotional_tone\\\": \\\"Tense and emotional, with <PERSON> displaying vulnerability and power, while <PERSON> remains neutral yet intrigued\\\", \\\"character_details\\\": [\\\"name\\\": \\\"安娜\\\", \\\"action\\\": \\\"pose\\\": \\\"Sitting in the corner of the cell, slightly hunched, indicating emotional strain\\\", \\\"gesture\\\": \\\"Using her hands to demonstrate releasing flames, with small magical flames emanating from her fingertips\\\", \\\"interaction\\\": \\\"Facing <PERSON>, engaging in a heartfelt conversation\\\", \\\"emotion\\\": \\\"expression\\\": \\\"Nervous but determined\\\", \\\"emotional_state\\\": \\\"Anxious yet resolute, striving to explain her actions\\\", \\\"intensity\\\": \\\"High emotional intensity\\\", \\\"appearance\\\": \\\"A young woman with long, flowing red hair, styled with loose waves, and light skin. Her expression is calm and gentle, with a subtle smile and soft, brown eyes. She has a light blue mark on her forehead, resembling a cross with a small square in the middle. She also wears small light blue teardrop earrings, and a light blue stone necklace with gold accents. She wears a long, flowing cream dress with light blue swirling motifs near the hem. A dark green cloak with decorative swirls around the edges is draped over her shoulders. She carries a brown wooden spear adorned with green leaves. She has a brown belt with a gold buckle. On the belt are two pouches, one light brown, and one dark brown, a light blue cuff is visible around her left wrist. She is barefoot.\\\", \\\"name\\\": \\\"罗兰·温布顿\\\", \\\"action\\\": \\\"pose\\\": \\\"Sitting across from Anna, leaning slightly forward to show attentiveness\\\", \\\"gesture\\\": \\\"Listening intently, with hands resting calmly on his lap\\\", \\\"interaction\\\": \\\"Engaging with Anna, maintaining eye contact and showing interest\\\", \\\"emotion\\\": \\\"expression\\\": \\\"Neutral and attentive\\\", \\\"emotional_state\\\": \\\"Calm and assessing, recognizing Anna's potential\\\", \\\"intensity\\\": \\\"Moderate emotional intensity\\\", \\\"appearance\\\": \\\"A young man with light brown, shoulder-length hair, light skin, and a gentle expression. He has soft, brown eyes and a subtle, calm smile. He is wearing a blue tunic with ornate gold floral patterns, and blue pants. He has a red belt with a gold buckle. He has a small brown pouch. He wears a blue cloak, lined in red, with gold accents. He wears a beige collar with gold circle accents. He wears brown boots with decorative gold accents. He is holding a silver sword with a wooden handle.\\\"], \\\"composition\\\": \\\"positioning\\\": \\\"Anna is positioned in the left corner of the cell, while Roland sits opposite her, creating a balanced interaction\\\", \\\"relative_scale\\\": \\\"Both characters are proportionate to the environment, neither overshadowing the other\\\", \\\"focus_hierarchy\\\": \\\"Primary focus on Anna and Roland, with the prison cell elements serving as the background\\\"\"", "clip": ["45", 0]}, "class_type": "CLIPTextEncode"}, "19": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader"}, "20": {"inputs": {"lora_name": "Art_<PERSON><PERSON><PERSON><PERSON>_吉卜力动画风格_V1.safetensors", "strength_model": 0.9, "model": ["19", 0]}, "class_type": "LoraLoaderModelOnly"}, "21": {"inputs": {"lora_name": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.1, "model": ["20", 0]}, "class_type": "LoraLoaderModelOnly"}, "23": {"inputs": {"value": 720}, "class_type": "easy int"}, "24": {"inputs": {"value": 1280}, "class_type": "easy int"}, "45": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "48": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader"}, "50": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader"}, "56": {"inputs": {"crop": "center", "clip_vision": ["50", 0], "image": ["103", 0]}, "class_type": "CLIPVisionEncode"}, "65": {"inputs": {"image_strength": "medium", "conditioning": ["18", 0], "style_model": ["48", 0], "clip_vision_output": ["56", 0]}, "class_type": "StyleModelApplySimple"}, "71": {"inputs": {"crop": "center", "clip_vision": ["50", 0], "image": ["124", 0]}, "class_type": "CLIPVisionEncode"}, "75": {"inputs": {"image_strength": "high", "conditioning": ["65", 0], "style_model": ["48", 0], "clip_vision_output": ["71", 0]}, "class_type": "StyleModelApplySimple"}, "78": {"inputs": {"width": 1024, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "disabled", "image": ["17", 0]}, "class_type": "ImageResizeKJ"}, "79": {"inputs": {"upscale_model": ["80", 0], "image": ["78", 0]}, "class_type": "ImageUpscaleWithModel"}, "80": {"inputs": {"model_name": "4x_NMKD-Siax_200k.pth"}, "class_type": "UpscaleModelLoader"}, "81": {"inputs": {"tile_width": ["99", 0], "tile_height": ["99", 1], "image": ["101", 0]}, "class_type": "TTP_Image_Tile_Batch"}, "82": {"inputs": {"image": ["81", 0]}, "class_type": "easy imageBatchToImageList"}, "83": {"inputs": {"images": ["84", 0]}, "class_type": "ImageListToImageBatch"}, "84": {"inputs": {"tile_size": 1024, "overlap": 64, "samples": ["86", 0], "vae": ["85", 0]}, "class_type": "VAEDecodeTiled"}, "85": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "86": {"inputs": {"noise": ["87", 0], "guider": ["89", 0], "sampler": ["91", 0], "sigmas": ["88", 0], "latent_image": ["92", 0]}, "class_type": "SamplerCustomAdvanced"}, "87": {"inputs": {"noise_seed": 316430325547060}, "class_type": "RandomNoise"}, "88": {"inputs": {"scheduler": "simple", "steps": 4, "denoise": 0.3, "model": ["93", 0]}, "class_type": "BasicScheduler"}, "89": {"inputs": {"model": ["93", 0], "conditioning": ["90", 0]}, "class_type": "BasicGuider"}, "90": {"inputs": {"guidance": 2.5, "conditioning": ["100", 0]}, "class_type": "FluxGuidance"}, "91": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "92": {"inputs": {"pixels": ["82", 0], "vae": ["85", 0]}, "class_type": "VAEEncode"}, "93": {"inputs": {"lora_name": "Flux Dev模型4步出图lora_Flux Dev 4-step.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["21", 0], "clip": ["45", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "97": {"inputs": {"padding": 128, "tiles": ["83", 0], "positions": ["81", 1], "original_size": ["81", 2], "grid_size": ["81", 3]}, "class_type": "TTP_Image_Assy"}, "99": {"inputs": {"width_factor": 2, "height_factor": 4, "overlap_rate": 0.1, "image": ["101", 0]}, "class_type": "TTP_Tile_image_size"}, "100": {"inputs": {"text": "<PERSON><PERSON><PERSON><PERSON>, high quality, detailed, photograph , hd, 8k , 4k , sharp, highly detailed", "clip": ["93", 1]}, "class_type": "CLIPTextEncode"}, "101": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 2, "image": ["79", 0]}, "class_type": "ImageScaleToTotalPixels"}, "102": {"inputs": {"filename_prefix": "ComfyUI", "images": ["97", 0]}, "class_type": "SaveImage"}, "103": {"inputs": {"inputcount": 2, "direction": "right", "match_image_size": true, "Update inputs": null, "image_1": ["136", 0], "image_2": ["137", 0]}, "class_type": "ImageConcatMulti"}, "104": {"inputs": {"image": "actress_1.png", "upload": "image"}, "class_type": "LoadImage"}, "107": {"inputs": {"image": "actor_1.png", "upload": "image"}, "class_type": "LoadImage"}, "111": {"inputs": {"text": "\"\\\"setting\\\": \\\"location\\\": \\\"A dark, damp prison cell with wet stone walls illuminated by dim torchlight. The cell is secured with cold and sturdy iron bars.\\\", \\\"time_of_day\\\": \\\"Late afternoon with the weak light from torches contributing to the dimness\\\", \\\"weather\\\": \\\"The cell is underground; minimal indication of external weather, but the atmosphere is moist and oppressive.\\\", \\\"mood\\\": \\\"Claustrophobic and tense, emphasizing the grim and oppressive nature of the prison\\\", \\\"visual_elements\\\": \\\"lighting\\\": \\\"Soft, flickering torchlight casting long shadows on the damp walls, creating a somber atmosphere\\\", \\\"props\\\": \\\"Sturdy iron bars, wooden benches, and medieval prison furnishings such as a simple table and a few basic tools\\\", \\\"architecture\\\": \\\"Medieval-inspired stone walls and fortified iron bars typical of a secure prison\\\", \\\"special_effects\\\": \\\"Subtle wisps of moisture in the air, enhancing the dampness, with small puddles forming on the uneven stone floor\\\"\"", "clip": ["45", 0]}, "class_type": "CLIPTextEncode"}, "113": {"inputs": {"text": "<PERSON><PERSON><PERSON><PERSON>,", "clip": ["45", 0]}, "class_type": "CLIPTextEncode"}, "115": {"inputs": {"conditioning_to": ["113", 0], "conditioning_from": ["111", 0]}, "class_type": "ConditioningConcat"}, "116": {"inputs": {"model": ["121", 0], "conditioning": ["115", 0]}, "class_type": "BasicGuider"}, "117": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "118": {"inputs": {"noise_seed": 1052188874101453}, "class_type": "RandomNoise"}, "119": {"inputs": {"width": ["23", 0], "height": ["24", 0], "batch_size": 1}, "class_type": "EmptyLatentImage"}, "120": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "121": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["23", 0], "height": ["24", 0], "model": ["21", 0]}, "class_type": "ModelSamplingFlux"}, "124": {"inputs": {"samples": ["127", 0], "vae": ["120", 0]}, "class_type": "VAEDecode"}, "127": {"inputs": {"noise": ["118", 0], "guider": ["116", 0], "sampler": ["117", 0], "sigmas": ["130", 0], "latent_image": ["119", 0]}, "class_type": "SamplerCustomAdvanced"}, "130": {"inputs": {"scheduler": "normal", "steps": 10, "denoise": 1, "model": ["121", 0]}, "class_type": "BasicScheduler"}, "136": {"inputs": {"index": 0, "image0": ["104", 0], "image1": ["107", 0], "image2": ["145", 0]}, "class_type": "easy imageIndexSwitch"}, "137": {"inputs": {"index": 1, "image0": ["104", 0], "image1": ["107", 0], "image2": ["145", 0]}, "class_type": "easy imageIndexSwitch"}, "142": {"inputs": {"images": ["124", 0]}, "class_type": "PreviewImage"}, "143": {"inputs": {"images": ["17", 0]}, "class_type": "PreviewImage"}, "144": {"inputs": {"images": ["103", 0]}, "class_type": "PreviewImage"}, "145": {"inputs": {"width": ["23", 0], "height": ["24", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage"}}