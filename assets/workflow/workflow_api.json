{"6": {"inputs": {"text": "Create a detailed fantasy scene where the characters <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> are in a medieval castle setting. <PERSON> is addressing <PERSON><PERSON><PERSON> and <PERSON>, arranging their living quarters. <PERSON><PERSON><PERSON> is depicted with a look of regret, while <PERSON> appears calm and composed. In the background, <PERSON> transforms into a bluebird, soaring into the sky to dodge <PERSON><PERSON><PERSON>'s magical attack. The sky is clear with a few clouds, and the scene is set during the day. <PERSON> watches the duel with a serious expression. <PERSON> is seen visiting <PERSON>'s room, sitting by his bed for a brief moment, showing concern and determination. The overall mood is tense yet hopeful, with soft lighting illuminating the castle's stone walls and wooden furniture. The colors are natural with a slight warm tone, capturing the medieval fantasy atmosphere.", "clip": ["11", 0]}, "class_type": "CLIPTextEncode"}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode"}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage"}, "10": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "11": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "12": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader"}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["27", 0]}, "class_type": "SamplerCustomAdvanced"}, "16": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect"}, "17": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler"}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["26", 0]}, "class_type": "BasicGuider"}, "25": {"inputs": {"noise_seed": 165915107217837}, "class_type": "RandomNoise"}, "26": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance"}, "27": {"inputs": {"width": 512, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage"}, "30": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 512, "height": 1024, "model": ["39", 0]}, "class_type": "ModelSamplingFlux"}, "39": {"inputs": {"lora_name": "Art_<PERSON><PERSON><PERSON><PERSON>_吉卜力动画风格_V1.safetensors", "strength_model": 0.6, "model": ["12", 0]}, "class_type": "LoraLoaderModelOnly"}}