#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the FAISS index files and compare with database.
"""

import os
import faiss
import sqlite3
from datetime import datetime

# Define paths
VISUAL_FAISS_INDEX_PATH = "/Users/<USER>/video_database/indexes/visual_faiss.index"
TRANSCRIPT_FAISS_INDEX_PATH = "/Users/<USER>/video_database/indexes/transcript_faiss.index"
DB_PATH = "/Users/<USER>/video_database/database/video_library.sqlite3"

def get_db_connection():
    """Establishes a connection to the SQLite database."""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Access columns by name
    return conn

def check_faiss_index(index_path):
    """Check a FAISS index file and return information about it."""
    if not os.path.exists(index_path):
        return {
            "exists": False,
            "size": 0,
            "vectors": 0,
            "modified": None
        }
    
    # Get file stats
    size = os.path.getsize(index_path)
    modified = datetime.fromtimestamp(os.path.getmtime(index_path))
    
    # Load the index and get vector count
    try:
        index = faiss.read_index(index_path)
        vectors = index.ntotal
        dimension = index.d
    except Exception as e:
        return {
            "exists": True,
            "size": size,
            "vectors": "Error loading index",
            "modified": modified,
            "error": str(e)
        }
    
    return {
        "exists": True,
        "size": size,
        "vectors": vectors,
        "dimension": dimension,
        "modified": modified
    }

def get_db_stats():
    """Get statistics from the database."""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Total clips
    cursor.execute("SELECT COUNT(*) FROM video_clips")
    total_clips = cursor.fetchone()[0]
    
    # Clips with visual embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE visual_embedding_path IS NOT NULL")
    visual_embeddings = cursor.fetchone()[0]
    
    # Clips with text embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE text_embedding_path IS NOT NULL")
    text_embeddings = cursor.fetchone()[0]
    
    # Clips with both embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE visual_embedding_path IS NOT NULL AND text_embedding_path IS NOT NULL")
    both_embeddings = cursor.fetchone()[0]
    
    conn.close()
    
    return {
        "total_clips": total_clips,
        "visual_embeddings": visual_embeddings,
        "text_embeddings": text_embeddings,
        "both_embeddings": both_embeddings
    }

def main():
    """Main function to check FAISS indexes and database."""
    print("Checking FAISS indexes and database...")
    
    # Check FAISS indexes
    visual_index_info = check_faiss_index(VISUAL_FAISS_INDEX_PATH)
    transcript_index_info = check_faiss_index(TRANSCRIPT_FAISS_INDEX_PATH)
    
    # Get database stats
    db_stats = get_db_stats()
    
    # Print results
    print("\nVisual FAISS Index:")
    print(f"  Exists: {visual_index_info['exists']}")
    if visual_index_info['exists']:
        print(f"  Size: {visual_index_info['size'] / (1024*1024):.2f} MB")
        print(f"  Vectors: {visual_index_info['vectors']}")
        if 'dimension' in visual_index_info:
            print(f"  Dimension: {visual_index_info['dimension']}")
        print(f"  Last Modified: {visual_index_info['modified']}")
    
    print("\nTranscript FAISS Index:")
    print(f"  Exists: {transcript_index_info['exists']}")
    if transcript_index_info['exists']:
        print(f"  Size: {transcript_index_info['size'] / (1024*1024):.2f} MB")
        print(f"  Vectors: {transcript_index_info['vectors']}")
        if 'dimension' in transcript_index_info:
            print(f"  Dimension: {transcript_index_info['dimension']}")
        print(f"  Last Modified: {transcript_index_info['modified']}")
    
    print("\nDatabase Statistics:")
    print(f"  Total Clips: {db_stats['total_clips']}")
    print(f"  Clips with Visual Embeddings: {db_stats['visual_embeddings']}")
    print(f"  Clips with Text Embeddings: {db_stats['text_embeddings']}")
    print(f"  Clips with Both Embeddings: {db_stats['both_embeddings']}")
    
    # Check for discrepancies
    if visual_index_info['exists'] and 'vectors' in visual_index_info and isinstance(visual_index_info['vectors'], int):
        if visual_index_info['vectors'] != db_stats['both_embeddings']:
            print(f"\nWARNING: Visual index has {visual_index_info['vectors']} vectors but database has {db_stats['both_embeddings']} clips with both embeddings.")
    
    if transcript_index_info['exists'] and 'vectors' in transcript_index_info and isinstance(transcript_index_info['vectors'], int):
        if transcript_index_info['vectors'] != db_stats['both_embeddings']:
            print(f"\nWARNING: Transcript index has {transcript_index_info['vectors']} vectors but database has {db_stats['both_embeddings']} clips with both embeddings.")

if __name__ == "__main__":
    main()
