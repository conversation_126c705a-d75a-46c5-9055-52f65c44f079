#config.py
import os
import yaml
import logging
from dotenv import load_dotenv
import warnings
from typing import Any, Dict, Optional

# 2. 警告过滤设置
load_dotenv()

# 3. 日志配置
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# Logger Configuration for External Libraries
for lib in ["httpx", "PIL", "httpcore", "googleapiclient", "google_auth_httplib2", "openai", "urllib3", "pydub", 'sentence_transformers', 'azure.core.pipeline.policies.http_logging_policy', 'paramiko.transport','h5py','chardet.charsetprober','bs4.dammit','torio._extension.utils', 'matplotlib','boto3','botocore','filelock']:
    logging.getLogger(lib).setLevel(logging.ERROR)

warnings.filterwarnings("ignore", category=FutureWarning, module="stanza")
warnings.filterwarnings("ignore", category=FutureWarning, module="transformers.tokenization_utils_base")

# 4. 所有常量和配置变量集中定义
# 4.1 基础配置
PWD = os.getcwd()

# 4.2 API相关配置
TAVILY_API_KEY = os.getenv('TAVILY_API_KEY')
TAVILY_API_URL = "https://api.tavily.com"
TAVILY_RESULTS = 80
TAVILY_REFERENCE_RESULTS = 10
DASHSCOPE_API_KEY = os.getenv('DASHSCOPE_API_KEY')
MINIMAX_API_KEY = os.getenv('MINIMAX_API_KEY')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

# 4.3 Azure相关配置
AZURE_REGION = "japaneast"
AZURE_SUBSCRIPTION_KEY = os.getenv('AZURE_SUBSCRIPTION_KEY')
AZURE_WHISPER_ENDPOINT = 'https://whisper-tts-ai-mcn.openai.azure.com'
AZURE_WHISPER_KEY = os.getenv('AZURE_WHISPER_KEY')
AZURE_WHISPER_DEPLOYMENT_NAME = 'whisper-tts'
# 设置 Azure 凭据
TEXT_ANALYTICS_KEY = os.getenv('AZURE_TEXT_ANALYTICS_KEY')
TEXT_ANALYTICS_ENDPOINT = os.getenv('AZURE_TEXT_ANALYTICS_ENDPOINT')
SPEECH_KEY = os.getenv('AZURE_SPEECH_KEY')
SPEECH_REGION = os.getenv('AZURE_SPEECH_REGION')

# 4.4 Google相关配置
GOOGLE_IMAGE_SEARCH_API_KEY = os.getenv('GOOGLE_IMAGE_SEARCH_API_KEY')
GOOGLE_IMAGE_SEARCH_CX = 'e5b9b033d5ea94788'

# 4.5 路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TEMP_FILE_DIR = os.path.join(PWD, 'data/temp')
MODELS_DIR = os.path.join(PWD, 'models')
TOPIC_DIR = os.path.join(PWD, '1-theme-talker/topics')
SCRIPT_DIR = os.path.join(PWD, '1-theme-talker/results', '{theme}')
OUTPUT_DIR = os.path.join(PWD, '1-theme-talker/results', '{theme}')
AUDIO_DIR = os.path.join(PWD, '1-theme-talker/results', '{theme}', 'audio')
CLIP_DIR = os.path.join(PWD, '1-theme-talker/results', '{theme}', 'clips')
IMAGE_DIR = os.path.join(PWD, '1-theme-talker/results', '{theme}', 'images')
TEMP_DIR = os.path.join(PWD, '1-theme-talker/results', '{theme}', 'temp')

# Spine Pipeline Configuration
ENABLE_SPINE_PIPELINE = True  # 启用Spine流水线

RESIZED_IMAGE_DIR = os.path.join(IMAGE_DIR, 'resized')  # 新增：处理后图片的子目录名
FILTERED_IMAGE_DIR=os.path.join(TEMP_DIR, 'filtered_images')
# 设置基础路径
SETTING_DIR = os.path.join(BASE_DIR, "setting")
CONFIG_FILE_PATH = os.path.join(SETTING_DIR, "settings.yaml")
GOOGLE_OAUTH_JSON = os.path.join(SETTING_DIR, 'uploadyoutube.json')

# Asset configuration
# PWD = os.getcwd()
ASSET_DIR = os.path.join(BASE_DIR, "assets")
INTRO_MUSIC = os.path.join(ASSET_DIR, 'music','intro_8.m4a')
OUTRO_MUSIC = os.path.join(ASSET_DIR, 'music','outro.m4a')

# 数据目录路径
DATA_DIR = os.path.join(PWD, 'data')
LOG_DIR = os.path.join(PWD, 'data/logs')
VIDEO_DIR = os.path.join(PWD, 'data/videos', '{theme}')
DATABASE_PATH = os.path.join(PWD, 'data', 'data.db')
EMBEDDINGS_PATH = os.path.join(DATA_DIR, 'embeddings')
#  设置音频缓存路径
AUDIO_CACHE_DIR = os.path.join(PWD, 'data/audio_cache')
# 设置向量存储路径
VECTOR_STORE_DIR = os.path.join(os.path.dirname(DATABASE_PATH), 'vector_store')
FAISS_INDEX_PATH = os.path.join(VECTOR_STORE_DIR, 'image_embeddings.index')
METADATA_PATH = os.path.join(VECTOR_STORE_DIR, 'metadata.json')
# 文本处理相关路径
# Animation Drama Related Paths
ANIMATION_DRAMA_DIR = os.path.join(PWD, '2-animation-drama')
ANIMATION_OUTPUTS_DIR = os.path.join(ANIMATION_DRAMA_DIR, 'outputs')
ANIMATION_EPISODES_DIR = os.path.join(ANIMATION_DRAMA_DIR, 'episodes')
ANIMATION_TEMP_DIR = os.path.join(ANIMATION_DRAMA_DIR, 'temp')



# Video全局参数
VIDEO_RESOLUTION = (1920, 1080)  # Width x Height
VIDEO_BITRATE = '6000K'  # 提升至6Mbps
VIDEO_FPS = 25
VIDEO_PRESET = 'medium'
VIDEO_CRF = 23  # 降低CRF值以提高质量（范围0-51，越小质量越好）
VIDEO_CODEC = 'libx264'
VIDEO_AUDIO_CODEC = 'aac'
VIDEO_AUDIO_BITRATE = '192k'  # 新增：音频比特率

XFADE_TRANSITION_TIME = '1.000'
VOICE_DELAY_TIME = 1000 #milliseconds
# 新增：FFmpeg 通用参数组
FFMPEG_BASE_PARAMS = [
    '-hide_banner',
    '-loglevel', 'error',
    '-stats',
    '-stats_period', '10'
]

# 新增：视频编码通用参数组
VIDEO_ENCODE_PARAMS = [
    '-c:v', VIDEO_CODEC,
    '-preset', VIDEO_PRESET,
    '-crf', str(VIDEO_CRF),
    '-r', str(VIDEO_FPS),
    '-movflags', '+faststart',  # 支持快速播放
    '-pix_fmt', 'yuv420p'      # 确保兼容性
]

# 新增：音频编码通用参数组
AUDIO_ENCODE_PARAMS = [
    '-c:a', VIDEO_AUDIO_CODEC,
    '-b:a', VIDEO_AUDIO_BITRATE
]

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Cache-Control": "max-age=0",
    "Referer": "https://www.google.com"
}


# 字体配置
EN_FONT = "/home/<USER>/.fonts/static/PlaywriteAUQLD-Regular.ttf"
JP_FONT = "/home/<USER>/.fonts/MPLUSRounded1c-Regular.ttf"
CN_FONT = "/home/<USER>/.fonts/ZhiMangXing-Regular.ttf"  # 请替换为实际的中文字体路径
PT_FONT = "/home/<USER>/.fonts/Pacifico-Regular.ttf"  # 请替换为实际的葡萄牙语字体路径
ES_FONT = "/home/<USER>/.fonts/Pacifico-Regular.ttf"  # 请替换为实际的西班牙语字体路径

# 语言和字体的映射表
LANGUAGE_FONT_MAP = {
    'English': EN_FONT,
    'Japanese': JP_FONT,
    'Chinese': CN_FONT,
    'Portuguese': PT_FONT,
    'Spanish': ES_FONT
    # 可以根据需要添加更多语言
}

EXPECTED_EMBEDDING_DIMENSION = 3072

# 添加语言缩写对照表
LANGUAGE_CODES = {
    "English": "en",
    "english": "en",
    "Spanish": "es",
    "spanish": "es",
    "Portuguese": "pt",
    "portuguese": "pt",
    "Japanese": "ja",
    "japanese": "ja",
    "Chinese": "zh",
    "chinese": "zh",
    "中文": "zh",
    "zh-cn": "zh",
    "zh-tw": "zh"
}


# Placeholder for YAML configuration data
_config_data = None

def load_config():
    global _config_data

    if not os.path.exists(CONFIG_FILE_PATH):
        logger.error(f"Configuration file not found at {CONFIG_FILE_PATH}")
        raise FileNotFoundError(f"Configuration file not found at {CONFIG_FILE_PATH}")

    try:
        with open(CONFIG_FILE_PATH, 'r') as file:
            _config_data = yaml.safe_load(file)
            logger.info(f"Configuration successfully loaded from {CONFIG_FILE_PATH}")
    except yaml.YAMLError as exc:
        logger.error(f"Failed to parse the configuration file: {exc}")
        raise

def get_param(param_path, default=None):
    if _config_data is None:
        logger.error("Configuration has not been loaded. Call load_config() first.")
        raise RuntimeError("Configuration data is not loaded. Call load_config() first.")

    keys = param_path.split('.')
    value = _config_data

    try:
        for key in keys:
            value = value[key]
        return value
    except KeyError:
        logger.warning(f"Parameter '{param_path}' not found in configuration. Using default value.")
        return default

def reload_config():
    logger.info("Reloading configuration...")
    load_config()
    logger.info("Configuration successfully reloaded.")

# Automatically load the configuration when the module is imported
load_config()

# 图片元数据文件命名规则
IMAGE_METADATA_SUFFIX = "images_metadata"

def get_image_metadata_path(directory: str, theme: str) -> str:
    """
    获取图片元数据文件的完整路径

    Args:
        directory: 目录路径
        theme: 主题名称

    Returns:
        str: 元数据文件的完整路径
    """
    filename = f"{theme}_{IMAGE_METADATA_SUFFIX}.json"
    return os.path.join(directory, filename)