# generate_summary.py
import sys
import os
import re
import argparse
from typing import List, Dict, Tuple, Any, Optional
from tqdm import tqdm
import tiktoken
from config import logger, get_param
from modules.nlp_model import LANGUAGE_CODES
from modules.gpt4_interface import call_gpt_json_response
from modules.embedding_management import (
    get_embedding_manager,
    get_embeddings
)
from modules.text_preprocessing import split_text_for_analysis
from modules.utils import smart_paragraph_split, read_file_with_encoding
import unicodedata
import numpy as np
from dataclasses import dataclass
import json
import math
import time

MAX_GROUP_TOKENS = 10000
CHAPTER_SUMMARY_MAX_LENGTH = 1000
GROUP_SUMMARY_MAX_LENGTH = 3000
CONTEXT_MAX_LENGTH = 1000
FINAL_SUMMARY_MAX_TOKENS = 10000
PROGRESS_SAVE_INTERVAL_CHAPTERS = 5
PROGRESS_SAVE_INTERVAL_GROUPS = 3

def preprocess_chinese_text(text: str) -> str:
    """预处理中文文本"""
    # 统一全角/半角字符
    text = unicodedata.normalize('NFKC', text)
    
    # 处理特殊引号
    text = re.sub(r'[「『]', '"', text)
    text = re.sub(r'[」』]', '"', text)
    
    # 处理破折号和省略号
    text = re.sub(r'[—]{2,}', '——', text)
    text = re.sub(r'[.]{3,}|…{2,}', '……', text)
    
    # 确保标点符号后有适当的空格
    text = re.sub(r'([。！？…）】」』\)])(?!\s)', r'\1\n', text)
    
    return text

def save_summary(summary: str, output_path: str) -> None:
    """保存摘要到文件
    
    Args:
        summary: 要保存的摘要文本
        output_path: 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(summary)
        logger.info(f"Summary successfully saved to {output_path}")
    except Exception as e:
        logger.error(f"Error saving summary to {output_path}: {e}")
        raise

def summarize_chunk(i, chunk, style, language, embedding_manager, window_size):
    """生成单个章节的摘要"""
    try:
        # 获取当前chunk的顺序上下文信息
        embedding, context_texts = embedding_manager.get_embedding_with_context(
            text=chunk,
            sequence_position=i,
            window_size=window_size
        )
        
        # 精简上下文信息，只保留关键句或短句
        simplified_context = "\n".join([f"- {text}" for text in context_texts])
        
        data = {
            "text": chunk,
            "max_length": 500,  # 根据需要调整
            "style": style,
            "language": language,
            "context_info": simplified_context  # 精简后的上下文信息
        }
        response = call_gpt_with_retry("summarize_chapter", data)
        if response and "chapter_summary" in response:
            return i, response["chapter_summary"]
        else:
            logger.warning(f"章节 {i} 的摘要为空")
            return i, None
    except Exception as e:
        logger.error(f"章节 {i} 的摘要生成失败: {e}")
        return i, None

def enhance_context_ordered(
    context: str,
    summaries: List[str],
    part_index: int,
    window_size: int = 2,
    max_context_length: int = 1000
) -> str:
    """使用顺序增强上下文"""
    if not context and part_index == 0:
        return ""
    
    # 保持前文的顺序上下文
    enhanced_context = context[-max_context_length:] + "\n\n相关章节："
    
    # 添加当前部分的前后文本作为上下文
    start = max(0, part_index - window_size)
    end = min(len(summaries), part_index + window_size + 1)
    relevant_summaries = summaries[start:end]
    
    for summary in relevant_summaries:
        enhanced_context += f"\n- {summary[:100]}..."  # 只保留前100个字符
    
    return enhanced_context

def call_gpt_with_retry(function_name: str, data: Dict[str, Any], retries: int = 2, delay: int = 2) -> Dict[str, Any]:
    """调用GPT接口并实现重试机制"""
    required_params = {
        "summarize_chapter": ["text", "max_length", "style", "language", "context_info"],
        "summarize_group": ["summaries", "context", "max_length", "style", "language"],
        "generate_final_summary_part": ["previous_context", "group_summaries", "part_index", "total_parts", "max_length", "style", "language"]
    }
    
    # 验证必要参数
    if function_name in required_params:
        missing_params = [param for param in required_params[function_name] if param not in data]
        if missing_params:
            raise ValueError(f"Missing required parameters for {function_name}: {missing_params}")
    
    for attempt in range(retries):
        try:
            response = call_gpt_json_response(function_name, data)
            if response:
                return response
        except Exception as e:
            logger.error(f"GPT 调用失败，尝试次数 {attempt + 1}/{retries}: {e}")
            time.sleep(delay)
    raise Exception(f"GPT 调用在重试后仍然失败: {function_name}")

def group_summaries_ordered(
    summaries: List[str],
    target_tokens: int = 10000
) -> List[List[str]]:
    """按顺序和令牌数分组摘要"""
    groups = []
    current_group = []
    current_tokens = 0
    tokenizer = tiktoken.get_encoding('cl100k_base')
    
    for summary in summaries:
        summary_tokens = len(tokenizer.encode(summary))
        
        if current_tokens + summary_tokens > target_tokens and current_group:
            groups.append(current_group)
            current_group = []
            current_tokens = 0
        
        current_group.append(summary)
        current_tokens += summary_tokens
    
    if current_group:
        groups.append(current_group)
    
    return groups

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='生成小说摘要'
    )
    
    parser.add_argument(
        'input_file',
        help='输入小说文本文件的路径'
    )
    
    parser.add_argument(
        '--window_size',
        type=int,
        default=get_param('WINDOW_SIZE', 2),
        help='上下文窗口大小'
    )
    
    args = parser.parse_args()
    
    # 从配置中获取其他参数
    args.output = get_param('OUTPUT_FILE', f"{os.path.splitext(args.input_file)[0]}_summary.txt")
    args.max_tokens = get_param('MAX_TOKENS', 8000)  # 输入token限制
    args.summary_length = get_param('SUMMARY_LENGTH', 10000)  # 期望的最终摘要长度
    args.gpt_output_limit = get_param('GPT_OUTPUT_LIMIT', 4096)  # GPT模型的输出token限制
    args.style = get_param('SUMMARY_STYLE', 'engaging')
    args.compression_ratio = get_param('COMPRESSION_RATIO', 0.3)
    args.language = get_param('LANGUAGE', 'Chinese')
    
    return args

def get_chunk_context(
    current_idx: int, 
    chunks: List[str], 
    embeddings: List[np.ndarray],
    window_size: int = 2
) -> Dict[str, Any]:
    """获取当前chunk的顺序上下文信息"""
    manager = get_embedding_manager()
    current_chunk = chunks[current_idx]
    
    # 获取基于顺序的上下文
    _, context_texts = manager.get_embedding_with_context(
        text=current_chunk,
        sequence_position=current_idx,
        window_size=window_size
    )
    
    # 简化上下文信息
    context = {
        "context_texts": context_texts  # 添加顺序上下文文本
    }
    
    return context

def process_novel(
    input_file: str,
    output_file: str = None,
    max_tokens: int = None,
    summary_length: int = None,
    gpt_output_limit: int = None,
    style: str = None,
    compression_ratio: float = None,
    language: str = None,
    window_size: int = 2
) -> None:
    """处理小说文本的主要流程"""
    try:
        # 获取输出目录
        output_dir = os.path.dirname(output_file) if output_file else os.path.dirname(input_file)
        os.makedirs(output_dir, exist_ok=True)
        
        # 尝试加载之前的进度
        progress = load_progress(output_dir)
        if progress:
            logger.info("找到之前的处理进度，继续处理...")
            chapter_summaries = progress.get('chapter_summaries', [])
            group_summaries = progress.get('group_summaries', [])
            final_summary_parts = progress.get('final_summary_parts', [])
            current_stage = progress.get('current_stage', 'chapter_summary')
        else:
            chapter_summaries = []
            group_summaries = []
            final_summary_parts = []
            current_stage = 'chapter_summary'

        # 如果参数为None，从配置中获取默认值
        max_tokens = max_tokens or get_param('MAX_TOKENS', 8000)
        summary_length = summary_length or get_param('SUMMARY_LENGTH', 10000)
        gpt_output_limit = gpt_output_limit or get_param('GPT_OUTPUT_LIMIT', 4096)
        style = style or get_param('SUMMARY_STYLE', 'engaging')
        compression_ratio = compression_ratio or get_param('COMPRESSION_RATIO', 0.3)
        language = language or get_param('LANGUAGE', 'Chinese')
        
        if not output_file:
            output_file = get_param('OUTPUT_FILE', f"{os.path.splitext(input_file)[0]}_summary.txt")
        
        start_time = time.time()
        
        # 读取和处理文本
        novel_text = read_file_with_encoding(input_file)
        if language == 'Chinese':
            novel_text = preprocess_chinese_text(novel_text)
            
        # 获取EmbeddingManager实例
        embedding_manager = get_embedding_manager()

        # 1. 分割文本并生成embeddings（如果还没开始）
        if current_stage == 'chapter_summary':
            text_chunks = split_text_for_analysis(novel_text, max_tokens)
            chunk_embeddings = embedding_manager.get_batch_embeddings(text_chunks, maintain_sequence=True)
            
            # 2. 为每个文本块生成摘要
            start_idx = len(chapter_summaries)  # 从上次处理的位置继续
            for i, chunk in enumerate(tqdm(text_chunks[start_idx:], desc="生成章节摘要")):
                context_info = get_chunk_context(i + start_idx, text_chunks, chunk_embeddings, window_size=window_size)
                
                data = {
                    "text": chunk,
                    "max_length": CHAPTER_SUMMARY_MAX_LENGTH,  # 使用常量
                    "style": style,
                    "language": language,
                    "context_info": context_info["context_texts"]
                }
                response = call_gpt_with_retry("summarize_chapter", data)
                logger.debug(f"GPT Response for chapter {i + start_idx}: {response}")  # 添加调试日志
                
                if response and "chapter_summary" in response:
                    chapter_summaries.append(response["chapter_summary"])
                    # 定期保存进度
                    if (i + 1) % PROGRESS_SAVE_INTERVAL_CHAPTERS == 0:  # 使用常量
                        save_progress({
                            'chapter_summaries': chapter_summaries,
                            'current_stage': 'chapter_summary',
                            'processed_chunks': i + start_idx + 1
                        }, output_dir)
                else:
                    logger.warning(f"章节 {i + start_idx} 的摘要生成失败或为空")
            
            current_stage = 'group_summary'
            save_progress({
                'chapter_summaries': chapter_summaries,
                'current_stage': current_stage
            }, output_dir)

        # 3. 使用顺序和令牌数分组（如果还没完成）
        if current_stage == 'group_summary':
            summary_groups = group_summaries_ordered(
                summaries=chapter_summaries,
                target_tokens=MAX_GROUP_TOKENS
            )
            
            # 4. 生成组级摘要
            start_idx = len(group_summaries)  # 从上次处理的位置继续
            for i, group in enumerate(tqdm(summary_groups[start_idx:], desc="生成组级摘要")):
                group_context = enhance_context_ordered(
                    context="",
                    summaries=summary_groups,
                    part_index=i + start_idx,
                    window_size=window_size
                )
                
                data = {
                    "summaries": group,
                    "context": group_context,
                    "max_length": GROUP_SUMMARY_MAX_LENGTH,  # 使用常量定义的长度
                    "style": style,
                    "language": language
                }
                response = call_gpt_json_response("summarize_group", data)
                logger.debug(f"GPT Response for group {i + start_idx}: {response}")  # 添加调试日志
                
                if response and "group_summary" in response:
                    group_summaries.append(response["group_summary"])
                    # 定期保存进度
                    if (i + 1) % PROGRESS_SAVE_INTERVAL_GROUPS == 0:  # 使用常量
                        save_progress({
                            'chapter_summaries': chapter_summaries,
                            'group_summaries': group_summaries,
                            'current_stage': 'group_summary',
                            'processed_groups': i + start_idx + 1
                        }, output_dir)
                else:
                    logger.warning(f"组 {i + start_idx} 的摘要生成失败或为空")
            
            current_stage = 'final_summary'
            save_progress({
                'chapter_summaries': chapter_summaries,
                'group_summaries': group_summaries,
                'current_stage': current_stage
            }, output_dir)

        # 5. 生成最终摘要（如果还没完成）
        if current_stage == 'final_summary':
            # 首先检查是否有组摘要
            if not group_summaries:
                logger.error("没有可用的组摘要内容")
                raise ValueError("Group summaries is empty")
                
            tokenizer = tiktoken.get_encoding('cl100k_base')
            
            # 直接根据目标总长度和GPT输出限制计算部分数
            num_parts = math.ceil(FINAL_SUMMARY_MAX_TOKENS / gpt_output_limit)
            
            logger.debug(f"目标最大tokens: {FINAL_SUMMARY_MAX_TOKENS}")
            logger.debug(f"GPT输出限制: {gpt_output_limit}")
            logger.debug(f"计划分割部分数: {num_parts}")
            
            # 均匀分配组摘要
            base_summaries_per_part = len(group_summaries) // num_parts
            remainder = len(group_summaries) % num_parts
            
            start_idx = len(final_summary_parts)
            current_total_tokens = sum(len(tokenizer.encode(part)) for part in final_summary_parts)
            
            for i in range(start_idx, num_parts):
                remaining_tokens = FINAL_SUMMARY_MAX_TOKENS - current_total_tokens
                if remaining_tokens <= 0:
                    logger.warning("已达到最大token限制，停止生成")
                    break
                
                # 计算当前部分的组摘要范围
                start_group_idx = i * base_summaries_per_part + min(i, remainder)
                end_group_idx = (i + 1) * base_summaries_per_part + min(i + 1, remainder)
                
                if i == num_parts - 1:
                    end_group_idx = len(group_summaries)
                
                current_group_summaries = group_summaries[start_group_idx:end_group_idx]
                
                # 获取上下文
                context = ""
                if i > 0 and final_summary_parts:
                    context = final_summary_parts[-1][-CONTEXT_MAX_LENGTH:]
                
                # 为每部分分配合理的token限制
                target_tokens_per_part = min(
                    gpt_output_limit,
                    FINAL_SUMMARY_MAX_TOKENS // num_parts if i < num_parts - 1 
                    else remaining_tokens  # 最后一部分使用所有剩余tokens
                )
                
                data = {
                    "group_summaries": current_group_summaries,
                    "previous_context": context,
                    "part_index": i + 1,
                    "total_parts": num_parts,
                    "max_length": target_tokens_per_part,
                    "style": style,
                    "language": language,
                    # 添加全局上下文信息
                    "global_context": {
                        "total_groups": len(group_summaries),
                        "current_group_range": f"{start_group_idx+1}-{end_group_idx}",
                        "story_progress": f"{(end_group_idx/len(group_summaries))*100:.1f}%"
                    }
                }
                
                logger.debug(f"处理部分 {i+1}/{num_parts}")
                logger.debug(f"组摘要范围: {start_group_idx} -> {end_group_idx}")
                logger.debug(f"当前部分目标tokens: {target_tokens_per_part}")
                logger.debug(f"剩余可用tokens: {remaining_tokens}")
                
                try:
                    response = call_gpt_with_retry("generate_final_summary_part", data)
                    
                    if response and "summary_part" in response:
                        final_summary_parts.append(response["summary_part"])
                        # 保存进度
                        save_progress({
                            'chapter_summaries': chapter_summaries,
                            'group_summaries': group_summaries,
                            'final_summary_parts': final_summary_parts,
                            'current_stage': 'final_summary',
                            'processed_parts': i + 1
                        }, output_dir)
                    else:
                        logger.error(f"最终摘要部分 {i} 返回内容为空")
                        raise Exception("Empty summary content")
                except Exception as e:
                    logger.error(f"生成最终摘要部分 {i} 失败: {e}")
                    raise

            # 合并所有部分
            final_text = "\n\n".join(part.strip() for part in final_summary_parts)
            
            # 智能分段并保存
            paragraphs = smart_paragraph_split(final_text)
            final_text = '\n\n'.join(paragraphs)
            save_summary(final_text, output_file)
            
            # 处理完成后删除进度文件
            progress_file = os.path.join(output_dir, "summary_progress.json")
            if os.path.exists(progress_file):
                os.remove(progress_file)
                
            logger.info(f"Processing completed in {time.time() - start_time:.2f} seconds")
            
    except Exception as e:
        logger.error(f"处理小说时出错: {e}")
        raise
        
def main():
    """主函数"""
    try:
        # 解析参数
        args = parse_arguments()
        
        process_novel(
            input_file=args.input_file,
            output_file=args.output,
            max_tokens=args.max_tokens,
            summary_length=args.summary_length,
            gpt_output_limit=args.gpt_output_limit,
            style=args.style,
            compression_ratio=args.compression_ratio,
            language=args.language,
            window_size=args.window_size
        )
    except Exception as e:
        logger.error(f"Error processing novel: {e}")
        sys.exit(1)
    finally:
        # 确保嵌入管理器保存索引
        embedding_manager = get_embedding_manager()
        embedding_manager.save()

def save_progress(state: dict, output_dir: str):
    """保存处理进度"""
    progress_file = os.path.join(output_dir, "summary_progress.json")
    with open(progress_file, "w", encoding="utf-8") as f:
        json.dump(state, f, ensure_ascii=False, indent=2)

def load_progress(output_dir: str) -> Optional[dict]:
    """加载处理进度"""
    progress_file = os.path.join(output_dir, "summary_progress.json")
    if os.path.exists(progress_file):
        with open(progress_file, "r", encoding="utf-8") as f:
            return json.load(f)
    return None

if __name__ == "__main__":
    main()