#!/bin/bash

#set -x  # Enable debugging

# Check if theme argument is provided
if [ "$#" -lt 1 ]; then
    echo "Usage: $0 <theme> [--lang <language>] [--script_type <type>] [--english_voice <voice>] [functions...]"
    exit 1
fi

# Configuration
theme="$1"
shift
lang="English"
script_type="peoplestory"
english_voice="English"
functions=()

# Process the arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --lang)
            lang="$2"
            shift 2
            ;;
        --script_type)
            script_type="$2"
            shift 2
            ;;
        --english_voice)
            english_voice="$2"
            shift 2
            ;;
        all)
            functions=(1 2 3 4 5 6 7 8 9 10 11)
            shift
            ;;
        [0-9]*)
            functions+=("$1")
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Debugging output to verify argument processing
echo "Language: $lang"
echo "Script Type: $script_type"
echo "English Voice: $english_voice"
echo "Functions: ${functions[@]}"

PWD=$(pwd)
SCRIPT_DIR="$PWD/1-theme-talker/results/$theme"
LOG_FILE="$PWD/data/logs/$theme_$(date +%Y-%m-%d_%H-%M-%S).log"  ####
COVER_DIR="cover_clips"

# Create script directory if it doesn't exist
mkdir -p "$SCRIPT_DIR"

# Logging functions
log_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    #echo "[$timestamp][shell:INFO] $1" | tee -a "$LOG_FILE"
    echo "[$timestamp][shell:INFO] $1"
}

log_error() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    #echo "[$timestamp][shell:ERROR] $1" | tee -a "$LOG_FILE"
    echo "[$timestamp][shell:ERROR] $1"
}

# Run command function
run_command() {
    log_info "Running command: $1"
    eval "$1"
    if [ $? -ne 0 ]; then
        log_error "Error running command: $1"
        exit 1
    fi
}

# Get language code from config.py
get_language_code() {
    local language=$1
    if [ "$language" = "English" ]; then
        echo ""
    else
        lang_code=$(python -c "from config import LANGUAGE_CODES; print(LANGUAGE_CODES.get('$language', ''))")
        #echo "Debug: get_language_code: language=$language, lang_code=$lang_code"
        echo "$lang_code"
    fi
}

setup_language_variables() {
    local lang="$1"
    lang_code=$(get_language_code "$lang")
    #echo "Debug: setup_language_variables: lang=$lang, lang_code=$lang_code"
    lang_suffix="${lang_code:+_$lang_code}"
    

    script_file_lang="${script_file/.txt/$lang_suffix.txt}"
    json_file_lang="${json_file/.json/$lang_suffix.json}"
    audio_json_file="${json_file/.json/${lang_suffix}_audio.json}"
    image_json_file="${json_file/.json/${lang_suffix}_images.json}"
    video_file_lang="${json_file/.json/${lang_suffix}_video.mp4}"
    subtitle_file_lang="${audio_file/.wav/${lang_suffix}.srt}"
    sub_video_file="${json_file/.json/${lang_suffix}_sub.mp4}"
    final_video_file="${json_file/.json/${lang_suffix}_final.mp4}"
 
    front_cover_clip="${SCRIPT_DIR}/${COVER_DIR}/front_cover_clip${lang_suffix}.mp4"
    end_cover_clip="${SCRIPT_DIR}/${COVER_DIR}/end_cover_clip${lang_suffix}.mp4"
   
    #echo "Debug: lang=$lang, front_cover_clip=$front_cover_clip"
    #echo "Debug: end_cover_clip=$script_file_lang"
    # echo "Debug: json_file_lang=$json_file_lang"
}

synthesize_audio() {
    setup_language_variables "$1"
    #run_command "python synthesize_audio.py --text \"$script_file_lang\" --theme \"$theme\" --lang \"$1\""
    run_command "python text2audio.py --text \"$script_file_lang\" --theme \"$theme\" --lang \"$1\""
    
    log_info "音频已合成：$audio_file_lang"
}

assign_images() {
    setup_language_variables "$1"
    local clip_dir="$SCRIPT_DIR/source-clips"
    
    #检查并生成视频描述
    if [ -d "$clip_dir" ]; then
        run_command "python generate_video_desc.py --video_dir \"$clip_dir\" --json \"$clip_dir/${theme}_metadata.json\""
    fi
    
 
    run_command "python assign_clip_image.py --json \"$audio_json_file\" --image_dir \"$SCRIPT_DIR/images/\" --theme \"$theme\" --clip_dir \"$clip_dir\" --generate_image"
 
    log_info "图片已分配：$script_file_lang"
}

image_generation() {
    setup_language_variables "$1"
    #local result_json="${audio_json_file%.*}_result.json"
    run_command "python image_generation.py --json \"$image_json_file\" --image_dir \"$SCRIPT_DIR/ai_images\""
    log_info "AI图片已生成"
}

generate_video() {
    setup_language_variables "$1"
    local result_json="${image_json_file%.*}_final.json"
    run_command "python generate_video.py --input \"$result_json\" --theme \"$theme\" --lang \"$1\" --crop --upscale"
    log_info "视频已生成：$video_file_lang"
}

generate_cover_clip() {
    setup_language_variables "$1"
    run_command "python generate_cover_clip.py \"$SCRIPT_DIR/images/\" --lang \"$1\""
    log_info "封面视频片段已生成"
}

generate_cover_image() {
    setup_language_variables "$1"
    run_command "python generate_cover_image.py --image \"$SCRIPT_DIR/covers/\" --script \"$script_file_lang\" --lang \"$1\""
    log_info "封面图片已生成"
}

generate_metadata() {
    setup_language_variables "$1"
    run_command "python generate_metadata.py \"$script_file_lang\" --lang \"$1\""
    log_info "元数据已生成"
}

generate_subtitles() {
    setup_language_variables "$1"
    run_command "python generate_subtitles.py --script \"$script_file_lang\" --video \"$video_file_lang\" --lang \"$1\""
    log_info "字幕已生成：$subtitle_file_lang"
}

embed_subtitles() {
    setup_language_variables "$1"
    local lang_param="$1"
    
    # 处理空参数和 English-Story 的情况
    if [ -z "$lang_param" ] || [ "$lang_param" = "English-Story" ]; then
        lang_param="English"
    fi
    
    run_command "python embed_subtitles.py --video \"$video_file_lang\" --sub \"$subtitle_file_lang\" --lang \"$lang_param\""
    log_info "字幕已嵌入视频：$sub_video_file"
}

concat_sub_video_with_cover() {
    setup_language_variables "$1"
    local video_file_to_use="$sub_video_file"
    local lang="$1"
    
    # 如果语言为空或为 English，则使用 ENGLISH_VOICE 设置
    if [ -z "$lang" ] || [ "$lang" = "English" ]; then
        lang="$ENGLISH_VOICE"
    fi
    
    # 直接使用 end_cover_clip 作为 outro 视频
    local outro_video_path="$end_cover_clip"
    
    # 检查 outro 视频文件是否存在
    if [ ! -f "$outro_video_path" ]; then
        log_error "Outro 视频文件不存在：$outro_video_path"
        exit 1
    fi
    
    # 修改命令构建，使用双引号转义单引号
    local concat_command="python concat_videos.py concat --video \"${video_file_to_use}\" --outro_video \"${outro_video_path}\""
    
    run_command "$concat_command"
    log_info "最终视频已创建（包含封面和 outro）：$final_video_file"
}

upload_video() {
    setup_language_variables "$1"
    local cover_image="${final_video_file/.mp4/.jpg}"
    run_command "python upload_to_google.py --script \"$script_file_lang\" --video \"$final_video_file\" --cover \"$cover_image\" --lang \"$1\""
    log_info "视频已上传：$final_video_file"
}

# Main function to execute the flow for the specified language
main() {
    local script_file="$SCRIPT_DIR/$theme.txt"
    local json_file="${script_file/.txt/.json}"
    local audio_file="${script_file/.txt/.wav}"
    local subtitle_file="${audio_file/.wav/.srt}"
    local metadata_file="${script_file/.txt/_metadata.txt}"

    log_info "正在处理语言：$lang"
    for func in "${functions[@]}"; do
        case "$func" in
            1)
                if [ "$lang" = "English" ] || [ "$lang" = "English-Story" ]; then
                    log_info "生成脚本需要单独处理。请确保英语脚本 $script_file 已经存在。"
                else
                    log_info "正在翻译脚本为 $lang"
                    run_command "python translate_script.py --lang $lang --orig_lang English \"$script_file\""
                fi
                ;;
            2) synthesize_audio "$lang" ;;
            3) assign_images "$lang" ;;
            4) image_generation "$lang" ;;
            5) generate_video "$lang" ;;
            6) generate_cover_clip "$lang" ;;
            7) generate_cover_image "$lang" ;;
            8) generate_subtitles "$lang" ;;
            9) embed_subtitles "$lang" ;;
            10) generate_metadata "$lang" ;;
            11) concat_sub_video_with_cover "$lang" ;;
            12) upload_video "$lang" ;;
            *) 
                log_error "无效的功能编号: $func"
                ;;
        esac
    done
}

main "$@"

# 在脚本最后添加，关闭调试输出
#set +x
