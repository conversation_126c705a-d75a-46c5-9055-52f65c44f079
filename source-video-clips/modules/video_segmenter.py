import logging
import whisper
import ffmpeg
import json
import argparse
import sys
from pathlib import Path
from datetime import datetime
from config import AUDIO_FORMAT, OUTPUT_DIR, WHISPER_MODEL, VIDEO_RESOLUTION, VIDEO_BITRATE, VIDEO_FPS, VIDEO_PRESET, VIDEO_CRF, VIDEO_CODEC, VIDEO_AUDIO_CODEC, VIDEO_AUDIO_BITRATE, FFMPEG_BASE_PARAMS, VIDEO_ENCODE_PARAMS, logger
from modules.utils import normalized_filename
from typing import Optional, List, Dict, Tuple, Any
from functools import lru_cache
import spacy
from tenacity import retry, stop_after_attempt, wait_exponential
from contextlib import contextmanager
import subprocess


# 添加到配置文件中
LANGUAGE_MODELS = {
    "en": "en_core_web_sm",  # 英文模型
    "zh": "zh_core_web_sm",  # 中文模型
}
MIN_SEGMENT_LENGTH = 30  # 最小分段长度（词数）
MAX_SEGMENT_DURATION = 90  # 最大分段时长（秒）
MERGE_ON_INCOMPLETE_SENTENCE = True

# 添加新的常量
END_PUNCTUATION = {
    "。", "！", "？", ".", "!", "?", "...", "…",
    ");", "）。", '."', "']", ")", "）"
}
INCOMPLETE_PUNCTUATION = {
    ",", "，", ";", "；", "-", "–", "...",
    "、", "：", ":"
}

# 新增工具函数
def get_segment_time(segment: dict, key: str, default: float = 0.0) -> float:
    """统一获取片段时间的方法"""
    primary_key = key
    fallback_key = f"{key}_time"
    return segment.get(primary_key, segment.get(fallback_key, default))

def get_segment_duration(segment: dict) -> float:
    """计算片段持续时间"""
    return get_segment_time(segment, "end") - get_segment_time(segment, "start")

@contextmanager
def safe_file_operation(file_path: Path):
    """文件操作的安全上下文管理器"""
    backup_path = None
    if file_path.exists():
        backup_path = file_path.with_suffix(f'{file_path.suffix}.bak')
        file_path.rename(backup_path)
    try:
        yield
    except Exception as e:
        if backup_path and backup_path.exists():
            backup_path.rename(file_path)
        raise e
    finally:
        if backup_path and backup_path.exists():
            backup_path.unlink()

# 修改后的音频提取函数
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def extract_audio(video_path: Path) -> Optional[Path]:
    """Extract audio from video file with retry mechanism."""
    audio_path = video_path.with_suffix(f'.{AUDIO_FORMAT}')
    try:
        process = (
            ffmpeg
            .input(str(video_path))
            .output(str(audio_path), acodec='pcm_s16le', ac=1, ar=16000)
            .overwrite_output()
        )
        with process.run_async(pipe_stdout=True, pipe_stderr=True) as proc:
            proc.wait()
        logger.info(f"Extracted audio to: {audio_path}")
        return audio_path
    except ffmpeg.Error as e:
        logger.error(f"Failed to extract audio: {e.stderr.decode() if e.stderr else str(e)}")
        if audio_path.exists():
            audio_path.unlink()
        return None

# 修改后的片段创建函数
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def create_clip(video_path: Path, start_time: float, end_time: float, output_path: Path) -> bool:
    """
    使用 ffmpeg 创建视频片段
    """
    try:
        duration = end_time - start_time
        if duration <= 0:
            logger.error(f"Invalid clip duration: {duration}s")
            return False

        output_args = {
            't': str(duration),
            'c:v': VIDEO_CODEC,
            'b:v': VIDEO_BITRATE,
            'preset': VIDEO_PRESET,
            'crf': str(VIDEO_CRF),
            'r': str(VIDEO_FPS),
            'pix_fmt': 'yuv420p',
            'movflags': '+faststart',
            'an': None,
            'avoid_negative_ts': 'make_zero',
            's': f"{VIDEO_RESOLUTION[0]}x{VIDEO_RESOLUTION[1]}"
        }

        try:
            # 设置 ffmpeg 日志级别为 error
            stream = (
                ffmpeg
                .input(str(video_path), ss=str(start_time))
                .output(str(output_path), **output_args)
                .global_args('-loglevel', 'error')  # 只输出错误信息
                .overwrite_output()
            )

            # 运行命令并捕获输出
            stdout, stderr = stream.run(capture_stdout=True, capture_stderr=True)

            # 验证输出文件
            if not output_path.exists() or output_path.stat().st_size == 0:
                logger.error(f"Failed to create clip: Output file is missing or empty: {output_path}")
                if stderr:
                    logger.error(f"FFmpeg stderr: {stderr.decode()}")
                return False

            return True

        except ffmpeg.Error as e:
            logger.error(f"FFmpeg error - stderr: {e.stderr.decode() if e.stderr else 'None'}")
            logger.error(f"FFmpeg error - stdout: {e.stdout.decode() if e.stdout else 'None'}")
            return False

    except Exception as e:
        logger.error(f"Error creating clip {output_path}: {str(e)}", exc_info=True)
        return False

# 修改后的元数据处理函数
def update_metadata(
    metadata_path: Path,
    segments: List[Dict],
    theme: str,
    normalized_theme: str
) -> bool:
    """
    原子方式更新元数据

    Args:
        metadata_path: 元数据文件路径
        segments: 新的片段列表
        theme: 主题名称
        normalized_theme: 规范化的主题名称

    Returns:
        bool: 更新是否成功
    """
    try:
        with safe_file_operation(metadata_path):
            metadata = {
                "segments": [],
                "last_updated": datetime.now().isoformat()
            }

            if metadata_path.exists():
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        if isinstance(existing_data, dict):
                            metadata["segments"] = existing_data.get("segments", [])
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in metadata file: {metadata_path}")
                except Exception as e:
                    logger.warning(f"Error reading metadata file: {e}")

            metadata["segments"].extend(segments)
            for i, segment in enumerate(metadata["segments"], 1):
                segment["segment_number"] = i

            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=4, ensure_ascii=False)

            logger.info(f"Successfully updated metadata with {len(segments)} new segments")
            return True

    except Exception as e:
        logger.error(f"Error updating metadata: {str(e)}")
        return False

# 修改后的预处理函数
def preprocess_segments(segments: List[Dict], min_duration: float = 3.0) -> List[Dict]:
    """预处理片段，合并过短的片段"""
    preprocessed = []
    current = None

    for segment in segments:
        start = get_segment_time(segment, "start")
        end = get_segment_time(segment, "end")
        duration = end - start

        if duration < min_duration and current:
            current["end_time"] = end
            current["text"] += " " + segment["text"]
        else:
            if current:
                preprocessed.append(current)
            current = {
                "start_time": start,
                "end_time": end,
                "text": segment["text"].strip()
            }

    if current:
        preprocessed.append(current)

    return preprocessed

@lru_cache(maxsize=2)  # 增加缓存大小以支持多语言
def get_nlp_model(lang: str = "en"):
    """Get cached spacy model instance for specified language."""
    try:
        model_name = LANGUAGE_MODELS.get(lang)
        if not model_name:
            logger.warning(f"Unsupported language: {lang}, falling back to English")
            model_name = LANGUAGE_MODELS["en"]
        return spacy.load(model_name)
    except OSError as e:
        logger.error(f"Failed to load spacy model {model_name}: {e}")
        return None

def segment_video(video_path: Path, model, theme: str = None) -> Optional[Dict]:
    """
    Segment video into clips based on transcription.
    """
    logger.debug(f"Starting video segmentation for: {video_path}")

    if isinstance(video_path, str):
        video_path = Path(video_path)

    try:
        # Extract theme from path if not provided
        if theme is None:
            try:
                theme = video_path.parent.name
                logger.debug(f"Extracted theme from path: {theme}")
            except Exception as e:
                logger.error(f"Failed to extract theme from path: {e}")
                theme = "default"

        # Input validation
        if not video_path.exists():
            logger.error(f"Video file not found: {video_path}")
            return None

        if video_path.suffix.lower() not in ['.mp4', '.avi', '.mkv']:
            logger.error(f"Unsupported video format: {video_path.suffix}")
            return None

        # Normalize video filename and theme
        normalized_stem = normalized_filename(video_path.stem)
        normalized_theme = normalized_filename(theme)
        logger.debug(f"Using normalized filename: {normalized_stem}")
        logger.debug(f"Using normalized theme: {normalized_theme}")

        # Create theme-specific output directory structure
        theme_dir = Path(OUTPUT_DIR) / normalized_theme
        clips_dir = theme_dir / "clips"
        clips_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Using clips directory: {clips_dir}")

        # 在这里定义 metadata_path，确保在后续代码中可用
        metadata_path = clips_dir / f"{normalized_theme}_metadata.json"
        logger.debug(f"Using metadata path: {metadata_path}")

        # 初始化元数据结构
        metadata = {
            "theme": theme,
            "normalized_theme": normalized_theme,
            "source_videos": [],
            "segments": [],
            "last_updated": datetime.now().isoformat()
        }

        # 检查是否已处理过该视频
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    existing_metadata = json.load(f)
                    if isinstance(existing_metadata, dict):
                        # 检查视频是否已处理
                        source_videos = existing_metadata.get("source_videos", [])
                        if str(video_path) in source_videos:
                            logger.info(f"Video already processed: {video_path}")
                            return None
                        # 保留现有数据
                        metadata["source_videos"] = source_videos
                        metadata["segments"] = existing_metadata.get("segments", [])
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in metadata file: {metadata_path}")
            except Exception as e:
                logger.warning(f"Error reading metadata file: {e}")

        # 添加当前视频到源视频列表
        if str(video_path) not in metadata["source_videos"]:
            metadata["source_videos"].append(str(video_path))

        # 获取当前片段数量
        current_segment_count = 0
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    existing_metadata = json.load(f)
                    if isinstance(existing_metadata, dict) and "segments" in existing_metadata:
                        current_segment_count = len(existing_metadata["segments"])
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in metadata file: {metadata_path}")
            except Exception as e:
                logger.warning(f"Error reading metadata file: {e}")

        # 提取音频
        audio_path = extract_audio(video_path)
        if not audio_path:
            return None

        try:
            # 转写和预处理
            result = model.transcribe(str(audio_path))
            preprocessed_segments = preprocess_segments(result["segments"])

            # 合并片段
            merged_segments = merge_segments(preprocessed_segments)

            # 处理片段并更新元数据
            successful_segments = process_segments(
                merged_segments,
                video_path,
                clips_dir,
                normalized_theme,
                normalized_stem,
                current_segment_count,
                metadata
            )

            if successful_segments == 0:
                logger.error("No segments were successfully processed")
                return None

            # 更新元数据
            if not update_metadata(metadata_path, metadata["segments"], theme, normalized_theme):
                logger.error("Failed to update metadata")
                return None

            return metadata

        finally:
            # 清理临时文件
            if audio_path and audio_path.exists():
                audio_path.unlink()
                logger.debug(f"Cleaned up temporary audio file: {audio_path}")

    except Exception as e:
        logger.error(f"Error during video segmentation: {e}", exc_info=True)
        return None

def process_segments(
    merged_segments: List[Dict],
    video_path: Path,
    clips_dir: Path,
    normalized_theme: str,
    normalized_stem: str,
    current_segment_count: int,
    metadata: Dict
) -> int:
    """
    处理片段，创建视频片段并更新元数据

    Returns:
        int: 成功处理的片段数量
    """
    successful_segments = 0

    for i, segment in enumerate(merged_segments, 1):
        start_time = segment.get("start", segment.get("start_time", 0))
        end_time = segment.get("end", segment.get("end_time", 0))
        duration = end_time - start_time
        text = segment["text"].strip()

        if duration < 0.5:
            logger.warning(f"Skipping segment {i} - too short: {duration:.2f}s")
            continue

        segment_number = current_segment_count + i
        clip_path = clips_dir / f"{normalized_theme}_segment_{segment_number:04d}.mp4"

        if create_clip(video_path, start_time, end_time, clip_path):
            successful_segments += 1
            segment_info = {
                "segment_number": segment_number,
                "start_time": start_time,         # Consistent key naming
                "end_time": end_time,             # Consistent key naming
                "duration": duration,
                "clip_path": str(clip_path),      # Consistent key naming
                "text": text,                     # Consistent key naming
                "description": "",
                "video_title": video_path.stem
            }
            metadata["segments"].append(segment_info)
            logger.debug(f"Successfully created clip {segment_number}: {clip_path}")

    return successful_segments

def save_metadata(metadata_path: Path, metadata: Dict) -> None:
    """保存或更新元数据文件"""
    try:
        if metadata_path.exists():
            with open(metadata_path, 'r', encoding='utf-8') as f:
                existing_metadata = json.load(f)

            if not isinstance(existing_metadata, dict):
                existing_metadata = {
                    "theme": metadata["theme"],
                    "normalized_theme": metadata["normalized_theme"],
                    "segments": []
                }

            if "segments" not in existing_metadata:
                existing_metadata["segments"] = []

            existing_metadata["segments"].extend(metadata["segments"])
            metadata = existing_metadata

            # 重新排序所有片段的编号
            for i, segment in enumerate(metadata["segments"], 1):
                segment["segment_number"] = i

        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=4, ensure_ascii=False)
        logger.debug(f"Saved metadata to: {metadata_path}")

    except Exception as e:
        logger.error(f"Failed to save metadata: {e}", exc_info=True)
        raise

def detect_language(text: str) -> str:
    """检测文本语言"""
    # 简单的语言检测：检查是否包含中文字符
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
    return "zh" if has_chinese else "en"

def get_nlp_for_text(text: str, nlp_cache: Dict[str, Any]) -> Any:
    """获取或初始化适当的语言模型"""
    lang = detect_language(text)
    if lang not in nlp_cache:
        nlp_cache[lang] = get_nlp_model(lang)
        if not nlp_cache[lang]:
            raise RuntimeError(f"Failed to initialize language model for {lang}")
    return nlp_cache[lang]

def analyze_text_structure(text: str, nlp_cache: Dict[str, Any]) -> dict:
    """改进的文本结构分析"""
    if not text.strip():
        return {
            "is_complete": False,
            "has_conjunction": False,
            "dependencies": set(),
            "word_count": 0
        }

    nlp = get_nlp_for_text(text, nlp_cache)
    doc = nlp(text)

    leading_tokens = list(doc[:3])
    conjunction_pos = {"CCONJ", "SCONJ", "CONJ"}
    conjunction_deps = {"mark", "conj", "cc", "prep"}
    common_conjunctions = {
        "and", "but", "or", "so", "because", "however", "therefore",
        "而且", "但是", "或者", "所以", "因为", "然而", "因此"
    }

    has_conjunction = any(
        token.pos_ in conjunction_pos or
        token.dep_ in conjunction_deps or
        token.text.lower() in common_conjunctions
        for token in leading_tokens
    )

    text_end = text.strip()
    is_complete = any(text_end.endswith(punct) for punct in END_PUNCTUATION)

    return {
        "is_complete": is_complete,
        "has_conjunction": has_conjunction,
        "dependencies": {token.dep_ for token in leading_tokens},
        "word_count": len(doc)
    }

def should_merge(current: Dict, next_segment: Dict, nlp_cache: Dict[str, Any]) -> bool:
    """改进的合并判断逻辑"""
    if not current or not next_segment:
        return False

    current_text = current.get("text", "").strip()
    next_text = next_segment.get("text", "").strip()

    if not current_text or not next_text:
        return False

    merged_duration = (
        next_segment.get("end", next_segment.get("end_time", 0))
        - current.get("start", current.get("start_time", 0))
    )
    if merged_duration > MAX_SEGMENT_DURATION:
        return False

    current_analysis = analyze_text_structure(current_text, nlp_cache)
    next_analysis = analyze_text_structure(next_text, nlp_cache)

    must_merge_conditions = [
        not current_analysis["is_complete"],
        next_analysis["has_conjunction"],
        any(current_text.rstrip().endswith(p) for p in INCOMPLETE_PUNCTUATION),
        current_analysis["word_count"] < MIN_SEGMENT_LENGTH
    ]

    optional_merge_conditions = [
        current_analysis["word_count"] < MIN_SEGMENT_LENGTH * 2
        and next_analysis["word_count"] < MIN_SEGMENT_LENGTH * 2,
        bool(next_analysis["dependencies"] & {"advcl", "relcl", "ccomp", "xcomp"})
    ]

    if any(must_merge_conditions):
        return True

    if any(optional_merge_conditions):
        return merged_duration <= MAX_SEGMENT_DURATION * 0.8

    return False

def merge_segments(segments: List[Dict]) -> List[Dict]:
    """合并片段"""
    if not segments:
        return []

    merged_segments = []
    current_segment = None
    nlp_cache = {}

    try:
        original_count = len(segments)

        for segment in segments:
            normalized_segment = {
                "start_time": segment.get("start", segment.get("start_time", 0)),
                "end_time": segment.get("end", segment.get("end_time", 0)),
                "text": segment.get("text", "").strip(),
                "duration": (
                    segment.get("end", segment.get("end_time", 0))
                    - segment.get("start", segment.get("start_time", 0))
                )
            }

            if current_segment is None:
                current_segment = normalized_segment
                continue

            if should_merge(current_segment, normalized_segment, nlp_cache):
                current_segment["end_time"] = normalized_segment["end_time"]
                current_segment["duration"] = (
                    current_segment["end_time"] - current_segment["start_time"]
                )
                current_segment["text"] = (
                    f"{current_segment['text'].strip()} {normalized_segment['text'].strip()}"
                )
            else:
                merged_segments.append(current_segment)
                current_segment = normalized_segment

        if current_segment:
            merged_segments.append(current_segment)

        final_count = len(merged_segments)
        avg_duration = sum(s["duration"] for s in merged_segments) / final_count
        logger.info(
            f"Merged {original_count} segments into {final_count} segments "
            f"(Average duration: {avg_duration:.2f}s)"
        )
        return merged_segments

    except Exception as e:
        logger.error(f"Error during segment merging: {e}", exc_info=True)
        return segments


def parse_arguments() -> argparse.Namespace:
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的命令行参数
    """
    parser = argparse.ArgumentParser(
        description='视频分段工具 - 将视频分割成基于转录文本的片段'
    )
    parser.add_argument(
        '--video',
        type=str,
        required=True,
        help='要处理的视频文件路径'
    )
    parser.add_argument(
        '--theme',
        type=str,
        default=None,
        help='视频主题名称（默认：从父目录名称提取）'
    )
    parser.add_argument(
        '--model',
        type=str,
        default=WHISPER_MODEL,
        help=f'用于转录的Whisper模型（默认：{WHISPER_MODEL}）'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志记录'
    )

    return parser.parse_args()


def setup_logging(verbose: bool = False) -> None:
    """
    根据详细程度配置日志级别

    Args:
        verbose: 是否启用详细日志记录
    """
    log_level = logging.DEBUG if verbose else logging.INFO
    logger.setLevel(log_level)

    # 如果尚未添加控制台处理程序，则添加一个
    if not logger.handlers:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)


def load_whisper_model(model_name: str) -> Optional[whisper.Whisper]:
    """
    加载Whisper模型

    Args:
        model_name: 要加载的Whisper模型名称

    Returns:
        Optional[whisper.Whisper]: 加载的模型，如果加载失败则为None
    """
    try:
        logger.info(f"正在加载Whisper模型: {model_name}")
        model = whisper.load_model(model_name)
        logger.info(f"成功加载 {model_name} 模型")
        return model
    except Exception as e:
        logger.error(f"加载Whisper模型失败: {e}")
        return None


def main() -> int:
    """
    脚本的主入口点

    Returns:
        int: 退出代码（0表示成功，非零表示失败）
    """
    args = parse_arguments()
    setup_logging(args.verbose)

    logger.info("开始视频分段处理")
    logger.info(f"视频: {args.video}")
    if args.theme:
        logger.info(f"主题: {args.theme}")

    try:
        # 将视频路径转换为Path对象
        video_path = Path(args.video)

        # 验证视频路径
        if not video_path.exists():
            logger.error(f"未找到视频文件: {video_path}")
            return 1

        if video_path.suffix.lower() not in ['.mp4', '.avi', '.mkv']:
            logger.error(f"不支持的视频格式: {video_path.suffix}")
            return 1

        # 加载Whisper模型
        model = load_whisper_model(args.model)
        if not model:
            logger.error("加载Whisper模型失败")
            return 1

        # 处理视频
        logger.info(f"正在处理视频: {video_path}")
        result = segment_video(video_path, model, args.theme)

        if result:
            segment_count = len(result.get("segments", []))
            logger.info(f"成功处理视频，生成了 {segment_count} 个片段")
            return 0
        else:
            logger.error("视频分段失败")
            return 1

    except KeyboardInterrupt:
        logger.info("用户中断了处理")
        return 130
    except Exception as e:
        logger.error(f"发生错误: {e}", exc_info=True)
        return 1
    finally:
        logger.info("视频分段处理完成")


if __name__ == "__main__":
    sys.exit(main())