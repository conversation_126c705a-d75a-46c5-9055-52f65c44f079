import logging
import whisper
import argparse
from pathlib import Path
import json
from typing import Optional, Dict, Any
from modules.logger_setup import setup_logging
from modules.video_searcher import search_videos
from modules.video_downloader import download_videos
from modules.video_segmenter import segment_video
from config import WHISPER_MODEL, MAX_RESULTS, logger
from modules.utils import normalized_filename
from datetime import datetime


def parse_arguments() -> argparse.Namespace:
    """
    Parse and validate command line arguments.
    
    Returns:
        argparse.Namespace: Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description='Video Clipper - Search, download and segment YouTube videos'
    )
    parser.add_argument(
        '--theme',
        type=str,
        required=True,
        help='Theme or keywords for video search'
    )
    parser.add_argument(
        '--urls',
        type=str,
        help='Path to file containing video URLs (one URL per line)'
    )
    parser.add_argument(
        '--exec',
        action='store_true',
        help='Execute both download and segment steps for URLs from file'
    )
    parser.add_argument(
        '--download',
        action='store_true',
        help='Execute search and download steps'
    )
    parser.add_argument(
        '--segment',
        type=str,
        metavar='VIDEO_DIR',
        help='Directory containing videos to segment'
    )
    
    args = parser.parse_args()
    logger.debug(f"Parsed arguments: {vars(args)}")
    return args

def load_existing_json(json_path: Path) -> Dict[str, Any]:
    """
    Load existing JSON file or create new structure.
    
    Args:
        json_path: Path to JSON file
        
    Returns:
        Dict containing JSON data
    """
    if json_path.exists():
        logger.debug(f"Loading existing JSON file: {json_path}")
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.warning(f"Invalid JSON file {json_path}: {e}")
        except Exception as e:
            logger.warning(f"Error reading JSON file {json_path}: {e}")
    
    logger.debug(f"Creating new JSON structure for: {json_path}")
    return {
        'theme': json_path.stem,
        'videos': []
    }

def save_json(json_path: Path, data: Dict[str, Any]) -> bool:
    """
    Save data to JSON file.
    
    Args:
        json_path: Path to JSON file
        data: Data to save
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.debug(f"Successfully saved JSON to: {json_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save JSON to {json_path}: {e}")
        return False

def process_segments(video_path: Path, theme: str, metadata: dict) -> Optional[bool]:
    """
    Update JSON with simplified video processing information.
    
    Args:
        video_path: Path to video file
        theme: Search theme
        metadata: Segment metadata
        
    Returns:
        Optional[bool]: True if successful, False if failed, None if no metadata
    """
    if not metadata:
        logger.debug(f"No metadata provided for: {video_path}")
        return None
        
    normalized_theme = normalized_filename(theme)
    json_path = video_path.parent / f"{normalized_theme}.json"
    logger.debug(f"Processing segments for video: {video_path}")
    
    try:
        json_data = load_existing_json(json_path)
        
        # 获取clips目录路径
        clips_dir = video_path.parent / "clips"
        clips_metadata_path = clips_dir / f"{video_path.stem}_metadata.json"
        
        # Find or create video entry
        video_entry = next(
            (v for v in json_data['videos'] if v.get('filepath') == str(video_path)),
            None
        )
        
        if video_entry is None:
            # 只保留必要的字段，创建简化的视频条目
            video_entry = {
                'url': metadata.get('url', ''),
                'filepath': str(video_path),
                'title': metadata.get('title', ''),
                'processed': True,
                'processed_date': datetime.now().isoformat(),
                'clips_dir': str(clips_dir),
                'clips_metadata': str(clips_metadata_path)
            }
            json_data['videos'].append(video_entry)
            json_data['total_videos'] = len(json_data['videos'])
        else:
            # 更新现有条目时也只更新必要的字段
            video_entry.update({
                'processed': True,
                'processed_date': datetime.now().isoformat(),
                'clips_dir': str(clips_dir),
                'clips_metadata': str(clips_metadata_path)
            })
            # 确保移除 video_info 字段（如果存在）
            video_entry.pop('video_info', None)
        
        # 更新最后处理时间
        json_data['last_updated'] = datetime.now().isoformat()
        
        logger.debug(f"Updated video entry with clips directory information")
        
        return save_json(json_path, json_data)
            
    except Exception as e:
        logger.error(f"Error processing segments for {video_path}: {e}")
        return False

def is_video_downloaded(video_url: str, normalized_theme: str) -> tuple[bool, Optional[Path]]:
    """
    检查视频是否已经完整下载
    
    Args:
        video_url: 视频URL
        normalized_theme: 标准化的主题名
        
    Returns:
        tuple: (是否已下载, 视频文件路径)
    """
    try:
        video_filename = f"{normalized_filename(video_url)}.mp4"
        video_path = Path.cwd() / 'downloads' / normalized_theme / video_filename
        
        if video_path.exists() and video_path.stat().st_size > 0:
            # 检查文件是否完整（不是部分下载）
            try:
                with open(video_path, 'rb') as f:
                    # 读取文件末尾以验证完整性
                    f.seek(-2048, 2)  # 从末尾读取最后2KB
                    f.read()
                return True, video_path
            except Exception as e:
                logger.warning(f"Video file {video_path} appears incomplete: {e}")
                video_path.unlink(missing_ok=True)  # 删除不完整的文件
                return False, None
        return False, None
    except Exception as e:
        logger.error(f"Error checking video download status: {e}")
        return False, None

def is_video_processed(video_path: Path, theme: str) -> bool:
    """
    检查视频是否已经处理过并且处理结果完整
    
    Args:
        video_path: 视频文件路径
        theme: 主题
        
    Returns:
        bool: 是否已处理
    """
    try:
        normalized_theme = normalized_filename(theme)
        json_path = video_path.parent / f"{normalized_theme}.json"
        
        if not json_path.exists():
            return False
            
        with open(json_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
            
        # 查找视频条目
        video_entry = next(
            (v for v in json_data.get('videos', []) 
             if v.get('filepath') == str(video_path)), 
            None
        )
        
        if not video_entry:
            return False
            
        # 检查处理状态和必要文件
        if video_entry.get('processed'):
            clips_metadata = Path(video_entry.get('clips_metadata', ''))
            if clips_metadata.exists():
                try:
                    with open(clips_metadata, 'r') as f:
                        metadata = json.load(f)
                    return bool(metadata)  # 确保元数据不为空
                except Exception as e:
                    logger.warning(f"Invalid metadata file for {video_path}: {e}")
                    return False
                    
        return False
        
    except Exception as e:
        logger.error(f"Error checking video processing status: {e}")
        return False

def execute_download_workflow(theme: str, urls_file: Optional[str] = None) -> Optional[list]:
    """
    Execute video search and download workflow, skipping already downloaded videos.
    """
    logger.info("Starting download workflow")
    
    normalized_theme = normalized_filename(theme)
    logger.debug(f"Using normalized theme: {normalized_theme}")
    
    if urls_file:
        try:
            with open(urls_file, 'r') as f:
                video_urls = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(video_urls)} URLs from file: {urls_file}")
        except Exception as e:
            logger.error(f"Failed to read URLs file {urls_file}: {e}")
            return None
    else:
        video_urls = search_videos(search_keywords=theme, max_results=MAX_RESULTS)
    
    if not video_urls:
        logger.error("No videos found")
        return None
    
    # 过滤已下载的视频
    new_urls = []
    existing_videos = []
    
    for url in video_urls:
        is_downloaded, video_path = is_video_downloaded(url, normalized_theme)
        if is_downloaded:
            logger.info(f"Skipping already downloaded video: {url}")
            if video_path:
                existing_videos.append(video_path)
        else:
            new_urls.append(url)
    
    # 下载新视频
    if new_urls:
        logger.info(f"Downloading {len(new_urls)} new videos")
        downloaded_videos = download_videos(new_urls, search_term=normalized_theme)
        if downloaded_videos:
            existing_videos.extend(downloaded_videos)
    
    return existing_videos if existing_videos else None

def execute_segment_workflow(video_dir: str, theme: str) -> bool:
    """
    Execute video segmentation workflow, skipping already processed videos.
    """
    logger.info("Starting segment workflow")
    video_dir_path = Path(video_dir)
    
    if not video_dir_path.exists():
        logger.error(f"Video directory not found: {video_dir}")
        return False
    
    # Step 3: Load Whisper Model
    logger.info(f"Loading Whisper model: {WHISPER_MODEL}")
    try:
        model = whisper.load_model(WHISPER_MODEL)
        logger.debug("Whisper model loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load Whisper model: {e}")
        return False
    
    # 获取需要处理的视频
    video_files = []
    for video_path in video_dir_path.glob('*.mp4'):
        if is_video_processed(video_path, theme):
            logger.info(f"Skipping already processed video: {video_path.name}")
            continue
        video_files.append(video_path)
    
    if not video_files:
        logger.info("No new videos to process")
        return True
    
    logger.info(f"Found {len(video_files)} videos to process")
    
    # 处理新视频
    success_count = 0
    for video_path in video_files:
        logger.info(f"Processing video: {video_path}")
        try:
            metadata = segment_video(str(video_path), model)
            if metadata:
                if process_segments(video_path, theme, metadata):
                    success_count += 1
                    logger.info(f"Successfully processed: {video_path.name}")
                else:
                    logger.error(f"Failed to process segments for: {video_path.name}")
            else:
                logger.error(f"Failed to generate metadata for: {video_path.name}")
        except Exception as e:
            logger.error(f"Error processing {video_path}: {e}")
    
    logger.info(f"Successfully processed {success_count} out of {len(video_files)} videos")
    return success_count > 0

def main():
    args = parse_arguments()
    setup_logging()
    
    logger.info("Starting Video Clipper Workflow")
    logger.info(f"Theme: {args.theme}")
    logger.debug(f"Working directory: {Path.cwd()}")
    
    try:
        if args.exec:
            if not args.urls:
                logger.error("--urls parameter is required when using --exec")
                return
                
            downloaded_videos = execute_download_workflow(args.theme, args.urls)
            if downloaded_videos:
                video_dir = str(Path(downloaded_videos[0]).parent)
                if not execute_segment_workflow(video_dir, args.theme):
                    return
        else:
            if args.download:
                if not execute_download_workflow(args.theme, args.urls):
                    return
                
            if args.segment:
                if not execute_segment_workflow(args.segment, args.theme):
                    return
                    
    except Exception as e:
        logger.error(f"An error occurred in the main workflow: {e}", exc_info=True)
    finally:
        logger.info("Video Clipper Workflow Completed")

if __name__ == "__main__":
    main() 