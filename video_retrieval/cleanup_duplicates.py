#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean up duplicate video entries in the database.
This script identifies videos with relative paths and updates or removes them.
"""

import os
import sqlite3
import argparse
from pathlib import Path
import video_config
import metadata_db

def normalize_video_path(path):
    """Convert relative paths to absolute paths and standardize format."""
    if path is None:
        return None
    return str(os.path.abspath(os.path.expanduser(str(path))))

def get_db_connection():
    """Establishes a connection to the SQLite database."""
    conn = sqlite3.connect(video_config.METADATA_DB_PATH)
    conn.row_factory = sqlite3.Row  # Access columns by name
    conn.execute("PRAGMA foreign_keys = ON")
    return conn

def find_relative_path_videos():
    """Find videos with relative paths in the database."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT id, video_path, source, source_id FROM long_videos WHERE video_path LIKE '../%' OR video_path LIKE './%'")
    relative_path_videos = cursor.fetchall()
    conn.close()
    return relative_path_videos

def find_duplicate_videos():
    """
    Find videos that have the same basename but different paths.
    Returns a list of tuples (relative_path_id, absolute_path_id, relative_path, absolute_path)
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Get all videos with relative paths
    relative_path_videos = find_relative_path_videos()
    
    duplicates = []
    for rel_video in relative_path_videos:
        rel_id = rel_video['id']
        rel_path = rel_video['video_path']
        
        # Extract the basename from the relative path
        basename = os.path.basename(rel_path)
        
        # Look for videos with the same basename but absolute paths
        cursor.execute("""
            SELECT id, video_path FROM long_videos 
            WHERE video_path LIKE ? AND id != ? AND video_path NOT LIKE '../%' AND video_path NOT LIKE './%'
        """, (f'%{basename}', rel_id))
        
        abs_videos = cursor.fetchall()
        for abs_video in abs_videos:
            abs_id = abs_video['id']
            abs_path = abs_video['video_path']
            duplicates.append((rel_id, abs_id, rel_path, abs_path))
    
    conn.close()
    return duplicates

def check_clips_for_video(video_id):
    """Check if a video has any clips in the database."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE original_long_video_id = ?", (video_id,))
    count = cursor.fetchone()[0]
    conn.close()
    return count

def update_clips_for_video(old_video_id, new_video_id):
    """Update clips to point to the new video ID."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("UPDATE video_clips SET original_long_video_id = ? WHERE original_long_video_id = ?", 
                  (new_video_id, old_video_id))
    updated_count = cursor.rowcount
    conn.commit()
    conn.close()
    return updated_count

def delete_video(video_id):
    """Delete a video from the database."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("DELETE FROM long_videos WHERE id = ?", (video_id,))
    deleted = cursor.rowcount > 0
    conn.commit()
    conn.close()
    return deleted

def cleanup_duplicates(dry_run=True):
    """
    Clean up duplicate videos in the database.
    
    Args:
        dry_run: If True, only print what would be done without making changes.
    """
    # Find videos with relative paths
    relative_path_videos = find_relative_path_videos()
    print(f"Found {len(relative_path_videos)} videos with relative paths")
    
    # Find duplicate videos
    duplicates = find_duplicate_videos()
    print(f"Found {len(duplicates)} duplicate videos")
    
    for rel_id, abs_id, rel_path, abs_path in duplicates:
        rel_clips = check_clips_for_video(rel_id)
        abs_clips = check_clips_for_video(abs_id)
        
        print(f"\nDuplicate found:")
        print(f"  Relative path: ID={rel_id}, Path={rel_path}, Clips={rel_clips}")
        print(f"  Absolute path: ID={abs_id}, Path={abs_path}, Clips={abs_clips}")
        
        if dry_run:
            if rel_clips > 0:
                print(f"  Would update {rel_clips} clips from ID {rel_id} to ID {abs_id}")
            print(f"  Would delete video with ID {rel_id}")
        else:
            if rel_clips > 0:
                updated = update_clips_for_video(rel_id, abs_id)
                print(f"  Updated {updated} clips from ID {rel_id} to ID {abs_id}")
            
            deleted = delete_video(rel_id)
            if deleted:
                print(f"  Deleted video with ID {rel_id}")
            else:
                print(f"  Failed to delete video with ID {rel_id}")
    
    # Handle remaining videos with relative paths (no duplicates found)
    remaining_rel_videos = find_relative_path_videos()
    if remaining_rel_videos:
        print(f"\nFound {len(remaining_rel_videos)} remaining videos with relative paths (no duplicates)")
        for video in remaining_rel_videos:
            rel_id = video['id']
            rel_path = video['video_path']
            abs_path = normalize_video_path(rel_path)
            
            print(f"\nRelative path video:")
            print(f"  ID={rel_id}, Path={rel_path}")
            print(f"  Normalized path: {abs_path}")
            
            if dry_run:
                print(f"  Would update path to: {abs_path}")
            else:
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute("UPDATE long_videos SET video_path = ? WHERE id = ?", (abs_path, rel_id))
                updated = cursor.rowcount > 0
                conn.commit()
                conn.close()
                
                if updated:
                    print(f"  Updated path to: {abs_path}")
                else:
                    print(f"  Failed to update path")

def main():
    parser = argparse.ArgumentParser(description="Clean up duplicate video entries in the database")
    parser.add_argument("--apply", action="store_true", help="Apply changes (default is dry run)")
    args = parser.parse_args()
    
    # Ensure directories exist
    video_config.ensure_directories()
    
    print("Starting database cleanup...")
    if not args.apply:
        print("DRY RUN MODE: No changes will be made to the database")
    
    cleanup_duplicates(dry_run=not args.apply)
    
    if not args.apply:
        print("\nThis was a dry run. To apply changes, run with --apply")

if __name__ == "__main__":
    main()
