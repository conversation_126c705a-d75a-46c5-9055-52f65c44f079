import os
import whisper
import nltk
from nltk.tokenize.texttiling import TextTilingTokenizer
from sentence_transformers import SentenceTransformer, util as st_util
import numpy as np
from scenedetect import open_video, SceneManager
from scenedetect.detectors import ContentDetector, AdaptiveDetector, ThresholdDetector
from scenedetect.scene_manager import save_images # For visualization/debug
import ffmpeg # Added for direct ffmpeg-python use
import json # Added
import shutil # Added

from video_retrieval import video_config
from video_retrieval import utils
from video_retrieval import metadata_db

# Download necessary NLTK data if not present
try:
    nltk.data.find('tokenizers/punkt')
except nltk.downloader.DownloadError:
    print("NLTK 'punkt' not found. Downloading...")
    nltk.download('punkt', quiet=True)
except Exception as e:
    print(f"Error during NLTK setup: {e}. Please ensure NLTK\\'s punkt tokenizer is available.")

# Global Whisper model cache
_whisper_model_cache = {}

def get_whisper_model(model_name=video_config.WHISPER_MODEL_NAME, device=video_config.DEVICE):
    """
    Loads and caches the Whisper model.
    Mirrors the logic from audio2text.py for model loading and caching if applicable,
    otherwise, provides a robust loading mechanism.
    """
    if model_name not in _whisper_model_cache or _whisper_model_cache[model_name].device.type != device:
        print(f"Loading Whisper model: {model_name} on device: {device}")
        try:
            model = whisper.load_model(model_name, device=device)
            print(f"Model {model_name} loaded to {model.device}")
        except Exception as e_load:
            print(f"Error loading Whisper model {model_name} to {device}: {e_load}")
            if device == "mps":
                print("Attempting to load Whisper model to CPU as fallback for MPS.")
                try:
                    model = whisper.load_model(model_name, device="cpu")
                    model = model.to(device)
                    print(f"Model {model_name} successfully loaded to CPU and moved to {device}.")
                except Exception as e_fallback_mps:
                    print(f"CPU fallback and move to MPS also failed for {model_name}: {e_fallback_mps}")
                    raise
            else:
                raise

        _whisper_model_cache[model_name] = model
        print(f"Whisper model {model_name} cached on {device}.")
    return _whisper_model_cache[model_name]


# --- 0.1 System Initialization & Parameter Pre-tuning ---
def pyscenedetect_param_tuning_mock(sample_video_paths_with_annotations):
    print("Mock: PySceneDetect parameter tuning. Returning default/pre-defined parameters from video_config.")
    return video_config.DEFAULT_PYSCENEDETECT_PARAMS


# --- 0.2 Processing a Single Long Video ---

def transcribe_video_whisper(video_path, model_name=video_config.WHISPER_MODEL_NAME, device=video_config.DEVICE):
    """
    Transcribes the video using Whisper.
    Returns the full transcript text, segment-level timestamps, and word-level timestamps.
    """
    print(f"Requesting Whisper model: {model_name} on device: {device} for {video_path}")

    effective_device = device
    try:
        model = get_whisper_model(model_name, effective_device)
        result = model.transcribe(video_path, verbose=False, word_timestamps=True, language=None)
    except Exception as e_transcribe_word_ts:
        print(f"Error during Whisper transcription for {video_path} with word_timestamps=True: {e_transcribe_word_ts}")
        print("Attempting transcription without word_timestamps as a fallback.")
        try:
            model = get_whisper_model(model_name, effective_device)
            result = model.transcribe(video_path, verbose=False, word_timestamps=False, language=None)
        except Exception as e_transcribe_no_ts:
            print(f"Fallback Whisper transcription (no word_timestamps) also failed for {video_path}: {e_transcribe_no_ts}")
            return None, None, None

    full_transcript_text = result.get("text", "").strip()
    segments = result.get("segments", [])

    word_timestamps = []
    if 'words' in result and result['words']:
        word_timestamps = result['words']
    elif segments and all('words' in seg for seg in segments):
        for seg in segments:
            if seg['words']:
                 word_timestamps.extend(seg['words'])

    if not word_timestamps and segments:
        print("Word timestamps not directly available in expected format. Trying to reconstruct from segments if necessary or will rely on segment timings.")

    print(f"Transcription complete for {video_path}. Length: {len(full_transcript_text)} chars. Segments: {len(segments)}. Word timestamps: {len(word_timestamps)}")
    return full_transcript_text, segments, word_timestamps


def detect_scenes_pyscenedetect(video_path, tuned_params):
    """
    Detects scenes using PySceneDetect with multiple detectors.
    `tuned_params` should be a dict like video_config.DEFAULT_PYSCENEDETECT_PARAMS.
    Returns a sorted list of unique scene boundary timestamps (in seconds).
    """
    print(f"Starting PySceneDetect for {video_path}...")
    video = None # Initialize video to None
    try:
        video = open_video(video_path)
        scene_manager = SceneManager()

        if 'content_detector' in tuned_params and tuned_params['content_detector'].get('threshold'):
            params_cd = tuned_params['content_detector']
            scene_manager.add_detector(ContentDetector(threshold=params_cd['threshold'], min_scene_len=params_cd['min_scene_len']))

        if 'adaptive_detector' in tuned_params and tuned_params['adaptive_detector'].get('adaptive_threshold'):
            params_ad = tuned_params['adaptive_detector']
            scene_manager.add_detector(AdaptiveDetector(adaptive_threshold=params_ad['adaptive_threshold'], min_scene_len=params_ad['min_scene_len']))

        if 'threshold_detector' in tuned_params and tuned_params['threshold_detector'].get('threshold'):
            params_td = tuned_params['threshold_detector']
            scene_manager.add_detector(ThresholdDetector(threshold=params_td['threshold'], min_scene_len=params_td['min_scene_len'], fade_bias=params_td.get('fade_bias', 0.0)))

        if not scene_manager._detector_list:
            print("No PySceneDetect detectors were configured or enabled based on tuned_params. Skipping visual scene detection.")
            return []

        scene_manager.detect_scenes(video=video, show_progress=False) # Pass video object as 'video'
        scene_list_raw = scene_manager.get_scene_list()

        visual_boundaries_sec = set()
        if scene_list_raw:
            for _scene_start_tc, scene_end_tc in scene_list_raw:
                visual_boundaries_sec.add(scene_end_tc.get_seconds())

        sorted_boundaries = sorted(list(visual_boundaries_sec))
        print(f"PySceneDetect found {len(sorted_boundaries)} visual boundary candidates: {sorted_boundaries}")
        return sorted_boundaries

    except Exception as e:
        print(f"Error during PySceneDetect processing for {video_path}: {e}")
        import traceback
        traceback.print_exc()
        # No explicit release needed for video object from open_video if SceneManager handles it,
        # or if it's used in a context manager (which is not the case here).
        # If PySceneDetect's VideoStream needs explicit closing, it should be added.
        # For now, assuming SceneManager or garbage collection handles it.
        return []


def segment_text_semantically(full_transcript_text, whisper_segments, word_timestamps, method=video_config.TEXT_SEGMENTATION_METHOD, device=video_config.DEVICE):
    """
    Segments transcript based on semantic topic changes using TextTiling or Sentence Embeddings.
    Returns a list of topic boundary timestamps (in seconds).
    """
    print(f"Starting semantic text segmentation using {method}...")
    if not full_transcript_text or not (whisper_segments or word_timestamps):
        print("Empty transcript or no timing information (segments/words), cannot perform semantic segmentation.")
        return []

    topic_boundaries_sec = set()

    if method == "texttiling":
        if not full_transcript_text:
            print("TextTiling requires full transcript text. Skipping.")
            return []
        try:
            ttt = TextTilingTokenizer()
            print("TextTiling based on full_transcript_text is selected, but mapping its character-based boundaries to precise timestamps using only Whisper segments/words is complex and currently a simplified placeholder. Prefer 'sentence_embeddings' with good word_timestamps for more accurate time mapping.")
        except Exception as e:
            print(f"Error during TextTiling: {e}")

    elif method == "sentence_embeddings":
        sentences_with_times = []
        if word_timestamps:
            current_sentence_text = []
            current_sentence_start = -1.0
            current_sentence_end = -1.0
            for i, word_info in enumerate(word_timestamps):
                word_text = word_info.get('word', '').strip()
                word_start = word_info.get('start')
                word_end = word_info.get('end')

                if not word_text or word_start is None or word_end is None: continue

                if not current_sentence_text:
                    current_sentence_start = word_start

                current_sentence_text.append(word_text)
                current_sentence_end = word_end

                if any(word_text.endswith(p) for p in ['.', '!', '?']) or i == len(word_timestamps) - 1:
                    if current_sentence_text:
                        sentences_with_times.append({
                            "text": " ".join(current_sentence_text),
                            "start": current_sentence_start,
                            "end": current_sentence_end
                        })
                        current_sentence_text = []
                        current_sentence_start = -1.0
            if current_sentence_text:
                 sentences_with_times.append({"text": " ".join(current_sentence_text), "start": current_sentence_start, "end": current_sentence_end})

        elif whisper_segments:
            print("Warning: Word timestamps not available/used for sentence segmentation. Using Whisper segments as pseudo-sentences. This is less accurate.")
            for seg in whisper_segments:
                sentences_with_times.append({"text": seg['text'].strip(), "start": seg['start'], "end": seg['end']})

        if not sentences_with_times:
            print("No sentences/segments to process for sentence embeddings.")
            return []

        print(f"Processing {len(sentences_with_times)} sentences/segments for embedding-based segmentation.")
        try:
            model = SentenceTransformer(video_config.SENTENCE_EMBEDDING_MODEL, device=device)
            sentence_texts = [s['text'] for s in sentences_with_times if s['text']]
            if not sentence_texts or len(sentence_texts) < 2:
                print("Not enough non-empty sentences to compare for topic boundaries.")
                return []

            embeddings = model.encode(sentence_texts, convert_to_tensor=True, show_progress_bar=False)

            similarities = []
            for i in range(embeddings.shape[0] - 1):
                sim = st_util.pytorch_cos_sim(embeddings[i], embeddings[i+1]).item()
                similarities.append(sim)

            for i, sim_score in enumerate(similarities):
                if sim_score < video_config.SENTENCE_SIMILARITY_THRESHOLD:
                    if sentences_with_times[i].get('end') is not None:
                        topic_boundaries_sec.add(sentences_with_times[i]['end'])
        except Exception as e_sent_emb:
            print(f"Error during Sentence Embedding segmentation: {e_sent_emb}")
            import traceback
            traceback.print_exc()

    sorted_boundaries = sorted(list(topic_boundaries_sec))
    print(f"Semantic segmentation ({method}) found {len(sorted_boundaries)} topic boundary candidates: {sorted_boundaries}")
    return sorted_boundaries


def merge_and_optimize_boundaries(whisper_segments, word_timestamps, visual_boundaries_sec, topic_boundaries_sec, video_duration_sec):
    """
    Merges boundaries from different sources (Whisper segments, visual, semantic)
    and optimizes them to adhere to min/max clip duration constraints.
    Returns a sorted list of final cut points in seconds.
    """
    print("Merging and optimizing boundaries...")
    candidate_boundaries = {0.0, video_duration_sec}

    if whisper_segments:
        for seg in whisper_segments:
            if seg.get('end') is not None:
                candidate_boundaries.add(float(seg['end']))

    if visual_boundaries_sec:
        for ts in visual_boundaries_sec:
            candidate_boundaries.add(float(ts))

    if topic_boundaries_sec:
        for ts in topic_boundaries_sec:
            candidate_boundaries.add(float(ts))

    sorted_candidates = sorted([
        ts for ts in list(candidate_boundaries)
        if ts is not None and 0.0 <= ts <= video_duration_sec + 0.1
    ])

    if sorted_candidates and sorted_candidates[-1] < video_duration_sec - 0.1:
        sorted_candidates.append(video_duration_sec)
        sorted_candidates = sorted(list(set(sorted_candidates)))

    if not sorted_candidates or len(sorted_candidates) < 2:
        print("Not enough candidate boundaries to form clips. Defaulting to full video or no clips.")
        return [0.0, video_duration_sec] if video_duration_sec > 0 else []

    print(f"Initial merged and sorted candidate cut points ({len(sorted_candidates)}): {sorted_candidates}")

    final_cut_points_sec = [0.0]

    current_idx = 0
    while current_idx < len(sorted_candidates):
        last_added_cut = final_cut_points_sec[-1]

        potential_next_cut_idx = -1
        for next_idx in range(current_idx, len(sorted_candidates)):
            candidate_ts = sorted_candidates[next_idx]
            if candidate_ts <= last_added_cut:
                continue

            segment_duration = candidate_ts - last_added_cut
            if segment_duration >= video_config.MIN_CLIP_DURATION_SEC:
                potential_next_cut_idx = next_idx
                break

        if potential_next_cut_idx == -1:
            if last_added_cut < video_duration_sec:
                if (video_duration_sec - last_added_cut) > 0.1:
                     final_cut_points_sec.append(video_duration_sec)
            break

        chosen_candidate_ts = sorted_candidates[potential_next_cut_idx]
        segment_duration = chosen_candidate_ts - last_added_cut

        if segment_duration > video_config.MAX_CLIP_DURATION_SEC:
            best_intermediate_split = -1.0
            for inter_idx in range(potential_next_cut_idx - 1, -1, -1):
                inter_candidate_ts = sorted_candidates[inter_idx]
                if inter_candidate_ts <= last_added_cut:
                    break

                first_part_duration = inter_candidate_ts - last_added_cut
                if video_config.MIN_CLIP_DURATION_SEC <= first_part_duration <= video_config.MAX_CLIP_DURATION_SEC:
                    best_intermediate_split = inter_candidate_ts
                    break

            if best_intermediate_split != -1.0:
                final_cut_points_sec.append(best_intermediate_split)
                try:
                    current_idx = sorted_candidates.index(best_intermediate_split)
                except ValueError:
                    current_idx = potential_next_cut_idx
            else:
                hard_split_point = last_added_cut + video_config.MAX_CLIP_DURATION_SEC
                print(f"Warning: Segment from {last_added_cut:.2f}s to {chosen_candidate_ts:.2f}s is too long ({segment_duration:.2f}s). No suitable natural intermediate split found. Adding chosen_candidate_ts, or consider hard split if this is frequent.")
                final_cut_points_sec.append(chosen_candidate_ts)
                current_idx = potential_next_cut_idx + 1
        else:
            final_cut_points_sec.append(chosen_candidate_ts)
            current_idx = potential_next_cut_idx + 1

    final_cut_points_sec = sorted(list(set(pt for pt in final_cut_points_sec if pt is not None)))
    if not final_cut_points_sec or final_cut_points_sec[0] > 0.001:
        final_cut_points_sec.insert(0, 0.0)
        final_cut_points_sec = sorted(list(set(final_cut_points_sec)))

    if final_cut_points_sec[-1] < video_duration_sec - 0.1:
        final_cut_points_sec.append(video_duration_sec)
        final_cut_points_sec = sorted(list(set(final_cut_points_sec)))

    refined_cuts = []
    if final_cut_points_sec:
        refined_cuts.append(final_cut_points_sec[0])
        for i in range(1, len(final_cut_points_sec)):
            prev_cut = refined_cuts[-1]
            current_cut = final_cut_points_sec[i]
            if (current_cut - prev_cut) >= video_config.MIN_CLIP_DURATION_SEC - 0.01:
                refined_cuts.append(current_cut)
            else:
                if i < len(final_cut_points_sec) -1 :
                    if refined_cuts: refined_cuts.pop()
                    refined_cuts.append(current_cut)
                elif current_cut > refined_cuts[-1]:
                    refined_cuts.append(current_cut)

    final_cuts_after_duration_filter = sorted(list(set(refined_cuts)))

    if len(final_cuts_after_duration_filter) < 2 and video_duration_sec > 0:
        final_cuts_after_duration_filter = [0.0, video_duration_sec]
    elif not final_cuts_after_duration_filter and video_duration_sec > 0:
         final_cuts_after_duration_filter = [0.0, video_duration_sec]

    print(f"Final refined cut points after duration constraints ({len(final_cuts_after_duration_filter)}): {final_cuts_after_duration_filter}")
    return final_cuts_after_duration_filter


def split_video_ffmpeg_batch(long_video_path: str, final_cut_points_sec: list[float],
                             output_clips_dir: str, original_long_video_id: int,
                             original_video_description: str | None = None,
                             original_video_keywords: list[str] | None = None):
    """
    Splits the long video into clips based on final_cut_points_sec using ffmpeg.
    Adds metadata for each new clip to the database.

    Args:
        long_video_path: Path to the long video.
        final_cut_points_sec: List of timestamps (seconds) for splitting.
        output_clips_dir: Directory to save the clips.
        original_long_video_id: DB ID of the parent long video.
        original_video_description: Description associated with the original long video.
        original_video_keywords: Keywords associated with the original long video.
    """
    os.makedirs(output_clips_dir, exist_ok=True)
    base_name = os.path.splitext(os.path.basename(long_video_path))[0]
    output_template = os.path.join(output_clips_dir, f"{base_name}_clip_%04d.mp4")

    created_clip_paths_with_metadata = []

    try:
        probe = ffmpeg.probe(long_video_path)
        video_duration = float(probe['format']['duration'])
    except ffmpeg.Error as e:
        print(f"Error probing video duration for {long_video_path}: {e.stderr.decode('utf8') if e.stderr else str(e)}")
        return []

    cut_points = [0.0] + [pt for pt in final_cut_points_sec if 0 < pt < video_duration] + [video_duration]
    cut_points = sorted(list(set(cut_points))) # Unique and sorted

    valid_segments_to_create = []
    if len(cut_points) > 1:
        for i in range(len(cut_points) - 1):
            start_t = cut_points[i]
            end_t = cut_points[i+1]
            if (end_t - start_t) >= video_config.MIN_CLIP_DURATION_SEC - 0.01: # Tolerance for float precision
                 valid_segments_to_create.append((start_t, end_t))
            else:
                print(f"Skipping potential segment from {start_t:.2f}s to {end_t:.2f}s due to being shorter than min clip duration before ffmpeg.")

    if not valid_segments_to_create:
        print(f"No valid segments to create for {long_video_path} after duration filtering. This might mean the video itself is shorter than min_clip_duration or cut points were too close.")
        return []

    print(f"Starting FFMPEG splitting for {long_video_path} into {len(valid_segments_to_create)} clips based on cut points: {valid_segments_to_create}")
    current_clip_index = 0
    for start_time, end_time in valid_segments_to_create:
        duration = end_time - start_time
        if duration < video_config.MIN_CLIP_DURATION_SEC:
            print(f"Skipping segment (safeguard) from {start_time:.2f}s to {end_time:.2f}s due to short duration ({duration:.2f}s).")
            continue

        output_clip_path = output_template % current_clip_index

        try:
            print(f"Splitting: {long_video_path} from {start_time} to {end_time} -> {output_clip_path}")
            (
                ffmpeg
                .input(long_video_path, ss=start_time, to=end_time)
                .output(output_clip_path, c="copy", avoid_negative_ts="make_zero")
                .global_args('-hide_banner', '-loglevel', 'error')
                .run(overwrite_output=True)
            )

            keywords_to_store = json.dumps(original_video_keywords) if original_video_keywords else None
            clip_id = metadata_db.add_video_clip(
                clip_path=output_clip_path,
                original_long_video_id=original_long_video_id,
                start_time_in_original=start_time,
                end_time_in_original=end_time,
                duration_seconds=end_time - start_time,
                description=original_video_description,
                keywords=keywords_to_store,
            )
            if clip_id:
                created_clip_paths_with_metadata.append({
                    "clip_id": clip_id,
                    "path": output_clip_path,
                    "start_sec": start_time,
                    "end_sec": end_time,
                    "description": original_video_description,
                    "keywords": original_video_keywords # Pass the list itself, not the JSON string
                })
                print(f"Successfully split and added to DB: {output_clip_path} (Clip ID: {clip_id})")
            else:
                print(f"Error adding clip {output_clip_path} to database.")
            current_clip_index += 1

        except ffmpeg.Error as e:
            print(f"Error splitting video {long_video_path} for segment {start_time}-{end_time}:")
            error_message = e.stderr.decode('utf8') if e.stderr else str(e)
            for line in error_message.split('\\\\n'):
                print(line)
        except Exception as e_gen:
            print(f"A general error occurred during splitting or DB insertion for segment {start_time}-{end_time} of {long_video_path}: {e_gen}")
            import traceback
            traceback.print_exc()

    print(f"Finished FFMPEG splitting for {long_video_path}. Created {len(created_clip_paths_with_metadata)} clips.")
    return created_clip_paths_with_metadata


def extract_transcript_for_segment(full_transcript_text, whisper_segments, word_timestamps,
                                   segment_start_sec, segment_end_sec):
    """
    Extracts the portion of the transcript corresponding to a given time segment.
    Uses word_timestamps if available (preferred for accuracy), otherwise falls back to segments.
    """
    if not (whisper_segments or word_timestamps) and not full_transcript_text:
        return "Transcript extraction error: No segment, word data, or full transcript available."

    if not word_timestamps and not whisper_segments and full_transcript_text:
        if segment_start_sec == 0.0 and full_transcript_text and segment_end_sec > 0:
             print("extract_transcript_for_segment: Word/Segment timestamps missing, cannot accurately extract sub-segment from full transcript for general case. If this is a full video clip, transcript is handled elsewhere.")
             return ""

    relevant_texts = []
    if word_timestamps:
        current_words = []
        for word_info in word_timestamps:
            word_start = word_info.get('start')
            word_end = word_info.get('end')
            word_text = word_info.get('word', "")

            if word_start is None or word_end is None: continue

            if word_start < segment_end_sec and word_end > segment_start_sec:
                current_words.append(word_text)

        segment_text_reconstructed = "".join(current_words).strip()
        return segment_text_reconstructed if segment_text_reconstructed else ""

    elif whisper_segments:
        print("extract_transcript_for_segment: Using Whisper segments for transcript extraction (fallback).")
        for seg in whisper_segments:
            seg_start = seg.get('start')
            seg_end = seg.get('end')
            seg_text = seg.get('text', "").strip()

            if seg_start is None or seg_end is None or not seg_text: continue

            overlap_start = max(segment_start_sec, seg_start)
            overlap_end = min(segment_end_sec, seg_end)

            if overlap_start < overlap_end:
                relevant_texts.append(seg_text)
        return " ".join(relevant_texts).strip()

    return ""


# --- Main Orchestration for Phase 0 ---
def process_long_video(long_video_path: str, tuned_pyscenedetect_params=None,
                       long_video_db_id_override: int | None = None,
                       source_info: dict | None = None):
    """
    Processes a single long video:
    1. Transcribes audio using Whisper.
    2. Detects visual scenes using PySceneDetect.
    3. Segments transcript semantically (TextTiling or Sentence Embeddings).
    4. Merges and optimizes boundary points.
    5. Splits video into clips based on these boundaries.
    6. Adds clips to the database with inherited metadata.

    Args:
        long_video_path: Path to the long video file.
        tuned_pyscenedetect_params: Optional pre-tuned parameters for PySceneDetect.
        long_video_db_id_override: Optional DB ID if the long video is already in the DB (e.g., from manual import).
                                   If provided, DB insertion for the long video is skipped.
        source_info: Optional dictionary containing source information (e.g., 'source', 'source_id', 'description', 'keywords')
                     This is used if the video is not already in the database or to pass metadata.

    Returns:
        A list of dictionaries, where each dictionary contains info about a created clip
        (path, start_sec, end_sec, description, keywords, clip_id, transcript_text for short/single clips).
        Returns empty list if processing fails or no clips are generated.
    """
    print(f"--- Starting processing for long video: {long_video_path} ---")
    if not os.path.exists(long_video_path):
        print(f"Video file not found: {long_video_path}")
        return []

    video_config.ensure_directories()
    output_clips_base_dir = video_config.CLIPS_DIR

    video_source = source_info.get('source', 'unknown') if source_info else 'unknown'
    video_source_id = source_info.get('source_id', None) if source_info else None
    video_description_from_source = source_info.get('description', None) if source_info else None
    if isinstance(video_description_from_source, list): # Handle if description was accidentally a list
        print(f"Warning: Source info description for {long_video_path} was a list, joining to string: {video_description_from_source}")
        video_description = " ".join(video_description_from_source)
    else:
        video_description = video_description_from_source

    video_keywords_from_source = source_info.get('keywords', []) if source_info else []
    if isinstance(video_keywords_from_source, str): # Handle if keywords was accidentally a string
        print(f"Warning: Source info keywords for {long_video_path} was a string, splitting by space: {video_keywords_from_source}")
        video_keywords = video_keywords_from_source.split()
    else:
        video_keywords = video_keywords_from_source if isinstance(video_keywords_from_source, list) else []

    raw_api_metadata_str = json.dumps(source_info.get('raw_api_metadata', {})) if source_info else None


    # 0. Add/get long video from database
    long_video_id = long_video_db_id_override
    if long_video_id is None:
        existing_video = metadata_db.get_long_video_by_path(long_video_path)
        if existing_video:
            long_video_id = existing_video['id']
            print(f"Video {long_video_path} already found in DB with ID: {long_video_id}. Using existing entry.")

            # Update DB if source_info provides new data or fills missing fields
            if video_description and not existing_video.get('description'):
                 metadata_db.update_long_video_metadata(long_video_id, description=video_description)

            db_keywords_list = []
            if existing_video.get('keywords'):
                try:
                    db_keywords_list = json.loads(existing_video['keywords'])
                except json.JSONDecodeError:
                    pass
            if video_keywords and not db_keywords_list:
                 metadata_db.update_long_video_metadata(long_video_id, keywords_json_str=json.dumps(video_keywords))

            # Refresh local vars with potentially updated DB state or original source_info
            video_description = existing_video.get('description', video_description) # Prefer DB if exists, else use (potentially new) video_description
            db_keywords_str_after_update = metadata_db.get_long_video_info(video_id=long_video_id).get('keywords') # Fetch again
            if db_keywords_str_after_update:
                try:
                    video_keywords = json.loads(db_keywords_str_after_update)
                except json.JSONDecodeError:
                    print(f"Warning: Could not parse keywords from DB for video ID {long_video_id} after potential update: {db_keywords_str_after_update}")
                    # video_keywords remains as from source_info or empty list
            # else: video_keywords from source_info is used if DB keywords were/are still null/empty

        else:
            print(f"Adding new video {long_video_path} to database.")
            keywords_to_db = json.dumps(video_keywords) if video_keywords else None
            long_video_id = metadata_db.add_long_video(
                video_path=long_video_path,
                source=video_source,
                source_id=video_source_id,
                description=video_description,
                keywords=video_keywords if video_keywords else [],
                raw_api_metadata=json.loads(raw_api_metadata_str) if raw_api_metadata_str else None,
            )
            if not long_video_id:
                print(f"Failed to add long video {long_video_path} to database. Aborting processing for this video.")
                return []
            print(f"Video {long_video_path} added to DB with ID: {long_video_id}")
    else: # long_video_db_id_override was provided
        print(f"Using provided long_video_db_id_override: {long_video_id} for {long_video_path}.")
        existing_video_data = metadata_db.get_long_video_info(video_id=long_video_id)
        if existing_video_data:
            # Prioritize source_info if provided and non-empty, else use DB version.
            video_description = video_description if video_description is not None else existing_video_data.get('description')

            if not video_keywords: # video_keywords from source_info is empty or source_info was None/empty
                db_keywords_str = existing_video_data.get('keywords')
                if db_keywords_str:
                    try:
                        video_keywords = json.loads(db_keywords_str)
                    except json.JSONDecodeError:
                        print(f"Warning: Could not parse keywords from DB for video ID {long_video_id}: {db_keywords_str}")
                        video_keywords = []
                else: # keywords field is NULL in DB
                    video_keywords = []
            # else: video_keywords from source_info (if any) is used

            # Update raw_api_metadata if new one is provided and DB one is empty/default
            if raw_api_metadata_str and raw_api_metadata_str != '{}' and \
               (not existing_video_data.get('raw_api_metadata') or existing_video_data.get('raw_api_metadata') == '{}'):
                metadata_db.update_long_video_metadata(long_video_id, raw_api_metadata_json=raw_api_metadata_str)
        else:
            print(f"Warning: long_video_db_id_override {long_video_id} provided, but no record found in DB. Proceeding with source_info if available (description='{video_description}', keywords={video_keywords}).")

    safe_base_name = utils.sanitize_filename(os.path.splitext(os.path.basename(long_video_path))[0])
    # Ensure long_video_id is valid before using in path
    if not long_video_id:
        print(f"Critical error: long_video_id is not set for {long_video_path}. Cannot create clips directory.")
        return []
    output_clips_dir = os.path.join(output_clips_base_dir, f"{safe_base_name}_{long_video_id}")
    os.makedirs(output_clips_dir, exist_ok=True)

    # Get video duration
    try:
        probe = ffmpeg.probe(long_video_path)
        video_duration_sec = float(probe['format']['duration'])
        # Update duration in DB for the long video entry
        metadata_db.update_long_video_metadata(long_video_id, duration_sec=video_duration_sec)
    except ffmpeg.Error as e:
        print(f"Error probing video duration for {long_video_path}: {e.stderr.decode('utf8') if e.stderr else str(e)}")
        print("Cannot proceed with splitting without video duration.")
        return []
    except Exception as e_gen: # Catch other potential errors like file not found if probe fails early
        print(f"Generic error probing video duration for {long_video_path}: {e_gen}")
        return []


    # --- Short Video No-Split Logic ---
    if video_duration_sec < video_config.SHORT_VIDEO_NO_SPLIT_THRESHOLD_SEC:
        print(f"Video {long_video_path} is shorter than {video_config.SHORT_VIDEO_NO_SPLIT_THRESHOLD_SEC}s. Will not be split, creating a single clip entry.")
        clip_name_for_short_video = f"{safe_base_name}_full.mp4" # Using original extension might be better
        output_clip_path_short_video = os.path.join(output_clips_dir, clip_name_for_short_video)

        try:
            copied_to_new_location = False
            if os.path.abspath(long_video_path) != os.path.abspath(output_clip_path_short_video):
                shutil.copy2(long_video_path, output_clip_path_short_video)
                print(f"Copied short video to clips directory: {output_clip_path_short_video}")
                clip_path_to_db = output_clip_path_short_video
                copied_to_new_location = True
            else:
                print(f"Short video {long_video_path} is already in the target clips directory structure. Using existing path.")
                clip_path_to_db = long_video_path

            # Transcribe the short video (still useful for text-based retrieval)
            full_transcript, whisper_segments, word_timestamps = transcribe_video_whisper(clip_path_to_db)
            if full_transcript is not None:
                 metadata_db.update_long_video_metadata(long_video_id, transcript_text=full_transcript) # Store transcript with long video too
            else:
                print(f"Transcription failed for short video {clip_path_to_db}. Proceeding without transcript for this item.")
                full_transcript = ""
                # whisper_segments = [] # Not strictly needed to re-init here
                # word_timestamps = []

            # Add a single clip entry
            keywords_to_store_in_db = json.dumps(video_keywords) if video_keywords else None
            clip_id = metadata_db.add_video_clip(
                clip_path=clip_path_to_db,
                original_long_video_id=long_video_id,
                start_time_in_original=0.0,
                end_time_in_original=video_duration_sec,
                duration_seconds=video_duration_sec,
                description=video_description,
                keywords=keywords_to_store_in_db,
                transcript_text=full_transcript,
            )
            if clip_id:
                print(f"Single clip entry created for short video: {clip_path_to_db} (Clip ID: {clip_id})")
                if copied_to_new_location:
                    try:
                        os.remove(long_video_path)
                        print(f"Successfully deleted original short video: {long_video_path}")
                    except OSError as e:
                        print(f"Error deleting original short video {long_video_path}: {e}")
                return [{
                    "clip_id": clip_id,
                    "path": clip_path_to_db,
                    "start_sec": 0.0,
                    "end_sec": video_duration_sec,
                    "description": video_description,
                    "keywords": video_keywords, # Return the list itself, not the JSON string
                    "transcript_text": full_transcript
                }]
            else:
                print(f"Failed to add single clip entry for short video {clip_path_to_db} to database.")
                return []
        except Exception as e_short_video:
            print(f"Error processing short video {long_video_path} as a single clip: {e_short_video}")
            import traceback
            traceback.print_exc()
            return []

    # --- Regular processing for videos that need splitting ---
    # 1. Transcribe audio (if not already done for short video)
    # Check if transcript for long_video_id already exists from a previous run or short video path.
    existing_lv_data = metadata_db.get_long_video_info(video_id=long_video_id)
    full_transcript = existing_lv_data.get('transcript_text') if existing_lv_data else None
    whisper_segments, word_timestamps = None, None # Reset, might need re-population

    if full_transcript is None or not full_transcript.strip(): # If None or empty/whitespace
        print(f"Transcribing full long video {long_video_path} as no existing transcript found or it was empty.")
        full_transcript, whisper_segments, word_timestamps = transcribe_video_whisper(long_video_path)
        if full_transcript is not None:
            metadata_db.update_long_video_metadata(long_video_id, transcript_text=full_transcript)
        else:
            print(f"Transcription failed for {long_video_path}. Proceeding without transcript-based segmentation.")
            full_transcript = ""
            whisper_segments = []
            word_timestamps = []
    else:
        print(f"Using existing transcript for {long_video_path} (length {len(full_transcript)}).")
        # If we have full_transcript but need segments/words for downstream process
        if video_config.TEXT_SEGMENTATION_METHOD != "none":
            print("Attempting to get segment/word timings from Whisper for existing transcript...")
            _, whisper_segments, word_timestamps = transcribe_video_whisper(long_video_path) # Re-run to get segments/words
            if not whisper_segments and not word_timestamps:
                print("Warning: Could not retrieve segment/word timestamps from Whisper even with existing transcript.")


    # 2. Detect visual scenes
    if tuned_pyscenedetect_params is None:
        tuned_pyscenedetect_params = video_config.DEFAULT_PYSCENEDETECT_PARAMS
    visual_boundaries_sec = detect_scenes_pyscenedetect(long_video_path, tuned_pyscenedetect_params)
    metadata_db.update_long_video_metadata(long_video_id, visual_boundaries_json=json.dumps(list(visual_boundaries_sec)))


    # 3. Segment transcript semantically
    topic_boundaries_sec = segment_text_semantically(full_transcript, whisper_segments, word_timestamps, method=video_config.TEXT_SEGMENTATION_METHOD)
    metadata_db.update_long_video_metadata(long_video_id, semantic_boundaries_json=json.dumps(list(topic_boundaries_sec)))

    # 4. Merge and optimize boundary points
    final_cut_points_sec = merge_and_optimize_boundaries(
        whisper_segments=whisper_segments,
        word_timestamps=word_timestamps,
        visual_boundaries_sec=visual_boundaries_sec,
        topic_boundaries_sec=topic_boundaries_sec,
        video_duration_sec=video_duration_sec
    )

    # If no meaningful cut points, treat as a single clip (similar to short video logic)
    if not final_cut_points_sec or len(final_cut_points_sec) <= 1 or \
       (len(final_cut_points_sec) == 2 and abs(final_cut_points_sec[0] - 0.0) < 0.01 and abs(final_cut_points_sec[1] - video_duration_sec) < 0.01) :
        print(f"No meaningful cut points identified for {long_video_path} after merging. Treating as a single clip.")
        clip_name_single_clip = f"{safe_base_name}_full_processed.mp4"
        output_clip_path_single_clip = os.path.join(output_clips_dir, clip_name_single_clip)

        try:
            # Copy to clips dir if not already there (e.g. if original was outside)
            copied_to_new_location_single_clip = False
            if os.path.abspath(long_video_path) != os.path.abspath(output_clip_path_single_clip):
                shutil.copy2(long_video_path, output_clip_path_single_clip)
                print(f"Copied video (treated as single clip) to clips directory: {output_clip_path_single_clip}")
                clip_path_to_db = output_clip_path_single_clip
                copied_to_new_location_single_clip = True
            else:
                print(f"Video {long_video_path} (treated as single clip) is already in the target clips directory. Using existing path.")
                clip_path_to_db = long_video_path

            keywords_to_store_in_db = json.dumps(video_keywords) if video_keywords else None
            clip_id = metadata_db.add_video_clip(
                clip_path=clip_path_to_db,
                original_long_video_id=long_video_id,
                start_time_in_original=0.0,
                end_time_in_original=video_duration_sec,
                duration_seconds=video_duration_sec,
                description=video_description,
                keywords=keywords_to_store_in_db,
                transcript_text=full_transcript
            )
            if clip_id:
                print(f"Single clip entry created for {clip_path_to_db} (Clip ID: {clip_id}) after full processing attempt.")
                if copied_to_new_location_single_clip:
                    try:
                        os.remove(long_video_path)
                        print(f"Successfully deleted original video (treated as single clip): {long_video_path}")
                    except OSError as e:
                        print(f"Error deleting original video {long_video_path} (treated as single clip): {e}")
                return [{
                    "clip_id": clip_id,
                    "path": clip_path_to_db,
                    "start_sec": 0.0,
                    "end_sec": video_duration_sec,
                    "description": video_description,
                    "keywords": video_keywords, # Return list
                    "transcript_text": full_transcript
                }]
            else:
                print(f"Failed to add single clip entry for {clip_path_to_db} to database.")
                return []
        except Exception as e_single_clip:
            print(f"Error processing video {long_video_path} as a single clip after merging: {e_single_clip}")
            import traceback
            traceback.print_exc()
            return []

    # 5. Split video into clips (batch operation)
    created_clips_info = split_video_ffmpeg_batch(
        long_video_path=long_video_path,
        final_cut_points_sec=final_cut_points_sec,
        output_clips_dir=output_clips_dir,
        original_long_video_id=long_video_id,
        original_video_description=video_description,
        original_video_keywords=video_keywords
    )

    # Post-processing for created clips: extract transcript for each clip
    # This was previously inside split_video_ffmpeg_batch or handled differently.
    # Now, we do it here based on the created_clips_info.
    if created_clips_info:
        print(f"Extracting transcripts for {len(created_clips_info)} newly created clips...")
        for clip_info in created_clips_info:
            clip_id = clip_info.get("clip_id")
            start_sec = clip_info.get("start_sec")
            end_sec = clip_info.get("end_sec")

            if clip_id is None or start_sec is None or end_sec is None:
                print(f"Warning: Clip info missing ID, start or end time: {clip_info}. Cannot extract transcript.")
                continue

            # Extract transcript for this specific segment using word_timestamps from the full video.
            # This requires full_transcript and word_timestamps to be available from the full video transcription.
            segment_transcript = extract_transcript_for_segment(
                full_transcript, whisper_segments, word_timestamps, start_sec, end_sec
            )

            if segment_transcript:
                metadata_db.update_video_clip_transcript(clip_id, segment_transcript)
                clip_info['transcript_text'] = segment_transcript # Add to returned info
                # print(f"Updated transcript for clip_id {clip_id} (length {len(segment_transcript)}).")
            else:
                # print(f"No specific transcript extracted for clip_id {clip_id}. DB entry will have empty/null transcript for now.")
                clip_info['transcript_text'] = "" # Ensure it exists in returned dict

    if created_clips_info:
        print(f"Successfully processed and split {long_video_path} into {len(created_clips_info)} clips.")
    else:
        print(f"No clips were generated from {long_video_path} by split_video_ffmpeg_batch, or an error occurred during splitting.")

    print(f"--- Finished processing for long video: {long_video_path} ---")
    return created_clips_info


# Example usage (for testing this module directly)
if __name__ == '__main__':
    print("Running Phase 0 Splitting directly (for testing purposes)...")

    video_config.ensure_directories()
    metadata_db.create_tables()

    # Define a path for a dummy long video (e.g., 70 seconds)
    dummy_video_path = os.path.join(video_config.LONG_VIDEOS_DIR, "sample_long_video_phase0_test.mp4")

    # Create dummy video if it doesn't exist
    if not os.path.exists(dummy_video_path):
        print(f"Test video {dummy_video_path} not found.")
        try:
            cmd_create_dummy = [
                "ffmpeg", "-y",
                "-f", "lavfi", "-i", "color=c=black:s=320x240:d=70",
                "-f", "lavfi", "-i", "sine=frequency=1000:duration=70",
                "-c:v", "libx264", "-c:a", "aac",
                "-shortest", dummy_video_path
            ]
            print(f"Attempting to create dummy video: {dummy_video_path}")
            # utils.run_ffmpeg_command is not defined in this provided snippet, assuming it's a helper
            # For standalone test, one might use subprocess directly or ensure utils is available.
            # Simplified for now:
            import subprocess
            process = subprocess.run(cmd_create_dummy, capture_output=True, text=True)
            if process.returncode == 0:
                print(f"Dummy video created at {dummy_video_path}")
            else:
                print(f"Failed to create dummy video. FFmpeg stderr: {process.stderr}")
        except Exception as e_create:
            print(f"Could not create dummy video due to error: {e_create}. Please provide a video manually.")

    # Define a path for a dummy short video (e.g., 30 seconds)
    dummy_short_video_path = os.path.join(video_config.LONG_VIDEOS_DIR, "sample_short_video_phase0_test.mp4")
    if not os.path.exists(dummy_short_video_path):
        print(f"Test short video {dummy_short_video_path} not found.")
        try:
            cmd_create_short_dummy = [
                "ffmpeg", "-y",
                "-f", "lavfi", "-i", "color=c=red:s=320x240:d=30",
                "-f", "lavfi", "-i", "sine=frequency=600:duration=30",
                "-c:v", "libx264", "-c:a", "aac",
                "-shortest", dummy_short_video_path
            ]
            print(f"Attempting to create dummy short video: {dummy_short_video_path}")
            import subprocess
            process_short = subprocess.run(cmd_create_short_dummy, capture_output=True, text=True)
            if process_short.returncode == 0:
                print(f"Dummy short video created at {dummy_short_video_path}")
            else:
                print(f"Failed to create dummy short video. FFmpeg stderr: {process_short.stderr}")
        except Exception as e_create_short:
            print(f"Could not create dummy short video: {e_create_short}.")


    # Test with the longer dummy video first
    if os.path.exists(dummy_video_path):
        print(f"\\n--- Testing Phase 0 with a normal length video: {dummy_video_path} ---")
        source_info_long = {
            "source": "manual_test_long",
            "source_id": os.path.splitext(os.path.basename(dummy_video_path))[0],
            "description": "A test video for phase 0 splitting (long duration example).", # Corrected
            "keywords": ["test", "long video", "splitting", "example"],      # Corrected
            "raw_api_metadata": {"info": "This is a test long video for splitting functionality."}
        }
        # Example of providing an existing DB ID (e.g. if downloader already added it)
        # To test this, you'd first run without override, note the ID, then run with it.
        # For a clean test run, set to None.
        long_video_db_id_for_test = None
        # existing_long_video = metadata_db.get_long_video_by_path(dummy_video_path)
        # if existing_long_video:
        #     long_video_db_id_for_test = existing_long_video['id']
        # else: # Add it first if we want to test override with a specific ID from a previous "run"
        #     temp_id = metadata_db.add_long_video(dummy_video_path, source_info_long['source'], source_info_long['source_id'], description=source_info_long['description'], keywords_json_str=json.dumps(source_info_long['keywords']), raw_api_metadata_json=json.dumps(source_info_long['raw_api_metadata']))
        #     if temp_id: long_video_db_id_for_test = temp_id

        process_long_video(dummy_video_path, source_info=source_info_long, long_video_db_id_override=long_video_db_id_for_test)
    else:
        print(f"Skipping test run for normal length video as {dummy_video_path} is not available.")

    # Test with the short dummy video
    if os.path.exists(dummy_short_video_path):
        print(f"\\n--- Testing Phase 0 with a short video (should not split): {dummy_short_video_path} ---")
        source_info_short = {
            "source": "manual_test_short",
            "source_id": os.path.splitext(os.path.basename(dummy_short_video_path))[0],
            "description": "A short test video for phase 0 no-split logic demonstration.", # Corrected
            "keywords": ["test", "short video", "no-split", "example"],                   # Corrected
            "raw_api_metadata": {"info": "This is a short test video for the no-split rule."}
        }
        process_long_video(dummy_short_video_path, source_info=source_info_short)
    else:
        print(f"Skipping test run for short video as {dummy_short_video_path} is not available.")

    print("\\nPhase 0 test script finished. Check DB and ./data directory for results.")