#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check and fix the FAISS index for video retrieval.
This script checks if the FAISS index is in sync with the database and rebuilds it if necessary.
"""

import os
import argparse
import sys
import shutil
import faiss
import numpy as np
from pathlib import Path

# Add the parent directory to the path so we can import video_config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from video_retrieval import video_config, metadata_db, phase1_indexing

def check_index_status():
    """
    Check the status of the FAISS indexes and database.
    Returns True if indexes need to be rebuilt, False otherwise.
    """
    # Get database stats
    conn = metadata_db.get_db_connection()
    cursor = conn.cursor()
    
    # Total clips
    cursor.execute("SELECT COUNT(*) FROM video_clips")
    total_clips = cursor.fetchone()[0]
    
    # Clips with both embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE visual_embedding_path IS NOT NULL AND text_embedding_path IS NOT NULL")
    both_embeddings = cursor.fetchone()[0]
    
    conn.close()
    
    # Check if index files exist
    visual_index_exists = os.path.exists(video_config.VISUAL_FAISS_INDEX_PATH)
    transcript_index_exists = os.path.exists(video_config.TRANSCRIPT_FAISS_INDEX_PATH)
    
    print("\nCurrent Status:")
    print(f"  Total Clips in Database: {total_clips}")
    print(f"  Clips with Both Embeddings: {both_embeddings}")
    print(f"  Visual Index Exists: {visual_index_exists}")
    print(f"  Transcript Index Exists: {transcript_index_exists}")
    
    if not visual_index_exists or not transcript_index_exists:
        print("\nWARNING: One or both index files are missing. Indexes need to be rebuilt.")
        return True
    
    try:
        visual_index = faiss.read_index(video_config.VISUAL_FAISS_INDEX_PATH)
        transcript_index = faiss.read_index(video_config.TRANSCRIPT_FAISS_INDEX_PATH)
        
        print(f"  Visual Index Vectors: {visual_index.ntotal}")
        print(f"  Transcript Index Vectors: {transcript_index.ntotal}")
        
        if visual_index.ntotal != both_embeddings or transcript_index.ntotal != both_embeddings:
            print("\nWARNING: Index vector counts do not match the number of clips with embeddings in the database.")
            print("This indicates that the indexes need to be rebuilt with --force.")
            return True
    except Exception as e:
        print(f"Error checking index files: {e}")
        return True
    
    return False

def backup_index_files():
    """Backup the existing FAISS index files."""
    visual_index_path = video_config.VISUAL_FAISS_INDEX_PATH
    transcript_index_path = video_config.TRANSCRIPT_FAISS_INDEX_PATH
    
    if os.path.exists(visual_index_path):
        backup_path = f"{visual_index_path}.backup"
        shutil.copy2(visual_index_path, backup_path)
        print(f"Backed up visual index to {backup_path}")
    
    if os.path.exists(transcript_index_path):
        backup_path = f"{transcript_index_path}.backup"
        shutil.copy2(transcript_index_path, backup_path)
        print(f"Backed up transcript index to {backup_path}")

def rebuild_indexes(force=True):
    """Rebuild the FAISS indexes."""
    print(f"\nRebuilding indexes with force={force}...")
    
    # Ensure directories exist
    video_config.ensure_directories()
    
    # Rebuild indexes
    phase1_indexing.process_video_library_for_indexing(force=force)
    
    print("Index rebuild complete.")

def main():
    parser = argparse.ArgumentParser(description="Check and fix FAISS indexes for video retrieval")
    parser.add_argument("--fix", action="store_true", help="Fix indexes if they are out of sync")
    parser.add_argument("--force", action="store_true", help="Force rebuild of all indexes")
    parser.add_argument("--no-backup", action="store_true", help="Skip backing up existing index files")
    args = parser.parse_args()
    
    print("Checking FAISS indexes and database...")
    
    # Check if indexes need to be rebuilt
    needs_rebuild = check_index_status()
    
    if needs_rebuild or args.force:
        if args.fix or args.force:
            if not args.no_backup:
                backup_index_files()
            
            rebuild_indexes(force=True)
            
            # Check status after rebuild
            print("\nAfter rebuild:")
            check_index_status()
        else:
            print("\nIndexes need to be rebuilt. Run with --fix to rebuild them.")
    else:
        print("\nIndexes appear to be in sync with the database. No rebuild needed.")
        if args.force:
            print("However, --force flag is set, so rebuilding anyway...")
            if not args.no_backup:
                backup_index_files()
            
            rebuild_indexes(force=True)
            
            # Check status after rebuild
            print("\nAfter rebuild:")
            check_index_status()

if __name__ == "__main__":
    main()
