#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the variance and similarity of embeddings in FAISS indexes.
This helps diagnose issues with embeddings that are too similar.
"""

import numpy as np
import faiss
import os
import matplotlib.pyplot as plt
import video_config

def analyze_faiss_index(index_path, index_name=""):
    """
    Analyzes a FAISS index to check for embedding similarity issues.
    Prints statistics and returns the embeddings for further analysis.
    """
    if not os.path.exists(index_path):
        print(f"Index file {index_path} does not exist.")
        return None

    try:
        # Load the index
        index = faiss.read_index(index_path)
        print(f"\n--- Analyzing {index_name} FAISS Index ---")
        print(f"Index contains {index.ntotal} vectors of dimension {index.d}")

        # Extract the raw vectors
        try:
            # For IndexIDMap, we need to access the underlying index
            if isinstance(index, faiss.IndexIDMap):
                print("Detected IndexIDMap, accessing underlying index...")
                base_index = faiss.downcast_index(index.index)

                # For IndexFlatIP (used in our code)
                if isinstance(base_index, faiss.IndexFlatIP):
                    print("Accessing vectors from IndexFlatIP...")
                    # For IndexFlatIP, we need to use the codes attribute
                    xb = faiss.vector_to_array(base_index.get_xb()).reshape(-1, base_index.d)
                elif isinstance(base_index, faiss.IndexFlat):
                    print("Accessing vectors from IndexFlat...")
                    xb = faiss.vector_to_array(base_index.get_xb()).reshape(-1, base_index.d)
                else:
                    print(f"Underlying index is of type: {type(base_index)}")
                    print("Cannot extract vectors from this index type.")
                    return None
            elif hasattr(index, 'get_xb'):
                # For direct flat indexes
                xb = faiss.vector_to_array(index.get_xb()).reshape(-1, index.d)
            else:
                print(f"Could not determine how to extract vectors from {index_name} index of type {type(index)}.")
                return None
        except Exception as e:
            print(f"Error extracting vectors from {index_name} index: {e}")
            import traceback
            traceback.print_exc()
            return None

        # Calculate pairwise cosine similarity
        cos = xb @ xb.T
        np.fill_diagonal(cos, 0)  # Zero out self-similarity

        # Print statistics
        print(f"Pairwise cosine similarity statistics:")
        print(f"  Min: {cos.min():.6f}")
        print(f"  Max: {cos.max():.6f}")
        print(f"  Mean: {cos.mean():.6f}")
        print(f"  Std: {cos.std():.6f}")

        # Check for potential issues
        if cos.mean() > 0.9:
            print(f"WARNING: Very high mean similarity ({cos.mean():.6f}) suggests embeddings are too similar!")
        elif cos.mean() > 0.7:
            print(f"CAUTION: High mean similarity ({cos.mean():.6f}) may indicate limited discriminative power.")
        else:
            print(f"Mean similarity ({cos.mean():.6f}) appears reasonable.")

        return xb, cos

    except Exception as e:
        print(f"Error analyzing {index_name} index: {e}")
        import traceback
        traceback.print_exc()
        return None

def plot_similarity_histogram(cos_matrix, title, output_path=None):
    """
    Plots a histogram of pairwise similarities.
    """
    plt.figure(figsize=(10, 6))

    # Flatten the matrix and remove self-similarities (diagonal)
    similarities = cos_matrix.flatten()

    plt.hist(similarities, bins=50, alpha=0.75)
    plt.title(f"Pairwise Cosine Similarity Distribution - {title}")
    plt.xlabel("Cosine Similarity")
    plt.ylabel("Frequency")
    plt.grid(True, alpha=0.3)

    # Add vertical line at mean
    mean_sim = np.mean(similarities)
    plt.axvline(mean_sim, color='r', linestyle='--', label=f'Mean: {mean_sim:.4f}')
    plt.legend()

    if output_path:
        plt.savefig(output_path)
        print(f"Saved histogram to {output_path}")
    else:
        plt.show()

    plt.close()

def main():
    """
    Main function to analyze both visual and text indexes.
    """
    print("Checking FAISS indexes for embedding similarity issues...")

    # Ensure output directory exists
    os.makedirs(os.path.join(video_config.DATA_DIR, "diagnostics"), exist_ok=True)

    # Analyze visual index
    visual_result = analyze_faiss_index(
        video_config.VISUAL_FAISS_INDEX_PATH,
        "Visual"
    )

    # Analyze text index
    text_result = analyze_faiss_index(
        video_config.TRANSCRIPT_FAISS_INDEX_PATH,
        "Text"
    )

    # Generate histograms if matplotlib is available
    try:
        if visual_result:
            _, visual_cos = visual_result
            plot_similarity_histogram(
                visual_cos,
                "Visual Embeddings",
                os.path.join(video_config.DATA_DIR, "diagnostics", "visual_similarity_hist.png")
            )

        if text_result:
            _, text_cos = text_result
            plot_similarity_histogram(
                text_cos,
                "Text Embeddings",
                os.path.join(video_config.DATA_DIR, "diagnostics", "text_similarity_hist.png")
            )
    except ImportError:
        print("Matplotlib not available. Skipping histogram generation.")

    print("\nAnalysis complete. If mean similarity values are very high (>0.9),")
    print("your embeddings may not be discriminative enough for effective search.")
    print("Consider rebuilding indexes with the fixes implemented.")

if __name__ == "__main__":
    main()
