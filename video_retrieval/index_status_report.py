#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate a comprehensive status report for the video retrieval system.
"""

import os
import faiss
import sqlite3
from datetime import datetime
import video_config
import metadata_db

def get_db_connection():
    """Establishes a connection to the SQLite database."""
    conn = sqlite3.connect(video_config.METADATA_DB_PATH)
    conn.row_factory = sqlite3.Row  # Access columns by name
    return conn

def check_faiss_index(index_path):
    """Check a FAISS index file and return information about it."""
    if not os.path.exists(index_path):
        return {
            "exists": False,
            "size": 0,
            "vectors": 0,
            "modified": None
        }
    
    # Get file stats
    size = os.path.getsize(index_path)
    modified = datetime.fromtimestamp(os.path.getmtime(index_path))
    
    # Load the index and get vector count
    try:
        index = faiss.read_index(index_path)
        vectors = index.ntotal
        dimension = index.d
    except Exception as e:
        return {
            "exists": True,
            "size": size,
            "vectors": "Error loading index",
            "modified": modified,
            "error": str(e)
        }
    
    return {
        "exists": True,
        "size": size,
        "vectors": vectors,
        "dimension": dimension,
        "modified": modified
    }

def get_db_stats():
    """Get comprehensive statistics from the database."""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Total videos
    cursor.execute("SELECT COUNT(*) FROM long_videos")
    total_videos = cursor.fetchone()[0]
    
    # Total clips
    cursor.execute("SELECT COUNT(*) FROM video_clips")
    total_clips = cursor.fetchone()[0]
    
    # Clips with visual embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE visual_embedding_path IS NOT NULL")
    visual_embeddings = cursor.fetchone()[0]
    
    # Clips with text embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE text_embedding_path IS NOT NULL")
    text_embeddings = cursor.fetchone()[0]
    
    # Clips with both embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE visual_embedding_path IS NOT NULL AND text_embedding_path IS NOT NULL")
    both_embeddings = cursor.fetchone()[0]
    
    # Videos without clips
    cursor.execute("""
        SELECT COUNT(*) FROM long_videos lv 
        WHERE NOT EXISTS (
            SELECT 1 FROM video_clips vc WHERE vc.original_long_video_id = lv.id
        )
    """)
    videos_without_clips = cursor.fetchone()[0]
    
    # Clips without embeddings
    cursor.execute("SELECT COUNT(*) FROM video_clips WHERE visual_embedding_path IS NULL OR text_embedding_path IS NULL")
    clips_without_embeddings = cursor.fetchone()[0]
    
    conn.close()
    
    return {
        "total_videos": total_videos,
        "total_clips": total_clips,
        "visual_embeddings": visual_embeddings,
        "text_embeddings": text_embeddings,
        "both_embeddings": both_embeddings,
        "videos_without_clips": videos_without_clips,
        "clips_without_embeddings": clips_without_embeddings
    }

def check_index_consistency():
    """Check if the FAISS indexes are consistent with the database."""
    visual_index_info = check_faiss_index(video_config.VISUAL_FAISS_INDEX_PATH)
    transcript_index_info = check_faiss_index(video_config.TRANSCRIPT_FAISS_INDEX_PATH)
    db_stats = get_db_stats()
    
    issues = []
    
    # Check if index files exist
    if not visual_index_info['exists']:
        issues.append("Visual FAISS index file does not exist")
    if not transcript_index_info['exists']:
        issues.append("Transcript FAISS index file does not exist")
    
    # Check vector counts
    if visual_index_info['exists'] and isinstance(visual_index_info['vectors'], int):
        if visual_index_info['vectors'] != db_stats['both_embeddings']:
            issues.append(f"Visual index has {visual_index_info['vectors']} vectors but database has {db_stats['both_embeddings']} clips with both embeddings")
    
    if transcript_index_info['exists'] and isinstance(transcript_index_info['vectors'], int):
        if transcript_index_info['vectors'] != db_stats['both_embeddings']:
            issues.append(f"Transcript index has {transcript_index_info['vectors']} vectors but database has {db_stats['both_embeddings']} clips with both embeddings")
    
    # Check for missing embeddings
    if db_stats['clips_without_embeddings'] > 0:
        issues.append(f"{db_stats['clips_without_embeddings']} clips are missing embeddings")
    
    # Check for videos without clips
    if db_stats['videos_without_clips'] > 0:
        issues.append(f"{db_stats['videos_without_clips']} videos have no clips")
    
    return issues

def main():
    """Generate a comprehensive status report."""
    print("=" * 80)
    print("VIDEO RETRIEVAL SYSTEM STATUS REPORT")
    print("=" * 80)
    print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check FAISS indexes
    visual_index_info = check_faiss_index(video_config.VISUAL_FAISS_INDEX_PATH)
    transcript_index_info = check_faiss_index(video_config.TRANSCRIPT_FAISS_INDEX_PATH)
    
    # Get database stats
    db_stats = get_db_stats()
    
    # Print database statistics
    print("DATABASE STATISTICS:")
    print("-" * 40)
    print(f"  Total Videos: {db_stats['total_videos']}")
    print(f"  Total Clips: {db_stats['total_clips']}")
    print(f"  Clips with Visual Embeddings: {db_stats['visual_embeddings']}")
    print(f"  Clips with Text Embeddings: {db_stats['text_embeddings']}")
    print(f"  Clips with Both Embeddings: {db_stats['both_embeddings']}")
    print(f"  Videos without Clips: {db_stats['videos_without_clips']}")
    print(f"  Clips without Embeddings: {db_stats['clips_without_embeddings']}")
    print()
    
    # Print FAISS index information
    print("FAISS INDEX STATUS:")
    print("-" * 40)
    print("Visual Index:")
    print(f"  Exists: {visual_index_info['exists']}")
    if visual_index_info['exists']:
        print(f"  Size: {visual_index_info['size'] / (1024*1024):.2f} MB")
        print(f"  Vectors: {visual_index_info['vectors']}")
        if 'dimension' in visual_index_info:
            print(f"  Dimension: {visual_index_info['dimension']}")
        print(f"  Last Modified: {visual_index_info['modified']}")
    
    print("\nTranscript Index:")
    print(f"  Exists: {transcript_index_info['exists']}")
    if transcript_index_info['exists']:
        print(f"  Size: {transcript_index_info['size'] / (1024*1024):.2f} MB")
        print(f"  Vectors: {transcript_index_info['vectors']}")
        if 'dimension' in transcript_index_info:
            print(f"  Dimension: {transcript_index_info['dimension']}")
        print(f"  Last Modified: {transcript_index_info['modified']}")
    print()
    
    # Check consistency
    issues = check_index_consistency()
    
    print("SYSTEM HEALTH CHECK:")
    print("-" * 40)
    if not issues:
        print("✅ All systems are functioning correctly!")
        print("✅ Indexes are in sync with the database")
        print("✅ No issues detected")
    else:
        print("⚠️  Issues detected:")
        for issue in issues:
            print(f"   - {issue}")
    
    print()
    
    # Calculate coverage percentages
    if db_stats['total_clips'] > 0:
        visual_coverage = (db_stats['visual_embeddings'] / db_stats['total_clips']) * 100
        text_coverage = (db_stats['text_embeddings'] / db_stats['total_clips']) * 100
        both_coverage = (db_stats['both_embeddings'] / db_stats['total_clips']) * 100
        
        print("EMBEDDING COVERAGE:")
        print("-" * 40)
        print(f"  Visual Embeddings: {visual_coverage:.1f}%")
        print(f"  Text Embeddings: {text_coverage:.1f}%")
        print(f"  Both Embeddings: {both_coverage:.1f}%")
        print()
    
    # Recommendations
    print("RECOMMENDATIONS:")
    print("-" * 40)
    if db_stats['videos_without_clips'] > 0:
        print(f"  - Process {db_stats['videos_without_clips']} videos that have no clips")
        print("    Run: python video_retrieval_main.py process_long_video --process_all_new_in_db")
    
    if db_stats['clips_without_embeddings'] > 0:
        print(f"  - Generate embeddings for {db_stats['clips_without_embeddings']} clips")
        print("    Run: python video_retrieval_main.py build_index")
    
    if not issues:
        print("  - System is ready for video search operations")
        print("    Test with: python video_retrieval_main.py search --query 'your search term'")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
