#!/usr/bin/env python3
"""
Fetches videos from Pixabay API based on keywords and saves them.
"""
import httpx
import asyncio
import math
from pathlib import Path
import os
import functools # For run_in_executor

import video_config
import token_bucket as tb # Functional token bucket
import downloader_common as common
from downloader_common import VideoProcessStatus # Import the Enum

PIXABAY_API_URL = "https://pixabay.com/api/videos/"
PER_PAGE_DEFAULT = 200 # Pixabay API default and common value, max is 200
SOURCE_NAME = "pixabay"

def _parse_description_from_pixabay_url(pixabay_page_url: str) -> str:
    """Helper to parse a description-like slug from a Pixabay video page URL."""
    if not pixabay_page_url:
        return ""
    try:
        # Example URL: https://pixabay.com/videos/thief-bag-car-stealing-criminal-3582/
        # Path part is /videos/thief-bag-car-stealing-criminal-3582/
        # We want 'thief-bag-car-stealing-criminal'
        path_segments = Path(pixabay_page_url.strip('/')).parts
        if len(path_segments) > 0:
            slug_with_id = path_segments[-1] # last part of the path, e.g., 'thief-bag-car-stealing-criminal-3582'
            parts = slug_with_id.split('-')
            if parts:
                if parts[-1].isdigit(): # Remove numeric ID if it's the last part
                    parts = parts[:-1]
                # Pixabay slugs usually don't have a leading 'video-' or 'photo-' prefix like Pexels once path is parsed
                return " ".join(parts).strip()
    except Exception as e:
        print(f"Error parsing Pixabay URL slug from {pixabay_page_url}: {e}")
    return ""

async def fetch_pixabay_page(session: httpx.AsyncClient, pixabay_api_key: str, query: str, page: int, per_page: int):
    """Fetches a single page of video results from Pixabay API."""
    params = {
        "key": pixabay_api_key,
        "q": query,
        "page": page,
        "per_page": per_page,
        "video_type": "film", # Aim for cinematic content, can be film, animation
        "safesearch": "true"
    }
    print(f"Fetching Pixabay: Query='{query}', Page={page}, PerPage={per_page}")
    try:
        response = await session.get(PIXABAY_API_URL, params=params, timeout=video_config.API_REQUEST_TIMEOUT_SECONDS)
        response.raise_for_status()
        data = response.json()
        # Pixabay API includes rate limit info in headers, which can be logged if needed
        # print(f"Pixabay Headers: {response.headers}") 
        return data
    except httpx.HTTPStatusError as e:
        print(f"HTTP error fetching Pixabay page for query '{query}', page {page}: {e.response.status_code} - {e.response.text}")
        if e.response.status_code == 429: # Rate limit specific message
            print("Pixabay API rate limit hit (429). Token bucket should manage this.")
            # Pixabay's 429 might not have a Retry-After, so rely on bucket's delay or add a fixed one.
            await asyncio.sleep(video_config.PIXABAY_RETRY_DELAY_ON_429_SECONDS) # Generic delay for 429
        raise
    except httpx.RequestError as e:
        print(f"Request error fetching Pixabay page for query '{query}', page {page}: {e}")
        raise
    except Exception as e:
        print(f"Unexpected error fetching Pixabay page for query '{query}', page {page}: {e}")
        raise

async def process_pixabay_hit(session: httpx.AsyncClient, hit_data: dict, query_keyword: str):
    """Processes a single video hit from Pixabay API response. Returns VideoProcessStatus."""
    video_id = hit_data.get("id")
    if not video_id:
        print("Skipping Pixabay hit due to missing ID.")
        return VideoProcessStatus.FAILED

    # === START MODIFICATION ===
    # Check if video already processed and downloaded
    loop = asyncio.get_event_loop()
    db_check_func = functools.partial(common.check_if_video_exists_in_db, SOURCE_NAME, str(video_id))
    video_exists_in_db = await loop.run_in_executor(None, db_check_func)

    if video_exists_in_db:
        print(f"Pixabay video ID {video_id} already in DB. Skipping download and metadata save.")
        return VideoProcessStatus.SKIPPED_EXISTS
    # === END MODIFICATION ===

    videos_map = hit_data.get("videos")
    if not videos_map or not isinstance(videos_map, dict):
        print(f"No 'videos' map found or invalid format for Pixabay ID {video_id}")
        return VideoProcessStatus.FAILED

    # Select best quality: large -> medium -> small. Prefer MP4.
    # Pixabay provides various resolutions, e.g., large, medium, small, tiny
    # Each might have different sizes (e.g., 1920x1080, 1280x720, etc.)
    # The example from user used `hit["videos"]["large"]["url"]`
    # We should iterate to find the best available MP4.
    
    best_video_url = ""
    chosen_width = 0
    chosen_height = 0
    chosen_file_ext = ".mp4"

    # Quality preference: large, medium, small
    for quality_key in ["large", "medium", "small"]:
        if quality_key in videos_map and videos_map[quality_key].get("url"):
            video_info = videos_map[quality_key]
            current_width = video_info.get("width", 0)
            current_height = video_info.get("height", 0)

            # Ensure it's a landscape video (width > height)
            if not (current_width and current_height and current_width > current_height):
                print(f"  Skipping Pixabay video quality '{quality_key}' (ID: {video_id}, W:{current_width}, H:{current_height}) as it is not landscape or dimensions are missing.")
                continue

            # For Pixabay, assume mp4 if type not specified, but prioritize if type info exists
            # Their API docs don't specify file_type per resolution, but typically they are mp4.
            best_video_url = video_info["url"]
            chosen_width = current_width
            chosen_height = current_height
            try:
                chosen_file_ext = Path(best_video_url.split('?')[0]).suffix or ".mp4"
            except Exception:
                chosen_file_ext = ".mp4"
            break # Found a suitable quality
            
    if not best_video_url:
        print(f"No suitable video URL found for Pixabay ID {video_id}")
        return VideoProcessStatus.FAILED

    output_path = common.get_video_download_path(SOURCE_NAME, video_id, chosen_file_ext)

    print(f"Processing Pixabay video ID {video_id}: URL {best_video_url}")
    download_successful = await common.download_video_content(session, best_video_url, output_path)

    if download_successful and output_path.exists():
        print(f"Pixabay video ID {video_id} downloaded to {output_path}")
        
        # 1. Attempt to parse description from the Pixabay pageURL
        video_page_url = hit_data.get('pageURL')
        parsed_description = _parse_description_from_pixabay_url(video_page_url)

        # 2. Get tags from API (comma-separated string)
        api_tags_str = hit_data.get("tags", "")
        api_tags_list = [tag.strip().lower() for tag in api_tags_str.split(',') if tag.strip()]
        
        # 3. Prepare description and keywords separately.
        video_description_to_save = parsed_description if parsed_description else ""
        
        keywords_to_save = set()
        for api_tag in api_tags_list:
            if api_tag: keywords_to_save.add(api_tag)
        
        # If no API tags, and no description derived from URL, consider query_keyword for keywords.
        # Or, if we have a description but no specific keywords from API, query_keyword can be a keyword.
        if not keywords_to_save and query_keyword:
            keywords_to_save.add(query_keyword.strip().lower())
        elif video_description_to_save and not keywords_to_save and query_keyword: # Has description, but no API keywords, use query
             keywords_to_save.add(query_keyword.strip().lower())
        # If there's no description AND no API tags, but we have a query keyword, it could be a description fallback too if desired.
        # Current logic: description is from URL slug. If slug is empty, description is empty.
        # query_keyword primarily fills empty keywords list.

        final_keywords_list = sorted(list(keywords_to_save))

        common.save_video_metadata(
            source_name=SOURCE_NAME,
            source_video_id=str(video_id),
            video_path=output_path,
            description=video_description_to_save,
            keywords=final_keywords_list,
            raw_api_data=hit_data,
            width=chosen_width if chosen_width else None, # Use chosen if available
            height=chosen_height if chosen_height else None,
            duration_override=float(hit_data["duration"]) if hit_data.get("duration") is not None else None
        )
        return VideoProcessStatus.PROCESSED_NEWLY
    else:
        print(f"Failed to download Pixabay video ID {video_id}.")
        return VideoProcessStatus.FAILED

async def crawl_pixabay_for_keyword(session: httpx.AsyncClient, pixabay_api_key: str, pixabay_bucket_state: dict, keyword: str, max_videos_per_keyword: int | None = None):
    """Crawls Pixabay for a single keyword."""
    page = 1
    # === START MODIFICATION ===
    # total_videos_processed_for_keyword = 0 # Old counter
    newly_processed_videos_for_keyword = 0 # New counter
    attempted_to_process_count = 0       # New counter
    # === END MODIFICATION ===
    # Pixabay limits to roughly first 500 results (e.g. 20 per_page * 25 pages)
    max_pages_to_fetch = video_config.PIXABAY_MAX_PAGES_PER_KEYWORD # Configurable safety limit, e.g., 25-30

    while page <= max_pages_to_fetch:
        # === START MODIFICATION ===
        if max_videos_per_keyword is not None and newly_processed_videos_for_keyword >= max_videos_per_keyword:
            print(f"Reached max_new_videos_per_keyword ({max_videos_per_keyword}) for Pixabay query '{keyword}'.")
            break
        
        if attempted_to_process_count > max_videos_per_keyword * video_config.PIXABAY_MAX_ATTEMPTS_FACTOR_FOR_NEW:
            print(f"Pixabay: Keyword '{keyword}'. Attempted to process {attempted_to_process_count} videos but only found {newly_processed_videos_for_keyword} new ones. Max attempts reached. Moving on.")
            break
        # === END MODIFICATION ===

        print(f"Pixabay: Attempting to acquire token for '{keyword}' page {page}...")
        if not await tb.acquire_async_token(pixabay_bucket_state):
            print(f"Pixabay: Failed to acquire token for '{keyword}' page {page}.")
            await asyncio.sleep(5) # Safety sleep
            continue
        print(f"Pixabay: Token acquired for '{keyword}' page {page}.")

        try:
            page_data = await fetch_pixabay_page(session, pixabay_api_key, keyword, page, PER_PAGE_DEFAULT)
        except Exception as e:
            print(f"Could not fetch Pixabay page {page} for keyword '{keyword}': {e}. Stopping for this keyword.")
            break

        if not page_data or not page_data.get("hits"):
            print(f"No more Pixabay videos found for keyword '{keyword}' on page {page}. Total hits in response: {page_data.get('totalHits',0) if page_data else 0}")
            break

        video_hits = page_data["hits"]
        print(f"Found {len(video_hits)} Pixabay videos on page {page} for keyword '{keyword}'. Total hits for query: {page_data.get('totalHits')}")

        # === START MODIFICATION ===
        page_processing_tasks = []
        videos_to_consider_on_this_page = 0
        for hit_item in video_hits:
            if max_videos_per_keyword is not None and newly_processed_videos_for_keyword >= max_videos_per_keyword:
                break
            page_processing_tasks.append(process_pixabay_hit(session, hit_item, keyword))
            videos_to_consider_on_this_page += 1
            attempted_to_process_count += 1
        
        if page_processing_tasks:
            results = await asyncio.gather(*page_processing_tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, Exception):
                    print(f"Error processing a Pixabay video item: {result}")
                elif result == VideoProcessStatus.PROCESSED_NEWLY:
                    newly_processed_videos_for_keyword += 1
        # === END MODIFICATION ===
        
        total_hits_overall = page_data.get("totalHits", 0)
        if page * PER_PAGE_DEFAULT >= total_hits_overall:
            print(f"Processed all available ({total_hits_overall}) Pixabay videos for keyword '{keyword}'.")
            break

        # === START MODIFICATION ===
        newly_processed_on_this_page = sum(1 for r in results if r == VideoProcessStatus.PROCESSED_NEWLY) if 'results' in locals() and results else 0
        if newly_processed_on_this_page == 0 and videos_to_consider_on_this_page > 0:
            print(f"Pixabay: Keyword '{keyword}'. Page {page}. Processed {videos_to_consider_on_this_page} video items, but 0 were new. Total new so far: {newly_processed_videos_for_keyword}.")
        # === END MODIFICATION ===
            
        page += 1
        # Optional small delay between pages if not fully handled by token bucket for Pixabay's stricter limits
        # await asyncio.sleep(1)

    print(f"Finished Pixabay crawl for keyword '{keyword}'. Processed {newly_processed_videos_for_keyword} new videos (attempted: {attempted_to_process_count}).")

async def main_pixabay_downloader(keywords: list[str] | None = None, keywords_file: str | Path | None = None, max_videos_per_keyword: int | None = None):
    """Main function to orchestrate Pixabay video downloading."""
    if not video_config.PIXABAY_API_KEY:
        print("PIXABAY_API_KEY not configured in video_config.py. Skipping Pixabay download.")
        return

    all_keywords = []
    if keywords:
        all_keywords.extend(keywords)
    if keywords_file:
        all_keywords.extend(common.read_keywords_from_file(keywords_file))
    
    if not all_keywords:
        all_keywords = common.read_keywords_from_file(video_config.KEYWORDS_FILE_PATH)
        if not all_keywords:
             print("No keywords provided and default keywords file is empty or not found. Skipping Pixabay download.")
             return

    all_keywords = sorted(list(set(kw for kw in all_keywords if kw.strip())))
    if not all_keywords:
        print("No valid keywords to process for Pixabay. Exiting.")
        return

    print(f"Starting Pixabay downloader for keywords: {all_keywords}")

    # Pixabay: e.g., 100 requests per 60 seconds
    pixabay_bucket_state = tb.create_async_token_bucket(
        capacity=video_config.PIXABAY_RATE_LIMIT_CAPACITY,
        period=video_config.PIXABAY_RATE_LIMIT_PERIOD_SECONDS
    )

    client_headers = {"User-Agent": video_config.DOWNLOADER_USER_AGENT}

    async with httpx.AsyncClient(headers=client_headers, timeout=video_config.API_REQUEST_TIMEOUT_SECONDS, follow_redirects=True) as session:
        for keyword in all_keywords:
            if not keyword.strip(): continue
            print(f"\n--- Starting Pixabay crawl for keyword: '{keyword}' ---")
            await crawl_pixabay_for_keyword(session, video_config.PIXABAY_API_KEY, pixabay_bucket_state, keyword, max_videos_per_keyword)
    
    print("\n--- Pixabay video download process completed. ---")

if __name__ == '__main__':
    # Example:
    # Ensure video_config.py has PIXABAY_API_KEY and other relevant settings.
    # video_config.ensure_directories()
    # metadata_db.create_tables()

    if not Path(video_config.KEYWORDS_FILE_PATH).exists():
        with open(video_config.KEYWORDS_FILE_PATH, 'w') as f:
            f.write("mountain landscape\n")
            f.write("business meeting\n")
        print(f"Created dummy keywords file: {video_config.KEYWORDS_FILE_PATH}")

    print("Running Pixabay Downloader directly (for testing purposes)...")
    # asyncio.run(main_pixabay_downloader(keywords=["beach sunset"], max_videos_per_keyword=5))
    asyncio.run(main_pixabay_downloader(keywords_file=video_config.KEYWORDS_FILE_PATH, max_videos_per_keyword=3)) 