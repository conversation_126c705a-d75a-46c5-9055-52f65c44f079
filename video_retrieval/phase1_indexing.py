import os
import re
import numpy as np
import faiss
import torch
import cv2
from PIL import Image
from transformers import CLIPModel, CLIPProcessor
import whisper
import json

from video_retrieval import video_config
from video_retrieval import utils
from video_retrieval import metadata_db

# Global Whisper model cache
_whisper_model_cache = {}

def clean_transcript_for_embedding(transcript_text):
    """
    Cleans transcript text to remove common boilerplate and sponsorship mentions
    to increase variance between embeddings.

    Args:
        transcript_text (str): The original transcript text

    Returns:
        str: Cleaned transcript text
    """
    if not transcript_text:
        return ""

    # Remove sponsor mentions and common boilerplate
    patterns = [
        r"hunter ?killer",  # Sponsor mentions
        r"sponsor",
        r"sponsored by",
        r"this video is brought to you by",
        r"thanks to our sponsor",
        r"like and subscribe",
        r"click the bell",
        r"check out the link in the description",
        r"follow me on",
        r"visit our website",
    ]

    cleaned_text = transcript_text
    for pattern in patterns:
        cleaned_text = re.sub(pattern, "", cleaned_text, flags=re.IGNORECASE)

    # Remove excessive whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

    print(f"Cleaned transcript from {len(transcript_text)} to {len(cleaned_text)} chars")
    return cleaned_text

def get_whisper_model(model_name=video_config.WHISPER_MODEL_NAME, device=video_config.DEVICE):
    """
    Loads and caches the Whisper model.
    Mirrors the logic from audio2text.py for model loading and caching.
    """
    if model_name not in _whisper_model_cache:
        print(f"Loading Whisper model: {model_name} on device: {device}")
        # Load model to CPU first, then handle potential sparse tensor issues before moving to target device.
        model = whisper.load_model(model_name)

        # Handle potential sparse tensor issues on some devices/versions (like in audio2text.py)
        # This section attempts to mirror the logic from audio2text.py for alignment_heads
        try:
            if "alignment_heads" in dict(model.named_buffers()):
                alignment_heads_buffer = model.get_buffer("alignment_heads")
                # Check if the buffer is sparse (older PyTorch or specific model states)
                if hasattr(alignment_heads_buffer, 'is_sparse') and alignment_heads_buffer.is_sparse:
                    print(f"Whisper model \"{model_name}\" has sparse alignment_heads. Attempting to densify.")
                    dense_heads = alignment_heads_buffer.to_dense()
                    # The audio2text.py example re-registers the buffer:
                    model.register_buffer("alignment_heads", dense_heads, persistent=False)
                    # Direct buffer manipulation can be version-dependent.
                    # For robustness, we log and rely on subsequent .to(device) to manage internal states if direct re-registration is skipped.
                    # If issues arise, this part might need specific attention based on the Whisper version.
                    print("Densification of alignment_heads performed by re-registering the buffer. Model will now be moved to device.")
                elif not hasattr(alignment_heads_buffer, 'is_sparse'):
                    print(f"alignment_heads buffer in model \"{model_name}\" does not have is_sparse attribute. Assuming dense or handled by library.")
                else:
                    print(f"Whisper model \"{model_name}\" alignment_heads are already dense.")
            else:
                print(f"No alignment_heads buffer found in model \"{model_name}\". Skipping densification check.")
        except Exception as e_sparse:
            print(f"Warning: Error during alignment_heads processing for model \"{model_name}\": {e_sparse}. Proceeding with model loading.")

        model = model.to(device)
        _whisper_model_cache[model_name] = model
        print(f"Whisper model {model_name} loaded and cached on {device}.")
    return _whisper_model_cache[model_name]


# --- CLIP4Clip Model Loading ---
def load_clip4clip_model(model_name="Searchium-ai/clip4clip-webvid150k", device=video_config.DEVICE):
    """
    Loads the CLIP4Clip model and processor.
    """
    print(f"Loading CLIP4Clip model from {model_name} on {device}")
    model = CLIPModel.from_pretrained(model_name, torch_dtype="auto").to(device)
    # Use the same model name for processor to ensure compatibility
    processor = CLIPProcessor.from_pretrained(model_name)
    print("CLIP4Clip model and processor loaded.")
    return model, processor

def load_sentence_transformer_model(model_name=video_config.TEXT_EMBEDDING_MODEL_FOR_INDEXING, device=video_config.DEVICE):
    """
    Loads the SentenceTransformer model for text embeddings.
    """
    print(f"Loading SentenceTransformer model: {model_name} on {device}")
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer(model_name, device=device)
        return model
    except Exception as e:
        print(f"Error loading SentenceTransformer model {model_name}: {e}")
        return None

# --- FFmpeg Processing --- (Frame and Audio Extraction)

def extract_frames_ffmpeg(clip_path, clip_id, frames_output_dir):
    """
    Extracts frames from a video clip using FFmpeg.
    Saves frames to frames_output_dir/clip_id_xxxx.jpg
    Returns a list of paths to the extracted frames.
    """
    os.makedirs(frames_output_dir, exist_ok=True)
    frame_pattern = os.path.join(frames_output_dir, f"{clip_id}_%04d.jpg")

    cmd = [
        "ffmpeg", "-i", clip_path,
        "-vf", f"fps={video_config.FRAME_EXTRACT_FPS},scale={video_config.FRAME_SCALE}",
        "-q:v", "2", # Quality for JPG (1-31, lower is better)
        frame_pattern,
        "-y"
    ]
    success, output = utils.run_ffmpeg_command(cmd)
    if not success:
        print(f"Failed to extract frames for {clip_path}: {output}")
        return []

    # List extracted frames
    extracted_frames = sorted([
        os.path.join(frames_output_dir, f)
        for f in os.listdir(frames_output_dir)
        if f.startswith(f"{clip_id}_") and f.endswith(".jpg")
    ])
    print(f"Extracted {len(extracted_frames)} frames for clip_id {clip_id} to {frames_output_dir}")
    return extracted_frames

def extract_audio_ffmpeg(clip_path, clip_id, audio_output_dir):
    """
    Extracts audio from a video clip to WAV format.
    Saves audio to audio_output_dir/clip_id.wav
    Returns the path to the extracted audio file.
    """
    os.makedirs(audio_output_dir, exist_ok=True)
    audio_output_path = os.path.join(audio_output_dir, f"{clip_id}.wav")

    cmd = [
        "ffmpeg", "-i", clip_path,
        "-vn", # No video
        "-acodec", "pcm_s16le", # WAV format
        "-ar", str(video_config.AUDIO_EXTRACT_SAMPLE_RATE),
        "-ac", "1", # Mono audio
        audio_output_path,
        "-y"
    ]
    success, output = utils.run_ffmpeg_command(cmd)
    if not success:
        print(f"Failed to extract audio for {clip_path}: {output}")
        return None
    print(f"Extracted audio for clip_id {clip_id} to {audio_output_path}")
    return audio_output_path

# --- Whisper Audio Re-Transcription (if needed) ---
def looks_like_placeholder(t: str) -> bool:
    """
    Checks if a transcript text looks like a placeholder rather than a real transcript.
    Returns True if the text is likely a placeholder.
    """
    if not t:
        return True

    # Check if it's too short to be a real transcript
    if len(t) < 30:
        return True

    # Check if it follows the pattern of placeholder text (filename + clip_id)
    if t.lower().startswith("the story of") and "clip_" in t:
        return True

    return False

def retranscribe_clip_audio_whisper(audio_path, clip_id):
    """
    Re-transcribes a given audio file using Whisper, leveraging a cached model.
    Returns the new transcript text.
    """
    print(f"Re-transcribing audio for clip_id {clip_id} from {audio_path} using cached Whisper model.")

    try:
        model = get_whisper_model() # Uses defaults from config
        result = model.transcribe(audio_path, language=None, verbose=False) # language=None for auto-detection

        full_text = result.get("text", "").strip()

        if full_text:
            print(f"Re-transcription successful for clip_id {clip_id}. Length: {len(full_text)}")
            # Ensure DB connection is handled per transaction or scoped properly
            conn = metadata_db.get_db_connection()
            try:
                conn.execute(
                    "UPDATE video_clips SET transcript_text = ? WHERE clip_id = ?",
                    (full_text, clip_id)
                )
                conn.commit()
            finally:
                conn.close()
            return full_text
        else:
            print(f"Re-transcription for clip_id {clip_id} resulted in empty text.")
            # Update the database with a placeholder indicating this is a silent clip
            placeholder_text = f"[SILENT_CLIP_{clip_id}]"
            conn = metadata_db.get_db_connection()
            try:
                conn.execute(
                    "UPDATE video_clips SET transcript_text = ? WHERE clip_id = ?",
                    (placeholder_text, clip_id)
                )
                conn.commit()
                print(f"Updated clip_id {clip_id} with placeholder text: '{placeholder_text}'")
            finally:
                conn.close()
            return placeholder_text
    except Exception as e:
        print(f"Error during Whisper re-transcription for clip_id {clip_id} ({audio_path}): {e}")
        import traceback
        traceback.print_exc()
        # Update the database with an error placeholder
        error_placeholder = f"[TRANSCRIPTION_ERROR_CLIP_{clip_id}]"
        try:
            conn = metadata_db.get_db_connection()
            conn.execute(
                "UPDATE video_clips SET transcript_text = ? WHERE clip_id = ?",
                (error_placeholder, clip_id)
            )
            conn.commit()
            conn.close()
            print(f"Updated clip_id {clip_id} with error placeholder: '{error_placeholder}'")
        except Exception as db_error:
            print(f"Failed to update database with error placeholder: {db_error}")
        return error_placeholder


# --- Embedding Extraction ---
def extract_visual_embeddings_internvideo(clip_id, frame_paths, model, processor, device=video_config.DEVICE):
    """
    Extracts visual embeddings from a sequence of frames using InternVideo2-CLIP (or similar).
    `model` and `processor` are the loaded visual model and its preprocessor.
    Returns a single Numpy array representing the aggregated visual embedding for the clip.
    """
    if not frame_paths:
        print(f"No frames provided for clip_id {clip_id}. Skipping visual embedding.")
        return None

    print(f"Extracting visual embeddings for clip_id {clip_id} from {len(frame_paths)} frames...")

    batch_size = 16 # Process frames in batches if many
    all_frame_embeddings = []

    try:
        for i in range(0, len(frame_paths), batch_size):
            batch_frame_paths = frame_paths[i:i+batch_size]
            images = []
            for frame_path in batch_frame_paths:
                try:
                    image = Image.open(frame_path).convert("RGB")
                    images.append(image)
                except Exception as e:
                    print(f"Warning: Could not load frame {frame_path}: {e}")
                    continue

            if not images: continue

            # Preprocess images using the model's processor/transform
            # The exact input format depends on the InternVideo2-CLIP model API.
            # It might expect a list of PIL Images, or a batched tensor.
            # Using the mock_processor defined in load_internvideo_clip_model for this example.
            try:
                # If processor expects list of PIL Images and returns a batch tensor:
                # inputs = processor(images=images, return_tensors="pt").to(device)
                # Or if it's a torchvision transform for individual images:
                processed_images = torch.stack([processor(img) for img in images]).to(device)
            except Exception as e:
                print(f"Error during image preprocessing for clip {clip_id}: {e}")
                continue

            with torch.no_grad():
                # frame_embeddings = model.get_image_features(**inputs) # Example HuggingFace
                # Using the mock_model's encode_vision for this example:
                frame_embeddings = model.encode_vision(processed_images) # Expects (B, C, H, W) tensor
            all_frame_embeddings.append(frame_embeddings.cpu().numpy())

        if not all_frame_embeddings:
            print(f"No frame embeddings generated for clip_id {clip_id}.")
            return None

        # Aggregate frame embeddings into a single clip embedding (e.g., mean pooling)
        clip_embedding_np = np.concatenate(all_frame_embeddings, axis=0)
        clip_embedding_np = np.mean(clip_embedding_np, axis=0)

        # L2 Normalize
        if clip_embedding_np.size > 0 : # Ensure not empty before norm
            norm = np.linalg.norm(clip_embedding_np)
            if norm > 0:
                clip_embedding_np = clip_embedding_np / norm

        print(f"Visual embedding extracted for clip_id {clip_id}. Shape: {clip_embedding_np.shape}")
        return clip_embedding_np

    except Exception as e:
        print(f"Error during visual embedding extraction for clip_id {clip_id}: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_text_embeddings_sentence_transformer(clip_id, transcript_text: str | None, model, description: str | None = None, keywords: str | None = None):
    """
    Extracts text embeddings from transcript text, description, and keywords using SentenceTransformer.
    """
    # Prioritize description, then keywords, then transcript.
    # Concatenate them with spaces, ensuring None or empty strings are handled.

    parts = []
    if description and description.strip():
        parts.append(description.strip())

    if keywords and keywords.strip():
        processed_keywords = ""
        try:
            # Attempt to parse keywords as a JSON list
            keyword_list = json.loads(keywords)
            if isinstance(keyword_list, list):
                # If it's a list, join its elements (converted to string and stripped) with spaces
                processed_keywords = " ".join(str(kw).strip() for kw in keyword_list if str(kw).strip())
            else:
                # If it parsed to JSON but wasn't a list (e.g., a JSON string or number),
                # treat the original keywords string as plain text.
                processed_keywords = keywords.strip()
        except json.JSONDecodeError:
            # Not a valid JSON string, assume it's already a plain string (e.g., space-separated or single keyword)
            processed_keywords = keywords.strip()

        if processed_keywords: # Add to parts only if there's something to add
            parts.append(processed_keywords)

    if transcript_text and transcript_text.strip():
        # Optionally clean transcript before adding
        # cleaned_transcript = clean_transcript_for_embedding(transcript_text.strip())
        # parts.append(cleaned_transcript)
        parts.append(transcript_text.strip())

    combined_text_input = " ".join(parts).strip()

    if not combined_text_input:
        # Use a fallback placeholder text for clips with empty fields
        fallback_text = f"Video clip {clip_id} with no descriptive text, keywords, or transcript."
        print(f"No description, keywords, or transcript text for clip_id {clip_id}. Using fallback text: '{fallback_text}'")
        combined_text_input = fallback_text

    print(f"Extracting text embedding for clip_id {clip_id} using combined input (description, keywords, transcript). Input length: {len(combined_text_input)}")
    try:
        embedding = model.encode(combined_text_input, convert_to_numpy=True, show_progress_bar=False)
        # L2 Normalize
        if embedding.size > 0 : # Ensure not empty before norm
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = embedding / norm
        print(f"Text embedding extracted for clip_id {clip_id}. Shape: {embedding.shape}")
        return embedding
    except Exception as e:
        print(f"Error extracting text embedding for clip_id {clip_id}: {e}")
        import traceback
        traceback.print_exc()
        return None

# --- FAISS Index Building ---
def check_embedding_variance(embeddings_np, modality_name=""):
    """
    Checks if the embeddings have sufficient variance.
    Returns True if variance is acceptable, False otherwise.
    """
    if embeddings_np.shape[0] < 2:
        print(f"Not enough {modality_name} embeddings to check variance.")
        return True  # Can't check variance with just one embedding

    # Calculate standard deviation across all embeddings
    std_per_dim = np.std(embeddings_np, axis=0)
    mean_std = std_per_dim.mean()

    # Check if variance is too low
    if mean_std < 0.05:
        print(f"WARNING: {modality_name} embeddings have very low variance (mean std: {mean_std:.6f}).")
        print(f"This suggests the {modality_name} embeddings are too similar and may not be discriminative.")
        return False
    else:
        print(f"{modality_name} embeddings have acceptable variance (mean std: {mean_std:.6f}).")
        return True

def build_faiss_indexes(all_clip_data, force_rebuild=False):
    """
    Builds FAISS indexes for visual and text embeddings.
    `all_clip_data` is a list of dicts, each like:
        {'clip_id': int, 'visual_embedding': np.array, 'text_embedding': np.array}
    Saves indexes to disk.

    Args:
        all_clip_data: List of clip data with embeddings
        force_rebuild: If True, rebuild indexes from scratch. If False, try incremental update.
    """
    print("Building FAISS indexes...")

    # Get all clips with complete embeddings from database
    all_clips_with_embeddings = metadata_db.get_all_clips_for_indexing(force=True)
    all_clips_with_embeddings = [clip for clip in all_clips_with_embeddings
                                if clip.get('visual_embedding_path') and clip.get('text_embedding_path')]

    print(f"Found {len(all_clips_with_embeddings)} clips with complete embeddings in database")

    # Load all embeddings from disk
    visual_embeddings_list = []
    text_embeddings_list = []
    clip_ids_list = []

    for clip_data in all_clips_with_embeddings:
        clip_id = clip_data['clip_id']
        visual_embedding_path = clip_data.get('visual_embedding_path')
        text_embedding_path = clip_data.get('text_embedding_path')

        try:
            if visual_embedding_path and text_embedding_path:
                if os.path.exists(visual_embedding_path) and os.path.exists(text_embedding_path):
                    visual_embedding = np.load(visual_embedding_path)
                    text_embedding = np.load(text_embedding_path)

                    visual_embeddings_list.append(visual_embedding)
                    text_embeddings_list.append(text_embedding)
                    clip_ids_list.append(clip_id)
                else:
                    print(f"Warning: Embedding files not found for clip_id {clip_id}")
        except Exception as e:
            print(f"Warning: Failed to load embeddings for clip_id {clip_id}: {e}")

    if not clip_ids_list:
        print("No valid embeddings found to build FAISS indexes.")
        return

    print(f"Successfully loaded embeddings for {len(clip_ids_list)} clips")
    clip_ids_np = np.array(clip_ids_list, dtype=np.int64)

    # Visual Index
    if visual_embeddings_list:
        visual_embeddings_np = np.stack(visual_embeddings_list).astype('float32')
        if visual_embeddings_np.ndim == 2 and visual_embeddings_np.shape[0] > 0:
            # Check variance before normalization
            visual_variance_ok = check_embedding_variance(visual_embeddings_np, "Visual")

            d_visual = visual_embeddings_np.shape[1]
            faiss.normalize_L2(visual_embeddings_np)            # <-- normalise in‑place
            visual_index = faiss.IndexFlatIP(d_visual)          # inner‑product == cosine
            visual_index_final = faiss.IndexIDMap(visual_index) # Map FAISS index to actual clip_ids
            visual_index_final.add_with_ids(visual_embeddings_np, clip_ids_np)
            faiss.write_index(visual_index_final, video_config.VISUAL_FAISS_INDEX_PATH)
            print(f"Visual FAISS index built with {visual_index_final.ntotal} vectors and saved to {video_config.VISUAL_FAISS_INDEX_PATH}")

            if not visual_variance_ok:
                print("WARNING: Visual index was built but may have poor search performance due to low variance.")
        else:
            print(f"Visual embeddings have incorrect shape or are empty: {visual_embeddings_np.shape}. Expected 2D array with rows.")

    # Text Index
    if text_embeddings_list:
        text_embeddings_np = np.stack(text_embeddings_list).astype('float32')
        if text_embeddings_np.ndim == 2 and text_embeddings_np.shape[0] > 0:
            # Check variance before normalization
            text_variance_ok = check_embedding_variance(text_embeddings_np, "Text")

            d_text = text_embeddings_np.shape[1]
            faiss.normalize_L2(text_embeddings_np)
            text_index = faiss.IndexFlatIP(d_text)
            text_index_final = faiss.IndexIDMap(text_index)
            text_index_final.add_with_ids(text_embeddings_np, clip_ids_np)
            faiss.write_index(text_index_final, video_config.TRANSCRIPT_FAISS_INDEX_PATH)
            print(f"Text FAISS index built with {text_index_final.ntotal} vectors and saved to {video_config.TRANSCRIPT_FAISS_INDEX_PATH}")

            if not text_variance_ok:
                print("WARNING: Text index was built but may have poor search performance due to low variance.")
        else:
            print(f"Text embeddings have incorrect shape or are empty: {text_embeddings_np.shape}. Expected 2D array with rows.")

# --- Main Orchestration for Phase 1 ---
def process_video_library_for_indexing(video_library_path=video_config.CLIPS_DIR, force=False):
    """
    Orchestrates Phase 1: Preprocessing all video clips and building indexes.
    It processes clips found in `video_library_path` (typically `video_config.CLIPS_DIR` which includes Phase 0 outputs)
    and also any other short videos placed there manually.

    Args:
        video_library_path (str): Path to the directory containing video clips to process
        force (bool): If True, reprocess all clips and rebuild indexes, even for clips with existing embeddings
    """
    print(f"--- Starting Phase 1: Video Preprocessing & Index Building from {video_library_path} ---")

    # Load models once
    clip_model, clip_processor = load_clip4clip_model()
    # Load SentenceTransformer for building the text index (Option A)
    sentence_model = load_sentence_transformer_model()
    # Whisper model will be loaded on demand by get_whisper_model()

    if clip_model is None:
        print("Critical error: CLIP4Clip model failed to load. Aborting Phase 1.")
        return

    # Get clips from DB (either all clips if force=True, or only those needing embeddings if force=False)
    clips_to_process = metadata_db.get_all_clips_for_indexing(force=force)

    all_clip_embedding_data = []

    for clip_data_row in clips_to_process: # clip_data_row is a dict from metadata_db
        clip_id = clip_data_row['clip_id']
        clip_path = clip_data_row['clip_path']
        transcript_text = clip_data_row.get('transcript_text', '') # Default to empty string if missing

        # Get new description and keywords fields
        # get_all_clips_for_indexing in metadata_db should provide these as strings.
        # Keywords should already be a space-separated string from the DB.
        description = clip_data_row.get('description', '') # Default to empty string
        keywords = clip_data_row.get('keywords', '')       # Default to empty string

        print(f"\nProcessing clip_id: {clip_id}, path: {clip_path}")
        print(f"  Description: '{description[:100]}...'")
        print(f"  Keywords: '{keywords[:100]}...'")
        if transcript_text is not None:
            print(f"  Transcript Text: '{transcript_text[:100]}...'")
        else:
            print(f"  Transcript Text: None")

        # a. Video抽帧 (FFmpeg)
        clip_frames_dir = os.path.join(video_config.FRAMES_DIR, str(clip_id))
        frame_paths = [] # Initialize frame_paths
        if not os.path.exists(clip_path):
            print(f"Warning: Clip path {clip_path} not found. Skipping frame and audio extraction for clip_id {clip_id}.")
        else:
            frame_paths = extract_frames_ffmpeg(clip_path, str(clip_id), clip_frames_dir)

        # b. 音频提取 (FFmpeg) & c. Whisper转录 (if needed)
        if force or not transcript_text or looks_like_placeholder(transcript_text):
            print(f"Transcript missing or looks like placeholder for clip_id {clip_id}. Extracting audio and re-transcribing.")
            clip_audio_dir = os.path.join(video_config.AUDIO_CACHE_DIR, str(clip_id)) # Ensure audio_cache has subdirs per clip_id
            os.makedirs(clip_audio_dir, exist_ok=True)
            audio_path = None # Initialize audio_path
            if os.path.exists(clip_path): # Check again before audio extraction
                audio_path = extract_audio_ffmpeg(clip_path, str(clip_id), clip_audio_dir)
            else:
                print(f"Warning: Clip path {clip_path} still not found before audio extraction for clip_id {clip_id}.")

            if audio_path:
                # from .phase0_splitting import transcribe_video_whisper as phase0_transcriber # Removed
                # temp_transcript_text, _, _ = phase0_transcriber(audio_path) # Removed
                temp_transcript_text = retranscribe_clip_audio_whisper(audio_path, clip_id) # Use updated function
                if temp_transcript_text:
                    transcript_text = temp_transcript_text
                    # DB update is now handled within retranscribe_clip_audio_whisper
                    print(f"Transcript for clip_id {clip_id} updated via re-transcription.")
                else:
                    print(f"Failed to re-transcribe audio for clip_id {clip_id}")

        # d. CLIP4Clip 视觉嵌入提取
        visual_embedding = None
        # Visual embedding via CLIP4Clip
        if clip_model and frame_paths:
            # Sample a smaller number of evenly-spaced frames instead of using all frames
            max_frames = 12  # Limit to 12 frames to avoid diluting signal

            if len(frame_paths) > max_frames:
                # Select evenly spaced frames
                indices = np.linspace(0, len(frame_paths) - 1, max_frames, dtype=int)
                sampled_frame_paths = [frame_paths[i] for i in indices]
                print(f"Sampling {max_frames} evenly-spaced frames from {len(frame_paths)} total frames")
            else:
                sampled_frame_paths = frame_paths

            # Load images and compute variance to filter out static frames
            images = []
            frame_variances = []

            for frame_path in sampled_frame_paths:
                try:
                    # Load image
                    img = Image.open(frame_path).convert("RGB")
                    img_np = np.array(img)

                    # Compute variance in HSV space to better detect static frames
                    hsv_img = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)
                    variance = np.var(hsv_img)

                    # Only keep frames with sufficient variance (non-static frames)
                    if variance < 40:  # Threshold for static frames
                        print(f"Skipping static frame {frame_path} (variance: {variance:.2f})")
                        continue

                    images.append(img)
                    frame_variances.append(variance)
                except Exception as e:
                    print(f"Warning: Could not load frame {frame_path}: {e}")

            # If we have too few frames after filtering, use all frames
            if len(images) < 3 and len(sampled_frame_paths) > 3:
                print("Too few dynamic frames, using all frames instead")
                images = []
                for frame_path in sampled_frame_paths:
                    try:
                        img = Image.open(frame_path).convert("RGB")
                        images.append(img)
                    except Exception as e:
                        print(f"Warning: Could not load frame {frame_path}: {e}")

            if images:
                print(f"Using {len(images)} frames for visual embedding")
                inputs = clip_processor(images=images, return_tensors="pt", padding=True).to(video_config.DEVICE)
                with torch.no_grad():
                    embeddings = clip_model.get_image_features(**{"pixel_values": inputs.pixel_values})
                visual_embedding = embeddings.cpu().numpy().mean(axis=0)
                # Normalize
                norm = np.linalg.norm(visual_embedding)
                if norm > 0:
                    visual_embedding = visual_embedding / norm
            else:
                visual_embedding = None

        # e. SentenceTransformer 文本嵌入提取
        text_embedding = None
        if sentence_model:
            # We'll use extract_text_embeddings_sentence_transformer which handles empty transcripts
            text_embedding = extract_text_embeddings_sentence_transformer(
                clip_id,
                transcript_text,
                sentence_model,
                description=description, # Pass new field
                keywords=keywords        # Pass new field
            )
            if text_embedding is None:
                print(f"Failed to extract text embedding for clip_id {clip_id}")

        # Store embeddings (e.g., as .npy files) and update DB paths
        visual_embedding_path = None
        text_embedding_path = None

        # Store embeddings in a general embeddings directory, then per clip_id
        clip_specific_embeddings_dir = os.path.join(video_config.INDEXES_DIR, "embeddings_data", str(clip_id))
        os.makedirs(clip_specific_embeddings_dir, exist_ok=True)

        if visual_embedding is not None:
            visual_embedding_path = os.path.join(clip_specific_embeddings_dir, "visual_embedding.npy")
            np.save(visual_embedding_path, visual_embedding)
            print(f"Saved visual embedding to {visual_embedding_path}")

        if text_embedding is not None:
            text_embedding_path = os.path.join(clip_specific_embeddings_dir, "text_embedding.npy")
            np.save(text_embedding_path, text_embedding)
            print(f"Saved text embedding to {text_embedding_path}")

        if visual_embedding_path or text_embedding_path: # Update DB if any embedding was saved
            metadata_db.update_clip_embeddings(clip_id, visual_embedding_path, text_embedding_path)

        if visual_embedding is not None and text_embedding is not None:
            all_clip_embedding_data.append({
                'clip_id': clip_id,
                'visual_embedding': visual_embedding,
                'text_embedding': text_embedding
            })
        else:
            print(f"Skipping clip_id {clip_id} for FAISS index due to missing one or both embeddings.")

    # f. FAISS 索引构建
    # Always rebuild indexes to include all clips with embeddings, not just the newly processed ones
    print("Rebuilding FAISS indexes to include all clips with embeddings...")
    build_faiss_indexes(all_clip_embedding_data, force_rebuild=force)

    print("--- Phase 1: Video Preprocessing & Index Building Completed ---")


# Example usage (for testing this module directly)
if __name__ == '__main__':
    print("Running Phase 1 Indexing directly (for testing purposes)...")
    video_config.ensure_directories()
    metadata_db.create_tables() # Ensure tables exist

    # To test, you would need some clips in the CLIPS_DIR,
    # ideally processed by Phase 0 or placed manually AND registered in the DB.

    # Simplified test: Assume DB has some clips to process
    # First, maybe add a dummy clip record if DB is empty, for testing get_all_clips_for_indexing
    conn = metadata_db.get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM video_clips")
        count_result = cursor.fetchone()
        count = count_result[0] if count_result else 0
        if count == 0:
            print("No clips in DB. Manually add some clips and their records or run Phase 0 first.")
            # Example of adding a dummy record if a dummy clip exists (e.g., from phase0 test)
            # Adjust path to where your sample clips might actually be after phase0 run
            dummy_long_video_name = "sample_long_video" # From phase0 example
            dummy_segments_folder = os.path.join(video_config.CLIPS_DIR, f"{dummy_long_video_name}_segments")
            dummy_clip_for_indexing_path = os.path.join(dummy_segments_folder, f"{dummy_long_video_name}_clip_0000.mp4")

            if os.path.exists(dummy_clip_for_indexing_path):
                # Check if corresponding long video exists to get an ID, or use None
                long_video_id = metadata_db.get_long_video_id(os.path.join(video_config.LONG_VIDEOS_DIR, f"{dummy_long_video_name}.mp4"))

                metadata_db.add_video_clip(
                    clip_path=dummy_clip_for_indexing_path,
                    original_long_video_id=long_video_id,
                    duration_seconds=utils.get_video_duration(dummy_clip_for_indexing_path) or 5.0,
                    transcript_text="This is a test transcript for a dummy clip for phase 1 testing.",
                    description="A dummy clip for testing phase 1.", # Added for test
                    keywords="dummy test phase1"                     # Added for test (as string)
                )
                print(f"Added dummy clip record for {dummy_clip_for_indexing_path} to DB for testing Phase 1.")
            else:
                print(f"Dummy clip {dummy_clip_for_indexing_path} not found. Cannot add dummy record for Phase 1 test. Run Phase 0 test first or provide clips.")
    finally:
        conn.close()

    # Now run the indexing process
    # For testing, you can set force=True to reprocess all clips
    process_video_library_for_indexing(force=False)