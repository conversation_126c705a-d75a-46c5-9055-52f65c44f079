#!/usr/bin/env python3
"""
Imports manually downloaded videos and their associated .tags.txt files.
Files are expected to be in the directory specified by video_config.MANUAL_IMPORT_DIR.
"""
import os
import json
from pathlib import Path
import shutil # For moving files if needed

import video_config
import downloader_common as common # Reusing metadata saving and path generation
import metadata_db # For direct DB interaction if needed, or use common
import utils # For get_video_duration

SOURCE_NAME = "manual"

def import_manual_videos(manual_source_dir: str | Path | None = None):
    """
    Scans the manual import directory for .mp4 files and their corresponding .tags.txt files.
    Imports them into the system, saving metadata to the database and moving/organizing files.
    """
    if manual_source_dir:
        source_dir = Path(manual_source_dir)
    else:
        source_dir = Path(video_config.MANUAL_IMPORT_DIR)

    if not source_dir.exists() or not source_dir.is_dir():
        print(f"Manual import directory not found or is not a directory: {source_dir}")
        return

    print(f"Scanning for manual imports in: {source_dir}")
    processed_count = 0
    failed_count = 0

    for video_file in source_dir.rglob('*.mp4'): # Recursively search for mp4 files
        print(f"\nFound video file: {video_file}")
        # Change from .tags.txt to .txt for description file
        description_file = video_file.with_suffix(".txt") 
        source_video_id = video_file.stem # Use filename stem as unique ID for manual files

        description_content_from_file: str | None = None # Initialize
        if description_file.exists():
            print(f"Found description file: {description_file}")
            try:
                description_content = description_file.read_text(encoding='utf-8').strip()
                if description_content: 
                    description_content_from_file = description_content
                    print(f'Read description: "{description_content_from_file}"') 
                else:
                    print(f"Description file {description_file} is empty after stripping whitespace.")
            except Exception as e: # Ensure try has an except
                print(f"Error reading description file {description_file}: {e}")
        else:
            print(f"No .txt description file found for {video_file}. Video will be imported without description from file.")

        # Determine final path in the LONG_VIDEOS_DIR
        # We use common.get_video_download_path to ensure a consistent naming scheme if desired,
        # or simply move it to a subdirectory within LONG_VIDEOS_DIR.
        # For manual files, the source_video_id is the stem, so the filename will be manual_<stem>.mp4
        final_video_path = common.get_video_download_path(SOURCE_NAME, source_video_id, video_file.suffix)
        final_video_path.parent.mkdir(parents=True, exist_ok=True)

        # Move the video file to the structured long_videos directory
        try:
            if video_file.resolve() != final_video_path.resolve(): # Avoid moving if already in place (e.g. re-run)
                # Ensure the source directory for shutil.move is not the same as destination parent
                if video_file.parent.resolve() != final_video_path.parent.resolve():
                     shutil.move(str(video_file), str(final_video_path))
                     print(f"Moved video file to: {final_video_path}")
                elif video_file.name != final_video_path.name: # Same directory, different name
                    video_file.rename(final_video_path)
                    print(f"Renamed video file to: {final_video_path}")    
                else:
                    print(f"Video file {video_file} is already at the target path {final_video_path}.")
            else:
                 print(f"Video file {video_file} is already at the target path {final_video_path}.")
                 
            # Example: move tags file too, or delete it after processing
            if description_file.exists(): # This should be description_file now
                # Example: move tags file next to the new video path with a consistent name
                final_description_path = final_video_path.with_suffix(".txt") # Changed suffix to .txt
                if description_file.resolve() != final_description_path.resolve():
                    shutil.move(str(description_file), str(final_description_path))
                    print(f"Moved description file to: {final_description_path}")
                # Or delete: # description_file.unlink()

        except Exception as e:
            print(f"Error moving video/tags file for {video_file.stem}: {e}")
            failed_count += 1
            continue # Skip metadata saving if file operation failed

        # Save metadata to DB
        # For manual files, width/height/duration might need to be extracted if not in tags/metadata
        # The save_video_metadata function uses utils.get_video_duration if duration_override is None.
        # Width/height are not automatically extracted by save_video_metadata yet.
        # We can try to get them here if needed.
        video_duration = utils.get_video_duration(str(final_video_path))
        # Width/Height extraction would require ffprobe or opencv, similar to get_video_duration
        # For now, passing None for width/height unless specified elsewhere.

        # Raw API data for manual imports can be a simple dict with the original filename and tags
        raw_meta = {
            "original_filename": str(video_file.name),
            "original_path": str(video_file),
            "imported_description": description_content_from_file if description_content_from_file else ""
        }

        db_id = common.save_video_metadata(
            source_name=SOURCE_NAME,
            source_video_id=source_video_id, # Original filename stem
            video_path=final_video_path,
            description=description_content_from_file, # Corrected
            keywords=None,                             # Corrected, manual imports don't have separate keywords by default
            raw_api_data=raw_meta,
            width=None, # Add width extraction if needed
            height=None, # Add height extraction if needed
            duration_override=video_duration
        )
        if db_id:
            processed_count += 1
            print(f"Successfully imported and registered manual video: {final_video_path} with DB ID {db_id}")
        else:
            failed_count +=1
            print(f"Failed to save metadata for manual video: {final_video_path}")
            # Consider moving the file back or to an error directory if DB save fails
            # For simplicity, current common.save_video_metadata handles DB errors internally.

    print(f"\n--- Manual Import Scan Complete ---")
    print(f"Successfully processed: {processed_count} videos.")
    print(f"Failed to process: {failed_count} videos.")


if __name__ == '__main__':
    print("Running Manual Video Importer directly (for testing purposes)...")
    # Ensure config directories and DB are set up
    video_config.ensure_directories() 
    metadata_db.create_tables()

    # Create dummy files for testing in the configured MANUAL_IMPORT_DIR
    manual_dir_path = Path(video_config.MANUAL_IMPORT_DIR)
    manual_dir_path.mkdir(parents=True, exist_ok=True)
    
    # Create a subfolder for testing recursive search
    sub_manual_dir = manual_dir_path / "test_subdir"
    sub_manual_dir.mkdir(parents=True, exist_ok=True)

    # Dummy video 1 (in root of manual_dir)
    dummy_vid1_path = manual_dir_path / "my_manual_video_01.mp4"
    # dummy_tags1_path = manual_dir_path / "my_manual_video_01.tags.txt"
    dummy_desc1_path = manual_dir_path / "my_manual_video_01.txt" # Changed to .txt
    if not dummy_vid1_path.exists():
        # Create a tiny dummy mp4 file if ffmpeg is available
        # This is a simplified way to create a file; real videos should be used.
        try:
            utils.run_ffmpeg_command(["ffmpeg", "-y", "-f", "lavfi", "-i", "color=c=blue:s=10x10:d=1", "-t", "1", str(dummy_vid1_path)])
            print(f"Created dummy video: {dummy_vid1_path}")
        except Exception as e:
            print(f"Could not create dummy video {dummy_vid1_path} for test (ffmpeg might be missing or error): {e}")
            dummy_vid1_path.write_text("dummy video content") # Fallback to simple text file

    if not dummy_desc1_path.exists(): # Changed from dummy_tags1_path
        # dummy_tags1_path.write_text("holiday, beach, summer\nmanual tag, test video")
        dummy_desc1_path.write_text("A beautiful holiday at the beach during summer, this is a manual test video.") # Example sentence
        print(f"Created dummy description file: {dummy_desc1_path}")

    # Dummy video 2 (in subfolder)
    dummy_vid2_path = sub_manual_dir / "another_clip_xyz.mp4"
    # dummy_tags2_path = sub_manual_dir / "another_clip_xyz.tags.txt"
    dummy_desc2_path = sub_manual_dir / "another_clip_xyz.txt" # Changed to .txt
    if not dummy_vid2_path.exists():
        try:
            utils.run_ffmpeg_command(["ffmpeg", "-y", "-f", "lavfi", "-i", "color=c=green:s=10x10:d=1", "-t", "1", str(dummy_vid2_path)])
            print(f"Created dummy video: {dummy_vid2_path}")
        except Exception as e:
            print(f"Could not create dummy video {dummy_vid2_path} for test: {e}")
            dummy_vid2_path.write_text("dummy video content 2") # Fallback
            
    if not dummy_desc2_path.exists(): # Changed from dummy_tags2_path
        # dummy_tags2_path.write_text("conference, tech, business")
        dummy_desc2_path.write_text("A tech conference discussing business strategies.") # Example sentence
        print(f"Created dummy description file: {dummy_desc2_path}")
        
    # Dummy video 3 (no tags file)
    dummy_vid3_path = manual_dir_path / "video_no_tags.mp4"
    if not dummy_vid3_path.exists():
        try:
            utils.run_ffmpeg_command(["ffmpeg", "-y", "-f", "lavfi", "-i", "color=c=red:s=10x10:d=1", "-t", "1", str(dummy_vid3_path)])
            print(f"Created dummy video: {dummy_vid3_path}")
        except Exception as e:
            print(f"Could not create dummy video {dummy_vid3_path} for test: {e}")
            dummy_vid3_path.write_text("dummy video content 3") # Fallback

    import_manual_videos() # Call with default MANUA_IMPORT_DIR from config
    # import_manual_videos(manual_source_dir="/path/to/your/manual/videos") # Or specify a directory

    print("\nCheck the database and the '/data/long_videos/' directory for imported files.")
    print(f"Original manual files in '{manual_dir_path}' should have been moved or processed.") 