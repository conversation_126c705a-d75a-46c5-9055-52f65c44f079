#!/usr/bin/env python3
"""
Script to register existing video clips in the database.
This is useful when you have clips in the data/clips directory
but they haven't been registered in the database.
"""

import os
import argparse
import glob
from pathlib import Path

import config
import metadata_db
import utils

def find_all_video_files(directory):
    """Find all video files in a directory and its subdirectories."""
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
    video_files = []
    
    for ext in video_extensions:
        # Use glob to find all files with the given extension in the directory and subdirectories
        pattern = os.path.join(directory, '**', f'*{ext}')
        video_files.extend(glob.glob(pattern, recursive=True))
    
    return video_files

def extract_transcript_from_filename(filename):
    """
    Extract a simple transcript from the filename.
    This is a fallback when no transcript is available.
    """
    # Remove extension and path
    base_name = os.path.basename(filename)
    name_without_ext = os.path.splitext(base_name)[0]
    
    # Replace underscores and hyphens with spaces
    transcript = name_without_ext.replace('_', ' ').replace('-', ' ')
    
    return transcript

def register_clip(clip_path, force=False):
    """Register a clip in the database if it's not already registered."""
    # Check if clip is already in the database
    conn = metadata_db.get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT clip_id FROM video_clips WHERE clip_path = ?", (clip_path,))
    existing_clip = cursor.fetchone()
    conn.close()
    
    if existing_clip and not force:
        print(f"Clip already registered: {clip_path}")
        return False
    
    # Get video duration
    duration = utils.get_video_duration(clip_path)
    if not duration:
        print(f"Could not determine duration for {clip_path}. Skipping.")
        return False
    
    # Extract a simple transcript from the filename as a fallback
    transcript = extract_transcript_from_filename(clip_path)
    
    # Register the clip
    try:
        clip_id = metadata_db.add_video_clip(
            clip_path=clip_path,
            original_long_video_id=None,  # These are standalone clips
            duration_seconds=duration,
            transcript_text=transcript
        )
        print(f"Registered clip: {clip_path} with ID {clip_id}")
        return True
    except Exception as e:
        print(f"Error registering clip {clip_path}: {e}")
        return False

def register_all_clips_in_directory(directory, force=False):
    """Register all video clips in a directory and its subdirectories."""
    # Ensure the database tables exist
    metadata_db.create_tables()
    
    # Find all video files
    video_files = find_all_video_files(directory)
    print(f"Found {len(video_files)} video files in {directory}")
    
    # Register each clip
    registered_count = 0
    for video_file in video_files:
        if register_clip(video_file, force):
            registered_count += 1
    
    print(f"Registered {registered_count} new clips out of {len(video_files)} found.")
    return registered_count

def main():
    parser = argparse.ArgumentParser(description="Register existing video clips in the database.")
    parser.add_argument("--directory", default=config.CLIPS_DIR,
                        help=f"Directory containing video clips (default: {config.CLIPS_DIR})")
    parser.add_argument("--force", action="store_true",
                        help="Force re-registration of clips even if they already exist in the database")
    
    args = parser.parse_args()
    
    print(f"Registering clips from {args.directory}")
    register_all_clips_in_directory(args.directory, args.force)

if __name__ == "__main__":
    main()
