import time
import asyncio
import threading

def create_async_token_bucket(capacity: int, period: int):
    """Factory function to create and initialize an async token bucket's state."""
    return {
        "capacity": float(capacity),
        "period": float(period),
        "tokens": float(capacity),
        "last_updated_time": time.monotonic(),
        "lock": asyncio.Lock()
    }

async def acquire_async_token(bucket_state: dict, num_tokens: int = 1):
    """Acquires token(s) from an async token bucket, updating its state."""
    if num_tokens <= 0:
        raise ValueError("Number of tokens to acquire must be positive")

    async with bucket_state["lock"]:
        now = time.monotonic()
        elapsed_time = now - bucket_state["last_updated_time"]
        bucket_state["last_updated_time"] = now

        bucket_state["tokens"] += elapsed_time * (bucket_state["capacity"] / bucket_state["period"])
        bucket_state["tokens"] = min(bucket_state["tokens"], bucket_state["capacity"])

        if bucket_state["tokens"] < num_tokens:
            time_to_wait = (num_tokens - bucket_state["tokens"]) * (bucket_state["period"] / bucket_state["capacity"])
            await asyncio.sleep(time_to_wait)
            # After waiting, tokens would have been generated, update self.tokens before consuming
            # It's important to recalculate tokens generated during sleep based on the actual time waited
            actual_slept_time = time.monotonic() - now # More accurate measure of time passed
            bucket_state["tokens"] += actual_slept_time * (bucket_state["capacity"] / bucket_state["period"])
            bucket_state["tokens"] = min(bucket_state["tokens"], bucket_state["capacity"])


        if bucket_state["tokens"] >= num_tokens:
            bucket_state["tokens"] -= num_tokens
            return True
        else:
            # This path might be hit if multiple coroutines were waiting and one consumed tokens
            # just before this one could, or if precision issues with sleep/timing occur.
            # A loop with retries or a more complex signaling mechanism (like asyncio.Condition)
            # could make this more robust for high contention scenarios.
            # For simplicity, returning False. The caller might need to retry.
            return False


def create_sync_token_bucket(capacity: int, period: int):
    """Factory function to create and initialize a sync token bucket's state."""
    return {
        "capacity": float(capacity),
        "period": float(period),
        "tokens": float(capacity),
        "last_updated_time": time.monotonic(),
        "lock": threading.Lock()
    }

def acquire_sync_token(bucket_state: dict, num_tokens: int = 1):
    """Acquires token(s) from a sync token bucket, updating its state."""
    if num_tokens <= 0:
        raise ValueError("Number of tokens to acquire must be positive")

    with bucket_state["lock"]:
        now = time.monotonic()
        elapsed_time = now - bucket_state["last_updated_time"]
        bucket_state["last_updated_time"] = now

        bucket_state["tokens"] += elapsed_time * (bucket_state["capacity"] / bucket_state["period"])
        bucket_state["tokens"] = min(bucket_state["tokens"], bucket_state["capacity"])

        if bucket_state["tokens"] < num_tokens:
            time_to_wait = (num_tokens - bucket_state["tokens"]) * (bucket_state["period"] / bucket_state["capacity"])
            time.sleep(time_to_wait)
            actual_slept_time = time.monotonic() - now
            bucket_state["tokens"] += actual_slept_time * (bucket_state["capacity"] / bucket_state["period"])
            bucket_state["tokens"] = min(bucket_state["tokens"], bucket_state["capacity"])

        if bucket_state["tokens"] >= num_tokens:
            bucket_state["tokens"] -= num_tokens
            return True
        else:
            return False

# Example usage:
async def main_async():
    # Pexels: 200 requests per hour (3600 seconds)
    # pexels_bucket_state = create_async_token_bucket(capacity=200, period=3600)
    pexels_bucket_state = create_async_token_bucket(capacity=5, period=10) # Smaller capacity for testing


    async def pexels_task(task_id):
        print(f"Pexels Task {task_id}: Attempting to acquire token...")
        if await acquire_async_token(pexels_bucket_state):
            print(f"Pexels Task {task_id}: Token acquired! Processing...")
            await asyncio.sleep(0.1) # Simulate work
            print(f"Pexels Task {task_id}: Processing done.")
        else:
            print(f"Pexels Task {task_id}: Failed to acquire token.")


    tasks = [pexels_task(i) for i in range(10)]
    await asyncio.gather(*tasks)

def main_sync():
    # Pixabay: 100 requests per 60 seconds
    # pixabay_bucket_state = create_sync_token_bucket(capacity=100, period=60)
    pixabay_bucket_state = create_sync_token_bucket(capacity=3, period=5) # Smaller capacity for testing

    def pixabay_task(task_id):
        print(f"Pixabay Task {task_id}: Attempting to acquire token...")
        if acquire_sync_token(pixabay_bucket_state):
            print(f"Pixabay Task {task_id}: Token acquired! Processing...")
            time.sleep(0.1) # Simulate work
            print(f"Pixabay Task {task_id}: Processing done.")
        else:
            print(f"Pixabay Task {task_id}: Failed to acquire token.")


    threads = []
    for i in range(6):
        thread = threading.Thread(target=pixabay_task, args=(i,))
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

if __name__ == '__main__':
    print("Testing Async Token Bucket (Functional Approach)...")
    asyncio.run(main_async())
    print("\nTesting Sync Token Bucket (Functional Approach)...")
    main_sync() 