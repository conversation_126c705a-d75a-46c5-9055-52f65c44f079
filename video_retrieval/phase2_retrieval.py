import numpy as np
import faiss
import torch
import os

# 使用从项目根目录开始的绝对导入路径
from video_retrieval import video_config
from video_retrieval import metadata_db
# 假设 phase1_indexing 也是 video_retrieval 包内的模块
from video_retrieval.phase1_indexing import load_clip4clip_model, load_sentence_transformer_model

# --- Load Resources for Retrieval ---
def load_resources_for_retrieval():
    """
    Loads all necessary resources for the retrieval phase:
    - FAISS indexes (visual and text)
    - InternVideo2-CLIP model (text encoder part)
    - SentenceTransformer model
    - Metadata database connection (though typically used per query)
    Returns a dictionary of loaded resources.
    """
    print("Loading resources for retrieval...")
    resources = {}

    # Set environment variables for safer model loading
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["OBJC_DISABLE_INITIALIZE_FORK_SAFETY"] = "YES"
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["OMP_NUM_THREADS"] = "1"
    os.environ["OPENBLAS_NUM_THREADS"] = "1"
    os.environ["MKL_NUM_THREADS"] = "1"

    # Load FAISS indexes with better error handling
    try:
        if os.path.exists(video_config.VISUAL_FAISS_INDEX_PATH):
            # Set FAISS to use a single thread for safety
            faiss.omp_set_num_threads(1)
            resources['visual_index'] = faiss.read_index(video_config.VISUAL_FAISS_INDEX_PATH)
            print(f"Visual FAISS index loaded. Total vectors: {resources['visual_index'].ntotal}")
        else:
            print(f"Visual FAISS index not found at {video_config.VISUAL_FAISS_INDEX_PATH}")
            resources['visual_index'] = None
    except Exception as e:
        print(f"Error loading visual FAISS index: {e}")
        import traceback
        traceback.print_exc()
        resources['visual_index'] = None

    try:
        if os.path.exists(video_config.TRANSCRIPT_FAISS_INDEX_PATH):
            # Set FAISS to use a single thread for safety
            faiss.omp_set_num_threads(1)
            resources['text_index'] = faiss.read_index(video_config.TRANSCRIPT_FAISS_INDEX_PATH)
            print(f"Text FAISS index loaded. Total vectors: {resources['text_index'].ntotal}")
        else:
            print(f"Text FAISS index not found at {video_config.TRANSCRIPT_FAISS_INDEX_PATH}")
            resources['text_index'] = None
    except Exception as e:
        print(f"Error loading text FAISS index: {e}")
        import traceback
        traceback.print_exc()
        resources['text_index'] = None

    # Load models with better error handling and fallbacks

    # First try to load InternVideo2-CLIP model
    try:
        print("Loading CLIP4Clip model from Searchium-ai/clip4clip-webvid150k on cpu")
        clip_model, clip_processor = load_clip4clip_model()
        resources['intern_video_model'] = clip_model
        resources['clip_processor'] = clip_processor
    except Exception as e:
        print(f"Error loading CLIP4Clip model: {e}")
        import traceback
        traceback.print_exc()
        resources['intern_video_model'] = None
        resources['clip_processor'] = None

    # Then try to load SentenceTransformer model with fallback
    try:
        print(f"Loading SentenceTransformer model: {video_config.TEXT_EMBEDDING_MODEL_FOR_INDEXING} on cpu")

        # Try primary model first
        try:
            from sentence_transformers import SentenceTransformer
            sentence_model = SentenceTransformer(video_config.TEXT_EMBEDDING_MODEL_FOR_INDEXING, device="cpu")
            resources['sentence_model'] = sentence_model
            print("SentenceTransformer model loaded successfully.")
        except Exception as primary_error:
            print(f"Error loading primary SentenceTransformer model: {primary_error}")

            # Try fallback model
            try:
                print("Attempting to load fallback SentenceTransformer model...")
                fallback_model_name = "all-MiniLM-L6-v2"  # Much smaller model
                sentence_model = SentenceTransformer(fallback_model_name, device="cpu")
                resources['sentence_model'] = sentence_model
                print(f"Fallback SentenceTransformer model {fallback_model_name} loaded successfully.")
            except Exception as fallback_error:
                print(f"Error loading fallback SentenceTransformer model: {fallback_error}")
                resources['sentence_model'] = None
    except Exception as e:
        print(f"Error in SentenceTransformer loading process: {e}")
        import traceback
        traceback.print_exc()
        resources['sentence_model'] = None

    # Check if critical resources are available
    if resources['intern_video_model'] is None and resources['sentence_model'] is None:
        print("WARNING: All query encoding models failed to load. Retrieval will not work.")
    elif resources['intern_video_model'] is None:
        print("Warning: CLIP4Clip model failed to load. Using SentenceTransformer only.")
    elif resources['sentence_model'] is None:
        print("Warning: SentenceTransformer model failed to load. Using CLIP4Clip only.")

    if resources['visual_index'] is None and resources['text_index'] is None:
        print("WARNING: Both FAISS indexes failed to load. Retrieval will not work.")

    print("Retrieval resources loaded.")
    return resources

# --- Query Processing & Encoding ---
def encode_query_text(user_query_text, intern_video_model, sentence_model, clip_processor, device=video_config.DEVICE):
    """
    Encodes the user query text using both CLIP4Clip text encoder
    and the SentenceTransformer model.
    Returns two embeddings (L2 normalized).
    """
    query_visual_embedding_np = None
    query_transcript_embedding_np = None

    # SentenceTransformer text encoding first (more reliable)
    if sentence_model:
        try:
            print(f"Encoding query with SentenceTransformer: '{user_query_text}'")

            # Set memory management environment variables
            import os
            os.environ["TOKENIZERS_PARALLELISM"] = "false"
            os.environ["OBJC_DISABLE_INITIALIZE_FORK_SAFETY"] = "YES"
            # Add PyTorch memory management settings
            os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

            # Use a safer encoding approach with batching and explicit device control
            import torch
            with torch.no_grad():  # Disable gradient tracking for inference
                # Split long text into smaller chunks if needed
                max_length = 512
                if len(user_query_text) > max_length:
                    chunks = [user_query_text[i:i+max_length] for i in range(0, len(user_query_text), max_length)]
                    embeddings = []
                    for chunk in chunks:
                        chunk_embedding = sentence_model.encode(
                            chunk,
                            convert_to_numpy=True,
                            show_progress_bar=False,
                            batch_size=1  # Process one at a time
                        )
                        embeddings.append(chunk_embedding)
                    # Average the embeddings from all chunks
                    query_transcript_embedding = np.mean(embeddings, axis=0)
                else:
                    # For shorter text, encode directly
                    query_transcript_embedding = sentence_model.encode(
                        user_query_text,
                        convert_to_numpy=True,
                        show_progress_bar=False,
                        batch_size=1  # Process one at a time
                    )

            query_transcript_embedding_np = query_transcript_embedding.reshape(1, -1) # Ensure 2D for FAISS

            # L2 Normalize
            norm = np.linalg.norm(query_transcript_embedding_np)
            if norm > 0:
                query_transcript_embedding_np = query_transcript_embedding_np / norm
            print("Query encoded with SentenceTransformer.")
        except Exception as e:
            print(f"Error encoding query with SentenceTransformer: {e}")
            import traceback
            traceback.print_exc()

            # Fallback to a simpler encoding method if the main one fails
            try:
                print("Attempting fallback encoding method...")
                # Try with a simpler model if available
                from sentence_transformers import SentenceTransformer
                fallback_model_name = "all-MiniLM-L6-v2"  # Much smaller model
                fallback_model = SentenceTransformer(fallback_model_name, device="cpu")

                query_transcript_embedding = fallback_model.encode(
                    user_query_text,
                    convert_to_numpy=True,
                    show_progress_bar=False,
                    batch_size=1
                )
                query_transcript_embedding_np = query_transcript_embedding.reshape(1, -1)

                # L2 Normalize
                norm = np.linalg.norm(query_transcript_embedding_np)
                if norm > 0:
                    query_transcript_embedding_np = query_transcript_embedding_np / norm
                print(f"Successfully used fallback model {fallback_model_name} for encoding.")
            except Exception as fallback_error:
                print(f"Fallback encoding also failed: {fallback_error}")
                query_transcript_embedding_np = None

    # CLIP4Clip text encoding (more prone to segfaults on macOS)
    if intern_video_model and clip_processor:
        try:
            print(f"Encoding query with CLIP4Clip: '{user_query_text}'")

            # Explicitly move model to CPU to avoid potential CUDA issues
            intern_video_model = intern_video_model.to(device)

            # Process inputs on CPU first
            inputs = clip_processor(text=[user_query_text], return_tensors="pt", padding=True)

            # Move inputs to device after processing
            for key in inputs:
                if torch.is_tensor(inputs[key]):
                    inputs[key] = inputs[key].to(device)

            # Use a separate try block for the actual inference
            try:
                with torch.no_grad():
                    text_features = intern_video_model.get_text_features(**inputs)
                query_visual_embedding_np = text_features.cpu().numpy()  # shape (1, dim)
                # L2 Normalize
                norm = np.linalg.norm(query_visual_embedding_np)
                if norm > 0:
                    query_visual_embedding_np = query_visual_embedding_np / norm
                print("Query encoded with CLIP4Clip.")
            except Exception as inner_e:
                print(f"Error during CLIP4Clip inference: {inner_e}")
                import traceback
                traceback.print_exc()
                query_visual_embedding_np = None

                # If we have a text embedding from SentenceTransformer, use it as a fallback for visual embedding
                if query_transcript_embedding_np is not None:
                    print("Using SentenceTransformer embedding as fallback for visual embedding")
                    query_visual_embedding_np = query_transcript_embedding_np.copy()

        except Exception as e:
            print(f"Error encoding query with CLIP4Clip: {e}")
            import traceback
            traceback.print_exc()

            # If we have a text embedding from SentenceTransformer, use it as a fallback for visual embedding
            if query_transcript_embedding_np is not None:
                print("Using SentenceTransformer embedding as fallback for visual embedding")
                query_visual_embedding_np = query_transcript_embedding_np.copy()

    return query_visual_embedding_np, query_transcript_embedding_np

# --- FAISS Search ---
def search_in_faiss_index(query_embedding_np, index, top_k=video_config.TOP_K_RESULTS):
    """
    Performs a search in the given FAISS index.
    Returns distances and IDs (clip_ids) of the top_k results.
    """
    if index is None or query_embedding_np is None or index.ntotal == 0:
        print("FAISS index is not available, query embedding is missing, or index is empty. Skipping search.")
        return np.array([]), np.array([])

    # Dimension safety: query width must equal index.d
    if index is not None and query_embedding_np is not None:
        if query_embedding_np.shape[1] != index.d:
            print(f"Dimension mismatch: query dim {query_embedding_np.shape[1]} vs index dim {index.d}. Skipping this search.")
            return np.array([]), np.array([])

    try:
        # Ensure query is L2-normalized (required for cosine/IP search)
        print(f"Normalizing query embedding with shape {query_embedding_np.shape}")
        faiss.normalize_L2(query_embedding_np)

        # FAISS expects float32
        print(f"Converting query embedding to float32")
        query_embedding_float32 = query_embedding_np.astype('float32')

        # Add more debug info
        print(f"Performing FAISS search with k={top_k}")
        print(f"Index type: {type(index)}")
        print(f"Index contains {index.ntotal} vectors of dimension {index.d}")

        # Make sure we're using CPU
        faiss.omp_set_num_threads(1)  # Use a single thread to avoid potential issues

        # Use a try-except block specifically for the search operation
        try:
            distances, ids = index.search(query_embedding_float32, k=top_k)
            print(f"Search successful. Found {len(ids.flatten())} results.")
            # ids from IndexIDMap are the original clip_ids
            return distances.flatten(), ids.flatten()
        except Exception as search_error:
            print(f"Error during index.search operation: {search_error}")
            print(f"Query embedding stats: min={query_embedding_float32.min()}, max={query_embedding_float32.max()}, mean={query_embedding_float32.mean()}")
            # Return empty arrays as fallback
            return np.array([]), np.array([])
    except Exception as e:
        print(f"Error during FAISS search preparation: {e}")
        import traceback
        traceback.print_exc()
        return np.array([]), np.array([])

# --- Result Fusion & Ranking ---
def fuse_and_rank_results(vis_ids, vis_dists, txt_ids, txt_dists, w_vis=video_config.FUSION_WEIGHT_VISUAL, w_txt=video_config.FUSION_WEIGHT_TEXT):
    """
    Fuses results from visual and text search and re-ranks them.
    Distances are L2 distances; similarity is 1 / (1 + distance).
    Returns a sorted list of (clip_id, combined_score) tuples.
    """
    print("Fusing and ranking search results...")
    combined_scores = {}

    # Debug: Print the IDs from both searches
    print(f"DEBUG: Visual search IDs: {vis_ids.flatten().tolist()}")
    print(f"DEBUG: Text search IDs: {txt_ids.flatten().tolist()}")

    # Process visual results
    for clip_id, dist in zip(vis_ids.flatten(), vis_dists.flatten()):
        if clip_id == -1: continue # FAISS can return -1 if fewer than k results found
        similarity = 1.0 / (1.0 + float(dist)) # Convert L2 distance to similarity
        if clip_id not in combined_scores:
            combined_scores[clip_id] = {'vis_sim': 0.0, 'txt_sim': 0.0, 'count':0}
        combined_scores[clip_id]['vis_sim'] = similarity
        combined_scores[clip_id]['count'] +=1

    # Process text results
    for clip_id, dist in zip(txt_ids.flatten(), txt_dists.flatten()):
        if clip_id == -1: continue
        similarity = 1.0 / (1.0 + float(dist))
        if clip_id not in combined_scores:
            combined_scores[clip_id] = {'vis_sim': 0.0, 'txt_sim': 0.0, 'count':0}
        combined_scores[clip_id]['txt_sim'] = similarity
        combined_scores[clip_id]['count'] +=1

    # Calculate final weighted score
    final_results = []
    for clip_id, scores in combined_scores.items():
        final_score = (w_vis * scores['vis_sim']) + (w_txt * scores['txt_sim'])
        # Optional: Boost score if found in both modalities
        if scores['count'] == 2:
            final_score *= 1.1 # Simple boost for results found in both modalities
        final_results.append((clip_id, final_score))

    # Sort by final score in descending order
    final_results.sort(key=lambda x: x[1], reverse=True)

    print(f"Fusion complete. {len(final_results)} unique clips after fusion.")
    return final_results

# --- Main Orchestration for Phase 2 ---
def retrieve_results_for_query(user_query_text, loaded_resources, top_n_to_display=video_config.TOP_K_RESULTS):
    """
    Orchestrates the entire retrieval process for a user query.
    `loaded_resources` is the dict returned by `load_resources_for_retrieval()`.
    Returns a list of dictionaries, each containing metadata for a retrieved clip.
    """
    print(f"--- Starting Phase 2: Retrieval for query: '{user_query_text}' ---")

    # Store loaded_resources as a global variable for use in other functions
    global _retrieval_resources
    _retrieval_resources = loaded_resources

    intern_video_model = loaded_resources.get('intern_video_model')
    sentence_model = loaded_resources.get('sentence_model')
    visual_index = loaded_resources.get('visual_index')
    text_index = loaded_resources.get('text_index')

    if not all([intern_video_model, sentence_model, visual_index, text_index]):
        print("Critical retrieval resources missing (models or indexes). Aborting search.")
        if visual_index is None or visual_index.ntotal == 0:
             print("Visual index is empty or not loaded.")
        if text_index is None or text_index.ntotal == 0:
             print("Text index is empty or not loaded.")
        return []

    # 1. Encode Query Text
    clip_processor = loaded_resources.get('clip_processor')
    query_vis_emb_np, query_txt_emb_np = encode_query_text(
        user_query_text, intern_video_model, sentence_model, clip_processor
    )

    # --- Optional debug guard: print shapes before search ---
    print("DEBUG  visual_query shape:", None if query_vis_emb_np is None else query_vis_emb_np.shape)
    print("DEBUG  visual_index.d:", visual_index.d if visual_index else "N/A")
    # (You can add similar lines for text query if desired)

    if query_vis_emb_np is None and query_txt_emb_np is None:
        print("Query encoding failed for both modalities. Cannot perform search.")
        return []

    # 2. FAISS Search
    # a. Visual Search
    vis_dists, vis_ids = search_in_faiss_index(query_vis_emb_np, visual_index, top_k=video_config.TOP_K_RESULTS)
    if len(vis_ids) > 0:
        print(f"Visual search found {len(vis_ids[vis_ids != -1])} potential clips.")
    else:
        print("Visual search returned empty results")

    # b. Text Search
    txt_dists, txt_ids = search_in_faiss_index(query_txt_emb_np, text_index, top_k=video_config.TOP_K_RESULTS)
    if len(txt_ids) > 0:
        print(f"Text search found {len(txt_ids[txt_ids != -1])} potential clips.")
    else:
        print("Text search returned empty results")

    # 3. Fuse and Rank Results
    if len(vis_ids) > 0 or len(txt_ids) > 0:
        ranked_results = fuse_and_rank_results(vis_ids, vis_dists, txt_ids, txt_dists)
    else:
        ranked_results = []

    if not ranked_results:
        print("No results found after fusion and ranking.")
        return []

    # Debug: Print the ranked results
    print(f"DEBUG: Ranked results: {ranked_results[:10]}")

    # 4. Prepare results for presentation
    top_n_clip_ids = [clip_id for clip_id, score in ranked_results[:top_n_to_display]]
    print(f"DEBUG: Top {top_n_to_display} clip IDs from FAISS: {top_n_clip_ids}")

    # FAISS IndexIDMap directly stores the actual clip_ids from the database
    # No mapping is needed - the clip_ids returned by FAISS are the real database clip_ids
    # This is guaranteed by the build_faiss_indexes function which uses add_with_ids()
    db_clip_ids = top_n_clip_ids

    print(f"DEBUG: Using FAISS clip IDs directly as database IDs: {db_clip_ids}")

    # Validate that these clip_ids exist in the database (for debugging)
    conn = metadata_db.get_db_connection()
    cursor = conn.cursor()

    # Convert to int to ensure proper data types
    db_clip_ids = [int(clip_id) for clip_id in db_clip_ids]

    placeholders = ','.join('?' for _ in db_clip_ids)
    print(f"DEBUG: Executing query with {len(db_clip_ids)} clip_ids: {db_clip_ids[:5]}...")
    print(f"DEBUG: Query: SELECT clip_id FROM video_clips WHERE clip_id IN ({placeholders})")

    try:
        cursor.execute(f"SELECT clip_id FROM video_clips WHERE clip_id IN ({placeholders})", db_clip_ids)
        existing_clip_ids = [row[0] for row in cursor.fetchall()]
        print(f"DEBUG: Query returned {len(existing_clip_ids)} results")
    except Exception as e:
        print(f"ERROR: Database query failed: {e}")
        existing_clip_ids = []
    finally:
        conn.close()

    if len(existing_clip_ids) != len(db_clip_ids):
        missing_ids = set(db_clip_ids) - set(existing_clip_ids)
        print(f"WARNING: Some FAISS clip_ids not found in database: {missing_ids}")
        # Filter out missing IDs to prevent errors
        db_clip_ids = existing_clip_ids
        print(f"DEBUG: Filtered clip IDs to existing ones: {db_clip_ids}")

    retrieved_clip_metadata = []
    if db_clip_ids:
        # Get metadata for these top N clip_ids from the database
        print(f"DEBUG: Retrieving metadata for clip IDs: {db_clip_ids}")
        retrieved_clip_metadata = metadata_db.get_clip_metadata_for_retrieval(db_clip_ids)
        print(f"DEBUG: Retrieved metadata count: {len(retrieved_clip_metadata)}")

        # Add scores to the metadata for display/sorting if needed
        scores_map = {clip_id: score for clip_id, score in ranked_results}
        for clip_meta in retrieved_clip_metadata:
            clip_meta['retrieval_score'] = scores_map.get(clip_meta['clip_id'])
            print(f"DEBUG: Clip metadata: {clip_meta['clip_id']}, score: {clip_meta.get('retrieval_score')}")

        # Sort again by score if the DB query doesn't preserve order or if joining changed it
        retrieved_clip_metadata.sort(key=lambda x: x.get('retrieval_score', 0), reverse=True)

    print(f"--- Retrieval Complete. Returning {len(retrieved_clip_metadata)} top results. ---")
    return retrieved_clip_metadata


# Example usage (for testing this module directly)
if __name__ == '__main__':
    import os # Required for path checks in load_resources
    print("Running Phase 2 Retrieval directly (for testing purposes)...")
    video_config.ensure_directories() # Ensure data dirs exist for indexes
    metadata_db.create_tables() # Ensure DB schema is present

    # This test assumes that Phase 1 has been run and FAISS indexes exist.
    # And that models can be loaded (even if mock versions).

    # Check if FAISS indexes exist to provide a better test message
    visual_index_exists = os.path.exists(video_config.VISUAL_FAISS_INDEX_PATH)
    text_index_exists = os.path.exists(video_config.TRANSCRIPT_FAISS_INDEX_PATH)

    if not (visual_index_exists and text_index_exists):
        print("Warning: FAISS index files are missing. Retrieval test might not yield results.")
        print(f"Checked for visual index at: {video_config.VISUAL_FAISS_INDEX_PATH} (Exists: {visual_index_exists})")
        print(f"Checked for text index at: {video_config.TRANSCRIPT_FAISS_INDEX_PATH} (Exists: {text_index_exists})")
        print("Please run Phase 1 (indexing) first if you expect actual search results.")

    # Load all retrieval resources once
    retrieval_resources = load_resources_for_retrieval()

    if not retrieval_resources.get('visual_index') and not retrieval_resources.get('text_index'):
        print("No FAISS indexes loaded. Cannot proceed with test query.")
    elif not retrieval_resources.get('intern_video_model') or not retrieval_resources.get('sentence_model'):
        print("Query models not loaded. Cannot proceed with test query.")
    else:
        sample_query = "a person talking about technology"
        print(f"\nPerforming test query: '{sample_query}'")

        results = retrieve_results_for_query(sample_query, retrieval_resources)

        if results:
            print("\nTop Retrieved Clips:")
            for idx, clip_info in enumerate(results):
                print(f"  {idx+1}. Clip ID: {clip_info['clip_id']}")
                print(f"     Path: {clip_info['clip_path']}")
                print(f"     Score: {clip_info.get('retrieval_score', 'N/A'):.4f}")
                # Display new fields: description and keywords
                print(f"     Description: {clip_info.get('description', '')[:150]}...")
                print(f"     Keywords: {clip_info.get('keywords', '')[:150]}...") # Keywords are space-separated string
                print(f"     Transcript Snippet: {clip_info.get('transcript_text', '')[:100]}...")
        else:
            print("No clips retrieved for the test query.")