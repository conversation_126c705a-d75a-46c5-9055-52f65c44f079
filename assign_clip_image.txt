# assign_clip_image.py 流程说明
# 媒体资源分配与生成工具

这个工具集用于为文本段落分配或生成媒体资源（图片或视频片段）。

## 功能概述

系统提供两种主要模式：

1. **媒体分配模式**：为文本段落分配已有的图片或视频片段资源（原有功能）
2. **图片生成模式**：为文本段落生成新的图片，而不是从现有资源中分配

## 文件结构

- `assign_clip_image.py` - 主程序，负责处理命令行参数并调用相应模块
- `image_generation.py` - 负责生成图片的模块
- `image_assignment.py` - 负责分配已有媒体资源的模块

## 使用方法

### 1. 分配现有媒体资源（原有功能）

```bash
python assign_clip_image.py --json <脚本文件路径> --image_dir <图片目录路径> [--clip_dir <视频片段目录>] [--theme <主题名称>] [--using_anchor]
```

### 2. 生成新的图片（新功能）

```bash
python assign_clip_image.py --json <脚本文件路径> --image_dir <图片输出目录路径> [--theme <主题名称>] [--using_anchor] --Generate_Image
```

## 参数说明

- `--json`：输入JSON文件路径，包含段落文本和音频信息
- `--image_dir`：图片目录路径（分配模式下为输入目录，生成模式下为输出目录）
- `--clip_dir`：视频片段目录路径（可选，仅在分配模式下使用）
- `--theme`：主题名称（可选，默认从JSON文件名提取）
- `--using_anchor`：是否使用anchor占位符作为锚点
- `--generate_image`：是否生成图片而不是分配现有图片（新参数）

## 输出

工具会在与输入JSON文件相同目录下生成一个新的JSON文件，文件名为原文件名加上`_result`后缀。

例如，如果输入文件是`example_audio.json`，输出文件将是`example_audio_result.json`。

## 注意事项

1. 图片生成模式需要确保ComfyUI服务器正在运行，默认地址为127.0.0.1:8188
2. 生成模式会调用GPT-4生成图像提示词，然后调用ComfyUI生成图片
3. 使用生成模式时，图片输出目录必须存在且有写入权限
4. 当以生成模式运行时，不需要预先准备图片资源库

## 单独使用模块

各模块也可以单独使用：

### 仅生成图片

```bash
python image_generation.py --json <脚本文件路径> --image_dir <图片输出目录路径> [--theme <主题名称>]
```

### 仅分配媒体资源

```bash
python image_assignment.py --json <脚本文件路径> --image_dir <图片目录路径> [--clip_dir <视频片段目录>] [--theme <主题名称>] [--using_anchor]
```

## 开发笔记

与之前版本相比，本次重构进行了以下改进：

1. 清晰分离了图片生成和图片分配的功能
2. 移除了对旧模块的依赖，提高了代码的独立性
3. 简化了主程序结构，使其更易于维护
4. 保持了原有的命令行参数格式，确保向后兼容性 



image assign_clip_image
## 主要功能
该脚本用于为文本段落分配适当的媒体资源（图片或视频片段），使用智能匹配算法确保媒体内容与文本语义相关。

## 流程概述

### 1. 输入处理
- 读取JSON文件中的脚本段落
- 检查并处理图片目录下的有效图片
- 可选地加载视频片段数据

### 2. 媒体资源处理
- 验证图片有效性
- 检查图片元数据完整性
- 更新属性JSON文件，确保只包含有效图片
- 为图片生成描述（如果缺失）

### 3. 相似度计算
- 使用嵌入模型计算文本段落和媒体资源之间的语义相似度
- 生成相似度矩阵用于优化分配
- 分别处理段落-图片相似度和段落-视频片段相似度

### 4. 媒体分配优化
分配策略基于"分配即完成"原则，即每个段落一旦分配了媒体资源就不再参与后续分配。整个过程按以下顺序执行：

1. **初始锚点设置**：如启用anchor模式，将第一段设为anchor类型

2. **第一轮图片分配（高优先级图片）**
   - 计算所有文本段落与可用图片之间的语义相似度
   - 选取相似度最高的N张图片（N为可调参数，默认10张）
   - 仅从这N张图片中为最匹配的段落分配图片
   - 每张图片仅使用一次，防止重复分配
   - 成功分配图片的段落标记为已分配，不再参与后续分配

3. **视频片段分配**
   - 计算剩余未分配段落与视频片段之间的语义相似度
   - 允许视频片段分配给多个连续相关的段落，保证内容连贯性
   - 成功分配视频的段落标记为已分配，不再参与后续分配

4. **第二轮图片分配（确保图片至少使用一次）**
   - 统计仍未分配媒体的文本段落
   - 计算这些段落与所有未使用图片之间的语义相似度（不设匹配阈值）
   - 确保所有未使用的图片至少被使用一次
   - 每张图片仍只能使用一次，分配后立即标记为已使用
   - 成功分配图片的段落标记为已分配，不再参与后续分配

5. **第三轮图片分配（允许重复使用）**
   - 统计仍未分配媒体的文本段落
   - 允许重复使用之前已分配过的图片，但遵循最小重用间距规则
   - 避免同一图片过度集中使用，保证视觉多样性
   - 成功分配图片的段落标记为已分配，不再参与后续分配

6. **最终锚点填充**
   - 将所有仍未分配媒体的段落设为anchor占位符
   - 确保所有文本段落都有合理的处理方式，不留空白

### 优化目标
1. 所有文本段落必须分配一个媒体（图片、视频或anchor），不允许遗漏
2. 优先分配高语义匹配度的N张图片，确保最关键的文本段落获得最佳匹配
3. 确保所有图片至少被使用一次，避免图片资源浪费
4. 避免图片重复使用过于频繁，保证视觉体验的多样性
5. 优化视频片段的连续分配，提高场景的连贯性

### 分配策略总结表
| 步骤 | 目标 | 策略 |
|------|------|------|
| 第一轮图片分配 | 确保高匹配度的N张图片优先分配 | 选取相似度最高的N张图片，仅从这N张中进行分配，每张图片仅用一次 |
| 视频片段分配 | 优先覆盖连贯文本段落 | 允许视频片段匹配多个连续段落 |
| 第二轮图片分配 | 确保所有图片至少使用一次 | 没有匹配阈值，所有剩余图片必须被使用，每张图片仍然只能使用一次 |
| 第三轮图片分配 | 覆盖所有剩余文本段落 | 允许图片重复使用，但需遵守最小重用间距 |
| 最终锚点填充 | 确保所有文本都被分配 | 无法匹配的文本设为anchor |

### 5. 整数线性规划(ILP)优化
- 使用Google OR-Tools求解器优化图片分配
- 考虑相似度、重复使用惩罚和未使用图片优先级等因素
- 确保满足图片最大使用次数和最小重用间距等约束

### 6. 后处理优化
- 检查分配结果，确保不违反重用限制
- 优化相似度，在符合约束条件下提高匹配质量
- 避免连续锚点，提高媒体分配的平衡性

### 7. 结果保存
- 输出媒体分配状态统计信息
- 将结果保存为新的JSON文件（原文件名+_result后缀）

## 主要参数说明
- **json_path**: 输入的脚本JSON文件路径
- **image_dir**: 图片目录路径
- **clip_dir**: 视频片段目录路径（可选）
- **theme**: 主题名称（若未指定则从文件名提取）
- **using_anchor**: 是否使用anchor占位符作为锚点

## 关键约束参数
- **min_anchor_fraction**: 最少anchor比例（默认2%）
- **max_anchor_fraction**: 最多anchor比例（默认5%）
- **MIN_DISTANCE_BETWEEN_REUSE**: 图片重复使用的最小间距（默认3段）
- **MAX_TOTAL_USES**: 每张图片的最大使用次数（根据段落/图片数动态计算）

## 执行流程示例
1. **解析参数**：处理命令行输入
2. **处理脚本**：读取JSON文件并解析段落
3. **处理媒体资源**：验证和准备图片及视频资源
4. **计算相似度**：生成文本与媒体资源的相似度矩阵
5. **分配媒体**：使用多阶段策略为段落分配适当媒体
6. **打印状态**：输出分配结果统计
7. **保存结果**：将处理后的结果保存到新文件

## 输出结果
最终输出的JSON文件包含原始脚本段落和新增的媒体分配信息，每个段落都会被分配以下类型的媒体之一：
- **图片**：关联到语义相关的图片
- **视频片段**：关联到语义相关的视频片段（含时间信息）
- **锚点(anchor)**：无媒体资源的占位符

## 优化策略亮点
1. **动态阈值**：根据相似度分布动态确定匹配阈值
2. **优先未使用资源**：优先分配未使用的图片，提高资源利用率
3. **重复使用控制**：智能控制图片重复使用频率和间距
4. **多阶段分配**：采用多阶段策略确保最优媒体分配
5. **整数线性规划**：使用数学优化模型求解最优分配方案 