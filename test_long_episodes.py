#!/usr/bin/env python3
"""
测试长剧集模式的分集逻辑和剧本生成能力
"""

import json
import os
import sys
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ENABLE_SPINE_PIPELINE
from generate_episodes import (
    _determine_episodes_with_spine, 
    StoryParameters,
    EPISODE_SCRIPT_MIN_WORDS,
    EPISODE_SCRIPT_MAX_WORDS,
    TARGET_WORDS_PER_EPISODE
)


def test_long_episode_parameters():
    """测试长剧集参数设置"""
    print("=" * 60)
    print("测试长剧集参数设置")
    print("=" * 60)
    
    # 检查参数设置
    story_params = StoryParameters()
    
    print(f"最少集数: {story_params.min_episodes}")
    print(f"最多集数: {story_params.max_episodes}")
    print(f"目标单集长度: {story_params.target_episode_length} 词")
    print(f"最少场景数: {story_params.min_scenes_per_episode}")
    print(f"最多场景数: {story_params.max_scenes_per_episode}")
    print(f"批次大小: {story_params.batch_size}")
    
    print(f"\n剧本长度参数:")
    print(f"最少词数: {EPISODE_SCRIPT_MIN_WORDS}")
    print(f"最多词数: {EPISODE_SCRIPT_MAX_WORDS}")
    print(f"目标词数: {TARGET_WORDS_PER_EPISODE}")
    
    # 验证参数是否适合长剧集
    if story_params.max_episodes <= 10:
        print("✅ 集数控制在10集以内")
    else:
        print("❌ 集数超过10集")
    
    if story_params.target_episode_length >= 6000:
        print("✅ 单集长度支持长剧集")
    else:
        print("❌ 单集长度不足以支持长剧集")
    
    if story_params.max_scenes_per_episode >= 15:
        print("✅ 场景数支持复杂剧情")
    else:
        print("❌ 场景数可能不足以支持复杂剧情")


def create_mock_data_for_long_episodes(num_chapters: int = 200) -> tuple:
    """为长剧集测试创建模拟数据"""
    # 创建group_summaries
    groups = []
    chapters_per_group = 20  # 增加每组章节数以适应长剧集
    
    for i in range(0, num_chapters, chapters_per_group):
        group_chapters = list(range(i + 1, min(i + chapters_per_group + 1, num_chapters + 1)))
        group = {
            "group_id": f"g_{str(i//chapters_per_group + 1).zfill(3)}",
            "chapter_numbers": group_chapters,
            "group_summary": f"Group {i//chapters_per_group + 1} - 复杂剧情发展，包含多个角色线和冲突",
            "main_events": [
                f"主要事件 {i//chapters_per_group + 1}.1 - 角色冲突升级",
                f"主要事件 {i//chapters_per_group + 1}.2 - 关键转折点",
                f"主要事件 {i//chapters_per_group + 1}.3 - 情感高潮"
            ],
            "character_arcs": [
                f"角色弧线 {i//chapters_per_group + 1} - 主角成长",
                f"配角发展线 {i//chapters_per_group + 1}"
            ]
        }
        groups.append(group)
    
    group_summaries = {"groups": groups}
    
    # 创建global_outline
    global_outline = {
        "story_theme": "史诗级奇幻冒险",
        "main_characters": ["主角", "导师", "反派", "盟友A", "盟友B"],
        "story_arc": "英雄之旅的完整演绎",
        "total_estimated_episodes": 8,  # 长剧集目标
        "world_setting": {
            "genre": "奇幻",
            "tone": "史诗",
            "complexity": "高"
        }
    }
    
    return group_summaries, global_outline


def test_long_episode_allocation():
    """测试长剧集分配逻辑"""
    print("\n" + "=" * 60)
    print("测试长剧集分配逻辑")
    print("=" * 60)
    
    test_cases = [
        {"chapters": 100, "expected_episodes": "5集", "description": "中等长度小说"},
        {"chapters": 200, "expected_episodes": "8-10集", "description": "长篇小说"},
        {"chapters": 400, "expected_episodes": "10集", "description": "超长篇小说"},
        {"chapters": 800, "expected_episodes": "10集", "description": "巨型小说"}
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['description']} ({case['chapters']} 章节)")
        print("-" * 50)
        
        group_summaries, global_outline = create_mock_data_for_long_episodes(case['chapters'])
        
        try:
            if ENABLE_SPINE_PIPELINE:
                result = _determine_episodes_with_spine(
                    group_summaries, global_outline, "engaging", "Chinese"
                )
                
                total_episodes = result.get("total_episodes", 0)
                phases_count = len(result.get("spine_phases", []))
                
                print(f"总章节数: {case['chapters']}")
                print(f"阶段数: {phases_count}")
                print(f"总集数: {total_episodes}")
                print(f"预期集数: {case['expected_episodes']}")
                
                # 计算平均每集章节数
                if total_episodes > 0:
                    avg_chapters_per_episode = case['chapters'] / total_episodes
                    print(f"平均每集章节数: {avg_chapters_per_episode:.1f}")
                    
                    # 估算每集时长（假设每章节约500词，每分钟150词）
                    estimated_words_per_episode = avg_chapters_per_episode * 500
                    estimated_minutes_per_episode = estimated_words_per_episode / 150
                    print(f"估算每集时长: {estimated_minutes_per_episode:.1f} 分钟")
                
                # 检查是否符合长剧集要求
                if total_episodes <= 10:
                    print("✅ 集数控制在10集以内")
                else:
                    print("❌ 集数超过10集")
                
                if total_episodes >= 5:
                    print("✅ 集数不会过少")
                else:
                    print("⚠️  集数可能过少")
                
                # 分析内容密度
                if result.get("spine_phases"):
                    chapter_counts = [len(phase.get("chapters", [])) for phase in result["spine_phases"]]
                    min_chapters = min(chapter_counts)
                    max_chapters = max(chapter_counts)
                    print(f"章节分布: 最少{min_chapters}章/集, 最多{max_chapters}章/集")
                    
                    if min_chapters >= 10:
                        print("✅ 每集内容充实")
                    else:
                        print("⚠️  部分集数内容可能不够充实")
                
            else:
                print("Spine流水线未启用，跳过测试")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()


def analyze_long_episode_benefits():
    """分析长剧集模式的优势"""
    print("\n" + "=" * 60)
    print("长剧集模式优势分析")
    print("=" * 60)
    
    print("📈 内容深度优势:")
    print("  • 每集6000-8000词，支持复杂剧情发展")
    print("  • 多线程叙事，角色关系深度探索")
    print("  • 充分的世界观建设和氛围营造")
    print("  • 角色心理描写和成长弧线完整展现")
    
    print("\n🎯 观众体验优势:")
    print("  • 减少集数，降低追剧负担")
    print("  • 每集内容更充实，观看满足感更强")
    print("  • 减少剧情碎片化，故事连贯性更好")
    print("  • 适合深度观看和讨论")
    
    print("\n⚡ 制作效率优势:")
    print("  • 减少集数，降低整体制作工作量")
    print("  • 每集制作可以更精细化")
    print("  • 减少重复性工作（片头片尾等）")
    print("  • 更容易控制整体质量")
    
    print("\n📊 技术实现优势:")
    print("  • 更好的章节整合，减少内容割裂")
    print("  • 角色一致性更容易维护")
    print("  • 剧情连贯性检查更简单")
    print("  • 适合AI生成的长文本能力")


def main():
    """主测试函数"""
    print("开始测试长剧集模式...")
    
    # 测试参数设置
    test_long_episode_parameters()
    
    # 测试分配逻辑
    test_long_episode_allocation()
    
    # 分析优势
    analyze_long_episode_benefits()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("长剧集模式已优化，支持:")
    print("• 5-10集的合理集数控制")
    print("• 6000-8000词的长剧集内容")
    print("• 10-20个场景的复杂剧情结构")
    print("• 多线程叙事和深度角色发展")
    print("=" * 60)


if __name__ == "__main__":
    main()
